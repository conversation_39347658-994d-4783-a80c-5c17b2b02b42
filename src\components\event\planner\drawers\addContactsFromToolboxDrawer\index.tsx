import { useState } from 'react'
import { Drawer } from 'flowbite-react'
import HSDrawer from 'components/drawer'
import HSButton from 'components/button'
import HSTable from 'components/table'
import { supplierContactRoles } from 'lib/helpers/supplierContacts'
import { useLoadSupplierContacts } from 'lib/services/organizations.service'
import Loader from 'components/loader'
import type { ISupplierContact } from 'models/affiliateOrganizations'

interface IAddQuestionsFromToolboxDrawer {
	onClose: () => void
	onAddSelected: (selectedContacts: ToolboxItem[]) => void
	organizationId?: string
	show: boolean
}

interface ToolboxItem {
	id: string
	name: string
	firstName: string
	lastName: string
	phoneNumber: string
	companyName: string
	email: string
	role: string
	roleType: string
	plannerNotes: string
	isHotContact: boolean
	title: string
}

const AddContactsFromToolboxDrawer = ({
	onClose,
	onAddSelected,
	organizationId,
	show
}: IAddQuestionsFromToolboxDrawer) => {
	const [selectedContacts, setSelectedContacts] = useState<ToolboxItem[]>([])
	const { data: supplierContacts, isFetching } = useLoadSupplierContacts(
		organizationId ?? ''
	)

	const onClickAddContacts = () => {
		onAddSelected([...selectedContacts])
		setSelectedContacts([])
		onClose()
	}

	return (
		<HSDrawer
			position='right'
			onClose={onClose}
			open={show}
			style={{ width: '1400px' }}
		>
			<div className='flex h-full flex-col'>
				<Drawer.Header title='Add From Toolbox' titleIcon={() => null} />
				<div className='flex-1 p-4'>
					<p className='mb-4 text-sm text-gray-600'>
						Select items to include in the RFP
					</p>

					{isFetching ? (
						<Loader />
					) : (
						<HSTable
							columns={[
								{
									field: 'name',
									headerText: 'NAME',
									width: 200,
									render: (row: ISupplierContact) => (
										<div style={{ position: 'relative' }}>
											<div className='text-sm'>
												{row.firstName ?? ''} {row.lastName ?? ''}
											</div>
											{Boolean(row.role === supplierContactRoles.hot?.key)}
										</div>
									)
								},
								{
									field: 'role',
									headerText: 'ROLE',
									width: 150,
									render: (row: ISupplierContact) => (
										<div className='text-sm'>{row.role}</div>
									)
								},
								{
									field: 'title',
									headerText: 'TITLE',
									width: 150,
									render: (row: ISupplierContact) => (
										<div className='text-sm'>{row.title ?? ''}</div>
									)
								},
								{
									field: 'companyName',
									headerText: 'COMPANY',
									width: 200,
									render: (row: ISupplierContact) => (
										<div className='text-sm'>{row.companyName}</div>
									)
								},
								{
									field: 'email',
									headerText: 'EMAIL',
									width: 200,
									render: (row: ISupplierContact) => (
										<div className='text-sm'>{row.id}</div>
									)
								},
								{
									field: 'phoneNumber',
									headerText: 'PHONE',
									width: 150,
									render: (row: ISupplierContact) => (
										<div className='text-sm'>{row.phoneNumber}</div>
									)
								}
							]}
							rows={supplierContacts ?? []}
							allowSelection
							allowPaging
							rowSelected={event => {
								const row = event.data as unknown as ToolboxItem
								if (!row.isHotContact) {
									setSelectedContacts([...selectedContacts, row])
								}
							}}
							rowDeselected={event =>
								setSelectedContacts(
									selectedContacts.filter(
										contact =>
											contact.id !== (event.data as unknown as ToolboxItem).id
									)
								)
							}
						/>
					)}
				</div>
				<div className='mt-auto p-4'>
					<div className='flex justify-end gap-2'>
						<HSButton color='light' onClick={onClose}>
							Cancel
						</HSButton>
						<HSButton
							disabled={selectedContacts.length === 0}
							onClick={onClickAddContacts}
						>
							Add Selected Items
						</HSButton>
					</div>
				</div>
			</div>
		</HSDrawer>
	)
}

export default AddContactsFromToolboxDrawer
