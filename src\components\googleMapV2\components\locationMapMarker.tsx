import type { IconDefinition } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { AdvancedMarker, Pin } from '@vis.gl/react-google-maps'

interface LocationMapMarkerProperties {
	position: google.maps.LatLngLiteral
	onClick?: () => void
	backgroundColor?: string | null
	title?: string | null
	icon: IconDefinition
	iconClassName?: string | null
}

const LocationMapMarker = (properties: LocationMapMarkerProperties) => {
	const { position, backgroundColor, onClick, title, icon, iconClassName } =
		properties
	return (
		<div title={title ?? ''}>
			<AdvancedMarker position={position} onClick={onClick}>
				<Pin
					background={backgroundColor ?? '#0a587e'}
					borderColor='#1e89a1'
					// glyphColor='#0f677a'
				>
					<FontAwesomeIcon icon={icon} className={iconClassName || ''} />
				</Pin>
			</AdvancedMarker>
		</div>
	)
}

export default LocationMapMarker
