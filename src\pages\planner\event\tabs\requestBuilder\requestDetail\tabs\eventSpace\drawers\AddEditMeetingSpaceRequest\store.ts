import type {
	IFoodAndBeverageRequest,
	IMeetingSpaceRequest
} from 'models/proposalResponseMonitor'
import { create } from 'zustand'

interface MeetingSpaceRequestStore {
	meetingSpaceRequest: IMeetingSpaceRequest | null
	setMeetingSpaceRequest: (
		meetingSpaceRequest: IMeetingSpaceRequest | null
	) => void
	foodAndBeverages: IFoodAndBeverageRequest[] | null
	setFoodAndBeverages: (
		foodAndBeverages: IFoodAndBeverageRequest[] | null
	) => void
}

const meetingSpaceRequestStore = create<MeetingSpaceRequestStore>(set => ({
	meetingSpaceRequest: null,
	setMeetingSpaceRequest: (meetingSpaceRequest: IMeetingSpaceRequest | null) =>
		set({ meetingSpaceRequest }),
	foodAndBeverages: null,
	setFoodAndBeverages: (foodAndBeverages: IFoodAndBeverageRequest[] | null) =>
		set({ foodAndBeverages })
}))

export default meetingSpaceRequestStore
