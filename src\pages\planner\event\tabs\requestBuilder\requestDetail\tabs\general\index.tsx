/* eslint-disable react/no-array-index-key */
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSDatePicker from 'components/datePicker'
import Loader from 'components/loader'
import HSSelect from 'components/select'
import HSTextField from 'components/textField'
import HSTooltip from 'components/tooltip'
import { eventTypeOptions } from 'lib/helpers/eventTypes'
import { groupTypeOptions } from 'lib/helpers/groupTypes'
import industryTypeOptions from 'lib/helpers/industryTypes'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { filterEventTypes, filterIndustryTypes } from './helper'
import {
	addBusinessDays,
	addDays,
	formatISO,
	isAfter,
	parseISO,
	subBusinessDays
} from 'date-fns'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import analytics from 'lib/analytics/segment/load'
import type { EventPlan } from 'models/proposalResponseMonitor'
import type { ChangedEventArgs } from '@syncfusion/ej2-react-calendars'
import { memo, useMemo } from 'react'
import useAttachmentsStore from 'lib/store/attachmentsStore'
import useEventPlanValidationErrors from 'lib/store/plannerEvent/validation'

interface EventProperties {
	name: string
	type: string
	value: string | number | boolean
}

const General = memo(() => {
	const { eventInfo: event, setProperty, mergeProperties } = eventInfoStore()
	const { userProfile } = useUserProfileContext()
	const { attachments } = useAttachmentsStore()

	const { responsesDueDate, selectionDate } = useEventPlanValidationErrors({
		eventPlan: event,
		attachments,
		userProfile
	})

	const onChange = (target: EventProperties) => {
		const { name, type, value } = target
		if (name === 'datesAreFlexible' && !value) {
			mergeProperties({
				datesAreFlexible: value as boolean,
				patternIsFlexible: false
			})

			analytics.track(`Flexible Dates Toggled ${value ? 'On' : 'Off'}`)
		} else if (name === 'allowDateSuggestions' && !value) {
			mergeProperties({
				allowDateSuggestions: value as boolean,
				patternIsFlexible: false
			})
			analytics.track(`Allow Date Suggestions Toggled ${value ? 'On' : 'Off'}`)
		} else if (name === 'meetingSpaceRequired') {
			let eventType = event?.type
			if (value) {
				if (eventType === 'roomsonly') {
					eventType = null
				}
			} else {
				eventType = 'roomsonly'
			}
			mergeProperties({
				[name as keyof EventPlan]: value,
				type
			})
		} else if (name === 'groupType' && value === 'wedding') {
			mergeProperties({
				[name]: value,
				type: 'wedding',
				industryType: 'other'
			})
		} else if (
			name === 'groupType' &&
			value !== 'association' &&
			value !== 'corporate'
		) {
			mergeProperties({
				[name as keyof EventPlan]: value,
				industryType: 'other'
			})
		} else {
			setProperty(name, value, type)
		}
	}

	const onChangeDate = (
		name: 'startDate' | 'responsesDueDate' | 'selectionDate',
		value: Date | undefined | null
	) => {
		if (event && value) {
			if (
				name === 'startDate' &&
				(event.endDate === null || !isAfter(parseISO(event.endDate), value))
			) {
				mergeProperties({
					startDate: formatISO(value, { representation: 'date' }),
					endDate: formatISO(addDays(value, (event.totalDays || 2) - 1), {
						representation: 'date'
					})
				})
			} else if (
				name === 'responsesDueDate' &&
				(event.selectionDate === null ||
					!isAfter(parseISO(event.selectionDate), value))
			) {
				mergeProperties({
					responsesDueDate: formatISO(value, { representation: 'date' }),
					selectionDate: formatISO(addDays(value, 7), {
						representation: 'date'
					})
				})
			} else {
				setProperty(name, formatISO(value, { representation: 'date' }), 'date')
			}
		}
	}

	const availableEventOptions = useMemo(
		() =>
			event
				? eventTypeOptions
						.filter(item => filterEventTypes(item, event))
						.map((o, index) => (
							<option key={index} value={o.value}>
								{o.name}
							</option>
						))
				: [],
		[event]
	)

	return event ? (
		<div className='flex flex-col gap-2 px-6 pb-6 pt-4'>
			<div className='text-lg font-semibold text-gray-900'>
				General Information
			</div>
			<div className='flex max-w-xl'>
				<div className='card'>
					<div className='flex flex-col gap-4 border-b p-4'>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-normal text-gray-700'>RFP Name</div>
							<HSTextField
								defaultValue={event.name ?? ''}
								onChange={event_ =>
									onChange({
										name: 'name',
										type: 'string',
										value: event_.target.value
									})
								}
								placeholder='RFP Name'
								isInvalid={!event.name}
							/>
						</div>
						<div className='flex items-center justify-between gap-4'>
							<div className='flex flex-col gap-2'>
								<div className='flex items-center gap-2'>
									<div className='text-sm font-normal text-gray-700'>
										Response Due
									</div>
									<HSTooltip content='Response Due'>
										<FontAwesomeIcon
											icon={faInfoCircle}
											className='text-gray-700'
										/>
									</HSTooltip>
								</div>
								<HSDatePicker
									value={
										event.responsesDueDate
											? parseISO(event.responsesDueDate)
											: undefined
									}
									min={
										event.created
											? addBusinessDays(
													new Date(),
													userProfile?.minimumResponsesDueDays ?? 4
												)
											: undefined
									}
									max={
										event.startDate
											? subBusinessDays(
													parseISO(event.startDate.split('T')[0]),
													1
												)
											: undefined
									}
									name='responsesDueDate'
									iserror={responsesDueDate.length > 0}
									change={(event_: ChangedEventArgs) =>
										onChangeDate('responsesDueDate', event_.value)
									}
									showClearButton={false}
									format='MMM dd, yyyy'
								/>
							</div>
							<div className='flex flex-col gap-2'>
								<div className='flex items-center gap-2'>
									<div className='text-sm font-normal text-gray-700'>
										Decision Date
									</div>
									<HSTooltip content='Decision Date'>
										<FontAwesomeIcon
											icon={faInfoCircle}
											className='text-gray-700'
										/>
									</HSTooltip>
								</div>
								<HSDatePicker
									name='selectionDate'
									value={
										event.selectionDate
											? parseISO(event.selectionDate)
											: undefined
									}
									min={
										event.responsesDueDate
											? addBusinessDays(parseISO(event.responsesDueDate), 1)
											: undefined
									}
									max={
										event.startDate
											? subBusinessDays(parseISO(event.startDate), 1)
											: undefined
									}
									iserror={selectionDate.length > 0}
									change={(event_: ChangedEventArgs) =>
										onChangeDate('selectionDate', event_.value)
									}
									showClearButton={false}
									format='MMM dd, yyyy'
								/>
							</div>
						</div>
					</div>
					<div className='flex flex-col gap-4 p-4'>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-normal text-gray-700'>
								Group Type
							</div>
							<HSSelect
								value={event.groupType || ''}
								isInvalid={!event.groupType}
								name='groupType'
								onChange={event_ =>
									onChange({
										name: 'groupType',
										type: 'string',
										value: event_.target.value
									})
								}
							>
								<option value=''>Please choose...</option>
								{groupTypeOptions.map((o, index) => (
									<option key={index} value={o.value}>
										{o.name}
									</option>
								))}
							</HSSelect>
						</div>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-normal text-gray-700'>
								Event Type
							</div>
							<HSSelect
								name='type'
								value={event.type || ''}
								disabled={!event.meetingSpaceRequired}
								onChange={event_ =>
									onChange({
										name: 'type',
										type: 'string',
										value: event_.target.value
									})
								}
							>
								<option value=''>Please choose...</option>
								{availableEventOptions}
							</HSSelect>
						</div>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-normal text-gray-700'>
								Industry Type
							</div>
							<HSSelect
								name='industryType'
								value={event.industryType || ''}
								onChange={event_ =>
									onChange({
										name: 'industryType',
										type: 'string',
										value: event_.target.value
									})
								}
							>
								<option value=''>Please choose...</option>
								{industryTypeOptions
									.filter(item => filterIndustryTypes(item, event))
									.map((o, index) => (
										<option key={index} value={o.value}>
											{o.name}
										</option>
									))}
							</HSSelect>
						</div>
					</div>
				</div>
			</div>
		</div>
	) : (
		<Loader />
	)
})

export default General
