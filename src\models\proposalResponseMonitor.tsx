import type { Duration } from 'date-fns'
import type { IConcessionRequest, ISuggestionRequest } from './eventPlans'
import type { SubscriptionInfo } from './organizations'
import type { Choice, IPlanner } from './planners'
import type { PropertyPartnerSeller } from './propertyPartner'
import type { IQuestion } from './questions'
import type { IAdditionalTaxesAndFees } from './venue'
import type { ISupplierContact } from './affiliateOrganizations'
import type { PreferredLocation } from './reporting'

export interface MappedEventPlan extends EventPlan {
	notSent: number
	requested: number
	requestedContacts: number
	requestedHopSkip: number
	reviewing: number
	proposed: number
	declined: number
	responded: number
	respondedContacts: number
	respondedHopSkip: number
	overallResponseRate: number
	remaining: number
	firstResponseTime: number
	averageResponseTime: number | null
	averageProposalValue: number | null
	hotelsWithoutHoteliers: { id: string | null; name: string | null }[]
	dueDays: number | null
	isTrial: boolean
	isBookingFee: boolean
	coverage: string | null
}

export interface IRfpResponseStatus {
	eventPlans: EventPlan[]
	organizations: Organizations
	// eslint-disable-next-line unicorn/no-keyword-prefix
	newHotelsAdded: INewHotelsAdded[]
}

export interface IEventPlanExtended extends EventPlan {
	concessionRequests: IConcessionRequest[] | null
	otherProposalRequests: ProposalRequest[] | null
	questions: IQuestion[] | null
	receivedToSubmitted: {
		minimum: {
			days: number
			hours: number
			minutes: number
		}
		average: {
			days: number
			hours: number
			minutes: number
		}
	} | null
	suggestionRequests: ISuggestionRequest[] | null
}

export interface Shortlist {
	id: string
	name: string
	hotels: Record<string, string>
	owner: {
		id: string | null
		firstName: string | null
		lastName: string | null
	}
	createdBy: string | null
	created: string | null
}

export interface EventPlan {
	ownerName: string | null
	responseRate: number | null
	venueLocations: string[]
	plannerName: string
	chainIds: string[]
	requestsResponded: number | null
	requestsAwaiting: number | null
	requestsProposed: number | null
	requestsTotal: number | null
	roomsValue: number | null
	roomNights: number | null
	foodAndBeverageValue: number | null
	fastestResponseTimeTicks: number | null
	totalResponseTimeTicks: number | null
	proposalsByChain: IProposalByChain[]
	id: string | null
	eventPlanId: string | null
	templateId: string | null
	organizationId: string | null
	organizationName: string | null
	includeOrganizationDescription: boolean | null
	itemType: string | null
	rfpCode: string | null
	isSiteSearch: boolean | null
	name: string | null
	contractSignatoryState: string | null
	responsesDueDate: string | null
	selectionDate: string | null
	status: string | null
	type: string | null
	groupType: string | null
	industryType: string | null
	notes: string | null
	description: string | null
	groupProfile: string | null
	successCriteria: string | null
	dealBreakers: string | null
	currencyCode: string | null
	measurementSystemCode: string | null
	platformSuggestions: PlatformSuggestions | null
	planners: IEventPlanner[] | null
	shortlists: Shortlist[] | null
	location: string | null
	latitude: number | null
	longitude: number | null
	startDate: string
	endDate: string | null
	startDayOfWeek: number | null
	totalDays: number | null
	alternateDates: { startDate: string | null; endDate: string | null }[] | null
	datesAreFlexible: boolean | null
	patternIsFlexible: boolean | null
	allowDateSuggestions: boolean | null
	selectedDateKey: string | null
	patternDays: PatternDays | null
	recurringEvent: boolean | null
	reservationMethod: string | null
	reservationMethods: ReservationMethods | null
	paymentMethods: PaymentMethods | null
	contractSigner: ContractSigner | null
	rfpOwner: RfpOwner | null
	shareEmailAddress: string | null
	sharePhoneNumber: string | null
	isSkipSearch: boolean | null
	isSelfSearch: boolean | null
	commissionable: boolean
	rebateEligible: boolean
	iataCode: string | null
	iataAgency: string | null
	isBookingFee: boolean
	bookingFeeRate: number | null
	estimatedSpend: number | null
	estimatedCommissionRate: number | null
	estimatedAttritionRate: number | null
	estimatedCommission: number | null
	estimatedCommissionDate: string | null
	meetingSpaceRequired: boolean | null
	peakMeetingRoomsRequired: number | null
	peakMeetingSpaceRequired: number | null
	peakAttendees: number | null
	largestMeetingSpaceRequired: number | null
	physicalDistancingRequired: boolean | null
	meetingSpaceComments: string | null
	estimatedNumberOfAttendees: number | null
	meetingSpaceBudget: number | null
	requireNamedMeetingSpace: boolean | null
	roomBlocksRequired: boolean | null
	totalRoomsRequested: number | null
	totalRoomTypes: number | null
	peakRooms: number | null
	totalRoomsBudget: number | null
	estimatedNumberOfRooms: number | null
	roomNightBudget: number | null
	roomRateMax: number | null
	roomRateMin: number | null
	canRequestAttritionRate: boolean | null
	requestAttritionRateDefault: number | null
	requestAttritionRate: number | null
	requestRebate: boolean | null
	rebateRequestAmount: number | null
	rebateRequestBasis: string | null
	firstSubmitted: string | null
	lastReportedResponseRate: number | null
	lastReportedResponseRateDate: string | null
	submitted: string | null
	created: string | null
	contracting: string | null
	firstContractSigned: string | null
	cancelled: string | null
	cancelledReason: string | null
	abandoned: string | null
	abandonedReason: string | null
	proposalsPaused: boolean | null
	proposalsPausedAt: string | null
	proposalsPausedBy: string | null
	proposalsPausedComment: string | null
	proposedValue: number | null
	contractedValue: number | null
	supportedBy: string | null
	createdBy: string | null
	lastModified: string | null
	lastModifiedBy: string | null
	deleted: string | null
	deletedBy: string | null
	isDeleted: boolean | null
	latestBidDateTime: string | null
	editingSentDateTime: string | null
	editedSentDateTime: string | null
	changeComments: string | null
	eventHistories: IEventHistory[] | null
	eventHistoryComments: string | null
	attachments: string[] | null
	meetingSpaceRequests: IMeetingSpaceRequest[] | null
	foodAndBeverageRequests: IFoodAndBeverageRequest[] | null
	roomBlockRequests: RoomBlock[] | null
	contractClauses: string[] | null
	concessionRequests: IConcessionRequest[] | null
	proposalRequests?: ProposalRequest[] | null
	summaryProposalRequests?: ProposalRequest[] | null
	proposalValues: ProposalValues | null
	proposalValuesAverage: ProposalValues | null
	siteSearch: SiteSearch | null
	supplierContacts: ISupplierContact[] | null
	templateOptions: string | null
	sourcingProfiles: string[] | null
	attachmentContainer: string | null
	proposalRequestStatuses: Record<
		string,
		| {
				status: string
				venueName: string
				venueId: string
				concessionValues: {
					concessionType: string
					savingsValue: number
				}[]
				rateSetContracted: {
					roomCostPlusPlus: number | null
					foodAndBeveragePlusPlus: number | null
					roomRentalPlusPlus: number | null
					serviceChargePlusPlus: number | null
				}
				rateSetProposed: {
					roomCostPlusPlus: number | null
					foodAndBeveragePlusPlus: number | null
					roomRentalPlusPlus: number | null
					serviceChargePlusPlus: number | null
				}
				proposalValues: ProposalValues

				chainId: string | null
				currencyCode: string
				brandId: string
				venueLocation: string
				responseTimeTicks: number | null
		  }[]
		| null
	>
	overdueForSelection: boolean | null
	overdueForResponses: boolean | null
	doNotCollectAttritionRate: boolean | null
	doNotCollectCutOffDate: boolean | null
	averageRoomRate: number | null
	rfpValue: number | null
	hasContractedValue: boolean | null
	savingsValue: number | null
	negotiatedValue: number | null
	concessionsValue: number | null
}
export interface IProposalByChain {
	chainId: string
	name: string
	count: number
	value: number
	totalRate: number
}

export interface IEventHistory {
	startDate: string | null
	endDate: string | null
	numberOfAttendees: number | null
	numberOfRooms: number | null
	propertyName: string | null
	roomRate: number | null
	fbSpend: number | null
	comments: string | null
}

export interface IFoodAndBeverageRequest {
	id: string | null
	type: string | null
	meetingSpaceRequestId: string | null
	dayNumber: number | null
	startTime: number | null
	endTime: number | null
	quantity: number | null
	quantityUnit: string | null
	notes: string | null
	sortIndex: number
}

export interface IMeetingSpaceRequest {
	id: string | null
	eventPlanId: string | null
	itemType: string | null
	meetingSpaceRequestType: string | null
	name: string | null
	layoutStyle: string | null
	dayNumber: number | null
	sortIndex: number | null
	startDateTime: string | null
	endDateTime: string | null
	startTime: number | null
	startMinutes: number | null
	endTime: number | null
	endMinutes: number | null
	capacity: number | null
	capacityMinimum: number | null
	capacityMaximum: number | null
	areaPerAttendee: number | null
	areaTotal: number | null
	areaTotalEdited: number | null
	additionalCapacity: number | null
	avRequired: boolean | null
	fbRequired: boolean | null
	physicalDistancingRequired: boolean | null
	excludeFromTotals: boolean | null
	isNegotiable: boolean | null
	diagramRequested: boolean | null
	hold24Hours: boolean | null
	notes: string | null
	created: string | null
	createdBy: string | null
	deleted: string | null
	deletedBy: string | null
	isDeleted: boolean | null
	fbDetails: { id: string | null } | undefined | null
}

interface PlatformSuggestions {
	optOut: string | null
	optOutBy: string | null
	optOutAt: string | null
}

export interface ContractSigner {
	name: string | null
	title: string | null
	address: string | null
	city: string | null
	state: string | null
	postalCode: string | null
	country: string | null
	email: string | null
	phone: string | null
}

export interface RfpOwner {
	name: string | null
	title: string | null
	address: string | null
	city: string | null
	state: string | null
	postalCode: string | null
	country: string | null
	email: string | null
	phone: string | null
}

export interface RoomBlock {
	id: string | null
	name: string | null
	peakSize: number
	paymentMethod: string | null
	allowPartialProposals: boolean | null
	minimumPeakRooms: number | null
	roomTypeRequests: RoomTypeRequest[]
	created: string | null
}

export interface RoomTypeRequest {
	roomType: number
	budget: number
	budgetLow: number | null
	roomNights: RoomNight[] | null
	notes: string | null
}

interface RoomNight {
	dayNumber: number
	roomsRequested: number | null
}

export interface SiteSearch {
	preferredLocation: {
		name: string | null
		customName: string | null
		latitude: number
		longitude: number
		preferred: boolean
		addedFromSearch: boolean
	}
	alternateLocations: PreferredLocation[]
	preferredBrands: { name: string; key: string }[]
	preferredStarRatings: { rating: number }[]
	otherBrands: string | null
	serviceLevel: string | null
	proximityMinutes: number | null
	proximityMiles: number | null
	proximityLocation: {
		name: string | null
		customName: string | null
		latitude: number
		longitude: number
		preferred: boolean
		addedFromSearch: boolean
	}
	additionalComments: string | null
	additionalLocations: {
		name: string | null
		customName: string | null
		latitude: number
		longitude: number
		preferred: boolean
		addedFromSearch: boolean
	}[]
	hotelTypes: string[]
}

interface IataProfile {
	id: string | null
	isDefault: boolean
	code: string | null
	agencyName: string | null
	type: string | null
	recertificationExpiryDate: string | null
	verifiedBy: string | null
	verified: string | null
	created: string | null
}

interface Organization {
	id: string | null
	userProfileId: string | null
	itemType: string | null
	accountType: string | null
	organizationType: string | null
	paymentCustomerId: string | null
	currentSubscriptionInfo: SubscriptionInfo | null
	name: string | null
	address: string | null
	address2: string | null
	city: string | null
	state: string | null
	zip: string | null
	country: string | null
	currencyCode: string | null
	measurementSystemCode: string | null
	primaryColor: string | null
	secondaryColor: string | null
	commissionEligible: boolean
	commissionWaived: boolean
	iataProfile: IataProfile
	iataProfiles: IataProfile[]
	canRequestAttritionRate: boolean | null
	requestAttritionRateDefault: number | null
	requestAttritionRate: number | null
	canRequestRebate: boolean | null
	rebateEligible: boolean
	minimumResponsesDueDays: number | null
	overrideMaximumProposalRequestLimit: number | null
	platformSuggestions: {
		optOut: boolean | null
		optOutBy: string | null
		optOutAt: string | null
	}
	addMembersWithEmailDomain: boolean
	memberEmailDomain: string | null
	isBogus: boolean
	firstAccountCreated: string | null
	totalMembers: number
	organizationRoles: unknown[]
	freeProEnabledRfpsRemaining: number
	canSendRfps: boolean | null
	sourcingStatuses: unknown[]
	declinedReasons: unknown[]
	createdBy: string | null
	created: string | null
	deleted: string | null
	deletedBy: string | null
	isDeleted: boolean
	attachmentContainer: string | null
}

export type Organizations = Record<string, Organization | null>

export interface INewHotelsAdded {
	date: string | null
	hotelsAdded: number
}

export interface RateDetail extends IProposalDates {
	key: string
	fbMinimum: string | null
	foodAndBeverageTaxRate: number | null
	avRate: number | null
	roomRental: number | null
	roomRentalTaxRate: number | null
	serviceChargeRate: string | number | null
	serviceChargeTaxRate: number | null
	meetingSpaceOnHold: boolean
	meetingSpaceOnHoldOption: string | null
}

export interface ICurrentBid {
	meetingSpaceAvailable: boolean
	meetingSpaceOnHoldOption: string | null
	roomRentalTaxRate: number | null
	foodAndBeverageTaxRate: number | null
	serviceChargeTaxRate: number | null
	roomRental: number | null
	promotionNotes: string | null
	promotionId: string | null
	comments: string | null
	taxesFeesAssessments: number | undefined | null | string
	rebateRequestApproved: boolean
	proposalDates: IProposalDates[] | null
	perRoomFees: number
	rebateAmount: number
	bidExpirationDate: string
	cutOffDate: string
	rebateRequestAmount: number
	meetingRates: RateDetail[] | null
	roomRates: IRoomRate[] | null
	attritionRate?: string | null
	serviceChargeRate: string | null
	isMeetingRateDependentOnDate: boolean | null
	fbMinimum: string | null
	offerDetailedFoodAndBeverage: boolean | null
	isPromotionApplied?: boolean | null
	meetingSpaceAssignments:
		| {
				tbd: string | null
				meetingRoomId: string | null
				meetingSpaceRequestId: string | null
				id: string | null
				notes: string | null
				customCapacity: number | null
		  }[]
		| null
	additionalTaxesAndFees: IAdditionalTaxesAndFees[]
	commissionPercent: null | string
	foodAndBeverageItems:
		| {
				id: string | null
				type: string | null
				foodAndBeverageRequestId: string | null
				quantity: number | null
				rate: number | null | undefined | string
				rateUnit: string | null
		  }[]
		| null
	offerDifferentRatesPerFbType: boolean | null
	isAverageWeighted: boolean
	averageRate: number | null
}

export interface ProposalRequest {
	proposed: ProposalRequest[] | null
	doNotCollectCutOffDate: boolean | null
	wonGroupTypes: string
	responseTimes: {
		wonOpenedToResponded: Duration
		wonReceivedToOpened: Duration
		wonRespondedToWon: Duration
	}
	totalOpportunities: number
	totalOpportunityValue: number
	id: string | null
	eventPlanId: string | null
	eventPlanName: string | null
	itemType: string | null
	rfpCode: string | null
	eventPlanStatus: string | null
	eventPlanType: string | null
	eventPlanGroupType: string | null
	eventPlanIndustryType: string | null
	eventPlanOrganizationName: string | null
	eventPlanRoomRateMin: number | null
	eventPlanRoomRateMax: number | null
	eventPlanTotalDays: number | null
	eventPlanPeakRooms: number | null
	eventPlanStartDate: string | null
	allowDateSuggestions: boolean | null
	meetingSpaceRequired: boolean | null
	roomBlocksRequired: boolean | null
	createdByOrganizationId: string | null
	organizationId: string | null
	organizationName: string | null
	commissionable: boolean
	isBookingFee: boolean | null
	isBookingFeeWaived: boolean | null
	bookingFeeWaived: number | null
	bookingFeeWaivedBy: string | null
	bookingFeeWaivedComment: string | null
	commissionPaid: number | null
	rebateEligible: boolean
	rebatePaid: number | null
	doNotCollectAttritionRate: boolean | null
	iataCode: string | null
	iataAgency: string | null
	canRequestAttritionRate: boolean | null
	requestAttritionRate: number | null
	totalRoomsRequested: number | null
	totalRoomsBudget: number | null
	meetingSpaceBudget: number | null
	maxValue: number | null
	roomRateMax: number | null
	roomRateMin: number | null
	proposalValues: ProposalValues
	proposalValue: number | null
	proposalValuesAverage: ProposalValues
	currencyCode: string | null
	measurementSystemCode: string | null
	shortlistRefs: string[]
	venueId: string
	venueName: string | null
	venueCity: string | null
	venueState: string | null
	venueImageUrl: string | null
	venueLocation: string | null
	geolocation: {
		type: string | null
		coordinates: [number, number]
		crs: {
			type: string | null
			properties: Record<string, string>
		}
	} | null
	destinations: { id: string; name: string }[]
	chainId: string | null
	brandId: string | null
	propertyOwner: PropertyPartnerSeller | null
	propertyManager: PropertyPartnerSeller | null
	propertySeller: PropertyPartnerSeller | null
	propertySellers: PropertyPartnerSeller[] | null
	selected: boolean
	plannerDeclined: boolean
	plannerDeclinedReason: string | null
	venueDeclinedReason: string | null
	plannerDeclinedComment: string | null
	venueDeclinedComment: string | null
	plannerDeclinedReasons: string[]
	venueDeclinedReasons: string[]
	status: string | null
	notes: string | null
	cancelContractingNotes: string | null
	isPlatformRecommendation: boolean
	promotionId: string | null
	supplierContacts: ISupplierContact[]
	supplierContactsEventPlan: ISupplierContact[]
	invitationMessage: string | null
	proposalContact: ProposalContact | null
	proposalRecipients: ProposalRecipient[] | null
	statusUpdated: string | null
	currentBid: ICurrentBid | null
	proposalDates: IProposalDates[] | null
	attachments: unknown[]
	firstSent: string | null
	sent: string | null
	responsesDueDate: string | null
	selectionDate: string | null
	sentBy: string | null
	sentByName: string | null
	received: ProposalRequest[] | null
	removed: string | null
	removedBy: string | null
	removedReason: string | null
	removedComment: string | null
	viewedComments: string | null
	declined: ProposalRequest[] | null
	submitted: string | null
	reviewed: string | null
	won: ProposalRequest[] | null
	lost: ProposalRequest[] | null
	signed: string | null
	expirationDate: string | null
	cutOffDate: string | null
	created: string | null
	createdBy: string | null
	deleted: string | null
	deletedBy: string | null
	isDeleted: boolean
	viewed: string | null
	proposalsPaused: boolean | null
	createdByOrganizationName?: string | null
	comparableHotelSource: string | null

	contentEngagementMetricsSummary: {
		lastUpdated: string | null
		metrics: {
			Impression: number
			ViewStart: number
			ViewEnd: number
			Conversion: number
			Download: number
		}
	}
	lostGroupTypes?: {
		type: string | null
		count: number
	}
	lostReasons:
		| {
				reason: string
				count: number
		  }[]
		| []
	declinedReasons:
		| {
				reason: string
				count: number
		  }[]
		| []
	venueCountry: string | null
	plannerOrganization: {
		id: string | null
		city: string | null
		state: string | null
		country: string | null
	}
	totalContractedRooms: string | null
}
export type PatternDays = Record<
	'Su' | 'Mo' | 'Tu' | 'We' | 'Th' | 'Fr' | 'Sa',
	boolean
>

export interface IRoomRate {
	dayNumber: number | null
	key: string | null
	offered: number | null
	rate: number | null
	requested: number | null
	roomBlockIndex: number | null
	roomType: number | null
}

export interface IProposalDates {
	concessionValuePlanned?: number | null
	totalValue: number | null
	averageValue: number | null
	key: string | null
	startDate: string | null
	endDate: string | null
	declineToBid: boolean | null
	isVaryingRate: boolean | null
	isOfferedDifferentThanRequested: boolean | null
	preferred: boolean | null
	alternate: boolean | null
	suggested: boolean | null
	contracted: boolean | null
	value: number | null
	proposalValues: ProposalValues | null
	roomRate?: number | null
	roomFees?: number | null
	roomTax?: number | null
	foodAndBeverage?: number | null
	totalRoomCost?: number | null
	serviceCharge?: number | null
	estTotalCost?: number | null
}

export interface ReservationMethods {
	bookingLink: boolean
	callIn: boolean
	masterList: boolean
}

export interface PaymentMethods {
	guestRooms: string | null
	meetingSpace: string | null
	foodAndBeverage: string | null
	audioVisual: string | null
	setupMaster: boolean | null
}

export interface ProposalContact {
	name: string | null
	email: string | null
	phone: string | null
	createdBy: string | null
}

export interface ProposalValues {
	totalRoomsOffered: number | null
	averageRoomRate: number
	isAverageWeighted: boolean
	roomRate: number | null
	roomCost: number | null
	roomTaxRate: number
	roomTax: number
	roomFees: number | null
	rebateAmount: number | null
	rebateRequestBasis: string | null
	foodAndBeverage: number | null
	foodAndBeverageTaxRate: number
	foodAndBeverageTax: number
	roomRental: number | null
	roomRentalTaxRate: number
	roomRentalTax: number
	serviceChargeRate: number
	serviceCharge: number
	serviceChargeTaxRate: number
	serviceChargeTax: number
	concessionValueProposed: number | null
	concessionValuePlanned: number | null
	portfolioType: string | null
}

interface ProposalRecipient {
	firstName: string | null
	lastName: string | null
	email: string | null
	createdBy: string | null
	invitationMessage: string | null
	sent: string | null
}

export interface IConcessionEvent {
	concessionRequest: IPlanner
	responses: IConcessionEventResponse[]
}

export interface IConcessionEventResponse {
	id: string | null
	itemType: string | null
	concessionRequestId: string | null
	eventPlanId: string | null
	venueId: string | null
	venueName: string | null
	proposalRequestStatus: string | null
	createdByType: string | null
	response: string | null
	responses: Choice[] | null
	comment: string | null
	concessionValueProposed: number | null
	concessionValuePlanned: number | null
	created: Date | null
	createdBy: string | null
}

export interface IEventPlanner {
	id: string | null
	firstName: string | null
	lastName: string | null
	owner: boolean | null
	isEditor: boolean | null
	canJoinChat: boolean | null
}

export interface ITrailLimitStatus {
	rfpsSent: number | null
	rfpLimit: number | null
}

export interface Received {
	totalOpportunities: number
	totalOpportunityValue: number
	totalContractedRooms: string | null
	id: string | null
	eventPlanId: string | null
	eventPlanName: string
	itemType: string
	rfpCode: string
	eventPlanStatus: string
	eventPlanType?: string
	eventPlanGroupType?: string
	eventPlanIndustryType: string | null
	eventPlanOrganizationName: string
	eventPlanRoomRateMin?: number
	eventPlanRoomRateMax?: number
	eventPlanTotalDays: number
	eventPlanPeakRooms: number
	eventPlanStartDate: string
	allowDateSuggestions: boolean
	meetingSpaceRequired: boolean
	roomBlocksRequired: boolean
	organizationId: string
	organizationName: string
	commissionable: boolean
	isBookingFee: boolean
	isBookingFeeWaived?: boolean
	bookingFeeWaived: string | null
	bookingFeeWaivedBy: string | null
	bookingFeeWaivedComment: string | null
	commissionPaid: string | null
	rebateEligible: boolean
	rebatePaid: string | null
	iataCode: string
	iataAgency: string
	canRequestAttritionRate?: boolean
	requestAttritionRate: string | null
	totalRoomsRequested: number
	totalRoomsBudget: number
	meetingSpaceBudget: string | null
	maxValue: string | null
	roomRateMax: string | null
	roomRateMin: string | null
	proposalValues: ProposalValues
	currencyCode: string
	measurementSystemCode: string
	shortlistRefs: string[] | null
	venueId: string
	venueName: string
	venueCity: string
	venueState: string
	venueImageUrl: string
	venueLocation: string
	geolocation: string | null
	destinations: string[] | null
	chainId: string
	brandId: string
	propertyOwner: string | null
	propertyManager: string | null
	propertySeller: string | null
	propertySellers: string[] | null
	selected: boolean
	plannerDeclined: boolean
	plannerDeclinedReason: string | null
	venueDeclinedReason: string | null
	plannerDeclinedComment: string | null
	venueDeclinedComment: string | null
	plannerDeclinedReasons: string[] | null
	venueDeclinedReasons: string[] | null
	status: string
	notes: string | null
	cancelContractingNotes: string | null
	isPlatformRecommendation: boolean
	promotionId: string | null
	supplierContacts: ISupplierContact[] | null
	invitationMessage: string | null
	proposalContact: ProposalContact
	proposalRecipients: string[] | null
	statusUpdated: string
	currentBid: string | null
	attachments: string[] | null
	firstSent: string
	sent: string
	responsesDueDate?: string
	selectionDate?: string
	sentBy: string
	sentByName: string

	removed: string | null
	removedBy: string | null
	removedReason: string | null
	removedComment: string | null
	viewedComments: string | null
	declined: string | null
	submitted: string | null
	reviewed: string | null
	won: string | null
	lost: string | null
	signed: string | null
	expirationDate?: string
	cutOffDate?: string
	created: string
	createdBy: string
	deleted: string | null
	deletedBy: string | null
	isDeleted: boolean
	proposalValuesAverage: ProposalValues
}
