/* eslint-disable @typescript-eslint/max-params */
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import { format, parseISO } from 'date-fns'
import type { EventPlan } from 'models/proposalResponseMonitor'
import {
	faEdit,
	faShareNodes,
	faTrashXmark
} from '@fortawesome/pro-light-svg-icons'
import { Link } from 'react-router-dom'
import HSTooltip from 'components/tooltip'

export const renderHotelCount = (item: EventPlan) => (
	<div className='text-sm font-normal text-gray-500'>
		{item.proposalRequests?.length} Hotel
		{item.proposalRequests?.length === 1 ? '' : 's'}
	</div>
)

export const renderDateCreated = (item: EventPlan) => (
	<div className='text-sm font-normal text-gray-500'>
		{item.created ? format(parseISO(item.created), 'PP') : ''}
	</div>
)

export const renderCreatedBy = (item: EventPlan) => (
	<div className='text-sm font-normal text-gray-500'>
		{item.planners?.[0].firstName} {item.planners?.[0].lastName}
	</div>
)

export const renderViewList = (item: EventPlan) => (
	<Link to={`/site-search/search/${item.id}`}>
		<HSButton size='sm'>View</HSButton>
	</Link>
)

export const renderActions = (
	item: EventPlan,
	handleDelete: () => void,
	handleShare: () => void,
	handleEdit: () => void
) => (
	<div className='flex items-center gap-2'>
		<HSTooltip content='Share'>
			<HSButton size='xs' color='light' onClick={handleShare}>
				<FontAwesomeIcon icon={faShareNodes} size='lg' />
			</HSButton>
		</HSTooltip>
		<HSTooltip content='Edit'>
			<HSButton size='xs' color='light' onClick={handleEdit}>
				<FontAwesomeIcon icon={faEdit} size='lg' />
			</HSButton>
		</HSTooltip>
		<HSTooltip content='Delete'>
			<HSButton size='xs' color='light' onClick={handleDelete}>
				<FontAwesomeIcon
					icon={faTrashXmark}
					className='text-red-500'
					size='lg'
				/>
			</HSButton>
		</HSTooltip>
	</div>
)

export const renderName = (item: EventPlan) => (
	<div className='text-sm font-normal text-gray-500'>{item.name}</div>
)
