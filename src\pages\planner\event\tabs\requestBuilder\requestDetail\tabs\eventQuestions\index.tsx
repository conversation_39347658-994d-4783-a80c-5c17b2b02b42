import {
	faEdit,
	faEye,
	faPlus,
	faTrashXmark
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import { getDefaultQuestion } from 'components/profile/tabs/questions/helper'
import QuestionModal from 'components/questionModal'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import {
	useAddQuestionToEvent,
	useDeleteQuestionFromEvent,
	useGetQuestionForEvent
} from 'lib/services/planner.service'
import questionStore from 'lib/store/plannerEvent/questionStore'
import type { IQuestion, IQuestionEvent } from 'models/questions'
import { useState } from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import QuestionTable from './questionTable'
import QuestionResponseDrawer from './questionResponseDrawer'

const EventQuestions = () => {
	const { eventId } = useParams()
	// const tableReference = useRef<{ resetSelection: () => void }>(null)
	const { questions, setObject: setQuestions } = questionStore()
	const { userProfile } = useUserProfileContext()
	// const { data: profileQuestions } = useGetQuestions(userProfile?.id ?? '')
	const { mutateAsync: addQuestion } = useAddQuestionToEvent()
	const { refetch: getQuestionForEvent } = useGetQuestionForEvent(
		eventId ?? '',
		!!eventId
	)
	const { mutateAsync: deleteQuestion } = useDeleteQuestionFromEvent()

	// const [showAddFromToolboxDrawer, setShowAddFromToolboxDrawer] =
	// 	useState(false)
	const [showCreateQuestionDrawer, setShowCreateQuestionDrawer] =
		useState(false)
	// const [selectedQuestionIds, setSelectedQuestionIds] = useState<string[]>([])
	const [selectedQuestion, setSelectedQuestion] =
		useState<Partial<IQuestion>>(getDefaultQuestion())
	const [editExisting, setEditExisting] = useState(false)
	const [showResponseContext, setShowResponseContext] = useState<{
		show: boolean
		question: IQuestionEvent | undefined
	}>({
		show: false,
		question: undefined
	})

	const refetchQuestion = () => {
		getQuestionForEvent()
			.then((response: { data: IQuestionEvent[] | undefined }) => {
				if (response.data) setQuestions(response.data)
			})
			.catch(() => {
				console.log('Error loading questions')
			})
	}

	// const addQuestionFromToolbox = () => {
	// 	if (selectedQuestionIds.length === 0) {
	// 		toast.warning('Please select at least one question to add.')
	// 	} else {
	// 		const selectedQuestions = profileQuestions?.filter(item =>
	// 			selectedQuestionIds.includes(item.id ?? '')
	// 		)
	// 		const promises = selectedQuestions?.map(async item =>
	// 			addQuestion({
	// 				eventPlanId: eventId || '',
	// 				question: item
	// 			})
	// 		)
	// 		Promise.all(promises ?? [])
	// 			.then(() => {
	// 				refetchQuestion()
	// 			})
	// 			.catch((error: unknown) => {
	// 				console.error(error)
	// 				toast.error('Error adding questions from toolbox.')
	// 			})
	// 		toast.success('Questions added successfully.')
	// 	}
	// 	closeAddFromToolboxDrawer()
	// }

	const onActionEdit = (item: IQuestion) => {
		setEditExisting(true)
		setSelectedQuestion(item)
		setShowCreateQuestionDrawer(true)
	}

	const closeCreateQuestionDrawer = () => {
		setShowCreateQuestionDrawer(false)
		setEditExisting(false)
		setSelectedQuestion(getDefaultQuestion())
	}

	const onActionDelete = (item: IQuestion) => {
		deleteQuestion({
			eventId: eventId ?? '',
			itemId: item.id ?? ''
		})
			.then(() => {
				refetchQuestion()
			})
			.catch((error: unknown) => console.error(error))
	}

	const onAddUpdate = (question_: IQuestion, stayOpen: boolean) => {
		setShowCreateQuestionDrawer(stayOpen)
		addQuestion({
			eventPlanId: eventId ?? '',
			question: question_
		})
			.then(() => {
				toast.success(
					`Question ${editExisting ? 'updated' : 'added'} successfully.`
				)
				refetchQuestion()
			})
			.catch((error: unknown) => {
				console.error(error)
				toast.error('Error adding question.')
			})
			.finally(() => {
				setEditExisting(false)
				setSelectedQuestion(getDefaultQuestion())
			})
	}

	const actionTemplate = (item: IQuestion) => (
		<div className='flex items-center justify-center gap-2'>
			<HSButton
				color='light'
				onClick={() => {
					setShowResponseContext({
						show: true,
						question: questions.find(q => q.question.id === item.id)
					})
				}}
				size='sm'
			>
				<FontAwesomeIcon icon={faEye} />
			</HSButton>
			<HSButton color='light' onClick={() => onActionEdit(item)} size='sm'>
				<FontAwesomeIcon icon={faEdit} />
			</HSButton>
			<HSButton color='light' onClick={() => onActionDelete(item)} size='sm'>
				<FontAwesomeIcon icon={faTrashXmark} className='text-red-600' />
			</HSButton>
		</div>
	)

	return (
		<>
			<div className='flex flex-col gap-4 p-4'>
				<div className='flex items-center justify-between gap-4'>
					<div className='flex flex-col'>
						<div className='text-lg font-semibold text-gray-900'>Questions</div>
						<div className='text-sm font-normal text-gray-500'>
							Personalize Your RFP with Custom Questions
						</div>
					</div>
					<div className='flex items-center gap-4'>
						{/* <HSButton
							color='text'
							onClick={() => setShowAddFromToolboxDrawer(true)}
						>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faToolbox} />
								Add from Toolbox
							</div>
						</HSButton> */}

						<HSButton onClick={() => setShowCreateQuestionDrawer(true)}>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faPlus} />
								Create New
							</div>
						</HSButton>
					</div>
				</div>

				<QuestionTable questions={questions} actionTemplate={actionTemplate} />
			</div>
			{/* <HSDrawer
				onClose={() => setShowAddFromToolboxDrawer(false)}
				open={showAddFromToolboxDrawer}
				title='Add from Toolbox'
				position='right'
				style={{ width: '50vw' }}
			>
				<div className='flex h-full flex-col justify-between gap-4'>
					<div className='flex flex-col gap-4'>
						<div className='text-lg font-semibold text-gray-900'>
							Add from Toolbox
						</div>
						<div className='text-sm font-normal text-gray-500'>
							Select items to include to the RFP
						</div>
						<HSTable
							ref={tableReference}
							allowSelection
							columns={[
								{
									field: 'text',
									headerText: 'Question Category',
									render: item =>
										requestGroups[item.requestGroupId ?? '']?.name ?? ''
								},
								{
									field: 'text',
									headerText: 'Question'
								}
							]}
							rows={
								profileQuestions?.filter(
									p => !questions.map(q => q.question.id).includes(p.id)
								) ?? []
							}
							rowSelected={event => {
								if (Array.isArray(event.data)) {
									setSelectedQuestionIds(selectedIds => [
										...selectedIds,
										...(event.data as IQuestion[]).map(a => a.id ?? '')
									])
								} else {
									setSelectedQuestionIds(selectedIds => [
										...selectedIds,
										(event.data as IQuestion).id ?? ''
									])
								}
							}}
							rowDeselected={event => {
								if (Array.isArray(event.data)) {
									setSelectedQuestionIds(selectedIds =>
										selectedIds.filter(
											id =>
												!(event.data as IQuestion[]).map(a => a.id).includes(id)
										)
									)
								} else {
									setSelectedQuestionIds(selectedIds =>
										selectedIds.filter(
											id => id !== (event.data as IQuestion).id
										)
									)
								}
							}}
						/>
					</div>
					<div className='flex w-full justify-end gap-4'>
						<HSButton color='light' onClick={closeAddFromToolboxDrawer}>
							Cancel
						</HSButton>
						<HSButton
							onClick={addQuestionFromToolbox}
							disabled={selectedQuestionIds.length === 0}
						>
							Add Selected items
						</HSButton>
					</div>
				</div>
			</HSDrawer> */}
			{showCreateQuestionDrawer ? (
				<QuestionModal
					show={showCreateQuestionDrawer}
					onClose={closeCreateQuestionDrawer}
					userProfileId={userProfile?.id ?? ''}
					editQuestion={{ ...(selectedQuestion as IQuestion) }}
					isEdit={editExisting}
					isProfile={false}
					onAddUpdate={onAddUpdate}
				/>
			) : null}
			{showResponseContext.show && showResponseContext.question ? (
				<QuestionResponseDrawer
					onClose={() => {
						setShowResponseContext({
							show: false,
							question: {} as IQuestionEvent
						})
					}}
					question={showResponseContext.question}
				/>
			) : null}
		</>
	)
}

export default EventQuestions
