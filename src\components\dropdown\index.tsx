/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable react/boolean-prop-naming */
import type { DropdownProps } from 'flowbite-react'
import { Dropdown } from 'flowbite-react'
import './index.css'
import HSTooltipWithEllipsis from 'components/tooltipEllipsis'

import { memo, useMemo, type JSX } from 'react'

interface IHSDropdownButtonProperties extends DropdownProps {
	items: ({
		id: string
		clickFunction: () => void
		disabled?: boolean
		cssName?: string
		item: React.ReactNode | string
	} | null)[]
	className?: string
	arrowClassName?: string
	showDropdownIcon?: boolean
	headerTemplate?: JSX.Element | React.ReactNode
	showTooltip?: boolean
}

const getTheme = (
	showDropdownIcon: boolean,
	arrowClassName?: string,
	className?: string
) => ({
	arrowIcon: showDropdownIcon
		? `ml-2 h-4 w-4 ${arrowClassName ?? ''}`
		: 'hidden',
	content: 'py-1 focus:outline-none',

	floating: {
		animation: 'transition-opacity',
		arrow: {
			base: 'absolute z-10 h-2 w-2 rotate-45 w-full justify-between',
			style: {
				dark: 'bg-gray-900 dark:bg-gray-700',
				light: 'bg-white',
				text: 'bg-white',
				auto: 'bg-white dark:bg-gray-700'
			},
			placement: '-4px'
		},
		base: 'z-50 w-fit divide-y divide-gray-100 rounded shadow !focus:outline-none ',
		content: 'py-1 text-sm text-gray-700 dark:text-gray-200',
		divider: 'my-1 h-px bg-gray-100 dark:bg-gray-600',
		header: `block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 `,
		hidden: 'invisible opacity-0',
		item: {
			container: '',
			base: 'flex w-full cursor-pointer items-center justify-start px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:bg-gray-600 dark:focus:text-white focus:ring-0',
			icon: 'mr-2 h-4 w-4'
		},
		style: {
			dark: 'bg-gray-900 text-white dark:bg-gray-700',
			light:
				'border border-gray-200 bg-white text-gray-900 focus:ring-0 focus:outline-none',
			text: 'border-0 border-gray-200 bg-white text-gray-900',
			auto: 'border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white'
		},
		target: `w-fit focus:ring-0 focus:outline-none ${className ?? ''}`
	},
	inlineWrapper: 'flex items-center'
})

const HSDropdownButton = memo((properties: IHSDropdownButtonProperties) => {
	const {
		items,
		className,
		arrowClassName,
		showDropdownIcon = true,
		headerTemplate,
		showTooltip = true,
		...rest
	} = properties

	const customTheme = useMemo(
		() => getTheme(showDropdownIcon, arrowClassName, className),
		[showDropdownIcon, arrowClassName, className]
	)

	return (
		<Dropdown theme={customTheme} {...rest} className='max-h-64 overflow-auto'>
			{headerTemplate ? (
				<>
					<Dropdown.Header>{headerTemplate}</Dropdown.Header>
					<Dropdown.Divider />
				</>
			) : null}
			{items.filter(Boolean).map(item => (
				<Dropdown.Item
					className={`${item?.cssName} ${item?.disabled ? 'opacity-50' : ''}`}
					disabled={item?.disabled}
					key={item?.id}
					onClick={() => {
						item?.clickFunction()
					}}
				>
					<span
						style={{
							width: 'inherit',
							overflow: 'hidden',
							textOverflow: 'ellipsis',
							textAlign: 'left'
						}}
					>
						{showTooltip ? (
							<HSTooltipWithEllipsis content={item?.item as string} />
						) : (
							<div>{item?.item}</div>
						)}
					</span>
				</Dropdown.Item>
			))}
		</Dropdown>
	)
})

export default HSDropdownButton
