/* eslint-disable react/jsx-no-duplicate-props */
/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable react/no-array-index-key */
import { faInfoCircle, faTrashXmark } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSCheckbox from 'components/checkbox'
import HSDateRangePicker from 'components/dateRangePicker'
import HSModal from 'components/modal'
import PageLoader from 'components/pageLoader'
import HSTable from 'components/table'
import HSToggleSwitch from 'components/toggleSwitch'
import HSTooltip from 'components/tooltip'
import {
	addYears,
	differenceInDays,
	format,
	formatISO,
	parseISO
} from 'date-fns'
import { Modal } from 'flowbite-react'
import analytics from 'lib/analytics/segment/load'
import { weekDayNames } from 'lib/helpers'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { reducingNumberOfDays } from 'pages/planner/event/common/helpers'
import { useEffect, useState } from 'react'

interface ChangingDates {
	startDate: Date | null
	endDate: Date | null
	startDateOriginal: Date | null
	endDateOriginal: Date | null
	newTotalDays: number
}

interface IEventDate {
	index: number
	startDate: Date | null
	endDate: Date | null
}

const EventDates = () => {
	const {
		eventInfo,
		disableFields,
		mergeProperties,
		replaceInArray,
		removeFromArray,
		setProperty
	} = eventInfoStore()
	const [eventDates, setEventDates] = useState<IEventDate[]>([])
	const [changingDates, setChangingDates] = useState<ChangingDates>()
	const [showConfirmModal, setShowConfirmModal] = useState(false)

	useEffect(() => {
		if (eventInfo?.startDate && eventInfo.endDate) {
			setEventDates([
				{
					startDate: parseISO(eventInfo.startDate),
					endDate: parseISO(eventInfo.endDate),
					index: -1
				}
			])
		}
		if (eventInfo?.alternateDates) {
			const alternateDates = eventInfo.alternateDates.map(date => [
				date.startDate ? parseISO(date.startDate) : null,
				date.endDate ? parseISO(date.endDate) : null
			])
			setEventDates(previous => [
				...previous,
				...alternateDates.map((dates, index) => ({
					startDate: dates[0],
					endDate: dates[1],
					index
				}))
			])
		}

		if (!eventInfo?.startDate && !eventInfo?.endDate) {
			setEventDates([
				{
					startDate: eventInfo?.startDate
						? parseISO(eventInfo.startDate)
						: null,
					endDate: eventInfo?.endDate ? parseISO(eventInfo.endDate) : null,
					index: -1
				}
			])
		}
	}, [eventInfo])

	const patternText = (startDate: Date, endDate: Date, disabled?: boolean) => {
		const totalDays = differenceInDays(endDate, startDate) + 1
		return (
			<div className='flex items-center gap-2'>
				<div
					className={`'text-sm text-gray-600' font-normal ${disabled ? 'text-primary-disabled' : ''}`}
				>
					{`${totalDays} days/${totalDays - 1} nights`}
				</div>
				<div className='text-xs font-medium text-gray-400'>
					({`${format(startDate, 'EEE')} - ${format(endDate, 'EEE')}`})
				</div>
			</div>
		)
	}

	const addAlternateDates = () => {
		if (eventInfo) {
			mergeProperties({
				alternateDates: [
					...(eventInfo.alternateDates ?? []),
					{ startDate: null, endDate: null }
				],
				startDate: eventInfo.startDate,
				endDate: eventInfo.endDate
			})
			analytics.track('Alternate Dates Added')
		}
	}
	const onChangeStartAndEndDate = (dateRangePeriod: {
		startDate: Date
		endDate: Date
	}) => {
		if (eventInfo) {
			const { endDate, startDate } = dateRangePeriod
			const { reducing, newTotalDays } = reducingNumberOfDays(
				startDate,
				endDate,
				eventInfo
			)

			if (reducing) {
				setShowConfirmModal(true)

				setChangingDates({
					startDate,
					endDate,
					startDateOriginal: parseISO(eventInfo.startDate),
					endDateOriginal: parseISO(eventInfo.endDate ?? ''),
					newTotalDays
				})
			} else {
				mergeProperties({
					startDate: formatISO(startDate, { representation: 'date' }),
					endDate: formatISO(endDate, { representation: 'date' })
				})
			}
		}
	}
	const onCancelConfirmModal = () => {
		setShowConfirmModal(false)

		if (changingDates?.startDateOriginal && changingDates.endDateOriginal) {
			mergeProperties({
				startDate: formatISO(changingDates.startDateOriginal, {
					representation: 'date'
				}),
				endDate: formatISO(changingDates.endDateOriginal, {
					representation: 'date'
				})
			})
		}

		setChangingDates({
			startDate: null,
			endDate: null,
			startDateOriginal: null,
			endDateOriginal: null,
			newTotalDays: 0
		})
	}
	const onConfirmModal = () => {
		setShowConfirmModal(false)

		mergeProperties(
			{
				startDate: changingDates?.startDate
					? formatISO(changingDates.startDate, { representation: 'date' })
					: undefined,
				endDate: changingDates?.endDate
					? formatISO(changingDates.endDate, { representation: 'date' })
					: undefined,
				meetingSpaceRequests: eventInfo?.meetingSpaceRequests?.filter(
					msr =>
						changingDates?.newTotalDays !== undefined &&
						(msr.dayNumber ?? 0) < changingDates.newTotalDays
				),
				roomBlockRequests: eventInfo?.roomBlockRequests?.map(rbr => ({
					...rbr,
					roomTypeRequests: rbr.roomTypeRequests.map(rtr => ({
						...rtr,
						roomNights: rtr.roomNights
							? rtr.roomNights.filter(
									rn =>
										changingDates?.newTotalDays !== undefined &&
										rn.dayNumber < changingDates.newTotalDays
								)
							: []
					}))
				}))
			},
			false,
			{ meetingSpaceRequests: true, roomBlockRequests: true }
		)
	}

	const onChangeAlternateDate = (
		index: number,
		dateRangePeriod: {
			startDate: Date
			endDate: Date
		}
	) => {
		replaceInArray(
			'alternateDates',
			{
				startDate: dateRangePeriod.startDate.toISOString().split('T')[0],
				endDate: dateRangePeriod.endDate.toISOString().split('T')[0]
			},
			index
		)
	}

	const onDeleteAlternateDates = (index: number) => {
		removeFromArray('alternateDates', index)
		analytics.track('Alternate Dates Removed')
	}

	const onChange = ({
		target: { name, type, value }
	}: {
		target: { name: string; type: string; value: boolean | string }
	}) => {
		if (name === 'datesAreFlexible' && !value) {
			mergeProperties({
				datesAreFlexible: Boolean(value),
				patternIsFlexible: false
			})

			analytics.track(`Flexible Dates Toggled ${value ? 'On' : 'Off'}`)
		} else if (name === 'allowDateSuggestions' && !value) {
			mergeProperties({
				allowDateSuggestions: Boolean(value),
				patternIsFlexible: false
			})
			analytics.track(`Allow Date Suggestions Toggled ${value ? 'On' : 'Off'}`)
		}
		// else if (name === 'meetingSpaceRequired') {
		// 	let { type } = event
		// 	if (value) {
		// 		if (type === 'roomsonly') {
		// 			type = null
		// 		}
		// 	} else {
		// 		type = 'roomsonly'
		// 	}

		// 	mergeProperties({
		// 		[name as keyof EventPlan]: value,
		// 		type
		// 	})
		// }
		else if (name === 'groupType' && value === 'wedding') {
			mergeProperties({
				[name]: value,
				type: 'wedding',
				industryType: 'other'
			})
		} else if (
			name === 'groupType' &&
			value !== 'association' &&
			value !== 'corporate'
		) {
			mergeProperties({
				[name as keyof EventPlan]: value,
				industryType: 'other'
			})
		} else {
			setProperty(name, value, type)
		}
	}

	const onTogglePatternDay = (day: string) => {
		if (eventInfo?.patternDays) {
			setProperty('patternDays', {
				...eventInfo.patternDays,
				[day as keyof typeof eventInfo.patternDays]:
					!eventInfo.patternDays[day as keyof typeof eventInfo.patternDays]
			})
			analytics.track(`Pattern Flexibility Changed`)
		}
	}

	return eventInfo ? (
		<div className='flex gap-6 px-6 pb-6 pt-4'>
			<div className='w-3/4'>
				<div className='flex flex-col gap-2'>
					<div className='text-lg font-semibold text-gray-900'>Event Dates</div>
					<div>
						<HSTable
							rows={eventDates}
							columns={[
								{
									field: 'date',
									headerText: 'Start and End Date',
									render: row => {
										const value =
											row.startDate && row.endDate
												? [row.startDate, row.endDate]
												: undefined
										return (
											<div className='flex w-80 flex-col'>
												<HSDateRangePicker
													onChange={event => {
														if (event.value) {
															if (row.index === -1) {
																onChangeStartAndEndDate({
																	startDate: event.value[0],
																	endDate: event.value[1]
																})
															} else {
																onChangeAlternateDate(row.index, {
																	startDate: event.value[0],
																	endDate: event.value[1]
																})
															}
														}
													}}
													value={value}
													format='MMM dd, yyyy'
													min={new Date()}
													max={addYears(new Date(), 10)}
													placeholder='Select'
													isInValid={!(row.startDate && row.endDate)}
													disabled={disableFields}
													showClearButton={false}
												/>
											</div>
										)
									},
									width: 350
								},
								{
									field: 'pattern',
									headerText: 'Pattern',
									render: row =>
										row.startDate && row.endDate ? (
											patternText(row.startDate, row.endDate)
										) : (
											<div>No dates requested</div>
										),
									width: 200
								},
								// {
								// 	field: 'preferred',
								// 	headerText: 'Preferred Date',
								// 	render: row => (
								// 		<HSToggleSwitch
								// 			checked={
								// 				row.index === 0
								// 					? !eventInfo.datesAreFlexible
								// 					: (eventInfo.datesAreFlexible ?? false)
								// 			}
								// 			name='datesAreFlexible'
								// 			disabled={disableFields}
								// 			onChange={checked =>
								// 				onChange({
								// 					target: {
								// 						name: 'datesAreFlexible',
								// 						value: checked,
								// 						type: typeof checked
								// 					}
								// 				})
								// 			}
								// 		/>
								// 	)
								// },
								{
									field: 'remove',
									headerText: 'Remove',
									width: 100,
									headerAlign: 'center',
									cellAlign: 'center',
									render: row => (
										<div className='flex items-center justify-center'>
											{row.index === -1 ? null : (
												<HSButton
													color='light'
													size='xs'
													onClick={() => onDeleteAlternateDates(row.index)}
												>
													<FontAwesomeIcon
														icon={faTrashXmark}
														className='text-red-600'
														size='lg'
													/>
												</HSButton>
											)}
										</div>
									)
								}
							]}
							allowPaging={false}
							borderStyling='rounded-t-lg'
						/>
						<div className='rounded-b-lg border'>
							<div className='w-32 px-4 py-2'>
								<HSButton
									color='text'
									onClick={addAlternateDates}
									disabled={disableFields}
								>
									+ Add New
								</HSButton>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className='w-1/4'>
				<div className='flex flex-col gap-2'>
					<div className='text-lg font-semibold text-gray-900'>
						Details & Pattern
					</div>
					<div className='card p-4'>
						<div className='flex flex-col gap-4'>
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-medium text-gray-900'>
									Date Range Preference
								</div>
								{(eventInfo.alternateDates?.length ?? 0) > 0 ? (
									<div className='flex flex-col gap-2'>
										<div className='flex items-center gap-2'>
											<HSCheckbox
												name='datesAreFlexible'
												onChange={event => {
													onChange({
														target: {
															name: 'datesAreFlexible',
															value: !event.target.checked,
															type: event.target.type
														}
													})
												}}
												checked={!eventInfo.datesAreFlexible}
												disabled={disableFields}
											/>
											<div className='text-sm font-normal text-gray-700'>
												Prefer{' '}
												{eventInfo.startDate && eventInfo.endDate
													? `${format(parseISO(eventInfo.startDate), 'PP')} to ${format(parseISO(eventInfo.endDate), 'PP')}`
													: null}
											</div>
										</div>
										<div className='flex items-center gap-2'>
											<HSCheckbox
												name='datesAreFlexible'
												checked={eventInfo.datesAreFlexible || false}
												disabled={disableFields}
												onChange={event =>
													onChange({
														target: {
															name: 'datesAreFlexible',
															value: event.target.checked,
															type: event.target.type
														}
													})
												}
											/>
											<div className='text-sm font-normal text-gray-700'>
												All dates are equal alternatives
											</div>
										</div>
									</div>
								) : (
									<div className='flex h-12 items-center justify-center text-sm font-normal text-primary-disabled'>
										Select 2 or more dates to set up preference
									</div>
								)}
							</div>
							<div className='border-b' />
							<div className='text-sm font-medium text-gray-900'>
								Alternate Dates
							</div>
							<div className='flex flex-col gap-1'>
								<div className='flex items-center gap-2'>
									<HSToggleSwitch
										checked={eventInfo.allowDateSuggestions || false}
										onChange={checked => {
											onChange({
												target: {
													name: 'allowDateSuggestions',
													value: checked,
													type: typeof checked
												}
											})
										}}
										name='allowDateSuggestions'
									/>
									<div className='text-sm font-normal text-gray-700'>
										Allow to Suggest Alternate Dates
									</div>
								</div>
								<div className='flex items-center gap-2'>
									<HSToggleSwitch
										checked={eventInfo.patternIsFlexible || false}
										name='patternIsFlexible'
										onChange={checked => {
											onChange({
												target: {
													name: 'patternIsFlexible',
													value: checked,
													type: typeof checked
												}
											})
										}}
										disabled={!eventInfo.allowDateSuggestions}
									/>
									<div className='text-sm font-normal text-gray-700'>
										Flexible Pattern?
									</div>
								</div>
							</div>
							{eventInfo.patternDays &&
							eventInfo.patternIsFlexible &&
							eventInfo.startDate &&
							eventInfo.endDate ? (
								<div className='flex flex-col gap-2'>
									<div className='flex items-center gap-2'>
										<div className='text-sm font-normal text-gray-700'>
											Optional start days
										</div>
										<HSTooltip
											content={
												<div>
													{Object.keys(eventInfo.patternDays)
														.map((day, dayIndex) => {
															if (
																eventInfo.patternDays?.[
																	day as keyof typeof eventInfo.patternDays
																]
															) {
																return weekDayNames[dayIndex]
															}
															return null
														})
														.filter(Boolean)
														.join(', ')}
												</div>
											}
										>
											<FontAwesomeIcon icon={faInfoCircle} />
										</HSTooltip>
									</div>
									<div className='flex items-center gap-3'>
										{Object.keys(eventInfo.patternDays).map((day, dayIndex) => (
											<HSTooltip
												content={weekDayNames[dayIndex]}
												key={`ep-alt-pd-${dayIndex}`}
											>
												<HSButton
													color={
														eventInfo.patternDays?.[
															day as keyof typeof eventInfo.patternDays
														]
															? 'primary'
															: 'light'
													}
													outline={
														!!eventInfo.patternDays?.[
															day as keyof typeof eventInfo.patternDays
														]
													}
													size='xs'
													onClick={() => onTogglePatternDay(day)}
												>
													{day.charAt(0).toUpperCase()}
												</HSButton>
											</HSTooltip>
										))}
									</div>
								</div>
							) : null}
							<div>
								{eventInfo.allowDateSuggestions === true &&
								eventInfo.patternIsFlexible === false ? (
									<div className='flex flex-col items-center text-sm font-normal text-primary-disabled'>
										<div>Hotels can only provide rates for:</div>
										<div>
											{eventInfo.startDate && eventInfo.endDate
												? patternText(
														parseISO(eventInfo.startDate),
														parseISO(eventInfo.endDate),
														true
													)
												: null}
										</div>
									</div>
								) : null}
								{eventInfo.allowDateSuggestions === false &&
								eventInfo.patternIsFlexible === false ? (
									<div className='flex items-center text-sm font-normal text-primary-disabled'>
										To specify a flexible pattern, please allow hotels to
										suggest alternate dates above.
									</div>
								) : null}
							</div>
						</div>
					</div>
				</div>
			</div>
			{showConfirmModal ? (
				<HSModal
					size='lg'
					openModal={showConfirmModal}
					onClose={() => {
						onCancelConfirmModal()
					}}
					header='Reduce number of days?'
					hideCloseButton
				>
					<Modal.Body>
						<div className='text-sm'>
							If you reduce the number of days in your event, you will lose
							meeting space requests and/or room block requests that you have
							already created.
						</div>
					</Modal.Body>
					<Modal.Footer>
						<div className='flex grow items-center justify-center gap-2'>
							<HSButton
								onClick={() => {
									onCancelConfirmModal()
								}}
								color='danger'
								className='grow'
							>
								Don&apos;t change the dates
							</HSButton>
							<HSButton
								onClick={() => {
									onConfirmModal()
								}}
								color='light'
								className='grow'
							>
								Got it! Go ahead with the change
							</HSButton>
						</div>
					</Modal.Footer>
				</HSModal>
			) : null}
		</div>
	) : (
		<PageLoader />
	)
}

export default EventDates
