import {
	PresetDirective,
	PresetsDirective
} from '@syncfusion/ej2-react-calendars'
import HSButton from 'components/button'
import HSDateRangePicker from 'components/dateRangePicker'
import HSDrawer from 'components/drawer'
import HSTextField from 'components/textField'
import { Drawer } from 'flowbite-react'
import { calculatePeriod, periodTypes } from 'lib/helpers'
import { useState } from 'react'
import { formatNumber } from '../../../../../../../../../lib/helpers/index'
import { addDays, parseISO, subYears } from 'date-fns'
import type { IEventHistory } from 'models/proposalResponseMonitor'
import { getDefaultEventHistory } from 'components/event/defaults'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'

interface AddEventHistoryProperties {
	onClose: () => void
	eventHistory?: IEventHistory
	index?: number | null
}

const AddEventHistory = (properties: AddEventHistoryProperties) => {
	const { onClose, eventHistory: eventHistoryToEdit, index } = properties
	const { addToArray, eventInfo, replaceInArray } = eventInfoStore()

	const [eventHistory, setEventHistory] = useState<IEventHistory>(() => {
		if (eventHistoryToEdit) return eventHistoryToEdit

		if (eventInfo?.eventHistories?.length) {
			const sortedHistories = [...eventInfo.eventHistories].sort((a, b) => {
				if (!a.startDate || !b.startDate) return 0
				return parseISO(a.startDate).getTime() - parseISO(b.startDate).getTime()
			})

			const earliestDate = parseISO(sortedHistories[0].startDate || '')
			const yearBefore = subYears(earliestDate, 1)

			return getDefaultEventHistory(
				yearBefore.toISOString(),
				addDays(yearBefore, 1).toISOString()
			)
		}

		return getDefaultEventHistory(
			subYears(new Date(), 1).toISOString(),
			addDays(subYears(new Date(), 1), 1).toISOString()
		)
	})

	const addItem = () => {
		addToArray('eventHistories', eventHistory)
		onClose()
	}

	const onEdit = () => {
		if (
			eventHistoryToEdit &&
			eventInfo &&
			index !== null &&
			index !== undefined &&
			index >= 0
		) {
			replaceInArray('eventHistories', eventHistory, index)
			onClose()
		}
	}

	return (
		<HSDrawer
			onClose={onClose}
			open
			position='right'
			style={{
				width: '400px'
			}}
		>
			<Drawer.Header
				title='Share Past Program Information'
				titleIcon={() => null}
			/>
			<div
				className='overflow-auto'
				style={{
					minHeight: 'calc(100vh - 9rem)',
					maxHeight: 'calc(100vh - 9rem)'
				}}
			>
				{eventInfo ? (
					<Drawer.Items>
						<div className='flex flex-col gap-4'>
							<div className='flex flex-col gap-8'>
								<div className='flex flex-col gap-4'>
									<HSTextField
										label='Property Name'
										placeholder='Enter Property Name'
										value={eventHistory.propertyName ?? ''}
										onChange={event =>
											setEventHistory({
												...eventHistory,
												propertyName: event.target.value
											})
										}
									/>
									<div className='flex flex-col gap-1'>
										<div className='text-sm font-medium text-gray-900'>
											Event Date Range
										</div>
										<HSDateRangePicker
											onChange={event => {
												if (Array.isArray(event.value)) {
													const [startDate, endDate] = event.value
													const currentStartDate = new Date(
														parseISO(
															eventHistory.startDate ?? new Date().toISOString()
														)
													)
													const currentEndDate = new Date(
														parseISO(
															eventHistory.endDate ?? new Date().toISOString()
														)
													)

													const startDateChanged =
														startDate.getTime() !== currentStartDate.getTime()
													const endDateChanged =
														endDate.getTime() !== currentEndDate.getTime()

													if (startDate.getTime() === endDate.getTime()) {
														if (startDateChanged) {
															setEventHistory({
																...eventHistory,
																startDate: startDate.toISOString(),
																endDate: addDays(endDate, 1).toISOString()
															})
														} else if (endDateChanged) {
															setEventHistory({
																...eventHistory,
																startDate: addDays(startDate, -1).toISOString(),
																endDate: endDate.toISOString()
															})
														}
													} else if (endDate.getTime() < startDate.getTime()) {
														if (startDateChanged) {
															setEventHistory({
																...eventHistory,
																startDate: startDate.toISOString(),
																endDate: addDays(startDate, 1).toISOString()
															})
														} else if (endDateChanged) {
															setEventHistory({
																...eventHistory,
																startDate: addDays(endDate, -1).toISOString(),
																endDate: endDate.toISOString()
															})
														}
													} else {
														setEventHistory({
															...eventHistory,
															startDate: startDate.toISOString(),
															endDate: endDate.toISOString()
														})
													}
												}
											}}
											value={[
												new Date(
													parseISO(
														eventHistory.startDate ?? new Date().toISOString()
													)
												),
												new Date(
													parseISO(
														eventHistory.endDate ?? new Date().toISOString()
													)
												)
											]}
											placeholder='Select Date Range'
											format='MMM dd, yyyy'
										>
											<PresetsDirective>
												{Object.keys(periodTypes).map(period => {
													const {
														type: { label, key },
														startDate,
														endDate
													} = calculatePeriod(period)
													return (
														<PresetDirective
															key={key}
															label={label}
															start={
																startDate === endDate
																	? new Date(addDays(parseISO(startDate), -1))
																	: new Date(startDate)
															}
															end={new Date(endDate)}
														/>
													)
												})}
											</PresetsDirective>
										</HSDateRangePicker>
									</div>
									<HSTextField
										placeholder='Enter Attendees'
										label='Attendees'
										value={formatNumber(eventHistory.numberOfAttendees)}
										onChange={event => {
											const attendeesValue = Number(
												event.target.value.replaceAll(',', '')
											)
											if (Number.isInteger(attendeesValue)) {
												setEventHistory({
													...eventHistory,
													numberOfAttendees: attendeesValue
												})
											}
										}}
									/>
									<div className='flex items-center justify-between gap-4'>
										<HSTextField
											label='Room Rate'
											placeholder='Rate'
											groupPlacement='right'
											tabIndex={0}
											groupItem={
												<div className='w-8 text-xs font-medium'>$</div>
											}
											value={formatNumber(eventHistory.roomRate)}
											onChange={event => {
												const roomRateValue = Number(
													event.target.value.replaceAll(',', '')
												)

												if (Number.isInteger(roomRateValue)) {
													setEventHistory({
														...eventHistory,
														roomRate: roomRateValue
													})
												}
											}}
										/>

										<HSTextField
											label='Peak Room'
											placeholder='Rate'
											className='w-16'
											groupPlacement='left'
											groupItem={
												<div className='text-xs font-medium'>Rooms</div>
											}
											value={formatNumber(eventHistory.numberOfRooms)}
											onChange={event => {
												const peakRoomValue = Number(
													event.target.value.replaceAll(',', '')
												)
												if (Number.isInteger(peakRoomValue)) {
													setEventHistory({
														...eventHistory,
														numberOfRooms: peakRoomValue
													})
												}
											}}
										/>
									</div>
									<HSTextField
										label='F&B Spend'
										placeholder='Rate'
										groupPlacement='right'
										tabIndex={0}
										groupItem={<div className='w-8 text-xs font-medium'>$</div>}
										value={formatNumber(eventHistory.fbSpend)}
										onChange={event => {
											const fbSpendValue = Number(
												event.target.value.replaceAll(',', '')
											)
											if (Number.isInteger(fbSpendValue)) {
												setEventHistory({
													...eventHistory,
													fbSpend: fbSpendValue
												})
											}
										}}
									/>
								</div>
							</div>
						</div>
					</Drawer.Items>
				) : null}
			</div>
			<div className='flex items-center gap-2'>
				<HSButton className='grow' color='light' onClick={onClose}>
					Cancel
				</HSButton>
				{eventHistoryToEdit ? (
					<HSButton className='grow' onClick={onEdit}>
						Edit
					</HSButton>
				) : (
					<HSButton className='grow' onClick={addItem}>
						Save
					</HSButton>
				)}
			</div>
		</HSDrawer>
	)
}

export default AddEventHistory
