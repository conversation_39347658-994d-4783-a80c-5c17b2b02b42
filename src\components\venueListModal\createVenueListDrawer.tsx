import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import HSTextArea from 'components/textarea'
import <PERSON>STextField from 'components/textField'
import HSToggleSwitch from 'components/toggleSwitch'
import WhatsThis from 'components/whatsThis'
import { Drawer } from 'flowbite-react'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import type { VenueListPayload } from 'lib/services/organizations.service'
import {
	addVenueList,
	mapVenueToAssociatedObject
} from 'lib/services/organizations.service'
import type { Venue } from 'models/venue'
import { useState } from 'react'
import { toast } from 'react-toastify'

interface CreateVenueListDrawerProperties {
	venueToAdd: Venue
	onClose: () => void
}

const CreateVenueListDrawer = (properties: CreateVenueListDrawerProperties) => {
	const { venueToAdd, onClose } = properties
	const { userProfile } = useUserProfileContext()
	const [venueListToAdd, setVenueListToAdd] = useState<VenueListPayload>({
		name: '',
		description: '',
		venues: [
			mapVenueToAssociatedObject(venueToAdd, {
				added: new Date().toISOString(),
				addedBy: userProfile?.id ?? ''
			})
		],
		isShared: false,
		userProfileId: userProfile?.organizationId ?? '',
		sharedWith: [
			{
				id: userProfile?.organizationId ?? '',
				partitionKey: userProfile?.organizationId ?? '',
				name: userProfile?.organizationName ?? '',
				itemType: 'organization'
			}
		]
	})

	const onAddVenueList = () => {
		addVenueList(userProfile?.organizationId ?? '', venueListToAdd)
			.then(response => {
				toast.success(`Create new list:${response.name} successfully`)
			})
			.catch((error: unknown) => console.error(error))
			.finally(() => onClose())
	}

	return (
		<HSDrawer
			open
			onClose={onClose}
			style={{ width: '500px' }}
			position='right'
		>
			<Drawer.Header title='Add New List' titleIcon={() => null} />
			<Drawer.Items
				className='flex flex-col gap-4 overflow-auto'
				style={{ height: 'calc(100vh - 8rem)' }}
			>
				<div className='flex flex-col gap-6'>
					<div className='flex flex-col gap-2'>
						<HSTextField
							label='Name'
							value={venueListToAdd.name}
							onChange={event => {
								setVenueListToAdd({
									...venueListToAdd,
									name: event.target.value
								})
							}}
							placeholder='Enter Name'
						/>

						<div className='flex items-center gap-2'>
							<HSToggleSwitch
								checked={venueListToAdd.isShared}
								onChange={isShared => {
									setVenueListToAdd({
										...venueListToAdd,
										isShared,
										sharedWith: isShared
											? [
													{
														id: userProfile?.organizationId ?? '',
														partitionKey: userProfile?.organizationId ?? '',
														name: userProfile?.organizationName ?? '',
														itemType: 'organization'
													}
												]
											: []
									})
								}}
								label='Share with team'
							/>
							<WhatsThis
								title='Private'
								content='Private lists are always visible to the Org Admin(s) and you. They are not visible to other members of the organization.'
							/>
						</div>
					</div>
					<div className='flex flex-col gap-2'>
						<HSTextArea
							id='venue-description'
							label='Description'
							value={venueListToAdd.description}
							placeholder='Enter Description'
							onChange={event => {
								setVenueListToAdd({
									...venueListToAdd,
									description: event.target.value
								})
							}}
							rows={5}
						/>
					</div>
				</div>

				<div className='flex flex-col gap-2'>
					<div className='text-sm font-medium text-gray-900'>Venue(s)</div>
					<div className='text-sm font-normal text-gray-600'>
						{venueToAdd.name}
					</div>
				</div>
			</Drawer.Items>
			<Drawer.Items>
				<div className='flex grow items-center gap-2'>
					<HSButton onClick={() => onClose()} color='light' className='grow'>
						Cancel
					</HSButton>
					<HSButton
						onClick={() => onAddVenueList()}
						className='grow'
						disabled={!venueListToAdd.name}
					>
						Add List
					</HSButton>
				</div>
			</Drawer.Items>
		</HSDrawer>
	)
}

export default CreateVenueListDrawer
