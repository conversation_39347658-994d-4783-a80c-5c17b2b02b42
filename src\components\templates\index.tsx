/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import { faFilter, faPlusCircle } from '@fortawesome/pro-duotone-svg-icons'
import { faLockAlt } from '@fortawesome/pro-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { rfpImportSourceFileTypes } from 'lib/helpers/importSourceFileTypes'
import { useDeleteTemplate, useGetTemplates } from 'lib/services/event.service'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { useEffect, useState } from 'react'
import {
	actionTemplate,
	dateTemplate,
	nameTemplate,
	patternTemplate,
	requestTemplate
} from './templates'
import Loader from 'components/loader'
import UpgradePrompt from 'components/upgradePrompt'
import EventPlanDuplicateModal from 'components/eventPlanDuplicateModal'
import useUserProfileStore from 'lib/store/userProfileStore'
import { useFeatureContext } from 'lib/providers/feature.provider'
import HSDropdownButton from 'components/dropdown'
import HSButton from 'components/button'
import HSPopover from 'components/popover'
import filterStore from './filterStore'
import HSTextField from 'components/textField'
import HSTable from 'components/table'
import ShareModal from 'components/shareModal'

interface ITemplatesProperties {
	onClickImportTemplates: (
		fileType: keyof typeof rfpImportSourceFileTypes
	) => void
}

const Templates = (properties: ITemplatesProperties) => {
	const { onClickImportTemplates } = properties
	const { userProfile } = useUserProfileStore()

	const { userProfile: currentUser } = useUserProfileContext()
	const { getFeatureByKey, getFeatureByKeyAndTier } = useFeatureContext()
	const templateFeature = getFeatureByKey('TEMPLATES')
	const [selectedTemplate, setSelectedTemplate] = useState<EventPlan | null>()
	const [showModal, setShowModal] = useState(false)
	const [gridData, setGridData] = useState<EventPlan[] | undefined>([])
	const [filteredData, setFilteredData] = useState<EventPlan[] | undefined>([])
	const [shareContext, setShareContext] = useState<EventPlan>()
	const [showShareModal, setShowShareModal] = useState(false)

	const { nameSearch, setNameSearch, clearAll } = filterStore()

	const { refetch: getTemplates, isFetching } = useGetTemplates(
		userProfile.id ?? ''
	)

	const { mutateAsync: deleteTemplate } = useDeleteTemplate()

	useEffect(() => {
		getTemplates()
			.then((response: { data: EventPlan[] | undefined }) =>
				setGridData(response.data)
			)
			.catch((error: unknown) => console.error(error))
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [userProfile.id])

	const onActionUse = (template: EventPlan) => {
		setSelectedTemplate(template)
		setShowModal(true)
	}

	const onActionShare = (template: EventPlan) => {
		setShareContext(template)
		setShowShareModal(true)
	}

	const onActionDelete = (template: EventPlan) => {
		deleteTemplate(template.id ?? '')
			.then(() => {
				setGridData(s => (s ?? []).filter(t => t.id !== template.id))
			})
			.catch((error: unknown) => console.error(error))
	}

	const onClick = (value: string) => {
		switch (value) {
			case 'Import From PDFs': {
				onClickImportTemplates(
					rfpImportSourceFileTypes.Pdf as keyof typeof rfpImportSourceFileTypes
				)
				break
			}
			case 'Import From Excel': {
				onClickImportTemplates(
					rfpImportSourceFileTypes.Excel as keyof typeof rfpImportSourceFileTypes
				)
				break
			}

			default: {
				break
			}
		}
	}

	const popOverContent = (
		<div className='max-h-96 w-80 overflow-y-auto p-4'>
			<div className='flex flex-col gap-2 p-2'>
				<div className='flex items-center justify-end'>
					<div className='flex gap-2'>
						<HSButton size='xs'>Save View</HSButton>
						<HSButton size='xs' onClick={() => clearAll()}>
							Clear All
						</HSButton>
					</div>
				</div>
			</div>

			<HSTextField
				placeholder='Start Typing Name'
				label='Name'
				value={nameSearch}
				onChange={event => setNameSearch(event.target.value)}
			/>
		</div>
	)

	useEffect(() => {
		if (nameSearch) {
			setFilteredData(
				gridData?.filter(item => {
					if (nameSearch && item.name) {
						return item.name.toLowerCase().includes(nameSearch.toLowerCase())
					}
					return true
				})
			)
		} else {
			setFilteredData(gridData)
		}
	}, [gridData, nameSearch])

	return (
		<div>
			<div className='flex items-center justify-between px-6 py-4'>
				<div className='text-xl font-semibold text-gray-900'>
					My Templates{' '}
					{templateFeature ? null : (
						<FontAwesomeIcon className='mt-4' icon={faLockAlt} />
					)}
				</div>
			</div>
			<div className='border-b' />
			<div
				className='flex flex-col gap-2 overflow-auto px-6 py-4'
				style={{ maxHeight: 'calc(100vh - 15rem)' }}
			>
				<div className='flex items-center justify-between'>
					<div className='text-sm font-medium text-gray-900'>
						{filteredData?.length ?? 0} Template
						{filteredData && filteredData.length > 1 ? 's' : ''}
					</div>
					<div className='flex items-center gap-4'>
						<div>
							<HSPopover
								content={popOverContent}
								aria-labelledby='filter'
								placement='bottom'
								arrow={false}
							>
								<div className='relative inline-block'>
									<HSButton color='light' size='sm'>
										<div className='flex items-center gap-2'>
											<FontAwesomeIcon icon={faFilter} />
											Filter
										</div>
									</HSButton>
								</div>
							</HSPopover>
						</div>
						{currentUser?.isAdmin ||
						currentUser?.previewExperiences?.AIIMPORTTEMPLATES ? (
							<HSDropdownButton
								items={[
									{
										id: '1',
										item: 'From PDF',
										clickFunction: () => onClick('Import From PDFs')
									},
									{
										id: '2',
										item: 'From Excel',
										clickFunction: () => onClick('Import From Excel')
									}
								]}
								label='Import New...'
								size='sm'
							>
								<FontAwesomeIcon icon={faPlusCircle} /> Import Template(s)
							</HSDropdownButton>
						) : null}
					</div>
				</div>
				<div>
					{isFetching ? (
						<Loader />
					) : (
						<HSTable
							rows={filteredData ?? []}
							allowPaging
							columns={[
								{
									field: 'name',
									headerText: 'Name',
									render: (item: EventPlan) => nameTemplate(item),
									sortable: true
								},
								{
									field: 'created',
									headerText: 'Created',
									render: (item: EventPlan) => dateTemplate(item.created),
									width: 200,
									sortable: true
								},
								{
									field: 'startDayOfWeek',
									headerText: 'Pattern',
									render: (item: EventPlan) => patternTemplate(item),
									width: 200,
									sortable: true
								},
								{
									field: 'requests',
									headerText: 'Requests',
									render: (item: EventPlan) => requestTemplate(item),
									width: 200,
									sortable: true
								},
								{
									field: 'id',
									headerText: 'Actions',
									render: (item: EventPlan) =>
										actionTemplate(
											item,
											onActionUse,
											onActionDelete,
											onActionShare
										),
									width: 100
								}
							]}
						/>
					)}
				</div>
			</div>
			{!templateFeature && (
				<UpgradePrompt feature={getFeatureByKeyAndTier('TEMPLATES')} />
			)}
			{/* {importTemplateContext.open ? (
				<ImportTemplate
					open={importTemplateContext.open}
					template={importTemplateContext.template}
					onClose={() => importTemplateContext.onClose()}
					type={importTemplateContext.type}
				/>
			) : null} */}
			{showModal ? (
				<EventPlanDuplicateModal
					show={showModal}
					duplicateAction='createRfpFromTemplate'
					createItemType='eventPlan'
					template={selectedTemplate}
					onClose={() => {
						setShowModal(false)
						setSelectedTemplate(null)
					}}
				/>
			) : null}

			<ShareModal
				show={showShareModal}
				userProfileId={userProfile.id ?? ''}
				itemsToShare={[shareContext] as EventPlan[]}
				onClose={() => setShowShareModal(false)}
				shareType='template'
			/>
		</div>
	)
}

export default Templates
