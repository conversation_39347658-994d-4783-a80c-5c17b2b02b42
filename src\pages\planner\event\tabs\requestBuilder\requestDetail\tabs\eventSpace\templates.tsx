/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable react/destructuring-assignment */
import {
	faEdit,
	faForkKnife,
	faMessageLines,
	faPaperclip,
	faTvMusic
} from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSCheckbox from 'components/checkbox'
import HSSelect from 'components/select'
import HSTextField from 'components/textField'
import HSTimePicker from 'components/timePicker'
import HSTooltip from 'components/tooltip'
import { formatNumber, numberMask } from 'lib/helpers'
import { roomLayoutOptions } from 'lib/helpers/roomLayouts'
import type { Attachment } from 'lib/store/attachmentsStore'
import type {
	IFoodAndBeverageRequest,
	IMeetingSpaceRequest
} from 'models/proposalResponseMonitor'

export interface TargetProperties {
	name: string
	targetType?: string
	value: boolean | null | undefined | string
}

export const renderLayout = (item: IMeetingSpaceRequest) => (
	<HSSelect
		value={item.layoutStyle ?? ''}
		onChange={() => {}}
		isInvalid={!item.layoutStyle}
		required
	>
		{Object.entries(roomLayoutOptions)
			.filter(Boolean)
			.map(option => (
				<option key={option[0]} value={option[0]}>
					{option[1]?.name ?? ''}
				</option>
			))}
	</HSSelect>
)

export const renderRoomFunction = (item: IMeetingSpaceRequest) => (
	<div>
		<HSTextField
			value={item.name ?? ''}
			onChange={() => {}}
			isInvalid={!item.name}
			required
			placeholder='Enter  Function'
		/>
	</div>
)

export const renderDetails = (
	item: IMeetingSpaceRequest,
	attachments: Attachment[],
	onEdit: () => void,
	foodAndBeveragesRequest: IFoodAndBeverageRequest[],
	onTabClick: (tab: number) => void
) => {
	const areAttachmentsAvailable = attachments.some(
		a => a.venueId === null && a.meetingSpaceRequestId === item.id
	)
	const isFoodAndBeverageRequested = foodAndBeveragesRequest.find(
		f => f.meetingSpaceRequestId === item.id
	)
	return (
		<div className='flex items-center gap-2'>
			<HSTooltip
				content={`Audio Visual ${item.avRequired ? 'Required' : 'Not Required'}`}
			>
				<FontAwesomeIcon
					icon={faTvMusic}
					size='lg'
					className={
						item.avRequired ? 'text-primary-700' : 'text-primary-disabled'
					}
					onClick={() => {
						onTabClick(0)
					}}
				/>
			</HSTooltip>
			<HSTooltip
				content={`Food and Beverage ${isFoodAndBeverageRequested ? 'Required' : 'Not Required'}`}
			>
				<FontAwesomeIcon
					icon={faForkKnife}
					size='lg'
					className={
						isFoodAndBeverageRequested
							? 'text-primary-700'
							: 'text-primary-disabled'
					}
					onClick={() => {
						onTabClick(1)
					}}
				/>
			</HSTooltip>
			<HSTooltip content='Message'>
				<FontAwesomeIcon
					icon={faMessageLines}
					size='lg'
					className={item.notes ? 'text-primary-700' : 'text-primary-disabled'}
					onClick={() => {
						onTabClick(2)
					}}
				/>
			</HSTooltip>
			<HSTooltip
				content={` ${areAttachmentsAvailable ? 'Attachments Available' : 'No Attachments'}`}
			>
				<FontAwesomeIcon
					icon={faPaperclip}
					size='lg'
					className={
						areAttachmentsAvailable
							? 'text-primary-700'
							: 'text-primary-disabled'
					}
					onClick={() => {
						onTabClick(2)
					}}
				/>
			</HSTooltip>
			<HSTooltip content='Edit Event Space'>
				<HSButton color='light' size='xs' onClick={onEdit}>
					<FontAwesomeIcon icon={faEdit} size='lg' className='text-gray-900' />
				</HSButton>
			</HSTooltip>
		</div>
	)
}

export const renderCapacity = (item: IMeetingSpaceRequest) => (
	<div className='flex w-40'>
		<HSTextField
			value={item.capacity ?? ''}
			mask={numberMask}
			onChange={() => {}}
			isInvalid={!item.capacity}
			required
			placeholder='Min'
			groupPlacement='left'
			groupItem={
				<div className='text-sm font-normal text-gray-600'>People</div>
			}
		/>
	</div>
)

export const formatTime = (hours: number, minutes: number) => {
	const formattedHours = hours.toString().padStart(2, '0')
	const formattedMinutes = minutes.toString().padStart(2, '0')
	return `${formattedHours}:${formattedMinutes}`
}

export const renderTimeRange = (
	item: IMeetingSpaceRequest,
	onChangeMsrTime: (
		id: string,
		prefix: 'start' | 'end',
		hours: number,
		minutes: number
	) => void,
	onChange: (id: string | null, target: TargetProperties) => void
) => {
	const { startTime, endTime, startMinutes, endMinutes, hold24Hours, id } = item

	const startTimeValue = formatTime(startTime ?? 0, startMinutes ?? 0)
	const endTimeValue = formatTime(endTime ?? 0, endMinutes ?? 0)

	return (
		<div className='flex items-center gap-2'>
			<HSTimePicker
				value={startTimeValue}
				onChange={event =>
					onChangeMsrTime(
						id ?? '',
						'start',
						Number(event.slice(0, 2)),
						Number(event.slice(3, 5))
					)
				}
				isInvalid={
					(startTime ?? 0) * 100 + (startMinutes ?? 0) >=
					(endTime ?? 0) * 100 + (endMinutes ?? 0)
				}
				invalidMessage='Must be before End Time'
			/>
			<HSTimePicker
				value={endTimeValue}
				onChange={event =>
					onChangeMsrTime(
						id ?? '',
						'end',
						Number(event.slice(0, 2)),
						Number(event.slice(3, 5))
					)
				}
				isInvalid={
					(startTime ?? 0) * 100 + (startMinutes ?? 0) >=
					(endTime ?? 0) * 100 + (endMinutes ?? 0)
				}
				invalidMessage='Must be after Start Time'
			/>
			<HSCheckbox
				label='24h Hold'
				checked={!!hold24Hours}
				onChange={event => {
					onChange(id, {
						name: 'hold24Hours',
						targetType: 'checkbox',
						value: event.target.checked
					})
				}}
			/>
		</div>
	)
}

export const renderEdit = () => (
	<HSTooltip content='Edit Event Space'>
		<HSButton color='light' size='xs' outline>
			<FontAwesomeIcon icon={faEdit} size='lg' className='text-gray-900' />
		</HSButton>
	</HSTooltip>
)

export const renderTotalSpace = (item: IMeetingSpaceRequest) => (
	<div className='text-sm font-normal text-gray-600'>
		{formatNumber(item.areaTotal ?? 0, '-')}
	</div>
)
