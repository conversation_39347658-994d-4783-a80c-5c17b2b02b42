import type { ProposalRequestExtended } from 'pages/rfps/common/model/proposalRequestExtended'
import type { IHotelImage } from './hotels'
import type { SubscriptionInfo } from './organizations'
import type { Member } from './reporting'
import type { ISupplierContact } from './affiliateOrganizations'

export interface IDestination {
	id: string | null
	name: string | null
	city: string | null
	state: string | null
	addMembersWithEmailDomain: boolean
	comparableDestinations: IComparableDestination[]
	destinationManagers: Member[] | []
	memberEmailDomain: string | null
	created: string | null
	subscriptionInfos: SubscriptionInfo[]
}

interface IComparableDestinationProperties {
	latitude: number
	longitude: number
	created: string | null
	createdBy: string | null
	isApproved: boolean
	approved: string | null
	approvedBy: string | null
}

export interface IComparableDestination {
	id: string | null
	partitionKey: string | null
	itemType: string | null
	name: string | null
	properties: IComparableDestinationProperties
}

export interface IMappedDestination extends IDestination {
	comparableDestinationsList: string | null
	destinationManagersList: string | null
	tiers: string | null
}

export type Coordinate = [number, number]

export interface INewDestination {
	firstName: string | null
	lastName: string | null
	companyName: string | null
	role: string | null
	requestSuggestions: boolean | null
	allowMessaging: boolean | null
	invitationMessage: string | null
	sent: string | null
	added: string | null
	location: IDestinationLocation | null
	organizationId: string | null
	hotelCount: number | null
	destinationManagers: Member[]
	venueMatchCriteria: {
		geolocation?: string[]
		geolocationExcludeVenueIds?: string[]
		geolocationIncludeVenueIds?: string[]
		propertySeller?: string[]
		chainId?: string[]
	} | null
	geopolygon: { type: string; coordinates: Coordinate[][] } | null
	comparableDestinations: IComparableDestination[] | null
	id: string | null
	propertyId: string | null
	itemType: string | null
	paymentCustomerId: string | null
	currentSubscriptionInfo: SubscriptionInfo
	name: string | null
	address: string | null
	address2: string | null
	city: string | null
	state: string | null
	zip: string | null
	country: string | null
	currencyCode: string | null
	measurementSystemCode: string | null
	phone: string | null
	fax: string | null
	addMembersWithEmailDomain: boolean | null
	memberEmailDomain: string | null
	imageUrl: string | null
	latitude: number | null
	longitude: number | null
	geolocation: {
		type: string | null
		coordinates: [number, number]
		crs: {
			type: string | null
			properties: Record<string, string>
		}
	} | null
	destinations: string[]
	airportCode: string | null
	airportDistance: number | null
	airportDistanceUnits: string | null
	airports: string[]
	guestRoomQuantity: number | null
	meetingRoomQuantity: number | null
	meetingSpaceSquareFeet: number | null
	largestMeetingSpaceSquareFeet: number | null
	conferenceFacilities: string | null
	starRating: number
	targetRanking: string | null
	description: string | null
	tagline: string | null
	lastSourceUpdateDate: string | null
	managedByPlatform: boolean | null
	firstAccountCreated: string | null
	receivedRfp: string | null
	respondedToRfp: string | null
	wonRfp: string | null
	invitationCode: InvitationCode
	reviewsCount: number
	ratingsMap: Record<string, number>
	averageRating: number
	expectationsDistribution: Record<string, number>
	expectationsSameOrBetter: number
	profileScore: ProfileScore
	platformSuggestion: PlatformSuggestion
	googlePlacesId: string | null
	externalContentLinks: string[]
	externalSourceIdentifiers: Record<string, string>
	created: string | null
	createdBy: string | null
	deleted: string | null
	deletedBy: string | null
	isDeleted: boolean | null
	modifiedBy: string | null
	modified: string | null
	documentFolders: string[]
	attachmentContainer: string | null
	contentEngagementMetricsSummary: ContentEngagementMetricsSummary
	weatherData: WeatherData
	source: string | null
}

interface InvitationCode {
	code: string | null
	expirationDate: string | null
}

interface PlatformSuggestion {
	isEligible: boolean | null
	withinMiles: number
	enabledBy: string | null
	enabledAt: string | null
	disabledBy: string | null
	disabledAt: string | null
}

interface ProfileScore {
	attributeScores: Record<string, number>
	total: number
	lastCalculated: string | null
}

interface ContentEngagementMetricsSummary {
	lastUpdated: string | null
	metrics: {
		Impression: number
		ViewStart: number
		ViewEnd: number
		Conversion: number
		Download: number
	}
}

export interface WeatherData {
	monthlyTemperatures: {
		C: number[]
		F: number[]
	}
	monthlyRainfall: {
		in: number[]
		cm: number[]
	}
	monthlyWeatherPatterns: string[]
}

export interface ICompetingDestination {
	id: string | null
	propertyId: string | null
	itemType: string | null
	name: string | null
	currentSubscriptionInfo: SubscriptionInfo | null
	phone: string | null
	imageUrl: string | null
	city: string | null
	state: string | null
	latitude: number | null
	longitude: number | null
	geolocation: { type: string; coordinates: number[] } | null
	guestRoomQuantity: number | null
	meetingRoomQuantity: number | null
	meetingSpaceSquareFeet: number | null
	largestMeetingSpaceSquareFeet: number | null
	description: string | null
	tagline: string | null
	hotelCount: number | null
	destinationManagers: string[]
	comparableDestinations: IComparableDestination[]
	geopolygon: { type: string; coordinates: number[][] } | null
}

export interface IDestinationRFP {
	id: string | null
	name: string | null
	status: string | null
	rpfCode: string | null
	organizationId: string | null
	organizationName: string | null
	responsesDueDate: string | null
	supplierContacts: ISupplierContact[]
}

export type IDestinationImage = IHotelImage

export interface DestinationProposalRequest {
	venueId: string | null
	venueName: string | null
	venueCity: string | null
	venueState: string | null
	received: ProposalRequestExtended[] | null
	won: ProposalRequestExtended[] | null
	lost: ProposalRequestExtended[] | null
	declined: ProposalRequestExtended[] | null
	winningMarkets: ProposalRequestExtended[] | null
	winningHotels: ProposalRequestExtended[] | null
	proposalValuesAverage: number | null
	conversionRate: unknown
	declinedAmount: unknown
	declinedGroupTypes: { type: string; count: number }[]
	declinedReasons: { reason: string; count: number }[]
	lostAmount: unknown
	wonAmount: unknown
	wonGroupTypes: { type: string; count: number }[]
	lostGroupTypes: { type: string; count: number }[]
	lostReasons: { reason: string; count: number }[]
	proposed: ProposalRequestExtended[] | null
	proposedAmount: unknown
	receivedAmount: unknown
	responseTimes: {
		average: {
			days: number
			hours: number
			minutes: number
			totalMinutes: number
		}
		minimum: {
			days: number
			hours: number
			minutes: number
			totalMinutes: number
		}
	}
}

export interface IDestinationLocation {
	name: string | null
	latitude: number | null
	longitude: number | null
	locationType?: string | null
}
