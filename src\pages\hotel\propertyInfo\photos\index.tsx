/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable @typescript-eslint/no-shadow */
import { formatImageUrl, formatNumber } from 'lib/helpers'
// import type { MouseEvent } from 'react'
import { useEffect, useState } from 'react'
import {
	getImages,
	setImageCaption,
	useDeleteVenuePhoto,
	usePostImageFormData
} from 'lib/services/hotels.service'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import type { IHotelImage } from 'models/hotels'
import Dropzone from 'react-dropzone'
import { useFeatureContext } from 'lib/providers/feature.provider'
import useHotelStore from 'lib/store/hotelStore'
import HSButton from 'components/button'
import { toast } from 'react-toastify'
import {
	faFile,
	faPenToSquare,
	faTrashCanXmark,
	faInfoCircle
} from '@fortawesome/pro-light-svg-icons'
import HSDropzone from 'components/dropzone'
import HSDrawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import HSCheckbox from 'components/checkbox'
import HSTextField from 'components/textField'
import { faCircleXmark } from '@fortawesome/pro-duotone-svg-icons'
import HSTooltipWithEllipsis from 'components/tooltipEllipsis'
import PhotoControl from 'components/photoControl'

const HotelPhotos = () => {
	const { venue: hotel, setProperty } = useHotelStore()
	const { getFeatureByKey } = useFeatureContext()
	const [photosFeature, setPhotosFeature] = useState(
		getFeatureByKey('PHOTOS', hotel)
	)
	const [images, setImages] = useState<IHotelImage[]>([])
	const [selectedImage, setSelectedImage] = useState<IHotelImage>()
	const [hoverPhotoId, setHoverPhotoId] = useState<string | null>(null)
	// const [headerHover, setHeaderHover] = useState(false)
	const [showEditDrawer, setShowEditDrawer] = useState<boolean>(false)
	const [showUploadDrawer, setShowUploadDrawer] = useState<boolean>(false)
	const [acceptedFiles, setAcceptedFiles] = useState<File[]>()
	const [selectedImages, setSelectedImages] = useState<
		IHotelImage[] | undefined
	>()

	const { mutateAsync: postImageFormData, isPending: isUploadingImages } =
		usePostImageFormData()
	const { mutateAsync: deleteVenuePhoto } = useDeleteVenuePhoto()

	useEffect(() => {
		if (hotel.id) {
			getImages(hotel.id, { branding: false, profile: true })
				.then(r => setImages(r))
				// eslint-disable-next-line @typescript-eslint/no-shadow
				.catch((error: unknown) => console.log(error))
			setPhotosFeature(
				getFeatureByKey('PHOTOS', {
					currentSubscriptionInfo: hotel.currentSubscriptionInfo
				})
			)
		}
	}, [hotel.id, hotel.currentSubscriptionInfo, getFeatureByKey])

	const onAddPhoto = (
		file: File,
		success: (response: IHotelImage[]) => void
	) => {
		const formData = new FormData()

		// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
		if (file.stream === undefined) {
			formData.append('imageUrl', JSON.stringify(file))
		} else {
			formData.append('file0', file, file.name)
		}
		formData.append('isDefault', 'true')

		postImageFormData({ venueId: hotel.id ?? '', formData })
			.then(r => {
				success(r)
			})
			.catch((error: unknown) => {
				console.error(error)
			})
			.finally(() => {
				setAcceptedFiles(undefined)
			})
	}

	const onAddPhotos = (
		files: File[],
		success: (response: IHotelImage[]) => void,
		imageProperty?: Partial<IHotelImage>
	) => {
		const formData = new FormData()

		let fileCounter = 0
		for (const file of files) {
			formData.append(`file${fileCounter}`, file, file.name)
			fileCounter += 1
		}
		const imageArray = files.map((f, index) => ({
			name: f.name.split('.')[0],
			extension: f.name.split('.')[1],
			url: f.name,
			caption: imageProperty?.caption ?? '',
			isDefault: false,
			sortIndex: imageProperty?.sortIndex ?? Number(images.length) + index + 1
		}))
		formData.append('imageArray', JSON.stringify(imageArray))
		toast.success('Uploading photos')
		postImageFormData({ venueId: hotel.id ?? '', formData })
			.then(r => {
				success(r)
			})
			.catch((error: unknown) => console.error(error))
			.finally(() => {
				setSelectedImages(undefined)
				setAcceptedFiles(undefined)
			})
	}

	const onDeletePhoto = (image: IHotelImage, success: () => void) => {
		deleteVenuePhoto({
			profileInfoId: hotel.id ?? '',
			image
		})
			.then(() => {
				success()
			})
			.catch((error: unknown) => {
				console.error(error)
			})
	}

	const deletePhoto = (
		event: React.MouseEvent<HTMLButtonElement>,
		image: IHotelImage
	) => {
		event.stopPropagation()
		toast.info('Deleting photo')
		const success = () => {
			setImages(images.filter(img => img.id !== image.id))
			toast.success('Photo deleted')
		}

		onDeletePhoto(image, success)
	}

	const deleteDefaultPhoto = () => {
		setProperty('imageUrl', null)
	}

	const addPhoto = (files: File[]) => {
		if (files.length > 1) {
			toast.error('Multiple images are not allowed.')
			return
		}
		toast.info(`Updating default photo`)

		const success = () => {
			toast.success('Photo added')
			setProperty('imageUrl', files[0].name)
		}
		onAddPhoto(files[0], success)
	}

	const addPhotos = (files: File[]) => {
		if (
			photosFeature &&
			photosFeature.properties.max > 0 &&
			images.length + files.length > photosFeature.properties.max
		) {
			toast.error(
				`You cannot upload more than ${formatNumber(photosFeature.properties.max)} photos.`
			)
			return
		}
		toast.info(`Adding ${files.length} photos`)

		const success = (responseData: IHotelImage[]) => {
			setImages([...responseData])
			toast.success(`${files.length} photos added`)
		}
		onAddPhotos(files, success)
	}

	const onHide = () => {
		setShowUploadDrawer(false)
		setAcceptedFiles(undefined)
	}

	const onImageCheck = (image: IHotelImage) => {
		if (selectedImages?.map(a => a.id).includes(image.id)) {
			setSelectedImages(selectedImages.filter(a => a.id !== image.id))
		} else {
			setSelectedImages([...(selectedImages ?? []), image])
		}
	}

	const onClickEditDrawer = (image: IHotelImage) => {
		setShowEditDrawer(true)
		setSelectedImage(image)
	}

	const closeEditDrawer = () => {
		setShowEditDrawer(false)
		setSelectedImage(undefined)
		setAcceptedFiles(undefined)
	}

	const saveEditChanges = (image: IHotelImage, files: File[]) => {
		const { name, extension, url, ...imageProperty } = image
		closeEditDrawer()
		toast.info('Updating photo')
		if (!url) {
			const success = (responseData: IHotelImage[]) => {
				toast.success('Photo updated')
				setImages([...responseData])
			}
			onDeletePhoto(image, () => onAddPhotos(files, success, imageProperty))
		}
		// else if (image.caption) {
		setImageCaption(image.id, hotel.id ?? '', image)
			.then(r => {
				setImages(() => images.map(img => (img.id === image.id ? r : img)))
				toast.info('Photo updated')
			})
			.catch((error: unknown) => console.log(error))
		// }
	}

	return (
		<>
			<div className='flex items-center justify-between border-b px-6 py-4'>
				<div className=''>
					<div className='text-xl font-semibold'>Photos</div>
					<div className='w-[480px] text-sm text-gray-500'>
						Hover over image to see more details
					</div>
				</div>
				<div className='flex gap-4'>
					<HSButton color='primary' outline-none disabled>
						Edit Order
					</HSButton>
					<HSButton color='primary' onClick={() => setShowUploadDrawer(true)}>
						Upload New Photos
					</HSButton>
				</div>
			</div>
			<div
				className='flex flex-col gap-6 overflow-y-auto p-6'
				style={{ maxHeight: 'calc(100vh - 14rem)' }}
			>
				<div className='flex flex-col gap-2 lg:w-2/5'>
					{/* <div className='grid grid-cols-2 gap-6'> */}
					<div className='flex items-center justify-between'>
						<div className='text-lg font-semibold text-gray-900'>
							Main Image
						</div>
					</div>
					<div className='flex items-center justify-between'>
						<PhotoControl
							imageSrc={
								hotel.imageUrl
									? formatImageUrl(hotel.imageUrl, hotel.id ?? '')
									: ''
							}
							onPhotoAdded={(f: File) => addPhoto([f])}
							onPhotoDeleted={deleteDefaultPhoto}
							style={{
								height: '317px'
							}}
							cropAspectRatioOptions={{
								ratio: 1,
								targetSize: { width: 600, height: 600 }
							}}
							isDisabled={false}
							imageClasses={null}
						/>
						<div />
					</div>
				</div>
				<div className='flex flex-col gap-2'>
					<div className='text-lg font-semibold text-gray-900'>
						Other Images
					</div>
					<div>
						<Dropzone
							accept={{
								'image/jpeg': [],
								'image/png': [],
								'image/svg': [],
								'image/gif': []
							}}
							onDrop={files => addPhotos(files)}
							noClick
							noKeyboard
						>
							{({ getRootProps, getInputProps, open }) => (
								<div
									{...getRootProps()}
									className={`${images.length === 0 ? 'items-center justify-center border border-gray-200' : ''} flex`}
									style={{ height: images.length === 0 ? '318px' : 'auto' }}
								>
									<input
										{...getInputProps()}
										disabled={
											!photosFeature ||
											(photosFeature.properties.max > 0 &&
												images.length >= photosFeature.properties.max)
										}
									/>
									{photosFeature ? (
										images.length === 0 ? (
											<div className='flex flex-col items-center justify-center gap-2'>
												<div className='text-sm text-gray-900'>No Photos</div>
												<HSButton
													size='xs'
													color='light'
													onClick={open}
													disabled={isUploadingImages}
												>
													Upload New Photos
												</HSButton>
											</div>
										) : null
									) : (
										<div className='text-red-700'>
											Your license does not allow you to upload additional
											photos.
										</div>
									)}
									<div className='-mx-2 flex flex-wrap'>
										{images
											// .sort((a, b) => a.id.localeCompare(b.id))
											// .sort((a, b) =>
											// 	(a.sortIndex ?? 0) > (b.sortIndex ?? 0) ? 1 : -1
											// )
											.map((image, index) => (
												<div
													// eslint-disable-next-line react/no-array-index-key
													key={index}
													className='rounder-lg mb-4 px-2'
													onMouseEnter={() => setHoverPhotoId(image.id)}
													onMouseLeave={() => setHoverPhotoId(null)}
												>
													<div className='rounded-lg shadow-sm'>
														<div
															className={`relative rounded-lg ${
																selectedImages
																	?.map(a => a.id)
																	.includes(image.id)
																	? 'border-[3px] border-primary-200'
																	: 'border-[3px] border-gray-200'
															}`}
														>
															<div className='absolute left-0 top-0 rounded-br-lg bg-gray-200 p-2'>
																<HSCheckbox
																	value={image.id}
																	onChange={() => onImageCheck(image)}
																	checked={selectedImages
																		?.map(a => a.id)
																		.includes(image.id)}
																/>
															</div>
															{hoverPhotoId === image.id ? (
																<>
																	<div className='absolute right-0 top-0 rounded-bl-lg bg-gray-200'>
																		<HSButton
																			size='sm'
																			color='text'
																			onClick={event =>
																				deletePhoto(event, image)
																			}
																		>
																			<FontAwesomeIcon
																				className='p-2 text-red-500'
																				icon={faTrashCanXmark}
																			/>
																		</HSButton>
																	</div>
																	<div className='absolute bottom-0 flex w-full flex-col rounded-b-md bg-white py-1 pl-2 pr-0'>
																		<div className='flex items-center justify-between'>
																			<div className='truncate font-medium text-gray-700'>
																				<HSTooltipWithEllipsis
																					content={`${image.name}.${image.extension}`}
																				/>
																			</div>

																			<div>
																				<HSButton
																					size='sm'
																					color='text'
																					onClick={() =>
																						onClickEditDrawer(image)
																					}
																				>
																					<span className='px-2'>
																						<FontAwesomeIcon
																							icon={faPenToSquare}
																						/>
																					</span>
																				</HSButton>
																			</div>
																		</div>
																		<div className='truncate text-gray-500'>
																			{image.caption}
																		</div>
																	</div>
																</>
															) : null}
															<a
																target='_blank'
																rel='noopener noreferrer'
																href={formatImageUrl(image.url, hotel.id ?? '')}
																onClick={event => event.stopPropagation()}
															>
																<img
																	src={formatImageUrl(
																		image.url,
																		hotel.id ?? ''
																	)}
																	alt={image.caption}
																	className='rounded-md object-cover'
																	style={{ width: '17rem', height: '17rem' }}
																/>
															</a>
														</div>
														{/* // TODO: Add tags */}
														{/* <div className='rounded-b-lg border border-t-0 border-gray-200 p-2 font-medium leading-none text-gray-700'>
														Tags goes here
													</div> */}
													</div>
												</div>
											))}
									</div>
								</div>
							)}
						</Dropzone>
					</div>
				</div>
			</div>
			{showUploadDrawer ? (
				<HSDrawer
					id='add-new-image-dialog'
					onClose={onHide}
					open={showUploadDrawer}
					position='right'
					style={{ width: '700px' }}
				>
					<div className='flex h-[calc(100vh-2rem)] flex-col justify-between'>
						<div className='flex flex-col gap-5'>
							<Drawer.Header title='Upload New Photos' titleIcon={() => null} />
							<div>
								<HSDropzone
									acceptedFiles={acceptedFiles}
									setAcceptedFiles={setAcceptedFiles}
									acceptedFileTypes={{
										'image/jpeg': [],
										'image/png': [],
										'image/svg': [],
										'image/gif': []
									}}
									type='image'
								/>
							</div>
						</div>
						<div className='flex gap-4'>
							<HSButton color='light' className='grow' onClick={onHide}>
								Cancel
							</HSButton>
							<HSButton
								className='grow'
								onClick={() => {
									onHide()
									addPhotos(acceptedFiles ?? [])
								}}
								disabled={(acceptedFiles?.length ?? 0) <= 0}
							>
								Upload
							</HSButton>
						</div>
					</div>
				</HSDrawer>
			) : null}
			{showEditDrawer ? (
				<HSDrawer
					id='edit-image-dialog'
					onClose={closeEditDrawer}
					open={showEditDrawer}
					position='right'
					style={{ width: '700px' }}
				>
					<div className='flex h-[calc(100vh-2rem)] flex-col justify-between'>
						<div className='flex flex-col gap-5'>
							<Drawer.Header title='Edit Photo' titleIcon={() => null} />
							<div className='card flex flex-col gap-4 p-4'>
								<div>
									<HSTextField
										label='Name'
										value={`${selectedImage?.name}`}
										onChange={event => {
											setSelectedImage(
												() =>
													({
														...selectedImage,
														name: event.target.value
													}) as IHotelImage
											)
										}}
										groupPlacement='left'
										groupItem={selectedImage?.extension}
									/>
								</div>
								<div>
									<HSTextField
										label={
											<div className='flex items-center gap-1'>
												Caption
												<FontAwesomeIcon
													id='hotel-caption-info-icon'
													className='text-gray-500'
													icon={faInfoCircle}
												/>
											</div>
										}
										value={selectedImage?.caption ?? ''}
										onChange={event => {
											setSelectedImage(
												() =>
													({
														...selectedImage,
														caption: event.target.value
													}) as IHotelImage
											)
										}}
									/>
								</div>
							</div>
							{selectedImage?.url ? (
								<div className='flex flex-col gap-2'>
									<div className='rounded-lg border border-gray-200'>
										<img
											src={formatImageUrl(selectedImage.url, hotel.id ?? '')}
											alt={selectedImage.caption}
											className='rounded-lg object-cover'
											style={{ width: '100%', height: '20rem' }}
										/>
									</div>
									<div
										className='flex items-center justify-between gap-4'
										key={selectedImage.name}
									>
										<div className='text-2xl'>
											<FontAwesomeIcon
												className='text-gray-500'
												icon={faFile}
											/>
										</div>
										<div className='flex flex-1 flex-col'>
											<div className='text-sm font-medium text-gray-900'>
												{selectedImage.name}.{selectedImage.extension}
											</div>
										</div>
										<div className='flex items-center justify-center'>
											<HSButton
												color='text-light'
												size='sm'
												onClick={() =>
													setSelectedImage(() => ({
														...selectedImage,
														url: ''
													}))
												}
											>
												<FontAwesomeIcon icon={faCircleXmark} />
											</HSButton>
										</div>
									</div>
								</div>
							) : null}
							{selectedImage?.url ? null : (
								<div>
									<HSDropzone
										acceptedFiles={acceptedFiles}
										setAcceptedFiles={setAcceptedFiles}
										singleFile
										acceptedFileTypes={{
											'image/jpeg': [],
											'image/png': [],
											'image/svg': [],
											'image/gif': []
										}}
										type='image'
									/>
								</div>
							)}
						</div>
						<div className='flex gap-4 py-2'>
							<HSButton
								color='light'
								className='grow'
								onClick={closeEditDrawer}
							>
								Cancel
							</HSButton>
							<HSButton
								className='grow'
								onClick={() => {
									saveEditChanges(
										selectedImage as IHotelImage,
										acceptedFiles ?? []
									)
								}}
								disabled={
									!(selectedImage?.url || (acceptedFiles?.length ?? 0) > 0)
								}
							>
								Save
							</HSButton>
						</div>
					</div>
				</HSDrawer>
			) : null}
		</>
	)
}

export default HotelPhotos
