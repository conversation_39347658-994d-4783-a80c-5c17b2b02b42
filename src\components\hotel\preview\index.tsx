import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import HSBadge from 'components/badge'
import { getHopSkipConfig } from 'lib/auth/auth.config'
import { formatImageUrl, formatNumber } from 'lib/helpers'
import { useGetAffiliateOrganizations } from 'lib/services/affiliateOrganizations.service'
import { useSearchPromotions } from 'lib/services/promotion.service'
import type { IAffiliateOrganization } from 'models/affiliateOrganizations'
import type { Venue } from 'models/venue'
import { memo, useEffect, useState } from 'react'
import type { Promotion } from 'models/promotion'
import type { IconProp } from '@fortawesome/fontawesome-svg-core'
import {
	faBedFront,
	faCameraSlash,
	faCrown,
	faGem,
	faPeopleGroup,
	faRulerTriangle
} from '@fortawesome/pro-light-svg-icons'
import { faCircleDot, faHandshake } from '@fortawesome/pro-regular-svg-icons'
import { faStar } from '@fortawesome/pro-solid-svg-icons'
import { ratingSystems } from 'lib/helpers/venueFeatures'
import HSTooltip from 'components/tooltip'
import AddRemoveHotelButton from 'components/event/planner/addRemoveHotelButton'
import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'

const getIcon = (type: string): IconProp => {
	switch (type) {
		case 'crown': {
			return faCrown
		}
		case 'diamond': {
			return faGem
		}
		case 'bubble': {
			return faCircleDot
		}
		default: {
			return faStar
		}
	}
}

interface HotelPreviewProperties {
	venue: Venue
	isSearch?: boolean
	onClick?: (venue: Venue) => void
	showAddToRFP?: boolean
	addRemoveProposalRequests?: (
		proposalRequest: Partial<ProposalRequest>[],
		callback: () => void,
		isAdmin: boolean
	) => Promise<void>
	disabledFields?: boolean
	eventInfo?: EventPlan
}

const hopSkipConfig = getHopSkipConfig()

const HotelPreview = memo((properties: HotelPreviewProperties) => {
	const {
		venue: hotel,
		isSearch,
		onClick,
		showAddToRFP,
		addRemoveProposalRequests,
		disabledFields,
		eventInfo
	} = properties
	const [promotions, setPromotions] = useState<Promotion[]>([])
	const { userProfile } = useUserProfileContext()

	const [paidAffiliates, setPaidAffiliates] =
		useState<Partial<IAffiliateOrganization>[]>()

	const { refetch: loadPromotions } = useSearchPromotions(
		{
			pageIndex: 0,
			pageSize: 10,
			sort: 'headline-asc',
			propertyId: hotel.id ?? ''
		},
		!!hotel.id
	)
	const { refetch: listAffiliateOrganizations } = useGetAffiliateOrganizations(
		{
			filter: '',
			top: '100',
			skip: '0',
			orderBy: { field: 'name', direction: 1 }
		},
		false
	)

	useEffect(() => {
		listAffiliateOrganizations()
			.then(r => {
				setPaidAffiliates(
					r.data
						?.filter(
							affiliateOrganization =>
								affiliateOrganization.subscriptionInfos.length > 0
						)
						.filter(a => hotel.propertySellers?.some(ps => ps.id === a.id))
						.map(a => ({
							id: a.id,
							name: a.name,
							filterId: 'propertySellerId',
							logoImageUrl: a.logoImageUrl
						}))
				)
			})
			.catch((error: unknown) => {
				console.log(error)
			})
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [hotel.propertySellers?.length])

	useEffect(() => {
		loadPromotions()
			.then(response => {
				setPromotions(response.data?.value ?? [])
			})
			.catch((error: unknown) => {
				console.log(error)
			})
	}, [loadPromotions])

	const getVenueRating = (): {
		ratingSystemKey: string
		label: string
		rating: number
		type: string
	}[] =>
		Object.keys(ratingSystems)
			.filter(rating => ratingSystems[rating]?.previewMode)
			.map(key => {
				const ratingSystem = ratingSystems[key]
				const venueRating = hotel.venueRatings?.[key]

				return {
					ratingSystemKey: key,
					label: ratingSystem?.label || '',
					rating: venueRating?.rating ?? 0,
					type: ratingSystem?.type ?? 'star'
				}
			})

	return (
		<div
			className={`flex rounded-lg border bg-gray-50 shadow-sm md:w-full ${isSearch ? 'shadow-md' : 'lg:w-1/2'}`}
		>
			<div
				className='relative max-w-[200px]'
				onClick={() => {
					if (onClick) {
						onClick(hotel)
					}
				}}
				role='button'
				tabIndex={0}
				onKeyDown={event => {
					if (event.key === 'Enter' && onClick) {
						onClick(hotel)
					}
				}}
			>
				<img
					className={`${hotel.imageUrl ? '' : 'grayscale'} h-full rounded-l-lg object-cover`}
					alt='hotel-image'
					src={
						hotel.imageUrl?.includes('coming-soon') || !hotel.imageUrl
							? '/images/hotel-coming-soon.png'
							: formatImageUrl(hotel.imageUrl, hotel.id ?? '')
					}
					loading='lazy'
				/>
				{hotel.imageUrl ? null : (
					<div className='absolute top-1/2 translate-x-4'>
						<HSBadge color='white'>
							<div className='flex items-center gap-1 whitespace-nowrap text-xs font-medium leading-none text-gray-700'>
								<div>
									<FontAwesomeIcon icon={faCameraSlash} />
								</div>
								<div>Image Coming Soon</div>
							</div>
						</HSBadge>
					</div>
				)}
			</div>
			<div className='flex flex-1 flex-col gap-3 p-4'>
				<div className='flex justify-between'>
					<div className='flex flex-col'>
						<div className='text-base font-semibold text-gray-700'>
							{hotel.name}
						</div>
						<div className='text-xs text-gray-500'>
							{hotel.city}, {hotel.state} {hotel.zip}
						</div>
					</div>
					{showAddToRFP ? (
						<div>
							<AddRemoveHotelButton
								hotel={hotel}
								disabledFields={!!disabledFields}
								addRemoveProposalRequests={async (pr, callback, isAdmin) => {
									if (addRemoveProposalRequests) {
										await addRemoveProposalRequests(pr, callback, isAdmin)
									}
								}}
								eventPlan={eventInfo}
							/>
						</div>
					) : null}
				</div>
				<div className='flex flex-col gap-2'>
					<div className='flex items-baseline divide-x text-sm font-medium'>
						{getVenueRating().map((rating, index) => (
							<div
								key={rating.ratingSystemKey}
								className={`flex items-center gap-1 ${index === 0 ? 'pr-2' : 'px-2'} leading-none'`}
							>
								<span
									className={
										rating.rating === 0 ? 'text-gray-300' : 'text-gray-700'
									}
								>
									{rating.rating === 0 ? 'N/A' : rating.rating}
								</span>
								<HSTooltip
									content={
										rating.rating === 0
											? `No ${rating.label} Rating`
											: rating.label
									}
								>
									<FontAwesomeIcon
										className={`text-sm ${rating.rating === 0 ? 'text-gray-300' : 'text-yellow-300'}`}
										icon={getIcon(rating.type)}
									/>
								</HSTooltip>
							</div>
						))}
					</div>
					<div className='flex gap-6'>
						<div className='flex items-center gap-2'>
							<FontAwesomeIcon
								className='w-4 text-gray-500'
								icon={faBedFront}
							/>
							<div className='text-sm font-normal text-gray-600'>
								{formatNumber(hotel.guestRoomQuantity)}
							</div>
						</div>
						<div className='flex items-center gap-2'>
							<FontAwesomeIcon
								className='w-6 text-gray-500'
								icon={faHandshake}
							/>
							<div className='flex items-center gap-1'>
								<div className='text-sm font-normal text-gray-600'>
									{formatNumber(hotel.meetingSpaceSquareFeet)}
								</div>
								<div className='text-sm font-normal text-gray-500'>
									Ft<sup>2</sup>
								</div>
							</div>
						</div>
						<div className='flex items-center gap-2'>
							<FontAwesomeIcon
								className='w-6 text-gray-500'
								icon={faPeopleGroup}
							/>
							<div className='text-sm font-normal text-gray-600'>
								{formatNumber(hotel.meetingRoomQuantity)}
							</div>
						</div>
						<div className='flex gap-2'>
							<FontAwesomeIcon
								className='w-4 text-gray-500'
								icon={faRulerTriangle}
							/>
							<div className='flex items-center gap-1'>
								<div className='text-sm font-normal text-gray-600'>
									{formatNumber(hotel.largestMeetingSpaceSquareFeet)}
								</div>
								<div className='text-sm font-normal text-gray-500'>Ft2</div>
							</div>
						</div>
					</div>
					<div className='flex gap-6'>
						{/* Hotel Types Section */}
						{userProfile?.previewExperiences?.SEARCHFILTERHOTELTYPE &&
						hotel.hotelTypes &&
						hotel.hotelTypes.length > 0 ? (
							<div className='flex items-center gap-2'>
								{hotel.hotelTypes.length >= 2 ? (
									<>
										{hotel.hotelTypes.slice(0, 2).map(h => (
											<HSBadge key={h} color='light'>
												{h}
											</HSBadge>
										))}
										{hotel.hotelTypes.length - 2 > 0 ? (
											<TooltipComponent
												content={hotel.hotelTypes.slice(2).join(', ')}
											>
												<HSBadge color='dark' className='cursor-pointer'>
													+{hotel.hotelTypes.length - 2}
												</HSBadge>
											</TooltipComponent>
										) : null}
									</>
								) : (
									hotel.hotelTypes.map(h => (
										<HSBadge key={h} color='light'>
											{h}
										</HSBadge>
									))
								)}
							</div>
						) : null}

						{/* Divider Between Hotel Types and Affiliates */}
						{hotel.hotelTypes && hotel.hotelTypes.length > 0
							? (paidAffiliates?.filter(a => a.logoImageUrl).length ?? 0) >
									0 && <div className='border-l border-gray-200' />
							: null}

						{/* Paid Affiliates Section */}
						{(paidAffiliates?.filter(a => a.logoImageUrl).length ?? 0) > 0 && (
							<div className='flex items-center gap-2'>
								{paidAffiliates
									?.filter(a => a.logoImageUrl)
									.map((a, ai) => (
										<img
											className='h-[30px] w-[30px] rounded-md border object-cover'
											// eslint-disable-next-line react/no-array-index-key
											key={ai}
											src={`${hopSkipConfig.imagesEndpoint.url}/${a.id}/${a.logoImageUrl}`}
											alt={a.name ?? ''}
											loading='lazy'
										/>
									))}
							</div>
						)}

						{/* Divider Between Affiliates and Promotions */}
						{(paidAffiliates?.filter(a => a.logoImageUrl).length ?? 0) > 0 &&
							promotions.length > 0 && (
								<div className='border-l border-gray-200' />
							)}

						{/* Promotions Section */}
						{promotions.length > 0 && (
							<div className='flex items-center gap-2'>
								<HSBadge color='yellow'>Special offer</HSBadge>
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	)
})

export default HotelPreview
