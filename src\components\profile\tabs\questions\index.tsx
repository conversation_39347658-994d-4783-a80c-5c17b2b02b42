/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable unicorn/no-keyword-prefix */
import type { IQuestion } from 'models/questions'
import { useEffect, useMemo, useState } from 'react'
import { getDefaultQuestion } from './helper'
import {
	useDeleteQuestion,
	useGetQuestions,
	useUpdateQuestion
} from 'lib/services/planner.service'
import type {
	GridComponent,
	RowDeselectEventArgs,
	RowSelectEventArgs
} from '@syncfusion/ej2-react-grids'
import {
	ColumnDirective,
	ColumnsDirective,
	Inject,
	Page,
	Sort,
	Selection
} from '@syncfusion/ej2-react-grids'
import { faFilter, faPlus } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import type { ISourcingProfile } from 'models/organizations'
import { includeTemplate, actionTemplate } from './templates'
import { useUpsertSourcingProfile } from 'lib/services/organizations.service'
import { mapSourcingProfileToAssociatedObject } from '../concessionRequests/helper'
import { requestGroups } from 'lib/helpers/requestGroups'
import Loader from 'components/loader'
import DataGrid from 'components/dataGrid'
import HSButton from 'components/button'
import HSBadge from 'components/badge'
import HSPopover from 'components/popover'
import filterStore from './filterStore'
import HSTextField from 'components/textField'
import type { IUserProfile } from 'models/userProfiles'
import HSIcon from 'components/HSIcon'
import { faSearch } from '@fortawesome/pro-regular-svg-icons'
import ShareModal from 'components/shareModal'
import SourcingProfileModal from 'components/sourcingProfileModal'
import QuestionModal from 'components/questionModal'

const Questions = ({ userProfile }: { userProfile: IUserProfile }) => {
	let gridInstance: GridComponent | null = null

	const [gridData, setGridData] = useState<IQuestion[] | undefined>([])
	const [searchText, setSearchText] = useState<string>('')
	const [filteredData, setFilteredData] = useState<IQuestion[] | undefined>([])
	const [selectedQuestion, setSelectedQuestion] =
		useState<Partial<IQuestion>>(getDefaultQuestion())
	const [editExisting, setEditExisting] = useState(false)
	const [showAddEditModal, setShowAddEditModal] = useState(false)
	const [showShareModal, setShowShareModal] = useState(false)
	const [sourcingProfileContext, setSourcingProfileContext] = useState<{
		showAddEditModal: boolean
		editExisting: boolean
		addNew: boolean
		sourcingProfile: ISourcingProfile | object
	}>({
		showAddEditModal: false,
		editExisting: false,
		addNew: false,
		sourcingProfile: {}
	})
	const [isDataLoading, setIsDataLoading] = useState(true)

	const { refetch: getQuestions, isFetching } = useGetQuestions(
		userProfile.id ?? '',
		false
	)
	const { mutateAsync: deleteQuestion } = useDeleteQuestion()
	const { mutateAsync: update } = useUpdateQuestion()
	const { mutateAsync: upsertSourcingProfile } = useUpsertSourcingProfile()

	const [selectedQuestionIds, setSelectedQuestionIds] = useState<string[]>([])

	const refetchQuestion = () => {
		setIsDataLoading(true)
		getQuestions()
			.then((response: { data: IQuestion[] | undefined }) => {
				setGridData(
					response.data?.map(q => ({
						...q,
						groupName: requestGroups[q.requestGroupId ?? '']?.name ?? '',
						groupSortIndex:
							requestGroups[q.requestGroupId ?? '']?.sortIndex ?? 0
					}))
				)
			})
			.catch((error: unknown) => console.error(error))
			.finally(() => setIsDataLoading(false))
	}

	useEffect(() => {
		refetchQuestion()
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	const {
		groupSearch,
		clearAll,
		setGroupSearch,
		questionSearch,
		setQuestionSearch
	} = filterStore()

	const onActionEdit = (item: IQuestion) => {
		setEditExisting(true)
		setSelectedQuestion(item)
		setShowAddEditModal(true)
	}

	const onActionDelete = (item: IQuestion) => {
		deleteQuestion({
			userProfileId: userProfile.id ?? '',
			itemId: item.id ?? ''
		})
			.then(() => {
				refetchQuestion()
			})
			.catch((error: unknown) => console.error(error))
	}

	const onAddNew = () => {
		setEditExisting(false)
		setSelectedQuestion(getDefaultQuestion())
		setShowAddEditModal(true)
	}

	const onAddUpdate = (newQuestion: IQuestion, stayOpen: boolean) => {
		refetchQuestion()
		setShowAddEditModal(false)
		setGridData([
			...(gridData ?? []).filter(cc => cc.id !== newQuestion.id),
			{ ...newQuestion }
		])
		setEditExisting(false)
		setSelectedQuestion(getDefaultQuestion())
		setShowAddEditModal(stayOpen)
	}

	// eslint-disable-next-line unicorn/prevent-abbreviations
	const updateQuestion = (question: IQuestion, props: Partial<IQuestion>) => {
		update({
			userProfileId: userProfile.id ?? '',
			question: {
				...question,
				...props
			}
		})
			.then(() => {
				refetchQuestion()
				gridInstance?.clearSelection()
			})
			.catch((error: unknown) => console.error(error))
	}

	const toggleInclude = (question: IQuestion) => {
		updateQuestion(question, {
			includeByDefault: !question.includeByDefault
		})
	}

	const onCancelAddEditSourcingProfile = () => {
		setShowAddEditModal(false)
		setSelectedQuestion({})
		setSourcingProfileContext({
			showAddEditModal: false,
			editExisting: false,
			addNew: false,
			sourcingProfile: {}
		})
	}

	const onAddUpdateSourcingProfile = (item: ISourcingProfile) => {
		upsertSourcingProfile({
			organizationId: userProfile.organizationId ?? '',
			sourcingProfileId: item.id ?? '',
			sourcingProfile: item
		})
			.then(response => {
				updateQuestion(selectedQuestion as IQuestion, {
					sourcingProfiles: [
						...(selectedQuestion.sourcingProfiles ?? []),
						mapSourcingProfileToAssociatedObject(response)
					]
				})
				// setSourcingProfiles(s => [
				// 	...(s ?? []).filter(profile => profile.id !== item.id),
				// 	item
				// ])
				setSelectedQuestion({})
				setSourcingProfileContext({
					showAddEditModal: false,
					editExisting: false,
					addNew: false,
					sourcingProfile: {}
				})
			})
			.catch((error: unknown) => console.error(error))
	}

	// const onTagAdded = (item: IQuestion, tag: string) => {
	// 	const sp = sourcingProfiles?.find(s => s.code === tag)
	// 	if (sp) {
	// 		updateQuestion(item, {
	// 			sourcingProfiles: [
	// 				...(item.sourcingProfiles ?? []),
	// 				mapSourcingProfileToAssociatedObject(sp)
	// 			]
	// 		})
	// 	} else {
	// 		setSourcingProfileContext({
	// 			showAddEditModal: true,
	// 			editExisting: false,
	// 			addNew: true,
	// 			sourcingProfile: {
	// 				id: null,
	// 				organizationId: userProfile.organizationId ?? '',
	// 				name: tag,
	// 				code: tag
	// 			}
	// 		})
	// 		setSelectedQuestion(item)
	// 	}
	// }

	// const onTagDeleted = (item: IQuestion, tag: string) => {
	// 	updateQuestion(item, {
	// 		sourcingProfiles: item.sourcingProfiles?.filter(v => v.name !== tag)
	// 	})
	// }

	const valueTemplate = (item: string) => (
		<div className='text-sm font-normal text-gray-600'>{item}</div>
	)

	const popOverContent = (
		<div className='flex max-h-96 w-80 flex-col gap-4 overflow-y-auto p-4'>
			<div className='flex flex-col gap-2 p-2'>
				<div className='flex items-center justify-between'>
					<div className='text-sm font-medium text-gray-900'>Filters</div>
					<div className='flex gap-2'>
						<HSButton size='xs'>Save View</HSButton>
						<HSButton size='xs' onClick={() => clearAll()}>
							Clear All
						</HSButton>
					</div>
				</div>
			</div>

			<HSTextField
				label='Group'
				placeholder='Start Typing Group'
				value={groupSearch}
				onChange={event => setGroupSearch(event.target.value)}
			/>

			<HSTextField
				label='Question'
				placeholder='Start Typing Target Role'
				value={questionSearch}
				onChange={event => setQuestionSearch(event.target.value)}
			/>
		</div>
	)

	useEffect(() => {
		let updatedData = gridData ?? []
		if (questionSearch !== '') {
			const search = questionSearch.toLowerCase()
			updatedData = updatedData.filter(item =>
				item.text?.toLowerCase().includes(search)
			)
		}
		if (groupSearch !== '') {
			const search = groupSearch.toLowerCase()
			updatedData = updatedData.filter(item =>
				item.groupName?.toLowerCase().includes(search)
			)
		}
		if (searchText !== '') {
			const search = searchText.toLowerCase()
			updatedData = updatedData.filter(
				item =>
					item.groupName?.toLowerCase().includes(search) ||
					item.text?.toLowerCase().includes(search)
			)
		}
		setFilteredData(updatedData)
		gridInstance?.setProperties({ dataSource: updatedData })
	}, [gridInstance, questionSearch, groupSearch, gridData, searchText])

	const filterCount = useMemo(
		() =>
			(questionSearch && questionSearch.length > 0 ? 1 : 0) +
			(groupSearch && groupSearch.length > 0 ? 1 : 0),
		[groupSearch, questionSearch]
	)

	return (
		<div>
			<div className='flex items-center justify-between px-6 py-4'>
				<div className='text-xl font-semibold text-gray-900'>My Questions</div>
			</div>
			<div className='border-b' />
			<div
				className='flex flex-col gap-2 overflow-auto px-6 py-4'
				style={{
					maxHeight: 'calc(100vh - 14rem)'
				}}
			>
				<div className='flex items-center justify-between gap-4'>
					<div className='text-sm font-medium text-gray-900'>
						{filteredData?.length} Questions
					</div>
					<div className='flex items-center gap-4'>
						<HSTextField
							showClearButton
							placeholder='Search'
							icon={HSIcon(faSearch)}
							value={searchText}
							onChange={event => setSearchText(event.target.value)}
							key='question-search'
							className='w-80'
						/>
						<div>
							<HSPopover
								content={popOverContent}
								aria-labelledby='filter'
								placement='bottom'
								arrow={false}
							>
								<div className='relative inline-block'>
									<HSButton color='light' size='sm'>
										<div className='flex items-center gap-2'>
											<FontAwesomeIcon icon={faFilter} />
											Filter
										</div>
									</HSButton>
									{filterCount > 0 ? (
										<HSBadge
											color='failure'
											title={`${filterCount} Filter/s Applied`}
											className='absolute right-0 top-0 z-10 -translate-y-1/2 translate-x-1/2 transform rounded-full bg-red-600'
										>
											<div className='text-md flex p-1 font-semibold text-white'>
												{filterCount}
											</div>
										</HSBadge>
									) : null}
								</div>
							</HSPopover>
						</div>
						<div className='h-6 border-l border-gray-400' />
						<HSButton
							color='light'
							onClick={() => setShowShareModal(true)}
							disabled={selectedQuestionIds.length === 0}
							size='sm'
						>
							Share
						</HSButton>
						<HSButton onClick={() => onAddNew()} size='sm' className='w-32'>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faPlus} />
								Create New
							</div>
						</HSButton>
					</div>
				</div>
				{isFetching || isDataLoading ? (
					<Loader />
				) : (
					<DataGrid
						filterSettings={{ type: 'Menu' }}
						dataSource={filteredData}
						loadingIndicator={{ indicatorType: 'Shimmer' }}
						ref={r => {
							gridInstance = r
						}}
						allowSorting
						allowMultiSorting
						allowTextWrap
						autoFit
						allowPaging
						pageSettings={{ pageSize: 10, pageSizes: [5, 10, 15] }}
						allowSelection
						selectionSettings={{
							checkboxOnly: true,
							type: 'Multiple',
							mode: 'Row'
						}}
						rowSelected={(event: RowSelectEventArgs) =>
							setSelectedQuestionIds(selectedIds => [
								...selectedIds,
								(event.data as IQuestion).id ?? ''
							])
						}
						rowDeselected={(event: RowDeselectEventArgs) =>
							setSelectedQuestionIds(selectedIds =>
								selectedIds.filter(id => id !== (event.data as IQuestion).id)
							)
						}
					>
						<ColumnsDirective>
							<ColumnDirective type='checkbox' width={60} />
							<ColumnDirective
								field='groupName'
								headerText='Group'
								template={(item: IQuestion) =>
									valueTemplate(item.groupName ?? 'No Grouping Specified')
								}
								width={250}
							/>

							<ColumnDirective
								field='text'
								headerText='Question'
								template={(item: IQuestion) => valueTemplate(item.text ?? '')}
								autoFit
							/>
							<ColumnDirective
								field='includeByDefault'
								headerText='Include in all new RFPs'
								allowFiltering={false}
								template={(item: IQuestion) =>
									includeTemplate(item, toggleInclude)
								}
								width={200}
								textAlign='Center'
							/>

							<ColumnDirective
								field='actions'
								headerText='Actions'
								allowFiltering={false}
								template={(item: IQuestion) =>
									actionTemplate(item, onActionEdit, onActionDelete)
								}
								width={150}
								textAlign='Center'
							/>
						</ColumnsDirective>
						<Inject services={[Sort, Page, Selection]} />
					</DataGrid>
				)}
			</div>
			{showShareModal ? (
				<ShareModal
					show={showShareModal}
					userProfileId={userProfile.id ?? ''}
					itemsToShare={
						gridData?.filter(item =>
							selectedQuestionIds.includes(item.id ?? '')
						) ?? []
					}
					// eslint-disable-next-line react/jsx-handler-names
					onClose={() => {
						setShowShareModal(false)
						gridInstance?.clearSelection()
					}}
					shareType='question'
				/>
			) : null}
			{showAddEditModal ? (
				<SourcingProfileModal
					show={sourcingProfileContext.showAddEditModal}
					editExisting={sourcingProfileContext.editExisting}
					addNew={sourcingProfileContext.addNew}
					value={sourcingProfileContext.sourcingProfile as ISourcingProfile}
					// eslint-disable-next-line react/jsx-handler-names
					onChange={(sourcingProfile: unknown) =>
						setSourcingProfileContext({
							...sourcingProfileContext,
							sourcingProfile
						})
					}
					onCancel={onCancelAddEditSourcingProfile}
					onAddUpdate={(item: ISourcingProfile) =>
						onAddUpdateSourcingProfile(item)
					}
				/>
			) : null}
			{showAddEditModal ? (
				<QuestionModal
					show={showAddEditModal}
					isEdit={editExisting}
					isProfile
					userProfileId={userProfile.id ?? ''}
					editQuestion={{ ...(selectedQuestion as IQuestion) }}
					onClose={() => {
						setShowAddEditModal(false)
					}}
					onAddUpdate={onAddUpdate}
				/>
			) : null}
		</div>
	)
}

export default Questions
