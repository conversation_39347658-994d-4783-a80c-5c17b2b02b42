/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable react/no-array-index-key */
import {
	faArrowLeft,
	faInfoCircle,
	faThumbsDown,
	faThumbsUp
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import HSBadge from 'components/badge'
import HSButton from 'components/button'
import Loader from 'components/loader'
import HSTooltipWithEllipsis from 'components/tooltipEllipsis'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import { formatCurrency, formatImageUrl, type ICurrency } from 'lib/helpers'
import type { sleepingRoomTypeOptions } from 'lib/helpers/sleepingRoomType'
import type { ProposalRequestStatusKey } from 'lib/helpers/statusMaps'
import {
	EventPlanStatusMap,
	MarketAvailabilityStatusMap
} from 'lib/helpers/statusMaps'
import {
	convertSummaryProposalRequestToRfp,
	summarizeAvailability
} from 'lib/services/comparableMarket'
import { useGetEventDetail } from 'lib/services/eventPlans.service'
import { getVenue } from 'lib/services/hotels.service'
import {
	addSummaryProposalRequestToRfp,
	declineSummaryProposalRequest,
	marketAvailabilityView
} from 'lib/services/planner.service'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { ProposalRequest } from 'models/proposalResponseMonitor'
import { useEffect, useMemo, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

const forReviewStatus = new Set([
	MarketAvailabilityStatusMap.Active?.key,
	MarketAvailabilityStatusMap.Reviewed?.key
])

const renderTooltipForAvailability = (availability: {
	roomTypesOffered:
		| {
				roomType: (typeof sleepingRoomTypeOptions)[number]
				rate: number
				rateCount: number
		  }[]
		| undefined
	meetingSpaceAvailable: boolean | undefined
	proposalCurrency: ICurrency | undefined
}) => {
	if (availability.roomTypesOffered && availability.meetingSpaceAvailable) {
		return 'This hotel has both guest rooms and meeting space available over your requested dates.'
	}
	if (!availability.roomTypesOffered && availability.meetingSpaceAvailable) {
		return 'This hotel has meeting space available over your requested dates but does not have guest rooms available at this time.'
	}
	if (availability.roomTypesOffered && !availability.meetingSpaceAvailable) {
		return 'This hotel has guest rooms available over your requested dates but does not have meeting space available at this time.'
	}
	return 'This hotel does not have guest rooms or meeting space available at this time.'
}

const PlannerHotelsAvailable = () => {
	const { eventId } = useParams()
	const navigate = useNavigate()
	const { eventInfo: eventPlan, mergeProperties, setObject } = eventInfoStore()
	const { refetch: loadEventInfo } = useGetEventDetail(eventId ?? '', !!eventId)
	const [venueIdsAddedToRfp, setVenueIdsAddedToRfp] = useState<string[]>([])
	const [isLoading, setIsLoading] = useState(false)
	const [groupedSummaryProposalRequests, setGroupedSummaryProposalRequests] =
		useState<{
			forReview: ProposalRequest[]
			addedToRfp: ProposalRequest[]
			notInterested: ProposalRequest[]
		}>({ forReview: [], addedToRfp: [], notInterested: [] })

	const { currencies } = useCurrencyContext()

	const currency = useMemo(
		() => currencies[eventPlan?.currencyCode ?? ''],
		[currencies, eventPlan?.currencyCode]
	)

	useEffect(() => {
		if (eventPlan?.proposalRequests) {
			setVenueIdsAddedToRfp(
				eventPlan.proposalRequests.reduce<string[]>((accumulator, current) => {
					accumulator.push(current.venueId)
					return accumulator
				}, [])
			)
		}
	}, [eventPlan?.proposalRequests])

	useEffect(() => {
		if (eventPlan?.summaryProposalRequests) {
			setGroupedSummaryProposalRequests({
				forReview: eventPlan.summaryProposalRequests.filter(
					pr =>
						pr.status &&
						pr.venueId &&
						forReviewStatus.has(pr.status) &&
						!venueIdsAddedToRfp.includes(pr.venueId)
				),
				addedToRfp: eventPlan.summaryProposalRequests.filter(pr =>
					venueIdsAddedToRfp.includes(pr.venueId)
				),
				notInterested: eventPlan.summaryProposalRequests.filter(
					pr => pr.status === MarketAvailabilityStatusMap.ClosedLost?.key
				)
			})
		} else {
			setGroupedSummaryProposalRequests({
				forReview: [],
				addedToRfp: [],
				notInterested: []
			})
		}
	}, [eventPlan?.summaryProposalRequests, venueIdsAddedToRfp])

	const handleAddToRfp = (summaryProposalRequest: ProposalRequest) => {
		if (eventPlan) {
			convertSummaryProposalRequestToRfp(summaryProposalRequest, eventPlan)
				.then(async r => {
					await addSummaryProposalRequestToRfp(
						eventPlan.id ?? '',
						summaryProposalRequest.venueId,
						summaryProposalRequest.id ?? ''
					)
						.then(response => {
							const summaryProposalRequests = Array.isArray(response)
								? response
								: []

							mergeProperties({
								summaryProposalRequests: [...summaryProposalRequests]
							})
						})
						.then(() => {
							setIsLoading(true)
							const proposalRequests = Array.isArray(r?.data?.proposalRequests)
								? r.data.proposalRequests
								: []
							const siteSearch = r?.data?.siteSearch || {}
							const supplierContacts = Array.isArray(r?.data?.supplierContacts)
								? r.data.supplierContacts
								: []

							mergeProperties({
								proposalRequests: [...proposalRequests],
								siteSearch: { ...siteSearch },
								supplierContacts: [...supplierContacts]
							})
						})
						.then(() => {
							loadEventInfo()
								.then(response => {
									if (response.data) {
										setObject(response.data, true)
									}
									setIsLoading(false)
								})
								.catch(() => {
									console.log('Error loading event info')
								})
						})
				})
				.catch((error: unknown) => {
					console.error('Error suggesting hotels', error)
				})
		}
	}

	const declineProposalRequest = (summaryProposalRequest: ProposalRequest) => {
		declineSummaryProposalRequest(
			eventPlan?.id ?? '',
			summaryProposalRequest.venueId,
			summaryProposalRequest.id ?? ''
		)
			.then(r => {
				const summaryProposalRequests = Array.isArray(r) ? r : []
				mergeProperties({
					summaryProposalRequests: [...summaryProposalRequests]
				})
			})
			.then(() => {
				loadEventInfo()
					.then(response => {
						if (response.data) {
							setObject(response.data, true)
						}
					})
					.catch(() => {
						console.log('Error loading event info')
					})
			})
			.then(() => {
				toast.success('Hotel removed from RFP')
			})
			.catch((error: unknown) => {
				console.error('Error suggesting hotels', error)
				toast.error('Error suggesting hotels')
			})
	}

	const onClickHotelCard = (summaryProposalRequest: ProposalRequest) => {
		getVenue(summaryProposalRequest.venueId)
			.then(() => {
				if (
					summaryProposalRequest.status ===
					MarketAvailabilityStatusMap.Active?.key
				) {
					marketAvailabilityView(
						summaryProposalRequest.eventPlanId ?? '',
						summaryProposalRequest.venueId
					)
						.then(response => {
							mergeProperties({
								summaryProposalRequests: [
									...(eventPlan?.summaryProposalRequests?.filter(
										spr => spr.venueId !== summaryProposalRequest.venueId
									) || []),
									response
								]
							})
						})
						.catch((error: unknown) => {
							console.error(error)
						})
				}
			})
			.catch((error: unknown) => {
				console.error(error)
			})
		navigate(
			`/planner/event/${eventId}/comparable-hotels/${summaryProposalRequest.venueId}`
		)
	}

	return (
		<div className='flex flex-col gap-2'>
			<div className='border-b p-4'>
				<div className='flex justify-between gap-2'>
					<div className='flex flex-col'>
						<div className='text-xl font-semibold text-gray-900'>
							Comparable Hotels with Availability in Your Requested Locations
						</div>
						<div className='text-sm font-normal text-gray-500'>
							These hotels have availability in your selected locations. Add
							them to your RFP to request an official proposal.
						</div>
					</div>
					<div>
						<HSButton
							outline
							onClick={() => {
								navigate(
									`/planner/event/${eventId}/review-proposals/proposal-tracker`
								)
							}}
							className='text-sm'
							color='gray'
						>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faArrowLeft} />
								<div> Back to RFP</div>
							</div>
						</HSButton>
					</div>
				</div>
			</div>
			{isLoading ? (
				<Loader />
			) : (
				<div className='flex flex-col gap-2 p-6'>
					<div className='flex flex-col gap-6'>
						{Object.keys(groupedSummaryProposalRequests).map((key, index) => {
							const count =
								groupedSummaryProposalRequests[
									key as keyof typeof groupedSummaryProposalRequests
								].length
							const groupLabel =
								key === 'forReview'
									? `Waiting for review`
									: key === 'addedToRfp'
										? `Added to RFP`
										: `Hotels I'm not interested in`

							return (
								<div key={index} className='flex flex-col gap-4'>
									<div className='flex items-center gap-2'>
										<div className='text-sm text-gray-600'>
											{groupLabel} ({count})
										</div>
										<hr className='h-0.5 flex-grow' />
									</div>

									{count === 0 ? (
										<div className='mt-5'>
											<h4>None</h4>
										</div>
									) : (
										<div className='grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4'>
											{groupedSummaryProposalRequests[
												key as keyof typeof groupedSummaryProposalRequests
											].map(
												(
													summaryProposalRequest,
													summaryProposalRequestIndex
												) => {
													const availability = summarizeAvailability({
														summaryProposalRequest,
														proposalCurrency: currency
													})
													const proposalRequest =
														eventPlan?.proposalRequests?.find(
															pr =>
																pr.venueId === summaryProposalRequest.venueId
														)
													const tooltipContent = () => (
														<div className='flex flex-col gap-1'>
															<div className='text-sm font-medium'>
																<div className='flex flex-col'>
																	{(availability.roomTypesOffered || []).map(
																		rto => (
																			<div key={rto.roomType.value}>
																				{rto.roomType.name}:{' '}
																				{formatCurrency(
																					rto.rate,
																					availability.proposalCurrency
																				)}
																			</div>
																		)
																	)}
																</div>
															</div>
														</div>
													)

													return (
														<div
															className='card max-w-96'
															key={summaryProposalRequestIndex}
															onClick={() => {
																onClickHotelCard(summaryProposalRequest)
															}}
															role='button'
															tabIndex={0}
															onKeyDown={event => {
																if (event.key === 'Enter') {
																	navigate(
																		`/planner/event/${eventId}/comparable-hotels/${summaryProposalRequest.venueId}`
																	)
																}
															}}
														>
															<div className='relative h-52 gap-2'>
																<img
																	className='h-full rounded-t-lg object-cover object-top'
																	style={{ width: '100%' }}
																	alt='hotel-image'
																	src={
																		summaryProposalRequest.venueImageUrl
																			? formatImageUrl(
																					summaryProposalRequest.venueImageUrl,
																					summaryProposalRequest.venueId
																				)
																			: '/images/hotel-coming-soon.png'
																	}
																/>
																<div className='absolute bottom-2 left-1 rounded px-1 py-1'>
																	<HSBadge
																		color={
																			MarketAvailabilityStatusMap[
																				summaryProposalRequest.status as ProposalRequestStatusKey
																			]?.color ?? 'gray'
																		}
																		size='xs'
																	>
																		{MarketAvailabilityStatusMap[
																			summaryProposalRequest.status as ProposalRequestStatusKey
																		]?.label ?? ''}
																	</HSBadge>
																</div>
																<div className='absolute bottom-2 right-1 px-1 py-1'>
																	<div className='rounded-md bg-white px-1 py-0.5 font-medium'>
																		{(availability.roomTypesOffered ?? [])
																			.length > 0 ? (
																			<TooltipComponent
																				content={tooltipContent}
																			>
																				<div className='flex items-center text-xs'>
																					<span className=''>
																						{
																							availability.roomTypesOffered?.[0]
																								.roomType.name
																						}
																						{(
																							availability.roomTypesOffered ??
																							[]
																						).length > 1
																							? `, +${(availability.roomTypesOffered ?? []).length - 1}`
																							: ''}
																						{' |'}
																					</span>
																					<span className=''>
																						{formatCurrency(
																							availability.roomTypesOffered?.[0]
																								?.rate ?? 0,
																							availability.proposalCurrency
																						)}
																					</span>
																				</div>
																			</TooltipComponent>
																		) : null}
																	</div>
																</div>
															</div>
															<div className='flex flex-col gap-2 p-3'>
																<HSTooltipWithEllipsis
																	key={summaryProposalRequest.id}
																	content={summaryProposalRequest.venueName}
																	className='max-w-72 text-sm font-semibold text-gray-700 hover:cursor-pointer hover:text-primary-700 hover:underline'
																/>
																<TooltipComponent
																	content={renderTooltipForAvailability(
																		availability
																	)}
																>
																	<div className='flex items-center gap-3'>
																		{eventPlan?.roomBlocksRequired ===
																		false ? null : (
																			<div className='flex items-center gap-4 border-r pr-3'>
																				<div className='text-xs font-normal text-gray-400'>
																					Rooms:
																				</div>
																				<div
																					className={`text-xs font-normal ${availability.roomTypesOffered ? 'text-green-600' : 'text-red-600'}`}
																				>
																					{availability.roomTypesOffered
																						? 'Available'
																						: 'Not Available'}
																				</div>
																			</div>
																		)}

																		<div className='flex items-center gap-2'>
																			<div className='text-xs font-normal text-gray-400'>
																				Event Space:
																			</div>
																			<div
																				className={`text-xs font-normal ${availability.meetingSpaceAvailable ? 'text-green-600' : 'text-red-600'}`}
																			>
																				{availability.meetingSpaceAvailable
																					? 'Available'
																					: 'Not Available'}
																			</div>
																		</div>
																	</div>
																</TooltipComponent>

																{proposalRequest?.id ? (
																	<div className='flex flex-col gap-1'>
																		<div className='text-xs font-semibold text-green-600'>
																			Added to RFP as a real Proposal Request
																		</div>
																		<div className='flex items-center gap-2'>
																			<div className='text-xs font-medium text-orange-600'>
																				{proposalRequest.status
																					? MarketAvailabilityStatusMap[
																							proposalRequest.status as ProposalRequestStatusKey
																						]?.label
																					: ''}
																			</div>
																			<TooltipComponent content="The RFP has not yet been sent to the hotel. If you don't want to send an RFP to this hotel, remove it on the Send RFP tab.">
																				<FontAwesomeIcon
																					icon={faInfoCircle}
																					className='text-orange-600'
																					size='sm'
																				/>
																			</TooltipComponent>
																		</div>
																	</div>
																) : (
																	<div className='flex items-center justify-between gap-2'>
																		{summaryProposalRequest.status ===
																		MarketAvailabilityStatusMap.ClosedLost
																			?.key ? null : (
																			<HSButton
																				outline
																				onClick={event => {
																					event.stopPropagation()
																					declineProposalRequest(
																						summaryProposalRequest
																					)
																				}}
																				className='text-sm'
																				color='gray'
																				disabled={
																					eventPlan?.status ===
																						EventPlanStatusMap.Contracting
																							?.key ||
																					eventPlan?.status ===
																						EventPlanStatusMap.Contracted
																							?.key ||
																					eventPlan?.status ===
																						EventPlanStatusMap.Cancelled?.key
																				}
																			>
																				<div className='flex items-center gap-2'>
																					<FontAwesomeIcon
																						icon={faThumbsDown}
																						size='sm'
																					/>
																					<div>Not Interested</div>
																				</div>
																			</HSButton>
																		)}
																		{summaryProposalRequest.status ===
																		MarketAvailabilityStatusMap.ClosedWon
																			?.key ? null : (
																			<HSButton
																				outline
																				onClick={event => {
																					event.stopPropagation()
																					handleAddToRfp(summaryProposalRequest)
																				}}
																				className='text-sm'
																				color='primary'
																				disabled={
																					eventPlan?.status ===
																						EventPlanStatusMap.Contracting
																							?.key ||
																					eventPlan?.status ===
																						EventPlanStatusMap.Contracted
																							?.key ||
																					eventPlan?.status ===
																						EventPlanStatusMap.Cancelled?.key
																				}
																			>
																				<div className='flex items-center gap-2'>
																					<FontAwesomeIcon
																						icon={faThumbsUp}
																						size='sm'
																					/>
																					<div>Add to RFP</div>
																				</div>
																			</HSButton>
																		)}
																	</div>
																)}
															</div>
														</div>
													)
												}
											)}
										</div>
									)}
								</div>
							)
						})}
					</div>
				</div>
			)}
		</div>
	)
}
export default PlannerHotelsAvailable
