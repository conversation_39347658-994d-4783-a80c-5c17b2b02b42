/* eslint-disable react/no-array-index-key */
import { faTrashXmark } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import HSTextField from 'components/textField'
import { Drawer } from 'flowbite-react'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { Shortlist } from 'models/proposalResponseMonitor'
import { useEffect, useState } from 'react'
import { v4 as uuid } from 'uuid'

interface INewShortlistProperties {
	showShortlistDrawer: boolean
	onClose: () => void
	manageShortlist: boolean
	onClickDeleteShortlist: (shortlist: Shortlist) => void
}

const PlannerNewShortlist = (properties: INewShortlistProperties) => {
	const {
		showShortlistDrawer,
		onClose,
		manageShortlist,
		onClickDeleteShortlist
	} = properties
	const { eventInfo: eventPlan, setProperty } = eventInfoStore()
	const { userProfile } = useUserProfileContext()
	const [shortlist, setShortlist] = useState<Shortlist>({
		hotels: {},
		name: '',
		id: '',
		owner: {
			id: '',
			firstName: '',
			lastName: ''
		},
		createdBy: '',
		created: new Date().toISOString()
	})

	useEffect(() => {
		if (showShortlistDrawer && userProfile?.id) {
			setShortlist({
				id: uuid(),
				hotels: {},
				owner: {
					id: userProfile.id,
					firstName: userProfile.firstName,
					lastName: userProfile.lastName
				},
				createdBy: userProfile.id,
				created: new Date()
			})
		}
	}, [
		userProfile?.id,
		userProfile?.firstName,
		userProfile?.lastName,
		showShortlistDrawer
	])

	const onChangeHotel = (hotel: {
		venueId: string | null
		venueName: string | null
	}) => {
		setShortlist(s => {
			const isAlreadySelected = hotel.venueId && s.hotels[hotel.venueId]

			const updatedHotels = isAlreadySelected
				? Object.fromEntries(
						Object.entries(s.hotels).filter(([key]) => key !== hotel.venueId)
					)
				: {
						...s.hotels,
						[hotel.venueId ?? '']: hotel.venueName ?? ''
					}

			return {
				...s,
				hotels: updatedHotels
			}
		})
	}

	const onClickCancel = () => {
		setShortlist({
			hotels: {},
			name: '',
			id: '',
			owner: {
				id: '',
				firstName: '',
				lastName: ''
			},
			createdBy: '',
			created: new Date().toISOString()
		})
		onClose()
	}

	const onClickCreateShortlist = () => {
		setProperty('shortlists', [
			...Object.values(eventPlan?.shortlists ?? {}),
			{ ...shortlist }
		])

		setShortlist({
			hotels: {},
			name: '',
			id: '',
			owner: {
				id: '',
				firstName: '',
				lastName: ''
			},
			createdBy: '',
			created: new Date().toISOString()
		})
		onClose()
	}

	return (
		<div>
			{manageShortlist ? (
				<HSDrawer
					onClose={() => {
						onClose()
					}}
					open={showShortlistDrawer}
					position='right'
					style={{ width: '500px', height: '100%' }}
				>
					<Drawer.Header
						title='Manage Your Shortlists'
						titleIcon={() => null}
					/>
					<Drawer.Items
						style={{ height: 'calc(100vh - 9rem)' }}
						className='overflow-auto'
					>
						<div className='flex flex-col gap-4 px-2'>
							<div className='card flex flex-col gap-2'>
								<div className='flex flex-col gap-2'>
									{Object.values(eventPlan?.shortlists || {}).map(
										(s, index) => (
											<div
												key={index}
												className='flex items-center justify-between gap-2 border-b px-4 py-2'
											>
												<div>{s.name}</div>
												<HSButton
													color='light'
													onClick={() => {
														onClickDeleteShortlist(s)
													}}
												>
													<FontAwesomeIcon icon={faTrashXmark} size='lg' />
												</HSButton>
											</div>
										)
									)}
								</div>
							</div>
						</div>
					</Drawer.Items>
					<div className='flex gap-4'>
						<HSButton
							className='flex-1'
							color='light'
							onClick={() => {
								onClose()
							}}
						>
							Cancel
						</HSButton>
					</div>
				</HSDrawer>
			) : (
				<HSDrawer
					onClose={() => {
						onClose()
					}}
					open={showShortlistDrawer}
					position='right'
					style={{ width: '500px', height: '100%' }}
				>
					<Drawer.Header title='Create New Shortlist' titleIcon={() => null} />
					<Drawer.Items
						style={{ height: 'calc(100vh - 9rem)' }}
						className='overflow-auto'
					>
						<div className='flex flex-col gap-4 px-2'>
							<div>
								<HSTextField
									label='Shortlist Name'
									placeholder='Enter a name for your shortlist'
									value={shortlist.name || ''}
									isInvalid={!shortlist.name}
									onChange={event =>
										setShortlist({ ...shortlist, name: event.target.value })
									}
								/>
							</div>
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-medium text-gray-900'>
									Select Hotels to add to this Shortlist
								</div>
								<div className='flex flex-col gap-2'>
									{eventPlan?.proposalRequests
										?.filter(
											pr =>
												!['Pending', 'New', 'Received', 'Declined'].includes(
													pr.status ?? ''
												)
										)
										.sort((c, n) =>
											(c.venueName ?? '') > (n.venueName ?? '') ? 1 : -1
										)
										.map(hotel => (
											<div key={hotel.id} className='flex items-center gap-2'>
												<input
													type='checkbox'
													checked={!!shortlist.hotels[hotel.venueId]}
													onChange={() => {
														onChangeHotel(hotel)
													}}
													className='h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500'
												/>
												<label
													htmlFor={`${hotel.id}`}
													className='text-sm font-normal text-gray-700'
												>
													{hotel.venueName}
												</label>
											</div>
										)) ?? []}
								</div>
							</div>
						</div>
					</Drawer.Items>
					<div className='flex gap-4'>
						<HSButton
							className='flex-1'
							color='light'
							onClick={() => {
								onClickCancel()
							}}
						>
							Cancel
						</HSButton>
						<HSButton
							className='flex-1'
							color='primary'
							onClick={() => {
								onClickCreateShortlist()
							}}
							disabled={
								!shortlist.name || Object.keys(shortlist.hotels).length === 0
							}
						>
							Create Shortlist
						</HSButton>
					</div>
				</HSDrawer>
			)}
		</div>
	)
}
export default PlannerNewShortlist
