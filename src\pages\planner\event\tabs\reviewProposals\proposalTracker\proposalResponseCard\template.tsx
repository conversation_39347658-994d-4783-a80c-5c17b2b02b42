/* eslint-disable react/no-array-index-key */
import { faEllipsisVertical } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSBadge from 'components/badge'
import HSButton from 'components/button'
import { format, parseISO } from 'date-fns'
import type { ProposalRequestStatusKey } from 'lib/helpers/statusMaps'
import { ProposalRequestStatusMap } from 'lib/helpers/statusMaps'
import { findSupplierContactOrTeammate } from 'lib/helpers/supplierContacts'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import type { IUserProfile } from 'models/userProfiles'
import { Link } from 'react-router-dom'

export const statusTemplate = (item: { status: string }) => (
	<div>
		<HSBadge
			color={
				ProposalRequestStatusMap[item.status as ProposalRequestStatusKey]
					?.color ?? 'gray'
			}
			className='w-40 items-center justify-center rounded-lg p-2'
		>
			{ProposalRequestStatusMap[item.status as ProposalRequestStatusKey]
				?.label ?? ''}
		</HSBadge>
	</div>
)

export const formatName = (
	name: string | null,
	createdBy: string | null,
	supplierContacts: ISupplierContact[] | null
) => {
	const suggestedBy = findSupplierContactOrTeammate(supplierContacts, createdBy)
	return (
		<div>
			<Link to='/'>
				<div className='text-sm font-medium text-primary-700 underline'>
					{name}
				</div>
			</Link>
			<div className='text-xs font-medium text-gray-400'>
				{(suggestedBy?.isHopSkip === true ? (
					<div className=''>Hopskip Comparable Hotel</div>
				) : null) ||
					(suggestedBy && (
						<div className=''>
							Suggested by {`${suggestedBy.firstName} ${suggestedBy.lastName}`}
						</div>
					))}
			</div>
		</div>
	)
}

export const formatIndustryContacts = (item: { accessToProposal: string }) => (
	<div className='flex flex-col gap-1'>
		{item.accessToProposal.split(', ').map((companyName, index) => (
			<HSBadge
				color={companyName === 'None' ? 'gray' : 'primary'}
				className='items-center justify-center rounded-md p-1'
				key={index}
			>
				{companyName}
			</HSBadge>
		))}
	</div>
)

export const formatActions = () => (
	<div className='flex gap-2'>
		<HSButton color='light' size='sm' onClick={() => {}}>
			View Proposal
		</HSButton>
		<HSButton color='text' size='lg' onClick={() => {}}>
			<FontAwesomeIcon icon={faEllipsisVertical} />
		</HSButton>
	</div>
)

export const defaultFollowUpMessage = (
	proposalRequest: ProposalRequest,
	userProfile: IUserProfile | undefined
) => `Hi${
	proposalRequest.proposalContact?.name
		? ` ${proposalRequest.proposalContact.name.split(' ')[0]}`
		: ''
}!

I’m following up with you on my request for a proposal for the ${
	proposalRequest.eventPlanName
} that I sent to your hotel on ${format(
	parseISO(proposalRequest.sent ?? ''),
	'EEEE, MMM d, yyyy'
)}.  Can you please respond as soon as possible to my request?

Best,
${userProfile?.firstName}
    
${proposalRequest.eventPlanOrganizationName}`

export const defaultBulkFollowUpMessage = (
	eventPlan: EventPlan,
	userProfile: IUserProfile | undefined
) => `Hi!

I’m following up with you on my request for a proposal for the ${
	eventPlan.name
} that is due on ${format(
	parseISO(eventPlan.responsesDueDate ?? ''),
	'EEEE, MMM d, yyyy'
)}.  Can you please respond as soon as possible to my request?

Best,
${userProfile?.firstName}
    
${eventPlan.organizationName}`
