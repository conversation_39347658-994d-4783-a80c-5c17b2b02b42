/* eslint-disable @typescript-eslint/no-unsafe-return */
import { customColumnTypes } from 'lib/common/dataGrid'
import { excelFormatStrings } from 'lib/services/report.service'
import { getHopSkipConfig } from 'lib/auth/auth.config'
import type { EventPlanStatusKey } from 'lib/helpers/statusMaps'
import {
	EventPlanStatusMap,
	ProposalRequestStatusMap
} from 'lib/helpers/statusMaps'
import type { ClipMode } from '@syncfusion/ej2-react-grids'
import type { EventPlan } from 'models/proposalResponseMonitor'
import type { IHotel } from '../openPipeline'

const hopskipConfig = getHopSkipConfig()

interface ColumnDefinition {
	name: string
	header: string
	format?: (item: EventPlan) => React.ReactNode
	exportFormat: (item: EventPlan) => string | number | null
	exportFormatString?: string
	customColumnType: string
	width?: number | undefined
	clipMode?: ClipMode
	autoFit?: boolean
}

export interface IGridData {
	id: string
	name: string
	rfpCode: string
	status: string | null
	ownerName: string
	submitted: string
	responsesDueDate: string
	responseRate: number
	selectionDate: string
	rfpValue: number
	requestsTotal: number
	requestsAwaiting: number
	requestsResponded: number
	eventPlan: {
		submitted: string | null
		status: string | null
		organizationName: string
	}
	eventPlanId: string
	venueId: string
	venueName: string
}

type OpenPipelineColumn = (
	hotelsListFilterFunction: (hotel: { status: string }) => boolean
) => ColumnDefinition[]

interface OpportunitiesRecord {
	'open-pipeline': OpenPipelineColumn
	'bid-history': ColumnDefinition[]
	'cost-savings': ColumnDefinition[]
	bookings: ColumnDefinition[]
}

const formatHotelsListExport =
	(
		item: EventPlan & {
			hotels?: IHotel[]
		}
	) =>
	(hotelsListFilterFunction: (hotel: { status: string }) => boolean) => {
		const filterHotels = (hotel: IHotel) =>
			typeof hotelsListFilterFunction === 'function'
				? hotelsListFilterFunction(hotel)
				: true
		const filteredHotels = (item.hotels || []).filter(hotel =>
			filterHotels(hotel)
		)
		return filteredHotels
			.map(hotel => `${hotel.venueName}, ${hotel.venueLocation}`)
			.join('; ')
	}

const columnsForOpenPipeline = (
	hotelsListFilterFunction: (hotel: { status: string }) => boolean
): ColumnDefinition[] => [
	{
		name: 'name',
		header: 'RFP Name',
		customColumnType: customColumnTypes.required.key,
		exportFormat: item =>
			`${hopskipConfig.clientRoot ?? hopskipConfig.api.host}/planner/event/${item.eventPlanId}/request-info/info|${item.name}`,
		exportFormatString: excelFormatStrings.hyperlink,
		clipMode: 'EllipsisWithTooltip',
		width: 350
	},
	{
		name: 'status',
		header: 'Status',
		exportFormat: item =>
			item.status
				? (EventPlanStatusMap[item.status as EventPlanStatusKey]?.label ?? '')
				: '',
		customColumnType: customColumnTypes.default.key,
		width: 150
	},
	{
		name: 'ownerName',
		header: 'Owner',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.ownerName,
		width: 250
	},
	{
		name: 'rfpValue',
		header: 'RFP Value',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.rfpValue,
		exportFormatString: excelFormatStrings.currency,
		width: 120
	},
	{
		name: 'submitted',
		header: 'Sent',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.submitted,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'responsesDueDate',
		header: 'Responses due',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.responsesDueDate,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'responseRate',
		header: 'Response rate',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => (item.responseRate ?? 0) / 100,
		exportFormatString: excelFormatStrings.percentageNoDecimals,
		width: 250
	},
	{
		name: 'selectionDate',
		header: 'Decision Due',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.selectionDate,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'requestsTotal',
		header: 'Hotels Requested',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => item.requestsTotal,
		exportFormatString: excelFormatStrings.numberNoDecimals,
		width: 150
	},
	{
		name: 'requestsAwaiting',
		header: 'Awaiting',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => item.requestsAwaiting,
		exportFormatString: excelFormatStrings.numberNoDecimals,
		width: 150
	},
	{
		name: 'requestsResponded',
		header: 'Responses',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => item.requestsResponded,
		exportFormatString: excelFormatStrings.numberNoDecimals,
		width: 150
	},
	{
		name: 'averageRoomRate',
		header: 'Avg Room Rate',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => item.averageRoomRate,
		exportFormatString: excelFormatStrings.currency
	},
	{
		name: 'hotelsList',
		header: 'Hotels Requested',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => formatHotelsListExport(item)(hotelsListFilterFunction)
	}
]

const columnsForBidHistory: ColumnDefinition[] = [
	{
		name: 'name',
		header: 'RFP Name',
		customColumnType: customColumnTypes.required.key,
		exportFormat: item =>
			`${hopskipConfig.clientRoot ?? hopskipConfig.api.host}/planner/event/${item.eventPlanId}/request-info/info|${item.name}`,
		exportFormatString: excelFormatStrings.hyperlink,
		clipMode: 'EllipsisWithTooltip',
		width: 350
	},
	{
		name: 'status',
		header: 'Status',
		exportFormat: item =>
			item.status
				? (EventPlanStatusMap[item.status as EventPlanStatusKey]?.label ?? '')
				: '',
		customColumnType: customColumnTypes.default.key,
		width: 150
	},
	{
		name: 'ownerName',
		header: 'Owner',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.ownerName,
		width: 250
	},
	{
		name: 'rfpValue',
		header: 'RFP Value',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.rfpValue,
		exportFormatString: excelFormatStrings.currency,
		width: 120
	},
	{
		name: 'submitted',
		header: 'Sent',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.submitted,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'responsesDueDate',
		header: 'Responses due',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.responsesDueDate,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'responseRate',
		header: 'Response rate',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => (item.responseRate ?? 0) / 100,
		exportFormatString: excelFormatStrings.percentageNoDecimals,
		width: 250
	},
	{
		name: 'selectionDate',
		header: 'Decision Due',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.selectionDate,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'averageRoomRate',
		header: 'AVG Room Rate',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.averageRoomRate || 0,
		exportFormatString: excelFormatStrings.currency,
		width: 200
	},
	{
		name: 'requestsTotal',
		header: 'Hotels Requested',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => item.requestsTotal,
		exportFormatString: excelFormatStrings.numberNoDecimals,
		width: 150
	},
	{
		name: 'requestsAwaiting',
		header: 'Awaiting',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => item.requestsAwaiting,
		exportFormatString: excelFormatStrings.numberNoDecimals,
		width: 150
	},
	{
		name: 'requestsResponded',
		header: 'Responses',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => item.requestsResponded,
		exportFormatString: excelFormatStrings.numberNoDecimals,
		width: 150
	},
	{
		name: 'peakRooms',
		header: 'Peak Room Nights',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.peakRooms || 0,
		exportFormatString: excelFormatStrings.numberNoDecimals,
		width: 180
	},
	{
		name: 'foodAndBeverageValue',
		header: 'F&B Meeting Space',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.foodAndBeverageValue || 0,
		exportFormatString: excelFormatStrings.currency,
		width: 200
	}
]

const columnsForCostSavings: ColumnDefinition[] = [
	{
		name: 'name',
		header: 'RFP Name',
		customColumnType: customColumnTypes.required.key,
		exportFormat: item =>
			`${hopskipConfig.clientRoot ?? hopskipConfig.api.host}/planner/event/${item.eventPlanId}/request-info/info|${item.name}`,
		exportFormatString: excelFormatStrings.hyperlink,
		clipMode: 'EllipsisWithTooltip',
		width: 350
	},
	{
		name: 'ownerName',
		header: 'Owner',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.ownerName,
		width: 250
	},
	{
		name: 'submitted',
		header: 'Sent',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.submitted,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'firstContractSigned',
		header: 'Contract',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.firstContractSigned,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'proposedValue',
		header: 'Proposed',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.proposedValue,
		exportFormatString: excelFormatStrings.currency,
		width: 150
	},
	{
		name: 'negotiated',
		header: 'Negotiated',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item =>
			item.hasContractedValue ? item.negotiatedValue : null,
		exportFormatString: excelFormatStrings.currency,
		width: 150
	},
	{
		name: 'concessionsValue',
		header: 'Concessions',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.concessionsValue,
		exportFormatString: excelFormatStrings.currency,
		width: 200
	},
	{
		name: 'savingsValue',
		header: 'Savings',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item =>
			item.hasContractedValue ? item.savingsValue : item.concessionsValue,
		exportFormatString: excelFormatStrings.currency,
		width: 150
	},
	{
		name: 'contractValue',
		header: 'Contract Value',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item =>
			item.hasContractedValue ? item.contractedValue : null,
		exportFormatString: excelFormatStrings.currency,
		width: 150
	}
]

const columnsForBookings: ColumnDefinition[] = [
	{
		name: 'name',
		header: 'RFP Name',
		customColumnType: customColumnTypes.required.key,
		exportFormat: item =>
			`${hopskipConfig.clientRoot ?? hopskipConfig.api.host}/planner/event/${item.eventPlanId}/request-info/info|${item.name}`,
		exportFormatString: excelFormatStrings.hyperlink,
		clipMode: 'EllipsisWithTooltip',
		width: 350
	},
	{
		name: 'status',
		header: 'Status',
		exportFormat: item =>
			item.status
				? (EventPlanStatusMap[item.status as EventPlanStatusKey]?.label ?? '')
				: '',
		customColumnType: customColumnTypes.default.key,
		width: 150
	},
	{
		name: 'ownerName',
		header: 'Owner',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.ownerName,
		width: 250
	},
	{
		name: 'contractValue',
		header: 'Contracted Value',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.rfpValue,
		exportFormatString: excelFormatStrings.currency,
		width: 120
	},
	{
		name: 'submitted',
		header: 'Sent',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.submitted,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'responsesDueDate',
		header: 'Responses due',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.responsesDueDate,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'responseRate',
		header: 'Response rate',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => (item.responseRate ?? 0) / 100,
		exportFormatString: excelFormatStrings.percentageNoDecimals,
		width: 250
	},
	{
		name: 'selectionDate',
		header: 'Decision Due',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.selectionDate,
		exportFormatString: excelFormatStrings.date,
		width: 150
	},
	{
		name: 'averageRoomRate',
		header: 'AVG Room Rate',
		customColumnType: customColumnTypes.default.key,
		exportFormat: item => item.averageRoomRate || 0,
		exportFormatString: excelFormatStrings.currency,
		width: 200
	},
	{
		name: 'requestsTotal',
		header: 'Hotels Requested',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => item.requestsTotal,
		exportFormatString: excelFormatStrings.numberNoDecimals,
		width: 150
	},
	{
		name: 'requestsAwaiting',
		header: 'Awaiting',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => item.requestsAwaiting,
		exportFormatString: excelFormatStrings.numberNoDecimals,
		width: 150
	},
	{
		name: 'requestsResponded',
		header: 'Responses',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: item => item.requestsResponded,
		exportFormatString: excelFormatStrings.numberNoDecimals,
		width: 150
	},
	{
		name: 'hotelsList',
		header: 'Contracted Hotels',
		customColumnType: customColumnTypes.optional.key,
		exportFormat: (item: EventPlan) => {
			const hotels = (item as EventPlan & { hotels?: IHotel[] }).hotels ?? []
			const contractedHotels = hotels.filter(
				h => h.status === ProposalRequestStatusMap.ContractSigned?.key
			)
			return contractedHotels
				.map(h => `${h.venueName}, ${h.venueLocation}`)
				.join('; ')
		},
		exportFormatString: ''
	}
]

const rawDataFieldsForPlanner: OpportunitiesRecord = {
	'open-pipeline': hotelsListFilterFunction =>
		columnsForOpenPipeline(hotelsListFilterFunction),
	'bid-history': columnsForBidHistory,
	'cost-savings': columnsForCostSavings,
	bookings: columnsForBookings
}
export default rawDataFieldsForPlanner
