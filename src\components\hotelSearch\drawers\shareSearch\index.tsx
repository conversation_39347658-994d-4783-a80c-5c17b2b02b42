/* eslint-disable react/no-array-index-key */
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import HSTextField from 'components/textField'
import { Drawer } from 'flowbite-react'
import { isEmail } from 'lib/helpers'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { useState } from 'react'
import { faTrashXmark } from '@fortawesome/pro-light-svg-icons'
import { useGetMyTeamMembers } from 'lib/services/userProfile.service'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import HSCheckbox from 'components/checkbox'
import Loader from 'components/loader'

interface ShareHotelSearchProperties {
	onClose: () => void
	siteSearches: EventPlan[]
}
const ShareHotelSearch = (properties: ShareHotelSearchProperties) => {
	const { onClose, siteSearches } = properties
	const [otherPlanners, setOtherPlanners] = useState<string[]>([''])
	const [selectedTeamMembers, setSelectedTeamMembers] = useState<string[]>([''])
	const { userProfile } = useUserProfileContext()
	const { data: teamMembers, isFetching } = useGetMyTeamMembers(
		userProfile?.id ?? ''
	)

	const emailsToShare = [
		...otherPlanners.filter(email => isEmail(email)),
		...selectedTeamMembers
	]

	const onShare = () => {
		console.log('Sharing with:', emailsToShare)
	}

	return (
		<HSDrawer
			position='right'
			style={{ width: '500px' }}
			onClose={onClose}
			open
			noPadding
		>
			<Drawer.Header
				title={
					siteSearches.length > 1
						? `Share ${siteSearches.length} Searches`
						: 'Share Saved Search'
				}
				titleIcon={() => null}
				className='px-6 pt-6'
			/>
			<Drawer.Items className='overflow-auto p-4' style={{ height: '80vh' }}>
				<div className='flex flex-col gap-6'>
					<div className='flex flex-col gap-1'>
						<div className='text-sm font-medium text-gray-900'>
							Share with team members
						</div>
						<div className='text-xs font-normal text-gray-500'>
							Team members will receive editable copies while your originals
							remain unchanged.
						</div>
					</div>
					<div className='flex flex-col gap-2'>
						<div className='text-sm font-medium text-gray-900'>
							Share with other planners (optional)
						</div>
						<div className='card'>
							{otherPlanners.map((planner, index) => (
								<div className='border-b px-4 py-2' key={index}>
									<div className='flex items-center gap-4'>
										<HSTextField
											placeholder='Enter email'
											value={planner}
											onChange={event => {
												const updatedPlanners = [...otherPlanners]
												updatedPlanners[index] = event.target.value
												setOtherPlanners(updatedPlanners)
											}}
											color={isEmail(planner) ? 'success' : 'light'}
											sizing='md'
										/>
										{isEmail(planner) ? (
											<HSButton
												color='light'
												onClick={() => {
													const updatedPlanners = [...otherPlanners]
													updatedPlanners.splice(index, 1)
													setOtherPlanners(updatedPlanners)
												}}
											>
												<FontAwesomeIcon
													icon={faTrashXmark}
													className='text-red-600'
													size='lg'
												/>
											</HSButton>
										) : null}
									</div>
								</div>
							))}
							<div className='px-4 py-2'>
								<HSButton
									color='text'
									size='sm'
									onClick={() => setOtherPlanners([...otherPlanners, ''])}
									disabled={!otherPlanners.every(email => isEmail(email))}
								>
									+ Add New
								</HSButton>
							</div>
						</div>
					</div>
					<div className='flex items-center justify-center'>
						<hr className='flex-grow border-gray-300' />
						<span className='px-2 text-sm text-gray-500'>Or</span>
						<hr className='flex-grow border-gray-300' />
					</div>
					<div className='flex flex-col gap-2'>
						<div className='text-sm font-medium text-gray-900'>
							Share with other planners (optional)
						</div>
						<div className='card'>
							<div className='rounded-t-md border-b bg-gray-50 p-2.5'>
								<div className='flex items-center gap-4'>
									<HSCheckbox
										checked={selectedTeamMembers.length === teamMembers?.length}
										onChange={event => {
											const { checked } = event.target
											if (checked) {
												setSelectedTeamMembers(
													teamMembers?.map(member => member.id ?? '') ?? []
												)
											} else {
												setSelectedTeamMembers([])
											}
										}}
									/>
									<div className='text-sm font-medium text-gray-900'>
										SELECT ALL ({teamMembers?.length ?? 0})
									</div>
								</div>
							</div>
							{isFetching ? (
								<Loader />
							) : (
								teamMembers?.map((planner, index) => (
									<div
										className={`border-b px-2 py-2.5 ${selectedTeamMembers.includes(planner.id ?? '') ? 'bg-primary-100' : ''}`}
										key={index}
									>
										<div className='flex items-center gap-4'>
											<HSCheckbox
												checked={selectedTeamMembers.includes(planner.id ?? '')}
												onChange={event => {
													const { checked } = event.target
													if (checked) {
														setSelectedTeamMembers([
															...selectedTeamMembers,
															planner.id ?? ''
														])
													} else {
														setSelectedTeamMembers(
															selectedTeamMembers.filter(
																id => id !== planner.id
															)
														)
													}
												}}
											/>
											<div className='flex flex-col'>
												<div className='text-sm font-normal text-gray-600'>
													{planner.firstName} {planner.lastName}
												</div>
												<div className='text-xs font-medium text-gray-400'>
													{planner.id}
												</div>
											</div>
										</div>
									</div>
								))
							)}
						</div>
					</div>
				</div>
			</Drawer.Items>
			<div className='mt-4 flex items-center justify-between gap-4 p-4'>
				<HSButton color='light' onClick={onClose} className='grow'>
					Cancel
				</HSButton>
				<HSButton
					className='grow'
					onClick={onShare}
					disabled={emailsToShare.length === 0}
				>
					{emailsToShare.length > 0
						? `Share with ${emailsToShare.length}
					${emailsToShare.length > 1 ? ' people' : ' person'}`
						: 'Share'}
				</HSButton>
			</div>
		</HSDrawer>
	)
}

export default ShareHotelSearch
