import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import Search from 'pages/planner/event/tabs/search'
import useSiteSearchStore from '../store'
import {
	addProposalRequestToSiteSearch,
	deleteVenueFromSiteSearch
} from 'lib/services/siteSearch.service'
import { toast } from 'react-toastify'

interface HotelSearchProperties {
	enableBackButton?: boolean
	eventPlan: EventPlan | undefined
}

const HotelSearch = (properties: HotelSearchProperties) => {
	const { enableBackButton, eventPlan } = properties
	const { setObject: setSelectedEventPlan, selectedEventPlan } =
		useSiteSearchStore()

	const updateProposalRequests = async (
		eventPlanId: string,
		proposalRequests: Partial<ProposalRequest>[],
		callbacks: ((eventPlan: EventPlan) => void)[]
	) => {
		if (selectedEventPlan?.id) {
			if (
				eventPlan?.proposalRequests?.find(
					pr => pr.venueId === proposalRequests[0].venueId
				) === undefined
			) {
				addProposalRequestToSiteSearch(eventPlanId, proposalRequests)
					.then(response => {
						const updatedEventPlan = {
							...selectedEventPlan,
							proposalRequests: [...(response.proposalRequests ?? [])],
							siteSearch: response.siteSearch || null
						}
						setSelectedEventPlan(updatedEventPlan, true)

						// Execute all callback functions with the response
						for (const callback of callbacks) callback(updatedEventPlan)
					})
					.catch((error: unknown) => {
						toast.error('Error adding proposal request to site search')
						console.error(
							'Error adding proposal request to site search:',
							error
						)
					})
			} else {
				deleteVenueFromSiteSearch(
					eventPlanId,
					proposalRequests[0].venueId ?? ''
				)
					.then(() => {
						const updatedEventPlan = {
							...selectedEventPlan,
							proposalRequests: selectedEventPlan.proposalRequests?.filter(
								pr => pr.venueId !== proposalRequests[0].venueId
							)
						}

						// Update the selected event plan
						setSelectedEventPlan(updatedEventPlan, true)

						// Execute all callback functions with the updated event plan
						for (const callback of callbacks) callback(updatedEventPlan)
					})
					.catch((error: unknown) => {
						toast.error('Error removing venue from search')
						console.error('Error removing venue from search:', error)
					})
			}
		}
	}

	const addRemoveProposalRequests = async (
		proposalRequests: Partial<ProposalRequest>[],
		callback: (ep: EventPlan) => void
	) => {
		if (selectedEventPlan?.id) {
			await updateProposalRequests(selectedEventPlan.id, proposalRequests, [
				callback,
				ep => {
					setSelectedEventPlan({ ...ep }, true)
				}
			])
		} else {
			// loadSiteSearches().then(r => {
			// 	setSiteSearches([...r.data])
			// 	setProposalRequestContext({ proposalRequests, callback })
			// 	setUseExistingList(false)
			// 	setShowAddNewList(true)
			// })
		}
	}

	return (
		<Search
			enableBackButton={enableBackButton}
			eventInfo={eventPlan}
			addRemoveProposalRequests={addRemoveProposalRequests}
		/>
	)
}

export default HotelSearch
