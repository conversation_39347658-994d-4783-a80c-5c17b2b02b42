import {
	faAngleDoubleLeft,
	faAngleDoubleRight,
	faAngleLeft,
	faAngleRight
} from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import { Button } from 'flowbite-react'

interface PaginationProperties {
	totalPages: number
	onPageChange: (page: number) => void
	currentPage: number
}

const HSPagination = (properties: PaginationProperties) => {
	const { totalPages, onPageChange, currentPage } = properties

	const handleFirstPage = () => onPageChange(1)
	const handleLastPage = () => onPageChange(totalPages)
	const handlePreviousPage = () => onPageChange(Math.max(currentPage - 1, 1))
	const handleNextPage = () =>
		onPageChange(Math.min(currentPage + 1, totalPages))
	const handlePageChange = (page: number) => {
		onPageChange(page)
	}

	return (
		<div className='flex items-center justify-between rounded-b-lg border-gray-200 px-4 py-2 dark:border-gray-700 dark:bg-gray-800'>
			<div className='flex items-center space-x-1'>
				<Button.Group className='w-full'>
					<HSButton
						size='xxs'
						color='page'
						onClick={handleFirstPage}
						disabled={currentPage === 1}
						className='rounded-none rounded-l-sm'
					>
						<FontAwesomeIcon className='h-2 w-2 p-1' icon={faAngleDoubleLeft} />
					</HSButton>
					<HSButton
						onClick={handlePreviousPage}
						disabled={currentPage === 1}
						size='xxs'
						color='page'
						className='rounded-none border-l-0'
					>
						<FontAwesomeIcon className='h-2 w-2 p-1' icon={faAngleLeft} />
					</HSButton>
					{(() => {
						const maxPagesToShow = 5
						let startPage = Math.max(
							1,
							currentPage - Math.floor(maxPagesToShow / 2)
						)
						const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1)

						if (endPage - startPage + 1 < maxPagesToShow) {
							startPage = Math.max(1, endPage - maxPagesToShow + 1)
						}

						const pageButtons = []
						for (let page = startPage; page <= endPage; page += 1) {
							pageButtons.push(
								<HSButton
									key={page}
									onClick={() => handlePageChange(page)}
									className='rounded-none border-l-0 px-2 py-1 text-sm'
									size='xxs'
									color={currentPage === page ? 'primary' : 'page'}
								>
									{page}
								</HSButton>
							)
						}
						return pageButtons
					})()}
					<HSButton
						size='xxs'
						color='page'
						onClick={handleNextPage}
						disabled={currentPage === totalPages}
						className='rounded-none border-x-0'
					>
						<FontAwesomeIcon className='h-2 w-2 p-1' icon={faAngleRight} />
					</HSButton>
					<HSButton
						size='xxs'
						color='page'
						onClick={handleLastPage}
						disabled={currentPage === totalPages}
						className='rounded-none rounded-r-sm'
					>
						<FontAwesomeIcon
							className='h-2 w-2 p-1'
							icon={faAngleDoubleRight}
						/>
					</HSButton>
				</Button.Group>
			</div>
		</div>
	)
}

export default HSPagination
