/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable no-param-reassign */

/* eslint-disable unicorn/no-array-reduce */
import { differenceInCalendarWeeks, format, parseISO } from 'date-fns'
import {
	EventPlanStatusMap,
	ProposalRequestStatusMap
} from 'lib/helpers/statusMaps'
import { useCallback, useEffect } from 'react'
import { useGetEvents } from 'lib/services/event.service'
import plannerDashboardStore from './dataStore'
import type { EventPlan } from 'models/proposalResponseMonitor'
import type { EventsWithStats } from './rfps/common'
import { calculateOpportunityValue } from 'lib/helpers/proposalValues'
import { dashboardStatuses } from './rfps/cardView/common'

const mapEvents = (event: EventPlan): EventsWithStats => {
	const proposalRequests = Object.keys(event.proposalRequestStatuses).reduce(
		(a, c) => {
			a.requestsTotal +=
				c === ProposalRequestStatusMap.Pending?.key
					? 0
					: Number(event.proposalRequestStatuses[c]?.length)
			a.requestsResponded += [
				ProposalRequestStatusMap.Pending?.key,
				ProposalRequestStatusMap.New?.key,
				ProposalRequestStatusMap.Received?.key
			].includes(c)
				? 0
				: Number(event.proposalRequestStatuses[c]?.length)
			a.requestsAwaiting = a.requestsTotal - a.requestsResponded
			return a
		},
		{ requestsResponded: 0, requestsAwaiting: 0, requestsTotal: 0 }
	)
	const owner = event.planners?.find(p => p.owner)
	return {
		...event,
		name:
			event.name ||
			`New RFP Created ${format(parseISO(event.created ?? ''), 'MMM d, yyyy')}`,
		rfpValue: calculateOpportunityValue(
			[
				EventPlanStatusMap.Contracting?.key,
				EventPlanStatusMap.Contracted?.key
			].includes(event.status ?? '')
				? event.proposalValues
				: event.proposalValuesAverage
		),
		ownerId: owner ? owner.id : null,
		ownerName: owner ? `${owner.firstName} ${owner.lastName}` : null,
		...proposalRequests,
		responseRate:
			proposalRequests.requestsTotal > 0
				? (proposalRequests.requestsResponded /
						proposalRequests.requestsTotal) *
					100
				: 0
	}
}

const usePlannerDashboardData = () => {
	const { refetch } = useGetEvents()

	const { setEvents, setIsLoading, resetStore, events, setMetrics } =
		plannerDashboardStore()

	const resetData = () => {
		resetStore()
		refetch()
			.then(response => {
				if (response.data) {
					setEvents(response.data.map(pr => mapEvents(pr)))
				}
			})
			.catch((error: unknown) => console.log(error))
	}

	const setupData = useCallback(() => {
		if (events.length >= 0) {
			const today = new Date()

			const latestMetrics = events.reduce(
				(a, c) => {
					const selectionDate = c.selectionDate
						? parseISO(c.selectionDate)
						: null
					const responsesDueDate = c.responsesDueDate
						? parseISO(c.responsesDueDate)
						: null
					if (dashboardStatuses.some(s => s.status === c.status && s.active)) {
						const overdue =
							c.status !== EventPlanStatusMap.Contracting?.key &&
							responsesDueDate !== null &&
							responsesDueDate < new Date()
						const overdueIndex = overdue ? 0 : 1

						const updatedOverdueProposalRequests = [
							...a.overdueProposalRequests
						]
						updatedOverdueProposalRequests[overdueIndex] = {
							...updatedOverdueProposalRequests[overdueIndex],
							count: updatedOverdueProposalRequests[overdueIndex].count + 1,
							rfps: [...updatedOverdueProposalRequests[overdueIndex].rfps, c]
						}
						a = {
							...a,
							overdueProposalRequests: updatedOverdueProposalRequests
						}
						a.activeProposalRequests.push(c)
						const status = a.activeStatuses.find(s => s.status === c.status)
						if (status) {
							status.count += 1
						}
						const activeStatus = a.activeStatuses.find(
							s => s.status === c.status
						)
						if (activeStatus) {
							activeStatus.value += c.rfpValue ?? 0
						}
					}
					if (
						[
							EventPlanStatusMap.Contracting?.key,
							EventPlanStatusMap.Contracted?.key
						].includes(c.status ?? '')
					) {
						const index =
							c.status === EventPlanStatusMap.Contracting?.key ? 0 : 1
						a.awardedRfps[index].count += 1
						a.awardedRfps[index].value += c.rfpValue ?? 0
					}
					if (
						[
							EventPlanStatusMap.New?.key,
							EventPlanStatusMap.Submitted?.key,
							EventPlanStatusMap.Bidding?.key
						].includes(c.status ?? '')
					) {
						a.awardedRfps[2].count += 1
						a.awardedRfps[2].value += c.rfpValue ?? 0
					}
					if (
						EventPlanStatusMap.Bidding?.key === c.status &&
						Number(
							c.proposalRequestStatuses[
								ProposalRequestStatusMap.Active?.key ?? ''
							]?.length
						) > 0
					) {
						a.unopenedRfps.push(c)
					}
					if (
						selectionDate !== null &&
						differenceInCalendarWeeks(selectionDate, today) === 0
					) {
						a.selectionsDueThisWeek.push(c)
					}
					if (
						responsesDueDate !== null &&
						differenceInCalendarWeeks(responsesDueDate, today) === 0
					) {
						a.responsesDueThisWeek.push(c)
					}
					a.totalOpportunityValue += c.rfpValue ?? 0

					return a
				},
				{
					activeProposalRequests: [],
					activeStatuses: dashboardStatuses
						.filter(s => s.active)
						.map(s => ({ ...s, count: 0, value: 0, status: s.status ?? '' })),
					overdueProposalRequests: [
						{ name: 'Overdue', count: 0, fill: '#B82B45', rfps: [] },
						{ name: 'Other', count: 0, fill: '#c0cadd', rfps: [] }
					],
					awardedRfps: [
						{
							name: ProposalRequestStatusMap.ClosedWon?.key,
							count: 0,
							value: 0,
							fill: '#95D9D0',
							label: 'Contracting'
						},
						{
							name: ProposalRequestStatusMap.ContractSigned?.key,
							count: 0,
							value: 0,
							fill: '#027587',
							label: 'Contract Signed'
						},
						{
							name: 'InProgress',
							count: 0,
							value: 0,
							fill: '#E0E0E0',
							label: 'RFP In Process'
						}
					],
					unopenedRfps: [],
					responsesDueThisWeek: [],
					selectionsDueThisWeek: [],
					totalOpportunityValue: 0
				}
			)
			setMetrics(latestMetrics)
			setIsLoading(false)
		}
	}, [events, setIsLoading, setMetrics])

	useEffect(() => {
		setIsLoading(true)
		setupData()
	}, [setIsLoading, setupData])

	return {
		resetData
	}
}

export default usePlannerDashboardData
