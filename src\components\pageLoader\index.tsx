/* eslint-disable @typescript-eslint/promise-function-async */
import Loader from 'components/loader'
import type { ReactNode } from 'react'
import { memo } from 'react'

interface PageLoaderProperties {
	children?: ReactNode
	show?: boolean
}

const PageLoader = memo(({ children, show = true }: PageLoaderProperties) =>
	show ? (
		<>
			<div className='absolute inset-0 z-50 flex flex-col items-center justify-center bg-gray-100 bg-opacity-75 dark:bg-gray-800 dark:bg-opacity-75'>
				<Loader />
			</div>
			{children}
		</>
	) : (
		children
	)
)

export default PageLoader
