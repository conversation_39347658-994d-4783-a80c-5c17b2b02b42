/* eslint-disable no-param-reassign */
/* eslint-disable unicorn/no-array-reduce */
import { faArrowLeft, faDownload } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSTable from 'components/table'
import { useGetEventDetail } from 'lib/services/eventPlans.service'
import { findHotelsInSearchIndex } from 'lib/services/hotels.service'
import { useCallback, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import type { IHotelList } from './templates'
import { renderAction, renderRooms, renderSpace } from './templates'
import HotelComparisonReport from './comparisonReport'
import type { Venue } from 'models/venue'

const HotelList = () => {
	const navigate = useNavigate()
	const { eventId } = useParams()
	const { data: eventPlan } = useGetEventDetail(eventId ?? '', !!eventId)
	const [venueMap, setVenueMap] = useState<Record<string, Venue>>({})
	const [showReportModal, setShowReportModal] = useState(false)
	// const [reportOptions, setReportOptions] = useState({
	// 	includeAll: true,
	// 	showNotes: true
	// })

	const setupVenueMap = useCallback(() => {
		const venueIds = eventPlan?.proposalRequests?.map(pr => pr.venueId) ?? []
		findHotelsInSearchIndex(venueIds)
			.then(r => {
				setVenueMap(
					r.value.reduce((a: Record<string, Venue>, c) => {
						a[c.id ?? ''] = c
						return a
					}, {})
				)
			})
			.catch((error: unknown) => console.log(error))
	}, [eventPlan?.proposalRequests])

	useEffect(() => {
		if ((eventPlan?.proposalRequests?.length ?? 0) > 0) {
			setupVenueMap()
		}
	}, [eventPlan?.proposalRequests?.length, setupVenueMap])

	const gridData = eventPlan?.proposalRequests?.map(pr => ({
		...pr,
		...venueMap[pr.venueId]
	}))

	return (
		<div className='flex flex-col'>
			<div className='flex items-center justify-between border-b p-4'>
				<div className='flex flex-col gap-1'>
					<div className='text-xl font-semibold text-gray-900'>
						Hotels Added
					</div>
					<div className='text-sm font-normal text-gray-500'>
						Added to the saved search hotels can be added to RFP in a bulk
					</div>
				</div>
				<HSButton color='light' onClick={() => navigate('/site-search')}>
					<div className='flex items-center gap-2'>
						<FontAwesomeIcon icon={faArrowLeft} />
						Back to Saved Searches
					</div>
				</HSButton>
			</div>
			<div className='flex flex-col gap-2 p-4'>
				<div className='flex items-center justify-between gap-4'>
					<div className='text-sm font-normal text-gray-900'>
						{eventPlan?.proposalRequests?.length} Hotels
					</div>
					<div className='flex items-center gap-4'>
						<HSButton
							color='light'
							onClick={() => {
								setShowReportModal(true)
							}}
						>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faDownload} />
								Download Report
							</div>
						</HSButton>
						<HSButton>Create RFP</HSButton>
					</div>
				</div>
				<HSTable
					defaultSort={{
						field: 'venueName',
						direction: 'asc'
					}}
					rows={gridData ?? []}
					columns={[
						{
							field: 'venueName',
							headerText: 'Hotel Name',
							sortable: true,
							clipMode: 'ellipsis'
						},
						{
							field: 'venueCity',
							headerText: 'City',
							width: 150,
							sortable: true,
							clipMode: 'ellipsis'
						},
						{
							field: 'venueState',
							headerText: 'State',
							width: 150,
							sortable: true,
							clipMode: 'ellipsis'
						},
						{
							field: 'guestRoomQuantity',
							headerText: 'Guest Rooms',
							width: 150,
							sortable: true,
							clipMode: 'ellipsis',
							render: (item: IHotelList) => renderRooms(item.guestRoomQuantity)
						},
						{
							field: 'meetingRoomQuantity',
							headerText: 'Meeting Rooms',
							width: 150,
							sortable: true,
							clipMode: 'ellipsis',
							render: (item: IHotelList) =>
								renderRooms(item.meetingRoomQuantity)
						},
						{
							field: 'meetingSpaceSquareFeet',
							headerText: 'Meeting Space',
							sortable: true,
							clipMode: 'ellipsis',
							width: 150,
							render: (item: IHotelList) =>
								renderSpace(item.meetingSpaceSquareFeet)
						},
						{
							field: 'largestMeetingSpaceSquareFeet',
							headerText: 'Largest Space',
							width: 150,
							sortable: true,
							clipMode: 'ellipsis',
							render: (item: IHotelList) =>
								renderSpace(item.largestMeetingSpaceSquareFeet)
						},
						{
							field: 'actions',
							headerText: 'Actions',
							width: 100,
							sortable: true,
							clipMode: 'ellipsis',
							render: (item: IHotelList) =>
								renderAction(
									item,
									() => {},
									() => {}
								)
						}
					]}
				/>
			</div>
			{showReportModal ? <HotelComparisonReport /> : null}
		</div>
	)
}

export default HotelList
