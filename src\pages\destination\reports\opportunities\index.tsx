/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable no-param-reassign */
/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/no-confusing-void-expression */
/* eslint-disable unicorn/no-array-reduce */
import { faInfoCircle, faSearch } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	PresetDirective,
	PresetsDirective
} from '@syncfusion/ej2-react-calendars'
import OpportunityComparison from './comparison'
import HSTextField from 'components/textField'
import HSIcon from 'components/HSIcon'
import DataGrid from 'components/dataGrid'
import type { GridComponent } from '@syncfusion/ej2-react-grids'
import {
	ColumnDirective,
	ColumnsDirective,
	Inject,
	Page,
	Sort
} from '@syncfusion/ej2-react-grids'
import ScaledBar from 'components/scaledBar'
import { useCallback, useEffect, useState } from 'react'
import { parseISO } from 'date-fns'
import { getProposalRequestLeadSource } from 'lib/common/proposalValues'
import { calculateGrossMarketValue } from 'lib/helpers/proposalValues'
import dateFilterStore from '../dateFilterStore'
import type {
	DestinationProposalRequest,
	INewDestination
} from 'models/destinations'
import {
	detailsTemplate,
	hotelNameTemplate,
	totalValueTemplate,
	valueTemplate
} from './template'
import { calculatePeriod, formatNumber, periodTypes } from 'lib/helpers'
import { useGetAssignedData } from 'lib/services/destinations.service'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import OpportunitySummary from './summary'
import type { HotelData } from './common/helper'
import { filterModes, filterSent } from './common/helper'
import { WarningBanner } from 'components/warningBanner'
import { Link } from 'react-router-dom'
import MetricCard from 'components/metric/card'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import HSDateRangePicker from 'components/dateRangePicker'

interface IOpportunities {
	destination: INewDestination
	proposals: DestinationProposalRequest
	isFeatureLocked: boolean
}

interface SupplierContactWithCount extends ISupplierContact {
	count: number
}

const Opportunities = (properties: IOpportunities) => {
	let gridInstance: GridComponent | null = null
	const { destination, proposals, isFeatureLocked } = properties
	// const { organizationId } = useParams()
	const { userProfile } = useUserProfileContext()
	const { dateFilter, setDateFilter } = dateFilterStore()
	const { data: assignedDataRaw } = useGetAssignedData(
		userProfile?.organizationId ?? '',
		!!userProfile?.organizationId
	)
	const [searchText, setSearchText] = useState('')

	const [hotelData, setHotelData] = useState<{
		sortCol: string
		sortDir: number
		data: HotelData[]
	}>({
		sortCol: 'venueName',
		sortDir: 1,
		data: []
	})
	const [assignedData, setAssignedData] = useState<{
		data: SupplierContactWithCount[]
	}>({ data: [] })

	const [filteredData, setFilteredData] = useState<HotelData[]>([])

	const processedProposals = useCallback(
		(startDate: Date, endDate: Date) => [
			...(proposals.received?.map(p => ({
				...p,
				type: 'received',
				opportunityValue: calculateGrossMarketValue(p.proposalValuesAverage)
			})) ?? []),
			...(filterSent(proposals.won, startDate, endDate)?.map(p => ({
				...p,
				type: 'won',
				opportunityValue: calculateGrossMarketValue(p.proposalValues)
			})) ?? []),
			...(filterSent(proposals.lost, startDate, endDate)?.map(p => ({
				...p,
				type: 'lost',
				opportunityValue: calculateGrossMarketValue(p.proposalValues)
			})) ?? []),
			...(filterSent(proposals.declined, startDate, endDate)?.map(p => ({
				...p,
				type: 'declined',
				opportunityValue: calculateGrossMarketValue(p.proposalValuesAverage)
			})) ?? [])
		],
		[proposals.declined, proposals.lost, proposals.received, proposals.won]
	)

	useEffect(() => {
		if (destination.id === filterModes.geolocation) {
			setAssignedData({ data: [] })
		} else if (assignedDataRaw) {
			const assigned: SupplierContactWithCount[] = []

			for (const currentItem of assignedDataRaw) {
				const supplierContact = currentItem.supplierContacts?.find(
					contact => contact.organizationId === destination.id
				)

				if (supplierContact) {
					const existingEntry = assigned.find(
						entry => entry.id === supplierContact.id
					)

					if (existingEntry) {
						existingEntry.count += 1
					} else {
						assigned.push({
							...supplierContact,
							count: 1
						})
					}
				}
			}
			setAssignedData({ data: assigned })
		}
	}, [assignedDataRaw, destination.id])

	useEffect(() => {
		const startDate = parseISO(dateFilter.startDate)
		const endDate = parseISO(dateFilter.endDate)

		const all = processedProposals(startDate, endDate)

		const updatedHotelData = {
			...hotelData,
			data: all
				.sort((c, n) =>
					c.submitted === null && n.submitted === null
						? 0
						: c.submitted
							? 1
							: -1
				)
				.reduce(
					(
						a: {
							venueId: string | null
							ids: string[]
							sent: number
							value: number
							bid: number
							data: unknown[]
						}[],
						c
					) => {
						const h = a.find(e => e.venueId === c.venueId)
						if (h) {
							if (!h.ids.includes(c.eventPlanId ?? '')) {
								h.sent += 1
								h.ids = [...h.ids, c.eventPlanId ?? '']
								h.value += c.opportunityValue
								h.bid += c.submitted === null ? 0 : 1
								h.data.push({
									...c,
									leadSource: getProposalRequestLeadSource({
										eventPlan: { supplierContacts: c.supplierContacts },
										proposalRequest: c
									})
								})
							}
						} else {
							a.push({
								venueId: c.venueId,
								venueName: c.venueName,
								sent: 1,
								bid: c.submitted === null ? 0 : 1,
								value: c.opportunityValue,
								ids: [c.eventPlanId ?? ''],
								data: [
									{
										...c,
										leadSource: getProposalRequestLeadSource({
											eventPlan: { supplierContacts: c.supplierContacts },
											proposalRequest: c
										})
									}
								],
								organizationId: c.organizationId
							})
						}
						return a
					},
					[]
				)
				.map(d => ({
					...d,
					bidRate: d.sent ? (d.bid / d.sent) * 100 : 0
				}))
		}
		setHotelData(updatedHotelData)
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [
		destination.id,
		dateFilter.startDate,
		dateFilter.endDate,
		proposals.received,
		proposals.won,
		proposals.lost,
		proposals.declined,
		processedProposals
	])

	useEffect(() => {
		let updatedData = hotelData.data
		if (searchText !== '') {
			updatedData = hotelData.data.filter(d =>
				d.venueName?.toLowerCase().includes(searchText.toLowerCase())
			)
		}
		setFilteredData(updatedData)
		gridInstance?.setProperties({ dataSource: updatedData })
	}, [gridInstance, hotelData.data, searchText])

	const onDateChangeFilter = (dates: Date[] | null) => {
		if (dates)
			setDateFilter({
				startDate: new Date(dates[0]).toISOString().split('.')[0],
				endDate: new Date(dates[1]).toISOString().split('.')[0]
			})
	}

	const renderGrid = () => (
		<DataGrid
			dataSource={filteredData ?? []}
			allowSorting
			allowPaging
			ref={r => {
				gridInstance = r
			}}
			sortSettings={{
				allowUnsort: true,
				columns: [
					{
						direction: 'Ascending',
						field: 'venueName'
					}
				]
			}}
		>
			<ColumnsDirective>
				<ColumnDirective
					field='venueName'
					headerText='Hotel Name'
					template={hotelNameTemplate}
					autoFit
					clipMode='EllipsisWithTooltip'
				/>
				<ColumnDirective
					field='sent'
					headerText='Sent'
					template={(item: { sent: number }) => valueTemplate(item.sent)}
					width={100}
					headerTextAlign='Center'
					textAlign='Center'
				/>
				<ColumnDirective
					field='bid'
					headerText='Bid'
					template={(item: { bid: number }) => valueTemplate(item.bid)}
					width={100}
					headerTextAlign='Center'
					textAlign='Center'
				/>
				<ColumnDirective
					field='bidRate'
					headerText='Bid Rate'
					template={(item: { bidRate: number }) =>
						valueTemplate(`${formatNumber(item.bidRate, '0')}%`)
					}
					width={100}
					headerTextAlign='Center'
					textAlign='Center'
				/>
				<ColumnDirective
					field='value'
					headerText='Total'
					template={totalValueTemplate}
					width={150}
				/>
				<ColumnDirective
					field='name'
					headerText='Details'
					template={(item: { venueId: string }) =>
						detailsTemplate(item, userProfile?.organizationId ?? '')
					}
					headerTextAlign='Center'
					textAlign='Center'
					width={150}
				/>
			</ColumnsDirective>
			<Inject services={[Page, Sort]} />
		</DataGrid>
	)

	return (
		<div>
			<div className='flex items-center justify-between border-b px-6 py-4'>
				<div className='text-lg font-semibold text-gray-900'>Opportunities</div>
				<div className='w-80'>
					<HSDateRangePicker
						placeholder='Select Date Range'
						onChange={({ value }: { value: Date[] | null }) =>
							onDateChangeFilter(value)
						}
						value={[
							new Date(dateFilter.startDate),
							new Date(dateFilter.endDate)
						]}
						format='MMM dd, yyyy'
					>
						<PresetsDirective>
							{Object.keys(periodTypes).map(period => {
								const {
									type: { label, key },
									startDate,
									endDate
								} = calculatePeriod(period)
								return (
									<PresetDirective
										key={key}
										label={label}
										start={new Date(startDate)}
										end={new Date(endDate)}
									/>
								)
							})}
						</PresetsDirective>
					</HSDateRangePicker>
				</div>
			</div>
			<div
				className='overflow-auto'
				style={{ maxHeight: 'calc(100vh - 12rem)' }}
			>
				{isFeatureLocked ? (
					<div className='p-4'>
						<WarningBanner
							variant='Warning'
							heading='Paid analytics features are locked'
							message={
								<p>
									Some analytics features are locked in the free version of
									HopSkip{' '}
									<Link
										to='/'
										className='font-semibold text-primary-700 underline'
									>
										Learn More
									</Link>
									.
								</p>
							}
							ctaPrompt='Upgrade Your Account'
							onClick={() => console.log('Upgrade Your Account')}
							hideIcon
						/>
					</div>
				) : null}

				<OpportunitySummary proposals={proposals} destination={destination} />
				<div className='px-4 py-6'>
					<OpportunityComparison
						proposals={proposals}
						isLocked={isFeatureLocked}
					/>
				</div>
				<div className='flex gap-4 px-4 py-6'>
					<div className='w-3/4'>
						<div className='card'>
							<div className='flex flex-col gap-4 p-4'>
								<div className='flex items-center justify-between'>
									<div className='flex items-center gap-2'>
										<div className='font-medium text-gray-700'>
											Opportunity sent to hotels
										</div>
										<FontAwesomeIcon
											icon={faInfoCircle}
											className='text-gray-700'
											key='opportunity-sent-to-hotels'
											id='info-opportunity-sent-to-hotels'
										/>
									</div>
									<div className='w-80'>
										<HSTextField
											showClearButton
											placeholder='Search'
											icon={HSIcon(faSearch)}
											value={searchText}
											onChange={event => setSearchText(event.target.value)}
											key='hotel-data-search'
											disabled={isFeatureLocked}
										/>
									</div>
								</div>

								<MetricCard isLocked={isFeatureLocked}>
									<div>{renderGrid()}</div>
								</MetricCard>
							</div>
						</div>
					</div>
					<div className='w-1/4'>
						<div className='card'>
							<div className='flex flex-col gap-4 p-4'>
								<div className='flex items-center gap-2'>
									<div className='font-medium text-gray-700'>
										Assigned by salesperson
									</div>
									<FontAwesomeIcon
										icon={faInfoCircle}
										className='text-gray-700'
										key='assigned-by-salesperson'
										id='info-assigned-by-salesperson'
									/>
								</div>
								<MetricCard isLocked={isFeatureLocked}>
									<ScaledBar
										data={assignedData.data.map(d => ({
											label: d.firstName ?? d.id?.split('@')[0] ?? '',
											formattedValue: formatNumber(d.count),
											value: d.count,
											color: '#EDEBFE'
										}))}
									/>
								</MetricCard>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}

export default Opportunities
