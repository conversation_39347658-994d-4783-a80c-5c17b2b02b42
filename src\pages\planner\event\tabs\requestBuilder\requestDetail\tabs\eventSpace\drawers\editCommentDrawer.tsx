import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import HSTextArea from 'components/textarea'
import { Drawer } from 'flowbite-react'
import { useState } from 'react'

interface IEditCommentDrawerProperties {
	onClose: () => void
	value?: string
	onChange?: (value: string) => void
}

const EditCommentDrawer = (properties: IEditCommentDrawerProperties) => {
	const { onClose, onChange, value } = properties
	const [comment, setComment] = useState(value ?? '')

	const onSave = () => {
		onChange?.(comment.trim())
		onClose()
	}
	return (
		<HSDrawer
			open
			onClose={onClose}
			position='right'
			style={{
				width: '400px'
			}}
		>
			<Drawer.Header title='Edit General Comment' titleIcon={() => null} />
			<Drawer.Items
				className='overflow-auto'
				style={{
					height: 'calc(100vh - 10rem)'
				}}
			>
				<HSTextArea
					rows={10}
					placeholder='Write text here...'
					value={comment}
					onChange={event => setComment(event.target.value)}
				/>
			</Drawer.Items>
			<Drawer.Items className='flex items-center justify-end gap-2'>
				<HSButton color='light' onClick={onClose} className='grow'>
					Cancel
				</HSButton>
				<HSButton
					color='primary'
					onClick={onSave}
					disabled={!comment.trim() || comment === value}
					className='grow'
				>
					Save
				</HSButton>
			</Drawer.Items>
		</HSDrawer>
	)
}

export default EditCommentDrawer
