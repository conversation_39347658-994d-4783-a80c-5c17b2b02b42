/* eslint-disable unicorn/no-array-reduce */
import HSButton from 'components/button'
import HSDonutChart from 'components/charts/donutChart'
import HSLegend from 'components/charts/legends'
import HSDrawer from 'components/drawer'
import HSTable from 'components/table'
import { But<PERSON>, Drawer } from 'flowbite-react'
import { ProposalRequestStatusMap } from 'lib/helpers/statusMaps'
import {
	findSupplierContactOrTeammate,
	supplierContactRoles
} from 'lib/helpers/supplierContacts'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import type { ProposalRequest } from 'models/proposalResponseMonitor'
import { useEffect, useState } from 'react'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import HSProgress from 'components/progressBar'
import { formatNumber } from 'lib/helpers'

interface IResponseStatusDetails {
	showDetails: boolean
	setDetails: React.Dispatch<React.SetStateAction<boolean>>
	chartData: {
		dataKey: string
		fill: string
		value: number
	}[]
	filterTrackedProposalRequests: (
		proposalRequest: ProposalRequest,
		isAdmin: boolean
	) => boolean
}

interface IResponseRateRow {
	key: string
	sort: string
	name: string
	type: string
	desc: string
	total: number
	responded: number
	fullName: string
}

export const typeFilters = {
	industryContacts: 'industryContacts',
	destination: 'dmo'
}

const ResponseStatusDetails = (properties: IResponseStatusDetails) => {
	const { showDetails, setDetails, chartData, filterTrackedProposalRequests } =
		properties
	const { eventInfo: eventPlan } = eventInfoStore()
	const { userProfile } = useUserProfileContext()
	const [responseRateData, setResponseRateData] = useState<IResponseRateRow[]>(
		[]
	)
	const [typeFilter, setTypeFilter] = useState(typeFilters.industryContacts)

	useEffect(() => {
		const proposalRequests = eventPlan?.proposalRequests?.filter(
			pr =>
				userProfile && filterTrackedProposalRequests(pr, userProfile.isAdmin)
		)
		const proposalRequestsNotRemoved = proposalRequests?.filter(
			pr => pr.status !== ProposalRequestStatusMap.Removed?.key
		)
		const responseRateRows: IResponseRateRow[] = (
			eventPlan?.supplierContacts ?? []
		)
			.filter(sc => sc.requestSuggestions)
			.reduce(
				(a: IResponseRateRow[], c: ISupplierContact) => {
					let key = ''
					let name = ''
					let sort = ''
					let type = ''
					let fullName = ''

					switch (c.role) {
						case supplierContactRoles.soa?.key:
						case supplierContactRoles.dmo?.key: {
							key = c.organizationId ?? ''
							name = c.companyName ?? ''
							sort = `${
								c.role === supplierContactRoles.dmo?.key ? 'AA' : 'ZZ'
							}-${name}`
							type = c.role
							fullName = `${c.firstName ?? ''} ${c.lastName ?? ''}`
							break
						}
						default: {
							key = supplierContactRoles.nso?.key ?? ''
							name = 'NSOs'
							sort = 'LL'
							type = supplierContactRoles.nso?.key ?? ''

							break
						}
					}

					const row = a.find(r => r.key === key)
					if (!row) {
						a.push({
							key,
							sort,
							name,
							type,
							fullName,
							desc: c.requestSuggestions
								? 'Suggesting Hotels'
								: 'Supporting Hotel Portfolio',
							total: 0,
							responded: 0
						})
					}
					return a
				},
				[
					{
						key: 'planner',
						sort: '00',
						type: 'planner',
						name: eventPlan?.organizationName,
						desc: 'Hotels handpicked',
						total: 0,
						responded: 0
					}
				]
			)

		const filterProposalRequestType = (
			pr: ProposalRequest,
			row: IResponseRateRow
		) => {
			const sc = findSupplierContactOrTeammate(
				eventPlan?.supplierContacts ?? [],
				pr.createdBy
			)
			if (sc) {
				if (sc.isHopSkip) return row.type === 'hopskip'
				switch (sc.role) {
					case supplierContactRoles.soa?.key:
					case supplierContactRoles.dmo?.key: {
						return row.type === sc.role && row.key === sc.organizationId
					}
					default: {
						return (
							row.type === supplierContactRoles.nso?.key &&
							row.key === supplierContactRoles.nso.key
						)
					}
				}
			}
			return row.type === 'planner'
		}

		setResponseRateData(
			responseRateRows.map((row: IResponseRateRow) => {
				const filteredProposals =
					proposalRequestsNotRemoved?.filter(pr =>
						filterProposalRequestType(pr, row)
					) ?? []

				const { total, responded } = filteredProposals.reduce(
					(accumulator, current) => {
						accumulator.total += 1
						accumulator.responded += [
							ProposalRequestStatusMap.New?.key,
							ProposalRequestStatusMap.Received?.key
						].includes(current.status ?? '')
							? 0
							: 1
						return accumulator
					},
					{ total: 0, responded: 0 }
				)

				return {
					...row,
					total,
					responded
				}
			})
		)
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [
		eventPlan?.organizationName,
		eventPlan?.proposalRequests,
		eventPlan?.supplierContacts,
		userProfile
	])

	const companyHeaderTemplate = () => (
		<div>
			{typeFilter === typeFilters.industryContacts ? 'Company' : 'Destination'}
		</div>
	)

	return (
		<div>
			<HSDrawer
				position='right'
				onClose={() => {
					setDetails(false)
				}}
				open={showDetails}
				style={{ width: '650px' }}
			>
				<Drawer.Header titleIcon={() => null} title='Response Status' />
				<Drawer.Items
					style={{ height: 'calc(100vh - 10rem)' }}
					className='overflow-auto'
				>
					<div className='flex flex-col gap-8 px-4'>
						<div className='card flex items-center'>
							<div className='flex items-center gap-4 border-r p-4'>
								<HSDonutChart
									data={chartData.map(item => ({
										name: item.dataKey,
										value: item.value,
										color: item.fill
									}))}
									label={`${chartData.reduce((accumulator, item) => accumulator + item.value, 0)}`}
									size={150}
									labelSize='text-3xl'
									showTooltip
								/>
								<div className='flex flex-col gap-1'>
									<HSLegend
										data={chartData.map(item => ({
											label: `${item.dataKey} (${item.value})`,
											color: item.fill
										}))}
									/>
								</div>
							</div>
							<div className='flex flex-col gap-4 p-4'>
								<div className='flex items-center gap-2'>
									<div className='text-2xl font-semibold leading-none text-gray-900'>
										40%
									</div>
									<div className='text-xs font-medium text-gray-500'>
										Response Rate
									</div>
								</div>
								<div className='flex items-center gap-2'>
									<div className='text-2xl font-semibold leading-none text-gray-900'>
										5%
									</div>
									<div className='text-xs font-medium text-gray-500'>
										Bid Rate
									</div>
								</div>
								<div className='border-b' />
								<div className='flex items-center gap-2'>
									<div className='text-2xl font-semibold leading-none text-gray-900'>
										10
									</div>
									<div className='text-xs font-medium text-gray-500'>
										Proposals Submitted
									</div>
								</div>
							</div>
						</div>
						<div className='card flex h-fit flex-col'>
							<div className='flex items-center justify-between gap-3 p-4'>
								<div className='text-base font-medium text-gray-700'>
									Detailed breakdown
								</div>
								<div className='flex items-center gap-4'>
									<div className='text-sm font-medium text-gray-900'>
										View by
									</div>
									<div className='text-gray-900'>
										<Button.Group className='w-fit'>
											<HSButton
												color='gray'
												className={`rounded-r-none ${
													typeFilter === typeFilters.industryContacts
														? 'bg-gray-200'
														: ''
												}`}
												onClick={() => {
													setTypeFilter(typeFilters.industryContacts)
												}}
											>
												Industry Contacts
											</HSButton>
											<HSButton
												color='gray'
												className={`rounded-l-none ${typeFilter === typeFilters.destination ? 'bg-gray-200' : ''}`}
												onClick={() => {
													setTypeFilter(typeFilters.destination)
												}}
											>
												Destination
											</HSButton>
										</Button.Group>
									</div>
								</div>
							</div>
							<div className='overflow-auto'>
								<div className='disabled-rounded-border border-t'>
									<HSTable
										rows={
											typeFilter === typeFilters.industryContacts
												? responseRateData
														.sort((c, n) => (c.sort > n.sort ? 1 : -1))
														.map(row => ({
															name: row.name,
															description: row.desc,
															total: row.total,
															responseRate: row.responded
																? (Number(row.responded) / row.total) * 100
																: 0,
															fullName: row.fullName
														}))
												: responseRateData
														.sort((c, n) => (c.sort > n.sort ? 1 : -1))
														.filter((r: IResponseRateRow) => r.type === 'dmo')
														.map(response => ({
															name: response.name,
															description: response.desc,
															total: response.total,
															responseRate: response.responded
																? (Number(response.responded) /
																		response.total) *
																	100
																: 0,
															fullName: response.fullName
														}))
										}
										allowPaging={false}
										columns={[
											{
												field: 'name',
												headerText: 'Company',
												width: 200,
												sortable: true,
												headerTemplate: companyHeaderTemplate,
												render: item => (
													<div>
														{typeFilter === typeFilters.industryContacts
															? item.name
															: item.fullName}
													</div>
												)
											},

											{
												field: 'responseRate',
												headerText: 'Response',
												width: 150,
												sortable: true,
												render: item => (
													<div className='flex flex-col gap-1'>
														<div className='flex items-center justify-between'>
															<div className='text-sm font-medium text-gray-600'>
																{item.responseRate.toFixed(0)}%
															</div>
															<div className='text-xs font-normal text-gray-500'>
																{`${formatNumber(item.total)} hotels`}
															</div>
														</div>
														<HSProgress
															progress={item.responseRate}
															size='sm'
															color='primary'
														/>
													</div>
												)
											}
										]}
									/>
								</div>
							</div>
						</div>
					</div>
				</Drawer.Items>
			</HSDrawer>
		</div>
	)
}

export default ResponseStatusDetails
