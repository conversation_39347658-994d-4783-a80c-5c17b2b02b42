/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable react/no-array-index-key */
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { useEffect, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import RFPReport from './tabs/reports'
import CompetitiveInsight from './tabs/competitiveInsight'
import Messaging from './tabs/messaging'
import SubmitProposal from './tabs/submitProposal'
import Summary from './tabs/summary'
import ProposalBuilder from './tabs/builder'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCircleExclamation } from '@fortawesome/pro-duotone-svg-icons'
import RfpSummary from '../summaryProposal/rfpSummary'
import HSTooltip from 'components/tooltip'
import { clientNavigate } from 'components/sidebar'

interface IChapterTab {
	chapterName: string
	chapterIcon?: string
	path: string
	visible?: boolean
	items: {
		itemName: string
		itemIcon?: string
		component?: React.ReactNode
		path: string
		error?: string[] | null
	}[]
}

export const getHotelierRfpTabs = ({
	proposal
}: {
	proposal: string[] | null | undefined
	submit: string[] | null | undefined
}) => {
	const result: IChapterTab[] = [
		{
			chapterName: 'Create Proposal',
			path: 'create-proposal',
			visible: true,
			items: [
				{
					itemName: 'RFP Summary',
					path: 'summary',
					component: <Summary />
				},
				{
					itemName: 'Proposal Builder',
					itemIcon: 'builder',
					path: 'builder',
					component: <ProposalBuilder />,
					error: proposal
				}
			]
		},
		{
			chapterName: 'Review & Submit',
			path: 'review',
			visible: true,
			items: [
				{
					itemName: 'Review Proposal',
					path: 'submit',
					component: <SubmitProposal />
				}
			]
		},
		{
			chapterName: 'Track Proposal',
			path: 'track-proposal',
			visible: true,
			items: [
				{
					itemName: 'Competitive Insights',
					path: 'insights',
					component: <CompetitiveInsight />
				}
			]
		},
		{
			chapterName: 'Follow Up',
			path: 'follow-up',
			visible: true,
			items: [
				{
					itemName: 'Message Planner',
					path: 'message-planner',
					component: <Messaging />
				}
			]
		},
		{
			chapterName: 'Reports',
			path: 'reports',
			visible: true,
			items: [
				{
					itemName: 'Reports',
					path: 'report',
					component: <RFPReport />
				}
			]
		}
	]
	return result
}

export const getHotelierSummaryRfpTabs = ({
	proposal,
	submit
}: {
	proposal: string[] | null | undefined
	submit: string[] | null | undefined
}) => {
	const result: IChapterTab[] = [
		{
			chapterName: 'Availability Response',
			path: 'availability-response',
			visible: true,
			items: [
				{
					itemName: 'RFP Summary',
					path: 'summary',
					component: <RfpSummary isSummary />
				},
				{
					itemName: 'Response Form',
					itemIcon: 'builder',
					path: 'response-form',
					component: <ProposalBuilder />,
					error: proposal
				}
			]
		},
		{
			chapterName: 'Review & Submit Response',
			path: 'review',
			visible: true,
			items: [
				{
					itemName: 'Review & Submit',
					path: 'submit',
					component: <SubmitProposal isSummary />,
					error: submit
				}
			]
		}
		// {
		// 	chapterName: 'Track Proposal',
		// 	path: 'track-proposal',
		// 	visible: true,
		// 	items: [
		// 		{
		// 			itemName: 'Competitive Insights',
		// 			path: 'insights',
		// 			component: <CompetitiveInsight isSummary />
		// 		}
		// 	]
		// }
	]
	return result
}

export interface IRFPProperties {
	chapterTabs: IChapterTab[]
}

const HotelierRfpTabs = (properties: IRFPProperties) => {
	const { chapterTabs } = properties
	const { eventId, chapterName, infoType, venueId } = useParams()
	const navigate = useNavigate()
	const [activeTab, setActiveTab] = useState<{ parent: number; child: number }>(
		{ parent: 0, child: 0 }
	)
	const { userProfile } = useUserProfileContext()
	const location = useLocation()

	useEffect(() => {
		const parent = chapterTabs.findIndex(
			tab => tab.path === chapterName?.toLowerCase()
		)
		const child = chapterTabs[parent]?.items.findIndex(
			item => item.path === infoType?.toLowerCase()
		)
		if (parent !== -1 && child !== -1) setActiveTab({ parent, child })
		// else navigate('/page-not-found')
	}, [chapterName, chapterTabs, infoType, navigate])

	const handleClick = (
		index: number,
		childIndex: number,
		tab: IChapterTab,
		item: {
			itemName: string
			itemIcon?: string
			component?: React.ReactNode
			path: string
			error?: string[] | null
		}
	) => {
		setActiveTab({ parent: index, child: childIndex })
		navigate(
			`/${userProfile?.role?.toLowerCase()}/proposal/${eventId}/${venueId}/${tab.path}/${item.path}`
		)
		clientNavigate(userProfile?.id ?? '', location)
	}

	return (
		<div className='flex gap-4'>
			{chapterName === 'new-profile' ? null : (
				<div
					className='flex h-fit w-60 flex-col overflow-y-auto rounded-lg border bg-white p-4'
					style={{ maxHeight: `calc(100vh - 12rem)` }}
				>
					<div className='flex flex-col gap-2'>
						{chapterTabs
							.filter(c => c.visible)
							.map((tab, index) => (
								<div className='flex flex-col gap-3' key={index}>
									<div className='text-sm font-medium text-gray-900'>
										{tab.chapterName}
									</div>
									<div className='flex flex-col gap-2'>
										{tab.items.map((item, childIndex) => (
											<div
												className={`h-max-[60vh] flex items-start justify-start rounded-lg px-4 py-2 text-sm font-medium first:ml-0 focus:outline-none disabled:cursor-not-allowed disabled:text-gray-400 disabled:dark:text-gray-500 ${activeTab.child === childIndex && activeTab.parent === index ? 'border bg-gray-100 text-primary-700 ring-1 ring-primary-700 dark:bg-gray-800 dark:text-primary-500' : 'border border-transparent text-gray-500 hover:bg-gray-50 hover:text-gray-600 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300'}`}
												key={childIndex}
												role='button'
												onClick={() => {
													handleClick(index, childIndex, tab, item)
												}}
												onKeyDown={event => {
													if (event.key === 'Enter') {
														handleClick(index, childIndex, tab, item)
													}
												}}
												tabIndex={0}
											>
												<div className='flex items-center justify-between'>
													<div className='whitespace-nowrap'>
														{item.itemName}
													</div>
													{item.error && item.error.length > 0 ? (
														<HSTooltip
															content={
																<div className='flex flex-col gap-1'>
																	{item.error.map(erorMessage => (
																		<div
																			className='text-left text-sm'
																			key={erorMessage}
																		>
																			{erorMessage}
																		</div>
																	))}
																</div>
															}
															placement='right'
														>
															<FontAwesomeIcon
																className='ml-2 text-red-400'
																icon={faCircleExclamation}
															/>
														</HSTooltip>
													) : null}
												</div>
											</div>
										))}
									</div>
								</div>
							))}
					</div>
				</div>
			)}
			<div
				className='h-fit w-full overflow-y-auto rounded-lg border bg-white'
				style={{ maxHeight: `calc(100vh - 12rem)` }}
			>
				{chapterTabs[activeTab.parent]?.items[activeTab.child]?.component}
			</div>
		</div>
	)
}

export default HotelierRfpTabs
