import { screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import UserAvatar from './index'
import { useMsal } from '@azure/msal-react'
import type { IUserProfile } from 'models/userProfiles'
import type { IPublicClientApplication } from '@azure/msal-browser'

import { Logger } from '@azure/msal-browser'
import renderWithProviders from 'testUtils'
import type { UserProfileContextValue } from 'lib/providers/common'
import { useLoggedInUserProfileContext } from 'lib/contexts/loggedInUserProfile.context'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'

const mockUserProfile = {
	id: 'mock-id',
	firstName: 'Test',
	lastName: 'Automation',
	email: '<EMAIL>'
} as IUserProfile

// Mock the useMsal hook
vi.mock('@azure/msal-react')
const useMsalMock = vi.mocked(useMsal)

// Mock the useUserProfileContext hook
vi.mock('lib/contexts/loggedInUserProfile.context')
const useLoggedInUserProfileContextMock = vi.mocked(
	useLoggedInUserProfileContext
)

vi.mock('lib/contexts/userProfile.context')
const useUserProfileContextMock = vi.mocked(useUserProfileContext)

const mockLogoutRedirectSuccess = vi.fn(
	async (): Promise<void> =>
		new Promise(resolve => {
			resolve()
		})
)

const mockLogoutRedirectFailure = vi.fn(
	async (): Promise<void> =>
		new Promise((_, reject) => {
			reject(new Error('Logout failed due to network issue'))
		})
)

const mockInstance = {
	logoutRedirect: mockLogoutRedirectSuccess
} as unknown as IPublicClientApplication

describe('UserAvatar', () => {
	const mockUserProfileContextValue = {
		userProfile: mockUserProfile
	} as UserProfileContextValue

	useMsalMock.mockReturnValue({
		instance: mockInstance,
		inProgress: 'none',
		accounts: [],
		logger: new Logger({})
	})
	useUserProfileContextMock.mockReturnValue(mockUserProfileContextValue)
	useLoggedInUserProfileContextMock.mockReturnValue(mockUserProfileContextValue)

	it('renders the user initials', () => {
		renderWithProviders(<UserAvatar />)
		const initials = screen.getByText('TA')
		expect(initials).toBeInTheDocument()
	})

	it('renders the dropdown items', () => {
		renderWithProviders(<UserAvatar />)
		fireEvent.click(screen.getByRole('button'))

		expect(screen.getByText('Test Automation')).toBeInTheDocument()
		expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
		expect(screen.getByText('My Profile')).toBeInTheDocument()
		// expect(screen.getByText('My Organization')).toBeInTheDocument()
		expect(screen.getByText('Subscriptions and Billing')).toBeInTheDocument()
		expect(screen.getByText('Log out')).toBeInTheDocument()
	})

	it('on User info click', async () => {
		renderWithProviders(<UserAvatar />)
		// Click Avatar to open drop down
		fireEvent.click(screen.getByRole('button'))
		fireEvent.click(screen.getByText('Test Automation'))

		// TODO: Implement the assertion after the Development
	})

	it('on My Profile click', async () => {
		renderWithProviders(<UserAvatar />)
		// Click Avatar to open drop down
		fireEvent.click(screen.getByRole('button'))
		fireEvent.click(screen.getByText('My Profile'))

		// TODO: Implement the assertion after the Development
	})

	it.skip('on My Organization click', async () => {
		renderWithProviders(<UserAvatar />)
		// Click Avatar to open drop down
		fireEvent.click(screen.getByRole('button'))
		fireEvent.click(screen.getByText('My Organization'))

		// TODO: Implement the assertion after the Development
	})

	it('on Subscriptions and Billing click', async () => {
		renderWithProviders(<UserAvatar />)
		// Click Avatar to open drop down
		fireEvent.click(screen.getByRole('button'))
		fireEvent.click(screen.getByText('Subscriptions and Billing'))

		// TODO: Implement the assertion after the Development
	})

	it('on logout success', async () => {
		renderWithProviders(<UserAvatar />)
		// Click Avatar to open drop down
		fireEvent.click(screen.getByRole('button'))
		fireEvent.click(screen.getByText('Log out'))

		expect(mockLogoutRedirectSuccess).toHaveBeenCalledTimes(1)
	})

	it('on logout failure', async () => {
		useMsalMock.mockReturnValue({
			instance: {
				...mockInstance,
				logoutRedirect: mockLogoutRedirectFailure
			},
			inProgress: 'none',
			accounts: [],
			logger: new Logger({})
		})
		renderWithProviders(<UserAvatar />)
		// Click Avatar to open drop down
		fireEvent.click(screen.getByRole('button'))
		fireEvent.click(screen.getByText('Log out'))

		expect(mockLogoutRedirectFailure).toHaveBeenCalledTimes(1)
	})
})
