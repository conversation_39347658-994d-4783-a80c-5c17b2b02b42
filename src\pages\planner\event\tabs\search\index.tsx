/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable unicorn/no-nested-ternary */
import {
	faFilter,
	faArrowUpWideShort,
	faArrowLeft,
	faCheck
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import GoogleMapsV2, { defaultCenter } from 'components/googleMapV2'
import HSToggleSwitch from 'components/toggleSwitch'
import { useGetAffiliateOrganizations } from 'lib/services/affiliateOrganizations.service'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import {
	GeoCodeDistanceCalculator,
	milesToKilometers,
	sortModes,
	sortOptions,
	viewModes
} from './common'
import { useLocation, useNavigate } from 'react-router-dom'
import type { ChoiceFilters, RangeFilters } from './store'
import { useSearchParametersStore } from './store'
import { getSiteSearchResults } from 'lib/services/siteSearch.service'
import LocationSelector from 'components/locationSelector'
import type { ILocation } from 'models/location'
import type { Venue } from 'models/venue'
import HSDropdownButton from 'components/dropdown'
import analytics from 'lib/analytics/segment/load'
import FilterDrawer from 'components/hotelSearch/drawers/filter'
import SearchMap from './searchMap'
import HotelDetail from 'components/hotel/detail'
import PageLoader from 'components/pageLoader'
import HSBadge from 'components/badge'
import hotelDetailStore from 'components/hotel/detail/store'
import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import VerticalHotelList from './hotelList'
import { getHopSkipConfig } from 'lib/auth/auth.config'
import { appInsights } from 'lib/auth/appInsights'
import HotelInfoMarker from './markerInfo'
import AddHotelToSearch from 'components/hotelSearch/drawers/addToSearch'

const geocodeDistanceCalculator = new GeoCodeDistanceCalculator()

interface SearchProperties {
	enableBackButton?: boolean
	eventInfo?: EventPlan
	disableFields?: boolean
	applyPromotion?: (venueId: string, promotionId: string) => void
	addRemoveProposalRequests?: (
		suggestHotels: ProposalRequest[],
		callback: () => void,
		isAdmin: boolean
	) => Promise<void>
}

const Search = memo((properties: SearchProperties) => {
	const {
		enableBackButton = false,
		eventInfo,
		disableFields,
		addRemoveProposalRequests
	} = properties
	const { setGoBackUrl, setShowAvailability, clearStore } = hotelDetailStore()
	const locationHook = useLocation()

	const { data: affiliateOrganizations = [] } = useGetAffiliateOrganizations({
		filter: '',
		top: '100',
		skip: '0',
		orderBy: { field: 'name', direction: 1 }
	})
	const [searchResults, setSearchResults] = useState<Venue[] | null>([])
	const navigate = useNavigate()
	const [viewMode, setViewMode] = useState(viewModes.grid)
	const [searchResultsCount, setSearchResultsCount] = useState(0)

	const [paidAffiliates, setPaidAffiliates] = useState<
		{
			id: string | null
			name: string | null
			filterId: string
			logoImageUrl: string | null
		}[]
	>([])
	const [showFilterDrawer, setShowFilterDrawer] = useState<boolean>(false)
	const [selectedVenueId, setSelectedVenueId] = useState<string>()
	const [isLoading, setIsLoading] = useState(false)
	const [selectedVenue, setSelectedVenue] = useState<Venue | null>(null)
	const [searchId, setSearchId] = useState<string | null>(null)
	const [showSiteSearchDrawer, setShowSiteSearchDrawer] = useState(false)

	console.log(paidAffiliates)

	useEffect(() => {
		setGoBackUrl(locationHook.pathname)
		setShowAvailability(false)
		return () => {
			clearStore()
		}
	}, [clearStore, locationHook.pathname, setGoBackUrl, setShowAvailability])

	const {
		location,
		setLocation,
		setSort,
		setPageIndex,
		setIncludePrevious,
		sort,
		pageIndex,
		radiusMiles,
		pageSize,
		locationBounds,
		rangeFilters,
		choiceFilters,
		airports,
		rankedOnly,
		addedVenueIds,
		comparableDestination,
		setRankedOnly,
		includePrevious,
		addVenueId,
		setSearching,
		searching,
		setLocationBounds
	} = useSearchParametersStore()

	useEffect(() => {
		if (eventInfo?.proposalRequests)
			for (const pr of eventInfo.proposalRequests) {
				addVenueId(pr.venueId)
			}
	}, [addVenueId, eventInfo, eventInfo?.proposalRequests])

	// Optimize: Only add new venue IDs to avoid unnecessary state updates
	useEffect(() => {
		if (eventInfo?.proposalRequests) {
			const newVenueIds = eventInfo.proposalRequests
				.map(pr => pr.venueId)
				.filter(vid => !addedVenueIds.includes(vid))
			for (const vid of newVenueIds) {
				addVenueId(vid)
			}
		}
	}, [addVenueId, eventInfo, eventInfo?.proposalRequests, addedVenueIds])

	useEffect(() => {
		const isSearching =
			(eventInfo?.siteSearch?.alternateLocations.length ?? 0) > 0

		setSearching(isSearching)
	}, [eventInfo?.siteSearch?.alternateLocations.length, setSearching])

	useEffect(() => {
		if (
			eventInfo &&
			(eventInfo.siteSearch?.alternateLocations.length ?? 0) > 0
		) {
			setLocation({
				latitude: Number(eventInfo.siteSearch?.alternateLocations[0].latitude),
				longitude: Number(
					eventInfo.siteSearch?.alternateLocations[0].longitude
				),
				name: eventInfo.siteSearch?.alternateLocations[0].name ?? ''
			})
		}
	}, [eventInfo, setLocation])

	const getSearchResults = useCallback(
		(viewMode_: string) => {
			setIsLoading(true)
			let pagingQ = `&$skip=${(pageIndex || 0) * pageSize}&$top=${pageSize}`
			const filters = []

			let searchQ = ''
			let sortQ = ''
			let geoDistance = ''

			if (viewMode_ === viewModes.map && locationBounds.length > 0) {
				const polygon = locationBounds.map(b => `${b.lng} ${b.lat}`).join(', ')
				filters.push(
					`geo.intersects(geolocation,geography'POLYGON((${polygon}))')`
				)
				pagingQ = ''
			} else if (comparableDestination.id) {
				const polygon = comparableDestination.geopolygon?.coordinates[0]
					.map(c => `${c[0]} ${c[1]}`)
					.join(', ')
				filters.push(
					`geo.intersects(geolocation,geography'POLYGON((${polygon}))')`
				)
			} else if (location.latitude && location.longitude) {
				geoDistance = `geo.distance(geolocation,geography'POINT(${location.longitude.toFixed(4)} ${location.latitude.toFixed(4)})')`
				filters.push(`${geoDistance} le ${milesToKilometers(radiusMiles)}`)
			} else if (location.name) {
				searchQ = `&search=${encodeURIComponent(location.name)}`
			}

			if (sort) {
				const sortParts = sort.split('-')
				if (sortParts[0] === 'distance') {
					sortQ = geoDistance
						? `&$orderby=${geoDistance}`
						: `&$orderby=search.score() desc`
				} else {
					sortQ = `&$orderby=${sortParts[0]} ${sortParts[1]}`
				}
			}

			filters.push('isDeleted ne true')

			for (const key of Object.keys(rangeFilters)) {
				const [min, max] = rangeFilters[key as keyof RangeFilters]
				if (min !== null && max !== null) {
					filters.push(`${key} ge ${min} and ${key} le ${max}`)
				} else if (min !== null) {
					filters.push(`${key} ge ${min}`)
				} else if (max !== null) {
					filters.push(`${key} le ${max}`)
				}
			}

			for (const key of Object.keys(choiceFilters)) {
				const searchProperty = key === 'propertySellerId' ? '' : key
				const values = choiceFilters[key as keyof ChoiceFilters]
				const { length } = values

				if (length > 0) {
					let filter = ''
					if (key === 'propertySellerId') {
						filter = `propertySellers/any(p: search.in(p/id, '${values.join(' ')}'))`
					} else if (key === 'hotelTypes') {
						filter = `hotelTypes/any(t: t eq '${values.join(`' or t eq '`)}')`
					} else {
						const hasIndependent = values.includes('INDEPENDENT')
						if (hasIndependent && length === 1) {
							filter = `${searchProperty} eq null`
						} else if (hasIndependent) {
							const nonIndependentValues = values.filter(
								c => c !== 'INDEPENDENT'
							)
							filter = `(search.in(${searchProperty}, '${nonIndependentValues.join(' ')}') or ${key} eq null)`
						} else {
							filter = `search.in(${searchProperty}, '${values.join(' ')}')`
						}
					}
					filters.push(filter)
				}
			}

			if (airports.filter) {
				filters.push(
					`airports/any(a: a/distanceMiles lt ${airports.distanceMiles} and search.in(a/type, '${airports.types.join(' ')}'))`
				)
			}

			if (rankedOnly) {
				filters.push('targetRanking ne null')
			}

			let filterQ = `&$filter=(${filters.join(' and ')})`

			if (viewMode_ === viewModes.map && addedVenueIds.length > 0) {
				filterQ += ` or search.in(id, '${addedVenueIds.join(' ')}')`
			}

			getSiteSearchResults(searchQ, filterQ, pagingQ, sortQ)
				.then(r => {
					const searchIdHeaderValue = r.headers['x-ms-azs-searchid'] as string
					const resultsCount = Number(r.data?.['@odata.count'])

					setSearchId(searchIdHeaderValue)

					analytics.track(
						`Searched for Hotels from ${eventInfo?.itemType === 'eventPlan' ? 'RFP' : 'Pre-RFP'}`,
						{
							SearchServiceName: getHopSkipConfig().search.serviceName,
							SearchId: searchIdHeaderValue,
							IndexName: 'venue-index',
							QueryTerms: location.name,
							Filters: {
								...choiceFilters,
								...rangeFilters
							},
							Sort: sort,
							ResultCount: resultsCount
						}
					)

					appInsights.trackEvent(
						{ name: 'Search' },
						{
							SearchServiceName: getHopSkipConfig().search.serviceName,
							SearchId: searchIdHeaderValue,
							IndexName: 'venue-index',
							QueryTerms: location.name,
							Filters: {
								...choiceFilters,
								...rangeFilters
							},
							Sort: sort,
							ResultCount: resultsCount
						}
					)
					if (rankedOnly && resultsCount === 0) {
						setRankedOnly(false)
					} else {
						setSearchResultsCount(resultsCount)
						if (includePrevious) {
							const updatedSearchResults = [
								...(searchResults ?? []),
								...(r.data?.value ?? []).map(
									(d: Venue) =>
										({
											...d,
											inRfp: addedVenueIds.includes(d.id ?? ''),
											distanceInFeet:
												location.locationType === 'ROOFTOP'
													? geocodeDistanceCalculator.calculateDistance(
															location.latitude ?? 0,
															location.longitude ?? 0,
															d.latitude ?? 0,
															d.longitude ?? 0,
															'ft'
														)
													: null
										}) as unknown as Venue
								)
							]
							setSearchResults(updatedSearchResults)
						} else {
							const updatedSearchResults =
								r.data?.value.map(
									d =>
										({
											...d,
											inRfp: addedVenueIds.includes(d.id ?? ''),
											distanceInFeet:
												location.locationType === 'ROOFTOP'
													? geocodeDistanceCalculator.calculateDistance(
															location.latitude ?? 0,
															location.longitude ?? 0,
															d.latitude ?? 0,
															d.longitude ?? 0,
															'ft'
														)
													: null
										}) as unknown as Venue
								) ?? []
							setSearchResults(updatedSearchResults)
						}
						// setCircleHotels(s => ({
						// 	...s,
						// 	selectedHotels: r.data.value
						// 		.filter(
						// 			d => searchParameters.addedVenueIds.includes(d.id) === false
						// 		)
						// 		.map(d => d.id)
						// }))
					}
				})
				.catch((error: unknown) => {
					console.error('Error fetching site search results:', error)
				})
				.finally(() => {
					setIsLoading(false)
				})
		},
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[
			pageIndex,
			pageSize,
			locationBounds,
			comparableDestination.id,
			comparableDestination.geopolygon?.coordinates,
			location.latitude,
			location.longitude,
			location.name,
			sort,
			airports.filter,
			airports.distanceMiles,
			airports.types,
			rankedOnly,
			addedVenueIds,
			radiusMiles,
			rangeFilters,
			choiceFilters,
			eventInfo?.itemType,
			includePrevious
		]
	)

	useEffect(() => {
		const updatedAffiliateOrganization = affiliateOrganizations
			.filter(
				affiliateOrganization =>
					affiliateOrganization.subscriptionInfos.length > 0
			)
			.map(a => ({
				id: a.id,
				name: a.name,
				filterId: 'propertySellerId',
				logoImageUrl: a.logoImageUrl
			}))
		setPaidAffiliates(updatedAffiliateOrganization)
	}, [affiliateOrganizations])

	useEffect(() => {
		if (searching) {
			getSearchResults(viewMode)
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [getSearchResults, viewMode])

	const onSortChanged = useCallback(
		(sort_: string) => {
			console.log('sort', sort_)
			setPageIndex(0)
			setSort(sort_)
			setIncludePrevious(false)
			analytics.track('Search Sort Changed', {
				sort: sort_
			})
		},
		[setIncludePrevious, setPageIndex, setSort]
	)

	const showFilters = () => {
		setShowFilterDrawer(true)
	}

	const hideFilterDrawer = () => {
		setShowFilterDrawer(false)
	}

	const getDefaultCenter = useMemo(() => {
		if (location.latitude) {
			return {
				lat: location.latitude,
				lng: location.longitude
			}
		}
		return defaultCenter
	}, [location.latitude, location.longitude])

	// Optimize: Memoize filterCount to avoid recalculating on every render
	const filterCount = useMemo(() => {
		let count = 0
		for (const key of Object.keys(rangeFilters)) {
			const [min, max] = rangeFilters[key as keyof RangeFilters]
			if (min !== null || max !== null) {
				count += 1
			}
		}

		for (const key of Object.keys(choiceFilters)) {
			const values = choiceFilters[key as keyof ChoiceFilters]
			if (values.length > 0) {
				count += 1
			}
		}

		if (airports.filter) {
			count += 1
		}

		return count
	}, [rangeFilters, choiceFilters, airports.filter])

	const memoizedAddRemoveProposalRequests = useCallback(
		async (
			pr: Partial<ProposalRequest>[],
			callback: () => void,
			isAdmin: boolean
		) => {
			await addRemoveProposalRequests?.(
				pr as ProposalRequest[],
				callback,
				isAdmin
			)
		},
		[addRemoveProposalRequests]
	)

	const handleAdd = async (
		pr: Partial<ProposalRequest>[],
		callback: () => void,
		isAdmin: boolean
	) => {
		if (eventInfo?.id) {
			await memoizedAddRemoveProposalRequests(pr, callback, isAdmin)
		} else {
			setSelectedVenue(
				searchResults?.find(venue => venue.id === pr[0].venueId) ?? null
			)
			setShowSiteSearchDrawer(true)
		}
	}

	const onViewModeChanged = (view: string) => {
		if (viewMode !== viewModes.map && view === viewModes.map) {
			setSearching(true)
		}
		setViewMode(view)
		// if (hoverHotel) {
		// 	setHoverHotel(null)
		// }
		analytics.track('Search View Mode Changed', {
			viewMode: view
		})
	}

	const sortOptionsList = useMemo(
		() =>
			sortOptions.map((item, index) => ({
				id: index.toString(),
				item: (
					<div className='flex items-center gap-4'>
						<div>{item.label}</div>
						{item.value === sort ? (
							<div>
								<FontAwesomeIcon icon={faCheck} className='text-primary-600' />
							</div>
						) : null}
					</div>
				),
				cssName: item.value === sort ? 'bg-primary-50' : '',
				clickFunction: () => {
					onSortChanged(item.value)
				}
			})),
		[onSortChanged, sort]
	)

	const onLocationChanged = (updatedLocation: ILocation) => {
		setLocation(updatedLocation)
		setPageIndex(0)
		setSort(location.locationType === 'ROOFTOP' ? sortModes.distance : sort)
		setLocationBounds([])
		setSearching(true)
		setIncludePrevious(false)
		analytics.track('Search Location Changed', {
			location
		})
	}

	// Move setSelectedVenue callback for HotelInfoMarker out of inline JSX and memoize
	const handleMarkerSelectVenue = useCallback(
		(sv: Venue | null) => {
			if (sv) {
				setSelectedVenue(sv)
			}
		},
		[setSelectedVenue]
	)

	// Optimize: Memoize markerComponent for GoogleMapsV2
	const markerComponent = useMemo(
		() =>
			searchResults?.map(venue => {
				const isAdded = eventInfo?.proposalRequests?.some(
					pr => pr.venueId === venue.id
				)
				return (
					<div key={venue.id} title={venue.name ?? ''}>
						<HotelInfoMarker
							venue={venue}
							setSelectedVenue={handleMarkerSelectVenue}
							isSelected={selectedVenue?.id === venue.id}
							isAdded={!!isAdded}
							eventInfo={eventInfo}
							memoizedAddRemoveProposalRequests={
								memoizedAddRemoveProposalRequests
							}
							onClickDetails={setSelectedVenueId}
							setShowSiteSearchDrawer={setShowSiteSearchDrawer}
						/>
					</div>
				)
			}) ?? [],
		[
			searchResults,
			eventInfo,
			selectedVenue?.id,
			handleMarkerSelectVenue,
			memoizedAddRemoveProposalRequests
		]
	)

	// Memoize setSelectedVenueId and setSelectedVenue to avoid unnecessary re-renders
	const memoizedSetSelectedVenueId = useCallback(
		(id: string) => setSelectedVenueId(id),
		[setSelectedVenueId]
	)
	const memoizedSetSelectedVenue = useCallback(
		(venue: Venue | null) => setSelectedVenue(venue),
		[setSelectedVenue]
	)

	return (
		<>
			<PageLoader show={isLoading}>
				<div className='flex flex-col'>
					<div className='flex items-center justify-between gap-6 border-b px-6 py-4'>
						<div className='text-xl font-semibold text-gray-900'>
							Add Hotels
						</div>
						{enableBackButton ? (
							<HSButton onClick={() => navigate('/site-search')} color='light'>
								<div className='flex items-center gap-2'>
									<FontAwesomeIcon icon={faArrowLeft} />
									Back to Saved Hotel Searches
								</div>
							</HSButton>
						) : null}
					</div>
					<div className='border-b px-6 py-4'>
						<div className='flex items-center justify-between gap-4'>
							<div className='w-80'>
								<LocationSelector
									value={location as ILocation}
									onChange={updatedLocation =>
										onLocationChanged(updatedLocation)
									}
								/>
							</div>

							<div className='flex items-center gap-4'>
								<div className='relative inline-block'>
									<HSButton color='light' onClick={showFilters}>
										<div className='flex items-center gap-2'>
											<FontAwesomeIcon icon={faFilter} />
											Filters
										</div>
									</HSButton>
									{filterCount > 0 ? (
										<HSBadge
											color='failure'
											title={`${filterCount} Filter/s Applied`}
											className='absolute right-0 top-0 z-10 -translate-y-1/2 translate-x-1/2 transform rounded-full bg-red-600'
										>
											<div className='text-md flex items-center justify-center p-1 font-semibold text-white'>
												{filterCount}
											</div>
										</HSBadge>
									) : null}
								</div>
								<HSDropdownButton
									items={sortOptionsList}
									label={
										<div className='flex items-center gap-2'>
											<FontAwesomeIcon icon={faArrowUpWideShort} />
											Sort Results
										</div>
									}
									color='light'
									// value={supplierContact?.role ?? ''}
									size='sm'
									// disabled={!isEmailVerified}
									arrowClassName='hidden'
								/>
								<div className='flex items-center gap-2'>
									<HSToggleSwitch
										checked={viewMode === viewModes.map}
										onChange={checked => {
											if (checked) onViewModeChanged(viewModes.map)
											else onViewModeChanged(viewModes.grid)
										}}
									/>
									<div className='whitespace-nowrap text-sm font-normal text-gray-900'>
										Full map experience
									</div>
								</div>
							</div>
						</div>
					</div>{' '}
					<div className='flex' style={{ height: '65vh', overflow: 'hidden' }}>
						{selectedVenueId ? (
							<div className='overflow-auto'>
								<HotelDetail
									venueId={selectedVenueId}
									onBackClick={() => {
										setSelectedVenueId(undefined)
									}}
									showCompHotelAddToRfp
									eventPlan={eventInfo}
								/>
							</div>
						) : viewMode === viewModes.map ? (
							<SearchMap
								searchResults={searchResults}
								eventId={eventInfo?.id ?? ''}
							/>
						) : (
							<>
								<div
									className={`${viewMode === viewModes.grid ? 'w-3/5' : 'w-1/4'} overflow-hidden`}
								>
									<VerticalHotelList
										searchResults={searchResults ?? []}
										eventInfo={eventInfo}
										setSelectedVenueId={memoizedSetSelectedVenueId}
										pageIndex={pageIndex}
										setPageIndex={setPageIndex}
										pageSize={pageSize}
										disableFields={disableFields}
										handleAdd={handleAdd}
										searchResultsCount={searchResultsCount}
										searchId={searchId}
										selectedVenue={selectedVenue}
										setSelectedVenue={memoizedSetSelectedVenue}
									/>
								</div>

								<div
									className={`${viewMode === viewModes.grid ? 'w-2/5' : 'w-3/4'} overflow-hidden`}
								>
									<GoogleMapsV2
										defaultCenter={{
											lat: getDefaultCenter.lat,
											lng: getDefaultCenter.lng ?? 0
										}}
										markerComponent={markerComponent}
									/>
								</div>
							</>
						)}
					</div>
				</div>
			</PageLoader>
			{showSiteSearchDrawer && selectedVenue ? (
				<AddHotelToSearch
					onClose={() => {
						setShowSiteSearchDrawer(false)
						setSelectedVenue(null)
					}}
					venueToAdd={{ ...selectedVenue }}
				/>
			) : null}

			{showFilterDrawer ? (
				<FilterDrawer
					hideFilterDrawer={hideFilterDrawer}
					showFilterDrawer={showFilterDrawer}
				/>
			) : null}
		</>
	)
})

export default Search
