import { render, screen } from '@testing-library/react'
import { Accordion } from 'flowbite-react'
import HSAccordion from '.'

describe('HSAccordion Component', () => {
	beforeEach(() => {
		render(
			<HSAccordion>
				<Accordion.Panel isOpen>
					<Accordion.Title>Profile Name</Accordion.Title>
					<Accordion.Content>
						<div className='flex flex-col gap-2'>Test User</div>
					</Accordion.Content>
				</Accordion.Panel>
				<Accordion.Panel>
					<Accordion.Title>Profile Email</Accordion.Title>
					<Accordion.Content>
						<div className='flex flex-col gap-2'><EMAIL></div>
					</Accordion.Content>
				</Accordion.Panel>
			</HSAccordion>
		)
	})

	it('renders children correctly', () => {
		expect(screen.getByText('Profile Name')).toBeInTheDocument()
		expect(screen.getByText('Profile Email')).toBeInTheDocument()
	})

	test('applies custom theme correctly', async () => {
		const accordionDiv = await screen.findByTestId('flowbite-accordion')

		expect(accordionDiv).toHaveClass(
			'divide-y divide-gray-200 border-gray-200 dark:divide-gray-700 dark:border-gray-700'
		)
	})
})
