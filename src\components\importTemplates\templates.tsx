/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable import/prefer-default-export */
import {
	faFileCirclePlus,
	faShareNodes,
	faTrashXmark
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSTooltip from 'components/tooltip'
import { format, startOfWeek, addDays } from 'date-fns'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { Link } from 'react-router-dom'

export const renderPattern = (item: EventPlan) =>
	item.totalDays && item.startDayOfWeek != null ? (
		<div className='flex items-center gap-2'>
			<div className='text-sm font-normal text-gray-600'>{`${item.totalDays} days/${item.totalDays - 1} nights`}</div>

			<div className='text-sm font-normal text-gray-400'>
				{`(${format(addDays(startOfWeek(new Date()), item.startDayOfWeek), 'EEE')} - ${format(addDays(startOfWeek(new Date()), item.startDayOfWeek + item.totalDays - 1), 'EEE')})`}
			</div>
		</div>
	) : null

export const renderRequest = (event: EventPlan) => {
	const meetingSpaceRequestRequired = (event.meetingSpaceRequests ?? []).length

	if (event.roomBlocksRequired && meetingSpaceRequestRequired) {
		return (
			<div className='flex flex-col gap-1'>
				<div>GuestRooms and Meeting Space</div>
			</div>
		)
	}
	if (event.roomBlocksRequired && !meetingSpaceRequestRequired) {
		return <div>GuestRooms</div>
	}
	if (meetingSpaceRequestRequired && !event.roomBlocksRequired) {
		return <div>Meeting Space</div>
	}
	return null
}

export const renderActions = (
	item: EventPlan,
	handleDelete: (item: EventPlan) => void,
	handleShare: (item: EventPlan) => void,
	handleCreate: (item: EventPlan) => void
) => (
	<div className='flex items-center gap-2'>
		<HSTooltip content='Create RFP using this template'>
			<HSButton size='xs' color='light' onClick={() => handleCreate(item)}>
				<FontAwesomeIcon icon={faFileCirclePlus} size='lg' />
			</HSButton>
		</HSTooltip>
		<HSTooltip content='Share'>
			<HSButton
				size='xs'
				color='light'
				onClick={() => {
					handleShare(item)
				}}
			>
				<FontAwesomeIcon icon={faShareNodes} size='lg' />
			</HSButton>
		</HSTooltip>
		<HSTooltip content='Delete'>
			<HSButton
				size='xs'
				color='light'
				onClick={() => {
					handleDelete(item)
				}}
			>
				<FontAwesomeIcon
					icon={faTrashXmark}
					size='lg'
					className='text-red-500'
				/>
			</HSButton>
		</HSTooltip>
	</div>
)

export const renderName = (item: EventPlan) => (
	<Link to={`/planner/event/${item.id}`}>
		<div className='text-sm font-medium text-primary-700 underline'>
			{item.name}
		</div>
	</Link>
)
