import { useEffect, useState } from 'react'
import { DialogComponent } from '@syncfusion/ej2-react-popups'
import LocationSelector from 'components/locationSelector'
import type { ILocation } from 'models/location'
import useHotelStore from 'lib/store/hotelStore'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import HSTextField from 'components/textField'
import GoogleMapsV2 from 'components/googleMapV2'
import HSPhoneNumber from 'components/phoneNumber'

const HotelLocation = () => {
	const { venue: hotel, setProperty, mergeProperties } = useHotelStore()
	const { userProfile } = useUserProfileContext()
	const { isAdmin } = userProfile ?? { isAdmin: false }

	const [showLocationSelector, setShowLocationSelector] = useState(false)
	const [selectedLocation, setSelectedLocation] = useState<Partial<ILocation>>()

	const onPropertyChange = (
		event: { target: { value: string; name: string } },
		name?: string
	) => {
		const v = event.target.value === 'Choose...' ? null : event.target.value
		// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
		setProperty(event.target.name ?? name, v)
	}

	const onLocationChange = (location: ILocation) => {
		if (location.addressComponents) {
			const addressComponents = location.addressComponents.country
				? {
						address: location.addressComponents.route
							? `${location.addressComponents.streetNumber ? `${location.addressComponents.streetNumber} ` : ''}${location.addressComponents.route}`
							: null,
						city: location.addressComponents.city ?? null,
						state: location.addressComponents.state ?? null,
						country: location.addressComponents.country,
						zip: location.addressComponents.zip ?? null
					}
				: {}
			mergeProperties({
				name:
					(!hotel.name || hotel.name.startsWith('New Hotel Profile Created')) &&
					location.placeName
						? location.placeName
						: hotel.name,
				latitude: location.latitude,
				longitude: location.longitude,
				...addressComponents
			})
		}
	}

	const onCancel = () => {
		setShowLocationSelector(false)
	}

	useEffect(() => {
		setSelectedLocation({
			name: hotel.address ?? ''
		})
	}, [hotel.address])

	return (
		<>
			<div className='border-b px-6 py-4 text-xl font-semibold'>Location</div>
			<div
				className='grid grid-cols-5 gap-4 overflow-auto p-4'
				style={{ maxHeight: 'calc(100vh - 10rem)' }}
			>
				<div className='card col-span-2 py-4'>
					<div className='mb-2 border-b px-4 pb-4 text-sm text-gray-500'>
						{isAdmin ? (
							<>Edit location details</>
						) : (
							<>
								Please{' '}
								<span className='font-semibold text-primary-600 underline'>
									contact us
								</span>{' '}
								to change your hotel&apos;s name or address
							</>
						)}
					</div>
					<div className='flex flex-col gap-4 px-4'>
						<HSTextField
							type='text'
							placeholder='Name'
							name='name'
							value={hotel.name ?? ''}
							onChange={onPropertyChange}
							disabled={!isAdmin}
							label='Name'
						/>

						{isAdmin ? (
							selectedLocation ? (
								<LocationSelector
									customPlaceholder='Lookup your address...'
									value={selectedLocation as ILocation}
									onChange={onLocationChange}
								/>
							) : null
						) : (
							<HSTextField
								type='text'
								placeholder='Address'
								name='address'
								value={hotel.address ?? ''}
								onChange={onPropertyChange}
								disabled={!isAdmin}
								label='Address'
							/>
						)}
						<HSTextField
							type='text'
							placeholder='Address 2'
							name='address2'
							value={hotel.address2 ?? ''}
							onChange={onPropertyChange}
							disabled={!isAdmin}
							label='Address Line 2'
						/>
						<div className='grid grid-cols-3 items-center gap-4'>
							<div className='col-span-2'>
								<HSTextField
									type='text'
									placeholder='City'
									name='city'
									value={hotel.city ?? ''}
									onChange={onPropertyChange}
									disabled={!isAdmin}
									label='City'
								/>
							</div>

							<HSTextField
								type='text'
								placeholder='State'
								name='state'
								value={hotel.state ?? ''}
								onChange={onPropertyChange}
								disabled={!isAdmin}
								label='State'
							/>
						</div>
						<div className='grid grid-cols-3 items-center gap-4'>
							<div className='col-span-2'>
								<HSTextField
									type='text'
									placeholder='Postal Code'
									name='zip'
									value={hotel.zip ?? ''}
									onChange={onPropertyChange}
									disabled={!isAdmin}
									label='Postal Code'
								/>
							</div>

							<HSTextField
								type='text'
								placeholder='Country'
								name='country'
								value={hotel.country ?? ''}
								onChange={onPropertyChange}
								disabled={!isAdmin}
								label='Country'
							/>
						</div>
						<HSPhoneNumber
							placeholder='Phone'
							name='phone'
							value={hotel.phone ?? ''}
							onChange={(phone, meta) =>
								onPropertyChange(
									{
										target: {
											value: meta.inputValue,
											name: 'phone'
										}
									},
									'phone'
								)
							}
							label='Phone'
						/>
						<HSPhoneNumber
							placeholder='Fax'
							name='fax'
							value={hotel.fax ?? ''}
							onChange={(phone, meta) =>
								onPropertyChange(
									{
										target: {
											value: meta.inputValue,
											name: 'fax'
										}
									},
									'fax'
								)
							}
							label='Fax'
						/>
					</div>
				</div>
				<div className='card col-span-3 p-4'>
					<GoogleMapsV2
						defaultCenter={{
							lat: hotel.latitude ?? 0,
							lng: hotel.longitude ?? 0
						}}
						markerSet={[
							{ lat: hotel.latitude ?? 0, lng: hotel.longitude ?? 0 }
						]}
					/>
				</div>
			</div>
			{showLocationSelector ? (
				<DialogComponent
					id='modalDialog'
					visible={showLocationSelector}
					showCloseIcon
					onBlur={onCancel}
					// eslint-disable-next-line react/jsx-handler-names
					close={onCancel}
					header='Location is required so that we can determine the latitude and longitude for location searches.'
					width='800px'
					cssClass='modal-body'
					target='#modal-dialog'
					isModal
				>
					<div className='grid grid-cols-5'>
						<div className='font-semibold'>Location</div>
						<div className='col-span-4 flex-1'>
							{selectedLocation ? (
								<LocationSelector
									value={selectedLocation as ILocation}
									onChange={onLocationChange}
									customPlaceholder='Select a place...'
								/>
							) : null}
							<div />
						</div>
					</div>
				</DialogComponent>
			) : null}
		</>
	)
}

export default HotelLocation
