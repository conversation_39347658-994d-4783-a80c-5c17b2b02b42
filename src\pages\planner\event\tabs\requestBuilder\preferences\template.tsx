import { faTrashXmark } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSCheckbox from 'components/checkbox'
import type { PreferredLocation } from 'models/reporting'

export const renderLocation = (item: PreferredLocation) => (
	<div className='flex flex-col gap-1'>
		<div className='text-sm font-normal text-gray-600'>{item.name}</div>
		{item.addedFromSearch ? (
			<div className='text-xs font-medium text-gray-400'>
				Added from hotel search
			</div>
		) : null}
	</div>
)

export const renderPreferredLocation = (
	item: PreferredLocation,
	handleClick: (item: PreferredLocation) => void
) => (
	<div>
		<HSCheckbox
			checked={item.preferred}
			name='preferredLocation'
			onChange={() => {
				handleClick(item)
			}}
		/>
	</div>
)

export const renderRemoveLocation = (
	item: PreferredLocation,
	removeLocation: (item: PreferredLocation) => void
) => (
	<HSButton color='light' onClick={() => removeLocation(item)} size='xs'>
		<FontAwesomeIcon icon={faTrashXmark} className='text-red-600' size='lg' />
	</HSButton>
)
