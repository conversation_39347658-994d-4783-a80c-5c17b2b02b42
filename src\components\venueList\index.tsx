/* eslint-disable unicorn/no-nested-ternary */
import { faPlus } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSCheckbox from 'components/checkbox'
import HSDrawer from 'components/drawer'
import Loader from 'components/loader'
import CreateVenueListDrawer from 'components/venueListModal/createVenueListDrawer'
import { Drawer } from 'flowbite-react'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import {
	mapVenueToAssociatedObject,
	updateVenueList,
	useGetVenueList
} from 'lib/services/organizations.service'
import type { IVenueList } from 'models/organizations'
import type { Venue } from 'models/venue'
import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import { toast } from 'react-toastify'

interface AddViewVenueListsProperties {
	onClose: () => void
	venue: Venue
}

const AddViewVenueLists = (properties: AddViewVenueListsProperties) => {
	const { onClose, venue } = properties
	const { userProfile } = useUserProfileContext()
	const [currentLists, setCurrentLists] = useState<IVenueList[]>([])
	const [venueLists, setVenueLists] = useState<IVenueList[]>([])
	const { isFetching, refetch: reloadVenueList } = useGetVenueList(
		userProfile?.organizationId ?? '',
		false
	)
	const [showAddNew, setShowAddNew] = useState(false)

	useEffect(() => {
		reloadVenueList()
			.then(response => {
				setVenueLists(response.data ?? [])
			})
			.catch((error: unknown) => console.error(error))
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	useEffect(() => {
		setCurrentLists(
			typeof venueLists === 'object' && Array.isArray(venueLists)
				? venueLists.filter(list => list.venues?.some(v => v.id === venue.id))
				: []
		)
	}, [venueLists, venue.id])

	const addVenueToList = (list: IVenueList) => {
		toast.info(`Adding to list: ${list.name}`)
		updateVenueList(userProfile?.organizationId ?? '', list.id ?? '', {
			...list,
			venues: [
				...(list.venues ?? []),
				mapVenueToAssociatedObject(venue, {
					added: new Date().toISOString(),
					addedBy: userProfile?.id
				})
			]
		})
			.then(r => {
				toast.success(`Added to list: ${list.name}`)
				setVenueLists([...venueLists.filter(l => l.id !== list.id), r])
			})
			.catch((error: unknown) => console.error(error))
	}

	const removeVenueFromList = (list: IVenueList) => {
		toast.error(`Removing from list: ${list.name}`)
		updateVenueList(userProfile?.organizationId ?? '', list.id ?? '', {
			...list,
			venues: list.venues?.filter(v => v.id !== venue.id)
		})
			.then(r => {
				setVenueLists([...venueLists.filter(l => l.id !== list.id), r])
				toast.error(`Removed from list: ${list.name}`)
			})
			.catch((error: unknown) => console.error(error))
	}

	return (
		<>
			<HSDrawer
				open
				onClose={onClose}
				style={{
					width: '30rem'
				}}
				position='right'
				noPadding
			>
				<Drawer.Header
					title='Your Collection'
					titleIcon={() => null}
					className='px-4 py-6'
				/>
				<Drawer.Items
					className='flex flex-col gap-4 overflow-auto'
					style={{
						minHeight: 'calc(100vh - 12rem)'
					}}
				>
					{isFetching ? (
						<Loader />
					) : venueLists.length > 0 ? (
						<div className='flex flex-col gap-2'>
							<div className='flex flex-wrap items-center gap-1 border-b p-4 text-sm font-normal text-gray-600'>
								<span>View all of your collections in the</span>
								<Link
									to={`/planner/toolbox/venues/${userProfile?.organizationId}/collections`}
								>
									Collections section
								</Link>
								<span>of your planner toolbox.</span>
							</div>
							<div className='p-4'>
								<div className='card'>
									{venueLists
										.sort((c, n) => ((c.name ?? '') > (n.name ?? '') ? 1 : -1))
										.map(list => (
											<div
												key={list.id}
												className='flex items-center gap-3 border-b px-4 py-2'
											>
												<HSCheckbox
													checked={currentLists.some(l => l.id === list.id)}
													onChange={event => {
														if (event.target.checked) {
															addVenueToList(list)
														} else {
															removeVenueFromList(list)
														}
													}}
												/>
												<div className='text-sm font-normal text-gray-800'>
													{list.name}
												</div>
											</div>
										))}
									<div className='flex items-center justify-center px-4 py-2'>
										<HSButton
											color='text'
											onClick={() => {
												setShowAddNew(true)
											}}
										>
											<div className='flex items-center gap-2'>
												<FontAwesomeIcon icon={faPlus} />
												<div className='font-medium'>Add new</div>
											</div>
										</HSButton>
									</div>
								</div>
							</div>
						</div>
					) : (
						<div className='text-sm font-normal text-gray-600'>
							You haven&apos;t created any lists
						</div>
					)}
				</Drawer.Items>
			</HSDrawer>
			{showAddNew ? (
				<CreateVenueListDrawer
					onClose={() => {
						reloadVenueList()
							.then(() => setShowAddNew(false))
							.catch((error: unknown) => console.error(error))
							.finally(() => onClose())
					}}
					venueToAdd={venue}
				/>
			) : null}
		</>
	)
}

export default AddViewVenueLists
