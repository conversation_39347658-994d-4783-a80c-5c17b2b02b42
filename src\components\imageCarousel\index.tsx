/* eslint-disable react/no-array-index-key */
import HSModal from 'components/modal'
import { Carousel, Modal } from 'flowbite-react'

interface ImageCarouselProperties {
	images: string[]
	onClose: () => void
	title?: string
}

const ImageCarousel = (properties: ImageCarouselProperties) => {
	const { images, onClose, title } = properties
	return (
		<HSModal openModal onClose={onClose} title={title}>
			<Modal.Body>
				<Carousel>
					{images.map((image, index) => (
						<div
							key={`image-carousel-${index}`}
							className='flex h-full items-center justify-center'
						>
							<img
								src={image}
								alt={`Item ${index + 1}`}
								className='h-full w-full object-cover'
							/>
						</div>
					))}
				</Carousel>
			</Modal.Body>
		</HSModal>
	)
}

export default ImageCarousel
