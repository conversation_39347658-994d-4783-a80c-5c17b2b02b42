/* eslint-disable unicorn/no-nested-ternary */
import HSButton from 'components/button'
import HSTable from 'components/table'
import { Button } from 'flowbite-react'
import type { ICurrency } from 'lib/helpers'
import { formatCurrency } from 'lib/helpers'
import { useState, useMemo } from 'react'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons'

const responseFilters = {
	valuesByChain: 'valuesByChain',
	valuesByBrand: 'valuesByBrand'
}

interface RfpResponseGridProperties {
	metrics: {
		valuesByChain: {
			count: number
			name: string
			value: number
		}[]
		valuesByBrand: {
			count: number
			name: string
			value: number
		}[]
	}
	currency: ICurrency
}

const BookingsGrid = (properties: RfpResponseGridProperties) => {
	const { metrics, currency } = properties
	const [typeFilter, setTypeFilter] = useState(responseFilters.valuesByChain)

	const filteredData = useMemo(() => {
		const sourceData =
			typeFilter === responseFilters.valuesByChain
				? metrics.valuesByChain
				: metrics.valuesByBrand

		return sourceData
			.map(item => {
				const count = item.count || 1
				const average = item.value / count

				return {
					destination: item.name,
					rawValue: average,
					formattedValue: formatCurrency(average, currency)
				}
			})
			.sort((a, b) => b.rawValue - a.rawValue)
			.slice(0, 10)
	}, [metrics, typeFilter, currency])

	const destinationHeaderTemplate = () => {
		const tooltipContent =
			typeFilter === responseFilters.valuesByChain
				? 'A leaderboard of the top 10 hotel chains where you have successfully secured the most bookings, reflecting your highest-generating partnerships'
				: 'A leaderboard of the top 10 hotel brands where you have successfully secured the most bookings, reflecting your highest-generating partnerships'

		return (
			<div className='flex items-center gap-2'>
				{typeFilter === responseFilters.valuesByChain ? 'Chain' : 'Brand'}
				<TooltipComponent content={tooltipContent}>
					<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
				</TooltipComponent>
			</div>
		)
	}

	return (
		<div className='flex h-96 flex-col'>
			<div className='flex items-center justify-between p-4'>
				<div className='text-base font-medium text-gray-700'>
					Top 10 Bookings
				</div>
				<div className='flex items-center gap-4'>
					<div className='text-sm font-medium text-gray-900'>by</div>
					<div className='text-gray-900'>
						<Button.Group className='w-fit'>
							<HSButton
								color='gray'
								className={`flex-1 rounded-r-none ${typeFilter === responseFilters.valuesByChain ? 'bg-gray-200' : ''}`}
								onClick={() => setTypeFilter(responseFilters.valuesByChain)}
							>
								Chain
							</HSButton>
							<HSButton
								color='gray'
								className={`rounded-l-none ${typeFilter === responseFilters.valuesByBrand ? 'bg-gray-200' : ''}`}
								onClick={() => setTypeFilter(responseFilters.valuesByBrand)}
							>
								Brand
							</HSButton>
						</Button.Group>
					</div>
				</div>
			</div>
			<div className='overflow-auto'>
				<HSTable
					rows={filteredData}
					allowPaging={false}
					columns={[
						{
							field: 'destination',
							headerText: 'Destination',
							headerTemplate: destinationHeaderTemplate,
							width: 200,
							sortable: true
						},
						{
							field: 'formattedValue',
							headerText: 'Contract Value',
							width: 150,
							sortable: true
						}
					]}
				/>
			</div>
		</div>
	)
}

export default BookingsGrid
