/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable react/no-array-index-key */
/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable @typescript-eslint/no-confusing-void-expression */
/* eslint-disable @typescript-eslint/no-unnecessary-template-expression */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable unicorn/no-array-reduce */
import {
	faArrowUpRight,
	faBedFront,
	faInfoCircle,
	faSackDollar,
	faSearch
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSDonut<PERSON>hart from 'components/charts/donutChart'
import HSLegend from 'components/charts/legends'
import { useEffect, useState } from 'react'
import { Bar, BarChart, CartesianGrid, Tooltip, XAxis, YAxis } from 'recharts'
import type { HotelData } from '../opportunities/common/helper'
import { typeFilters } from '../opportunities/common/helper'
import type {
	DestinationProposalRequest,
	INewDestination
} from 'models/destinations'
import {
	calculatePeriod,
	formatCurrency,
	formatNumber,
	periodTypes,
	type ICurrency
} from 'lib/helpers'
import {
	eachMonthOfInterval,
	parseISO,
	startOfMonth,
	formatISO,
	addDays,
	isSameMonth,
	format
} from 'date-fns'
import { getProposalRequestLeadSource } from 'lib/common/proposalValues'
import { eventTypeOptions } from 'lib/helpers/eventTypes'
import { groupTypeOptions } from 'lib/helpers/groupTypes'
import { calculateGrossMarketValue } from 'lib/helpers/proposalValues'
import { getSupplierLeadSource } from 'lib/helpers/supplierContacts'
import dateFilterStore from '../dateFilterStore'
import HSTextField from 'components/textField'
import HSIcon from 'components/HSIcon'
import { Inject } from '@syncfusion/ej2-react-base'
import type { GridComponent } from '@syncfusion/ej2-react-grids'
import {
	ColumnsDirective,
	ColumnDirective,
	Page,
	Sort
} from '@syncfusion/ej2-react-grids'
import DataGrid from 'components/dataGrid'
import {
	detailsTemplate,
	hotelNameTemplate,
	totalValueTemplate,
	valueTemplate
} from '../opportunities/template'
import ScaledBar from 'components/scaledBar'
import {
	PresetsDirective,
	PresetDirective
} from '@syncfusion/ej2-react-calendars'
import { Button } from 'flowbite-react'
import type { ProposalValues } from 'models/proposalResponseMonitor'
import { WarningBanner } from 'components/warningBanner'
import { Link, useNavigate, useParams } from 'react-router-dom'
import MetricCard from 'components/metric/card'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import HSDateRangePicker from 'components/dateRangePicker'

interface IBookingGenerated {
	destination: INewDestination
	proposals: DestinationProposalRequest
	currency: ICurrency
	isFeatureLocked: boolean
}

interface ISalesPerson {
	id: string
	name: string | null
	count: number
	value: number
	bid: number
	won: number
	conversionRate: number
}

interface ITypeData {
	value: string
	name: string
	color: string
	fill: string
	count: number
	amount: number
}
const getTooltipLabel = (name: string) => {
	const t = name.slice(0, 2)
	const n = name.slice(2)

	return t === 'gt'
		? (groupTypeOptions.find(o => o.value === n)?.name ?? 'Unknown')
		: (eventTypeOptions.find(o => o.value === n)?.name ?? 'Unknown')
}

const CustomTooltip = ({
	active,
	payload,
	label
}: {
	active: boolean
	payload: { name: string; value: number }[] | undefined
	label: string
}) => {
	if (active && payload?.length) {
		return (
			<div className='rounded border border-gray-200 bg-white p-4 shadow'>
				<p className='font-bold'>{label}</p>
				<div className='flex flex-col gap-2'>
					{payload.map((entry, index) =>
						entry.value > 0 ? (
							<p key={`item-${index}`} style={{ color: 'black' }}>
								{`${getTooltipLabel(entry.name)}: $${Math.round(entry.value)}`}
							</p>
						) : null
					)}
				</div>
			</div>
		)
	}
	return null
}

const BookingGenerated = (properties: IBookingGenerated) => {
	let gridInstance: GridComponent | null = null
	const { organizationId } = useParams()
	const { userProfile } = useUserProfileContext()
	const navigate = useNavigate()
	const { currency, destination, proposals, isFeatureLocked } = properties
	const { dateFilter, setDateFilter } = dateFilterStore()
	const [bookingData, setBookingData] = useState<{
		countWon: number
		totalValue: number
		averageValue: number
		data: { totalRoomsRequested: number | null; won: number | null }[]
	}>({
		countWon: 0,
		totalValue: 0,
		averageValue: 0,
		data: []
	})

	const [searchText, setSearchText] = useState('')
	const [averageDailyRate, setAverageDailyRate] = useState(0)

	const [typeFilter, setTypeFilter] = useState(typeFilters.groupType)
	const [typeData, setTypeData] = useState<{ data: ITypeData[] }>({
		data: []
	})
	const [hotelData, setHotelData] = useState<{
		sortCol: string
		sortDir: number
		data: { venueName: string; won: number | null }[]
	}>({
		sortCol: 'venueName',
		sortDir: 1,
		data: []
	})
	const [salespersonData, setSalespersonData] = useState<ISalesPerson[]>([])
	const [filteredData, setFilteredData] = useState<HotelData[]>([])
	const [monthlyTypeData, setMonthlyTypeData] = useState<
		{
			month: Date
			iso: number
			name: string
			total: number
		}[]
	>([])
	const [revenueGenerated, setRevenueGenerated] = useState(0)

	useEffect(() => {
		const monthsInInterval = eachMonthOfInterval({
			start: parseISO(dateFilter.startDate),
			end: parseISO(dateFilter.endDate)
		})
		const all = [
			...(proposals.won ?? []).map(p => ({
				...p,
				type: 'won',
				opportunityValue: calculateGrossMarketValue(p.proposalValues)
			})),
			...(proposals.lost ?? []).map(p => ({
				...p,
				type: 'lost',
				opportunityValue: calculateGrossMarketValue(p.proposalValues)
			})),
			...(proposals.declined ?? []).map(p => ({
				...p,
				type: 'declined',
				opportunityValue: calculateGrossMarketValue(p.proposalValuesAverage)
			}))
		]
		const uniqueRfps = all.reduce(
			(
				a: {
					totalValue: number
					totalHotels: number
					data: {
						eventPlanId: string
						value: number
						lostValue: number
						month: Date
						assigned: ISupplierContact | undefined
						bid: number
						won: number
						totalContractedRooms: number
						proposalValues: ProposalValues
						proposalValuesAverage: ProposalValues
						submitted: string | null
					}[]
				},
				c
			) => {
				const value = c.opportunityValue
				const item = a.data.find(d => d.eventPlanId === c.eventPlanId)
				if (item) {
					if (c.type === 'won' && item.value && item.value < value) {
						item.value = value
						a.totalValue += value
					}
					if (c.type !== 'won' && item.value && item.value < value) {
						item.lostValue = value
					}
					if (!item.month && c.sent) {
						item.month = startOfMonth(parseISO(c.sent))
					}
					if (!item.assigned) {
						item.assigned = c.supplierContacts.find(
							sc => sc.organizationId === destination.id && sc.assigned
						)
					}
					item.bid += c.type === 'declined' ? 0 : 1
					item.won += c.type === 'won' ? 1 : 0
				} else {
					const assigned = c.supplierContacts.find(
						sc => sc.assigned !== null && sc.organizationId === destination.id
					)
					const startDate = c.eventPlanStartDate
						? parseISO(c.eventPlanStartDate)
						: null
					a.data.push({
						eventPlanId: c.eventPlanId ?? '',
						eventPlanName: c.eventPlanName,
						organizationName: c.eventPlanOrganizationName,
						peakRooms: c.eventPlanPeakRooms,
						totalRoomsRequested: c.totalRoomsRequested,
						totalContractedRooms: c.totalContractedRooms,
						eventPlanStartDate: c.eventPlanStartDate,
						eventPlanEndDate:
							startDate && c.eventPlanTotalDays
								? formatISO(addDays(startDate, c.eventPlanTotalDays - 1))
								: null,
						assignedName: assigned
							? assigned.firstName && assigned.lastName
								? `${assigned.firstName} ${assigned.lastName}`
								: assigned.id
							: '',
						eventPlanStatus: c.eventPlanStatus,
						groupType: c.eventPlanGroupType,
						type: c.eventPlanType,
						assigned: c.supplierContacts.find(
							sc => sc.organizationId === destination.id && sc.assigned
						) || { id: 'unassigned', firstName: 'Unassigned' },
						leadSource: getSupplierLeadSource(
							c.supplierContacts.find(
								sc => sc.organizationId === destination.id
							) ?? {}
						),
						sent: c.sent,
						salesperson: c.salesperson,
						month: c.sent ? startOfMonth(parseISO(c.sent)) : null,
						bid: c.type === 'declined' ? 0 : 1,
						value: c.type === 'won' ? value : 0,
						lostValue: c.type === 'won' ? 0 : value,
						won: c.type === 'won' ? 1 : 0,
						proposalValues: c.proposalValues,
						proposalValuesAverage: c.proposalValuesAverage,
						submitted: c.submitted
					})
					a.totalValue += c.type === 'won' ? value : 0
				}
				return a
			},
			{ totalValue: 0, totalHotels: 0, data: [] }
		)
		const countWithValue = uniqueRfps.data.filter(d => d.value > 0).length
		setBookingData({
			countWon: uniqueRfps.data.filter(d => d.won > 0).length,
			totalValue: uniqueRfps.totalValue,
			averageValue:
				countWithValue > 0 ? uniqueRfps.totalValue / countWithValue : 0,
			data: [...uniqueRfps.data]
		})

		const typesData = {
			data: uniqueRfps.data
				.filter(d => d.value > 0)
				.reduce(
					(a, c) => {
						const groupType = `${typeFilters.groupType}${c.groupType ?? 'unknown'}`
						const g = a.find(item => item.value === groupType)
						if (g) {
							g.count += 1
							g.amount += c.value
						}
						const eventType = `${typeFilters.eventType}${c.type ?? 'unknown'}`
						const item = a.find(data => data.value === eventType)
						if (item) {
							item.count += 1
							item.amount += c.value
						}
						return a
					},
					[
						...groupTypeOptions.map(o => ({
							...o,
							value: `${typeFilters.groupType}${o.value}`,
							fill: o.color,
							count: 0,
							amount: 0
						})),
						...eventTypeOptions.map(o => ({
							...o,
							value: `${typeFilters.eventType}${o.value}`,
							fill: o.color,
							count: 0,
							amount: 0
						}))
					]
				)
		}
		setTypeData(typesData)

		const types = uniqueRfps.data
			.filter(d => d.value && d.value > 0)
			.reduce(
				(a, c) => {
					const groupType = `${typeFilters.groupType}${c.groupType ?? 'unknown'}`
					const eventType = `${typeFilters.eventType}${c.type ?? 'unknown'}`
					const g = a.find(item => isSameMonth(item.month, c.month))
					if (g) {
						g[groupType] += c.value
						g[eventType] += c.value
					}
					return a
				},
				monthsInInterval.map(m => ({
					month: m,
					iso: Number(format(m, 'yyyyMM')),
					name: format(m, 'MMM yy'),
					total: 0,
					...Object.fromEntries(
						groupTypeOptions.map(c => [`${typeFilters.groupType}${c.value}`, 0])
					),
					...Object.fromEntries(
						eventTypeOptions.map(c => [`${typeFilters.eventType}${c.value}`, 0])
					)
				}))
			)
		setMonthlyTypeData(types)

		const updatedHotelData = {
			...hotelData,
			data: all
				.reduce(
					(
						a: {
							venueId: string
							bid: number
							won: number
							value: number
							data: []
						}[],
						c
					) => {
						const won = c.type === 'won'
						const value = won ? c.opportunityValue : 0
						const h = a.find(item => item.venueId === c.venueId)
						if (h) {
							h.bid += c.type === 'declined' ? 0 : 1
							h.won += won ? 1 : 0
							h.value += value
							if (won) {
								h.data.push({
									...c,
									leadSource: getProposalRequestLeadSource({
										eventPlan: { supplierContacts: c.supplierContacts },
										proposalRequest: c
									})
								})
							}
						} else {
							a.push({
								venueId: c.venueId ?? '',
								venueName: c.venueName,
								bid: c.type === 'declined' ? 0 : 1,
								won: won ? 1 : 0,
								value,
								data: won
									? [
											{
												...c,
												leadSource: getProposalRequestLeadSource({
													eventPlan: { supplierContacts: c.supplierContacts },
													proposalRequest: c
												})
											}
										]
									: []
							})
						}
						return a
					},
					[]
				)
				.map(d => ({
					...d,
					conversionRate: d.bid ? (d.won / d.bid) * 100 : 0
				}))
		}
		setHotelData(updatedHotelData)
		setSalespersonData(
			uniqueRfps.data
				.reduce((a, c) => {
					const item = a.find(d => d.id === c.assigned?.id)
					if (item) {
						item.count += 1
						item.value += c.value
						item.bid += c.bid ? 1 : 0
						item.won += c.won ? 1 : 0
					} else {
						a.push({
							id: c.assigned.id,
							name: `${c.assigned.firstName ?? c.assigned.id.split('@')[0]}`,
							count: 1,
							value: c.value,
							bid: c.bid ? 1 : 0,
							won: c.won ? 1 : 0
						})
					}
					return a
				}, [])
				.map(d => ({
					...d,
					conversionRate: (Number(d.won) / d.count) * 100
				}))
		)

		const totalRoomsOffered = uniqueRfps.data
			.filter(item => item.won)
			.reduce((a, c) => {
				const currentTotalRoomsOffered = c.submitted
					? c.proposalValues?.totalRoomsOffered
					: c.proposalValuesAverage?.totalRoomsOffered
				const sum = a + (currentTotalRoomsOffered ?? 0)
				return sum
			}, 0)

		const totalRoomRate = uniqueRfps.data
			.filter(item => item.won)
			.reduce((a, c) => {
				const currentRoomCost = c.submitted
					? c.proposalValues?.roomCost
					: c.proposalValuesAverage?.roomCost
				const sum = a + (currentRoomCost ?? 0)
				return sum
			}, 0)

		setAverageDailyRate(totalRoomRate / totalRoomsOffered)

		const wonValue = uniqueRfps.data
			.filter(item => item.won)
			.reduce((a, c) => {
				if (c.won) {
					const sum = a + (c.value ?? 0)
					return sum
				}
				return a
			}, 0)
		setRevenueGenerated(wonValue)
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [destination.id, proposals.won, proposals.lost, proposals.declined])

	useEffect(() => {
		let updatedData = hotelData.data
		if (searchText !== '') {
			updatedData = hotelData.data.filter(d =>
				d.venueName.toLowerCase().includes(searchText.toLowerCase())
			)
		}
		setFilteredData(updatedData)
		gridInstance?.setProperties({ dataSource: updatedData })
	}, [gridInstance, hotelData.data, searchText])

	const onDateChangeFilter = (dates: Date[] | null) => {
		if (dates)
			setDateFilter({
				startDate: new Date(dates[0]).toISOString().split('.')[0],
				endDate: new Date(dates[1]).toISOString().split('.')[0]
			})
	}

	const getLabel = () => {
		const total = typeData.data
			.filter(d => d.value.startsWith(typeFilter))
			.reduce((a, c) => a + c.count, 0)
		return total
	}

	return (
		<div>
			<div className='border-b px-6 py-4'>
				<div className='flex items-center justify-between'>
					<div className='text-lg font-semibold text-gray-900'>
						Bookings Generated
					</div>
					<div className='w-80'>
						<HSDateRangePicker
							placeholder='Select Date Range'
							onChange={({ value }: { value: Date[] | null }) =>
								onDateChangeFilter(value)
							}
							value={[
								new Date(dateFilter.startDate),
								new Date(dateFilter.endDate)
							]}
							format='MMM dd, yyyy'
						>
							<PresetsDirective>
								{Object.keys(periodTypes).map(period => {
									const {
										type: { label, key },
										startDate,
										endDate
									} = calculatePeriod(period)
									return (
										<PresetDirective
											key={key}
											label={label}
											start={new Date(startDate)}
											end={new Date(endDate)}
										/>
									)
								})}
							</PresetsDirective>
						</HSDateRangePicker>
					</div>
				</div>
			</div>

			<div
				className='overflow-auto'
				style={{ maxHeight: 'calc(100vh - 12rem)' }}
			>
				{isFeatureLocked ? (
					<div className='p-4'>
						<WarningBanner
							variant='Warning'
							heading='Paid analytics features are locked'
							message={
								<p>
									Some analytics features are locked in the free version of
									HopSkip{' '}
									<Link
										to='/'
										className='font-semibold text-primary-700 underline'
									>
										Learn More
									</Link>
									.
								</p>
							}
							ctaPrompt='Upgrade Your Account'
							onClick={() => console.log('Upgrade Your Account')}
							hideIcon
						/>
					</div>
				) : null}

				<div className='px-4 py-6'>
					<div className='flex gap-4'>
						<div className='w-2/3'>
							<div className='flex flex-col gap-4'>
								<div className='card p-4'>
									<div className='flex flex-col gap-3'>
										<div className='flex items-center justify-between'>
											<div className='flex items-center gap-2'>
												<div className='font-medium text-gray-700'>
													Opportunities Won
												</div>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-700'
													key='dmo-opportunities-won'
													id='info-dmo-opportunities-won'
												/>
											</div>
											<HSButton
												color='text'
												onClick={async () =>
													navigate(
														`/${userProfile.role?.toLowerCase()}/reports/details/${organizationId}/won`
													)
												}
											>
												<div className='flex items-center gap-2'>
													<div>Details</div>
													<FontAwesomeIcon
														icon={faArrowUpRight}
														className='ml-2'
													/>
												</div>
											</HSButton>
										</div>
										<div className='flex gap-6'>
											<div className='w-1/3'>
												<div className='flex flex-col gap-2.5'>
													<div className='text-sm font-bold text-gray-500'>
														Total
													</div>
													<div className='flex items-center gap-2'>
														<div className='flex h-6 w-6 items-center justify-center rounded-md bg-green-100'>
															<FontAwesomeIcon
																icon={faSackDollar}
																className='text-green-700'
															/>
														</div>
														<div className='text-2xl font-semibold text-gray-900'>
															{formatNumber(bookingData.countWon)}
														</div>
													</div>
												</div>
											</div>
											<div className='flex w-1/3 gap-4'>
												<div className='border-l' />
												<div className='flex flex-col gap-2.5'>
													<div className='flex items-center justify-between'>
														<div className='flex items-center gap-2'>
															<div className='text-sm font-bold text-gray-500'>
																Total Room Nights
															</div>
														</div>
													</div>
													<div className='flex items-center gap-2'>
														<div className='flex h-6 w-6 items-center justify-center rounded-md bg-gray-100'>
															<FontAwesomeIcon
																icon={faBedFront}
																className='text-gray-700'
															/>
														</div>
														<div className='text-2xl font-semibold text-gray-900'>
															{formatNumber(
																bookingData.data
																	.filter(item => item.won)
																	.reduce((a, c) => {
																		const sum = a + (c.totalRoomsRequested ?? 0)
																		return sum
																	}, 0)
															)}
														</div>
														<div className='text-xs font-medium text-gray-500'>
															Room Nights
														</div>
													</div>
												</div>
											</div>
											<div className='flex w-1/3 gap-4'>
												<div className='border-l' />
												<div className='flex flex-col gap-2.5'>
													<div className='flex items-center justify-between'>
														<div className='flex items-center gap-2'>
															<div className='text-sm font-bold text-gray-500'>
																Hotels Awarded
															</div>
														</div>
													</div>
													<div className='flex items-center gap-2'>
														<div className='text-2xl font-semibold text-gray-900'>
															{formatNumber(
																hotelData.data.reduce((a, c) => {
																	const sum = a + ((c.won ?? 0) > 0 ? 1 : 0)
																	return sum
																}, 0)
															)}
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div className='card p-4'>
									<div className='flex flex-col gap-3'>
										<div className='font-medium text-gray-700'>
											Booking Summary
										</div>
										<div className='flex gap-6'>
											<div className='w-1/3'>
												<div className='flex flex-col gap-2.5'>
													<div className='flex items-center justify-between'>
														<div className='flex items-center gap-2'>
															<div className='text-sm font-bold text-gray-500'>
																Average Booking
															</div>
															<FontAwesomeIcon
																icon={faInfoCircle}
																className='text-gray-700'
																key='average-booking'
																id='info-average-booking'
															/>
														</div>
													</div>
													<div className='flex items-center gap-2'>
														<div className='text-2xl font-semibold text-gray-900'>
															{formatCurrency(
																bookingData.averageValue,
																currency
															)}
														</div>
													</div>
												</div>
											</div>
											<div className='flex w-1/3 gap-4'>
												<div className='border-l' />
												<div className='flex flex-col gap-2.5'>
													<div className='flex items-center justify-between'>
														<div className='flex items-center gap-2'>
															<div className='text-sm font-bold text-gray-500'>
																Average Daily Rate
															</div>
															<FontAwesomeIcon
																icon={faInfoCircle}
																className='text-gray-700'
																key='average-daily-rate'
																id='info-average-daily-rate'
															/>
														</div>
													</div>
													<div className='flex items-center gap-2'>
														<div className='text-2xl font-semibold text-gray-900'>
															{formatCurrency(averageDailyRate, currency)}
														</div>
													</div>
												</div>
											</div>
											<div className='flex w-1/3 gap-4'>
												<div className='border-l' />
												<div className='flex flex-col gap-2.5'>
													<div className='flex items-center justify-between'>
														<div className='flex items-center gap-2'>
															<div className='text-sm font-bold text-gray-500'>
																Revenue Generated
															</div>
															<FontAwesomeIcon
																icon={faInfoCircle}
																className='text-gray-700'
																key='revenue-generated'
																id='info-revenue-generated'
															/>
														</div>
													</div>
													<div className='flex items-center gap-2'>
														<div className='text-2xl font-semibold text-gray-900'>
															{formatCurrency(revenueGenerated, currency)}
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						{/* <div className='w-1/3'>
							<div className='flex flex-col gap-4'>
								<div className='card p-4'>
									<div className='flex flex-col gap-2.5'>
										<div className='flex items-center justify-between'>
											<div className='flex items-center gap-2'>
												<div className='font-medium text-gray-700'>
													Hotels Awarded
												</div>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-700'
												/>
											</div>
										</div>
										<div className='flex items-center gap-2'>
											<div className='text-2xl font-semibold text-gray-900'>
												15
											</div>
										</div>
									</div>
								</div>
								<div className='card p-4'>
									<div className='flex flex-col gap-2.5'>
										<div className='flex items-center justify-between'>
											<div className='flex items-center gap-2'>
												<div className='font-medium text-gray-700'>
													Revenue Generated
												</div>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-700'
												/>
											</div>
										</div>
										<div className='flex items-center gap-2'>
											<div className='text-2xl font-semibold text-gray-900'>
												{formatCurrency(bookingData.totalValue, currency)}
											</div>
										</div>
									</div>
								</div>
							</div>
						</div> */}
						<div className='w-1/3'>
							<div className='card h-full p-4'>
								<div className='flex flex-col gap-6'>
									<div className='flex items-center gap-2'>
										<div className='font-medium text-gray-700'>
											Conversion Rate
										</div>
										<FontAwesomeIcon
											icon={faInfoCircle}
											className='text-gray-700'
											key='booking-generated-conversion-rate'
											id='info-booking-generated-conversion-rate'
										/>
									</div>
									<div className='flex items-center justify-center gap-6'>
										<HSDonutChart
											data={[
												{
													name: 'Won',
													value: bookingData.countWon,
													color: '#BCF0DA'
												},
												{
													name: 'Lost',
													value: bookingData.data.length - bookingData.countWon,
													color: '#FBD5D5'
												}
											]}
											label={
												bookingData.data.length > 0
													? `${formatNumber((bookingData.countWon / bookingData.data.length) * 100)}%`
													: 'n/a'
											}
										/>
										<div className='flex flex-col gap-2.5'>
											<div className='text-xs font-medium text-gray-700'>
												Total {bookingData.data.length} Proposals submitted
											</div>
											<HSLegend
												data={[
													{
														label: `Won (${bookingData.countWon})`,
														color: '#BCF0DA'
													},
													{
														label: `Lost (${bookingData.data.length - bookingData.countWon})`,
														color: '#FBD5D5'
													}
												]}
												shape='circle'
											/>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className='px-4 py-6'>
					<div className='card p-4'>
						<div className='flex flex-col'>
							<div className='flex items-center justify-between gap-6'>
								<div className='flex items-center gap-2'>
									<div className='font-medium text-gray-700'>Bookings</div>
									<FontAwesomeIcon
										icon={faInfoCircle}
										className='text-gray-700'
										key='booking-generated-bookings'
										id='info-booking-generated-bookings'
									/>
								</div>
								<div className='flex items-center gap-4'>
									<div className='text-sm font-medium text-gray-700'>
										View by
									</div>
									<Button.Group color='gray'>
										<Button
											color='gray'
											size='sm'
											onClick={() => {
												const updatedView =
													typeFilter === typeFilters.eventType
														? typeFilters.groupType
														: typeFilters.eventType
												setTypeFilter(updatedView)
											}}
											className={
												typeFilter === typeFilters.groupType
													? 'bg-gray-300'
													: 'bg-white'
											}
										>
											Group Type
										</Button>
										<Button
											color='gray'
											className={
												typeFilter === typeFilters.eventType
													? 'bg-gray-300'
													: 'bg-white'
											}
											size='sm'
											onClick={() => {
												const updatedView =
													typeFilter === typeFilters.eventType
														? typeFilters.groupType
														: typeFilters.eventType
												setTypeFilter(updatedView)
											}}
										>
											Event Type
										</Button>
									</Button.Group>
								</div>
							</div>
							<MetricCard isLocked={isFeatureLocked}>
								<div className='py-4'>
									<div className='flex items-center justify-between gap-6'>
										<div className='flex w-1/4 items-center justify-center'>
											<HSDonutChart
												size={180}
												data={typeData.data
													.filter(d => d.value.startsWith(typeFilter))
													.map(item => ({
														...item,
														value: Number(item.count.toFixed(2))
													}))}
												showTooltip
												label={getLabel()}
											/>
										</div>
										<div className='flex gap-4'>
											<div className='border-l' />
											<div className='flex flex-col gap-3'>
												<div className='flex items-center gap-2'>
													<div className='text-sm font-bold text-gray-700'>
														Bookings by{' '}
														{typeFilter === typeFilters.eventType
															? 'Event'
															: 'Group'}{' '}
														Type
													</div>
													<div>
														<FontAwesomeIcon
															icon={faInfoCircle}
															className='text-gray-700'
															key='booking-generated-bookings-by-type'
															id='info-booking-generated-bookings-by-type'
														/>
													</div>
												</div>
												<BarChart
													width={900}
													height={236}
													margin={{ top: 10 }}
													data={monthlyTypeData}
												>
													<CartesianGrid
														strokeDasharray='3 3'
														horizontal
														vertical={false}
													/>
													<XAxis dataKey='name' />
													<YAxis
														yAxisId='left'
														tickFormatter={value =>
															`${formatCurrency(value / 1000, currency)}k`
														}
													/>
													<Tooltip
														content={CustomTooltip}
														wrapperStyle={{ zIndex: 1000 }}
													/>
													{(typeFilter === typeFilters.eventType
														? eventTypeOptions
														: groupTypeOptions
													).map((o, index) => (
														<Bar
															key={index}
															yAxisId='left'
															stackId='a'
															fill={o.color}
															dataKey={`${typeFilter}${o.value}`}
														/>
													))}
												</BarChart>
											</div>
										</div>
									</div>
								</div>
								<div className='flex items-center justify-center gap-4'>
									<HSLegend
										data={(typeFilter === typeFilters.eventType
											? eventTypeOptions
											: groupTypeOptions
										).map(item => ({
											color: item.color,
											label: item.name
										}))}
										shape='square'
									/>
								</div>
							</MetricCard>
						</div>
					</div>
				</div>
				<div className='px-4 py-6'>
					<div className='flex gap-4'>
						<div className='w-3/4'>
							<div className='card p-4'>
								<div className='flex flex-col gap-4'>
									<div className='flex items-center justify-between'>
										<div className='flex items-center gap-2'>
											<div className='font-semibold text-gray-700'>
												Bookings generated for Hotels
											</div>
											<FontAwesomeIcon
												icon={faInfoCircle}
												className='text-gray-700'
												key='booking-generated-for-hotels'
												id='info-booking-generated-for-hotels'
											/>
										</div>
										<div className='w-80'>
											<HSTextField
												showClearButton
												icon={HSIcon(faSearch)}
												placeholder='Search'
												value={searchText}
												onChange={event => setSearchText(event.target.value)}
												key='booking-generated-search'
												disabled={isFeatureLocked}
											/>
										</div>
									</div>

									<MetricCard isLocked={isFeatureLocked}>
										<div>
											<DataGrid
												dataSource={filteredData}
												allowSorting
												allowPaging
												allowFiltering={false}
												ref={r => {
													gridInstance = r
												}}
												sortSettings={{
													columns: [
														{ direction: 'Ascending', field: 'venueName' }
													]
												}}
											>
												<ColumnsDirective>
													<ColumnDirective
														field='venueName'
														headerText='Hotel Name'
														template={hotelNameTemplate}
														autoFit
														clipMode='EllipsisWithTooltip'
													/>

													<ColumnDirective
														field='bid'
														headerText='Bid'
														template={(item: { bid: number }) =>
															valueTemplate(item.bid)
														}
														headerTextAlign='Center'
														textAlign='Center'
														width={100}
													/>
													<ColumnDirective
														field='won'
														headerText='Won'
														template={(item: { won: number }) =>
															valueTemplate(item.won)
														}
														headerTextAlign='Center'
														textAlign='Center'
														width={100}
													/>
													<ColumnDirective
														field='conversionRate'
														headerText='Conversion'
														template={(item: { conversionRate: number }) =>
															valueTemplate(
																`${formatNumber(item.conversionRate, '0')} %`
															)
														}
														headerTextAlign='Center'
														textAlign='Center'
														width={100}
													/>
													<ColumnDirective
														field='value'
														headerText='Total'
														template={totalValueTemplate}
														headerTextAlign='Center'
														textAlign='Center'
														width={150}
													/>
													<ColumnDirective
														field='name'
														headerText='Details'
														template={(item: { venueId: string }) =>
															detailsTemplate(item, organizationId ?? '', 'won')
														}
														headerTextAlign='Center'
														textAlign='Center'
														width={150}
													/>
												</ColumnsDirective>
												<Inject services={[Page, Sort]} />
											</DataGrid>
										</div>
									</MetricCard>
								</div>
							</div>
						</div>
						<div className='w-1/4'>
							<div className='card p-4'>
								<div className='flex flex-col gap-4'>
									<div className='flex items-center gap-2'>
										<div className='font-medium text-gray-700'>
											Bookings by salesperson
										</div>
										<FontAwesomeIcon
											icon={faInfoCircle}
											className='text-gray-700'
											key='booking-by-salesperson'
											id='info-booking-by-salesperson'
										/>
									</div>
									<MetricCard isLocked={isFeatureLocked}>
										<ScaledBar
											data={salespersonData.map(d => ({
												...d,
												label: d.name,
												formattedValue: formatCurrency(d.value, currency),
												value: d.value,
												color: '#EDEBFE'
											}))}
										/>
									</MetricCard>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}

export default BookingGenerated
