/* eslint-disable import/prefer-default-export */
import { faSearch } from '@fortawesome/pro-light-svg-icons'
import HSIcon from 'components/HSIcon'
import HSTextField from 'components/textField'

export const SearchField = ({
	searchString,
	setSearchString
}: {
	searchString: string
	setSearchString: (string_: string) => void
}) => (
	<HSTextField
		icon={HSIcon(faSearch)}
		placeholder='Search'
		value={searchString}
		onChange={event => {
			setSearchString(event.target.value)
		}}
		showClearButton
	/>
)
