import {
	faGem,
	faCrown,
	type IconDefinition
} from '@fortawesome/pro-light-svg-icons'
import { faCircleDot, faStar } from '@fortawesome/pro-solid-svg-icons'

export const ratingSystems: Record<string, IRatingSystem | null> = {
	TripAdvisor: {
		ratingSystemKey: 'TripAdvisor',
		label: 'TripAdvisor',
		type: 'bubble',
		min: 1,
		max: 5,
		formatWithUnits: true,
		previewMode: true,
		icon: faCircleDot,
		completionColor: 'text-green-600'
	},
	AAADiamond: {
		ratingSystemKey: 'AAADiamond',
		label: 'AAA Diamonds',
		type: 'diamond',
		min: 1,
		max: 5,
		previewMode: true,
		icon: faGem,
		completionColor: 'text-blue-600'
	},
	NorthstarMedia: {
		ratingSystemKey: 'NorthstarMedia',
		label: 'Northstar Media',
		type: 'crown',
		min: 1,
		max: 5,
		formatWithUnits: true,
		previewMode: true,
		icon: faCrown,
		completionColor: 'text-yellow-600'
	},
	LocalStarRating: {
		ratingSystemKey: 'LocalStarRating',
		label: 'Local Star',
		type: 'star',
		min: 1,
		max: 5,
		previewMode: true,
		icon: faStar,
		completionColor: 'text-yellow-300'
	},
	Forbes: {
		ratingSystemKey: 'Forbes',
		label: 'Forbes',
		type: 'star',
		min: 1,
		max: 5,
		formatWithUnits: true,
		icon: faStar,
		completionColor: 'text-yellow-300'
	},

	Michelin: {
		ratingSystemKey: 'Michelin',
		label: 'Michelin',
		type: 'star',
		min: 1,
		max: 5,
		formatWithUnits: true,
		icon: faCircleDot,
		completionColor: 'text-green-600'
	},
	HotelstarsUnion: {
		ratingSystemKey: 'HotelstarsUnion',
		label: 'Hotelstars Union',
		type: 'star',
		min: 1,
		max: 5,
		formatWithUnits: true,
		icon: faStar,
		completionColor: 'text-yellow-300'
	},
	HellenicChamber: {
		ratingSystemKey: 'HellenicChamber',
		label: 'Hellenic Chamber of Hotels',
		type: 'star',
		min: 1,
		max: 5,
		formatWithUnits: true,
		icon: faStar,
		completionColor: 'text-yellow-300'
	}
}

export const hotelTypes: Record<string, IHotelTypes> = {
	Airport: {
		key: 'Airport',
		label: 'Airport',
		description:
			'Ideal for guests in transit, offering convenient access to airports, shuttle services, and amenities for travelers with varying schedules.'
	},
	AllInclusive: {
		key: 'AllInclusive',
		label: 'All-Inclusive',
		visibleInSearch: true,
		description:
			'A comprehensive stay where one price covers accommodations, meals, drinks, and on-site activities and entertainment.'
	},
	Boutique: {
		key: 'Boutique',
		label: 'Boutique',
		visibleInSearch: true,
		description:
			'Offers personalized service, unique design, and luxurious amenities, providing an intimate experience in fashionable locales.'
	},
	Business: {
		key: 'Business',
		label: 'Business',
		description:
			'Features amenities like high-speed internet and meeting rooms tailored for business travelers and close to commercial hubs.'
	},
	Casino: {
		key: 'Casino',
		label: 'Casino',
		description:
			'Located near a casino or has a casino on site. These hotels often cater to travelers looking to enjoy the excitement of a casino, as well as other amenities such as restaurants, bars, and entertainment.'
	},
	CityCenter: {
		key: 'CityCenter',
		label: 'City Center',
		description:
			'Located in the urban core, business and leisure travelers can easily access attractions, business centers, shopping, and dining.'
	},
	Conference: {
		key: 'Conference',
		label: 'Conference',
		description:
			'Designed with specialized meeting spaces and technological support to host corporate events, seminars, and educational conferences.'
	},
	ConventionCenter: {
		key: 'ConventionCenter',
		label: 'Convention Center',
		description:
			'Equipped with large-scale facilities for significant industry events and exhibitions, complete with comprehensive event services.'
	},
	Golf: {
		key: 'Golf',
		label: 'Golf',
		visibleInSearch: true,
		description:
			'Located on or near a golf course, with golf packages and amenities available to guests.'
	},
	Historical: {
		key: 'Historical',
		label: 'Historical',
		description:
			'Set in culturally rich buildings or areas, blending heritage and modern comforts, often in places of historical significance.'
	},
	Luxury: {
		key: 'Luxury',
		label: 'Luxury',
		visibleInSearch: true,
		description:
			'Provides an indulgent experience with exquisite accommodations, gourmet dining, personalized services, and exclusive amenities.'
	},
	Resort: {
		key: 'Resort',
		label: 'Resort',
		visibleInSearch: true,
		description:
			'These hotels are typically located in tourist destinations such as beach towns or ski resorts and often offer a wide range of amenities and activities on-site, such as pools, spas, golf courses, or other recreational facilities.'
	},
	SportsAndAdventure: {
		key: 'SportsAndAdventure',
		label: 'Sports and Adventure',
		description:
			'For those seeking an active stay, these hotels provide sports facilities, adventure excursions, and fitness programs.'
	},
	Sustainable: {
		key: 'Sustainable',
		label: 'Sustainable',
		description:
			'Focuses on reducing environmental impact with eco-friendly practices, offering responsible accommodations and services.',
		includeHotelAmenities: ['Eco-friendly']
	},
	Wellness: {
		key: 'Wellness',
		label: 'Wellness',
		description:
			'Promotes health and relaxation with spas, health programs, and nutritious dining options for rejuvenation.'
	}
}

export const amenityTypes: Record<string, IAmenitiesTypes> = {
	Hotel: { key: 'Hotel', label: 'Hotel' },
	Room: { key: 'Room', label: 'Room' },
	GuestService: { key: 'GuestService', label: 'Guest Service' },
	Facility: { key: 'Facility', label: 'Facility' },
	Business: { key: 'Business', label: 'Business' }
}

export const amenities: Record<string, string[]> = {
	Hotel: [
		'Adults only',
		'All-inclusive',
		'Beach',
		'Casino',
		'Eco-friendly',
		'Gym',
		'Golf course',
		'Pool',
		'Spa',
		'Skiing',
		'Winery'
	],
	Room: [
		'Calls (local)',
		'Room service',
		'View (garden)',
		'View (ocean or water)'
	],
	GuestService: [
		'Concierge services',
		'Airport Shuttle',
		'Internet access',
		'Laundry service',
		'Luggage storage'
	],
	Facility: [
		'Onsite catering',
		'Onsite restaurant',
		'Onsite security',
		'Outside caterers allowed',
		'Pet friendly',
		'Rental car service',
		'Space (outdoor)',
		'Space (private)',
		'Wheelchair accessible',
		'Dance floor',
		'Loading dock',
		'Portable heaters',
		'Portable walls',
		'Staging area',
		'Valet parking'
	],
	Business: ['AV capabilities', 'Business center', 'Video conferencing']
}

export interface IAmenitiesTypes {
	key: string
	label: string
}

export interface IHotelTypes {
	key: string
	label: string
	description: string
	includeHotelAmenities?: string[]
	visibleInSearch?: boolean
}

export interface IRatingSystem {
	ratingSystemKey: string
	label: string
	type: string
	min: number
	max: number
	formatWithUnits?: boolean
	previewMode?: boolean
	icon: IconDefinition
	completionColor?: string
}
