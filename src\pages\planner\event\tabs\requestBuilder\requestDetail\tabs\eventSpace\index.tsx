/* eslint-disable no-param-reassign */
/* eslint-disable unicorn/no-unused-properties */
/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable no-lonely-if */
/* eslint-disable no-underscore-dangle */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-plusplus */
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	faArrowDownFromBracket,
	faArrowsFromLine
} from '@fortawesome/pro-light-svg-icons'
import HSButton from 'components/button'
import HSToggleSwitch from 'components/toggleSwitch'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import HSTextField from 'components/textField'
import HSCheckbox from 'components/checkbox'
import { parseISO, differenceInDays, addDays, startOfWeek } from 'date-fns'
import { useState, useEffect } from 'react'
import { formatNumber, numberMask, UnitedStatesDollar } from 'lib/helpers'
import EditCommentDrawer from './drawers/editCommentDrawer'
import ShiftItemModal from './modals/shiftItemModal'
import useEventSpaceHooks from './hooks/eventSpaceHooks'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import EventTable from './eventTable'

const EventSpace = () => {
	const { eventInfo, disableFields } = eventInfoStore()
	const { currencies } = useCurrencyContext()
	const { onChangeMeetingSpace, onChange } = useEventSpaceHooks()

	const currency = eventInfo?.currencyCode
		? currencies[eventInfo.currencyCode]
		: UnitedStatesDollar

	const [availableDates, setAvailableDates] = useState<Date[]>([])

	const [showEditComment, setShowEditComment] = useState(false)
	const [showShiftItemsContext, setShowShiftItemsContext] = useState({
		fromDay: -1,
		delta: 0,
		showModal: false
	})

	const onClickShiftItems = (fromDay: number) => {
		setShowShiftItemsContext({ fromDay, delta: 0, showModal: true })
	}

	useEffect(() => {
		if (eventInfo) {
			const a: Date[] = []
			if (
				eventInfo.itemType === 'eventPlan' &&
				eventInfo.startDate &&
				eventInfo.endDate
			) {
				// total days
				const sd = parseISO(eventInfo.startDate)
				const ed = parseISO(eventInfo.endDate)
				for (let d = 0; d <= differenceInDays(ed, sd); d++) {
					a.push(addDays(sd, d))
				}
			} else if (eventInfo.itemType === 'eventPlanTemplate') {
				const sd = addDays(
					startOfWeek(new Date()),
					eventInfo.startDayOfWeek ?? 0
				)
				for (let d = 0; d <= (eventInfo.totalDays ?? 0) - 1; d += 1) {
					a.push(addDays(sd, d))
				}
			}
			setAvailableDates(a)
		}
	}, [
		eventInfo?.startDate,
		eventInfo?.endDate,
		eventInfo?.startDayOfWeek,
		eventInfo?.totalDays,
		eventInfo?.itemType,
		eventInfo
	])

	return (
		<div className='flex flex-col'>
			<div className='flex items-center justify-between gap-6 px-6 pt-4'>
				<div className='flex w-1/2 items-start gap-2'>
					<HSToggleSwitch
						checked={eventInfo?.meetingSpaceRequired || false}
						name='meetingSpaceRequired'
						disabled={disableFields}
						onChange={checked => {
							onChangeMeetingSpace({
								name: 'meetingSpaceRequired',
								targetType: 'checkbox',
								value: checked
							})
						}}
					/>
					<div className='flex items-start gap-4'>
						<div className='flex flex-col'>
							<div className='flex items-center gap-4'>
								<div className='text-lg font-semibold text-gray-900'>
									Meeting Space Requirements
								</div>
								<HSButton color='text'>
									<div className='flex items-center gap-2'>
										<FontAwesomeIcon icon={faArrowDownFromBracket} />
										Import
									</div>
								</HSButton>
								<HSButton color='text' onClick={() => onClickShiftItems(-1)}>
									<div className='flex items-center gap-2'>
										<FontAwesomeIcon icon={faArrowsFromLine} />
										Shift Items
									</div>
								</HSButton>
							</div>

							<div className='text-sm font-normal text-gray-500'>
								Specify your event space requirements
							</div>
						</div>
					</div>
				</div>
				{eventInfo?.meetingSpaceRequired ? (
					<div className='w-1/2'>
						<div className='card p-4'>
							<div className='flex items-start justify-between gap-4'>
								<div className='flex w-1/3 gap-2'>
									<div className='flex flex-col gap-1'>
										<div className='text-sm font-bold text-gray-500'>
											Meeting Rooms
										</div>
										<div className='flex items-end gap-2'>
											<div className='text-2xl font-semibold leading-none text-gray-900'>
												{formatNumber(eventInfo.peakMeetingRoomsRequired)}
											</div>
											<div className='text-xs font-medium text-gray-500'>
												on peak
											</div>
										</div>
									</div>
								</div>
								<div className='flex w-1/3 gap-4'>
									<div className='border-l' />
									<div className='flex flex-col gap-1'>
										<div className='text-sm font-bold text-gray-500'>
											Total Space
										</div>
										<div className='flex items-end gap-2'>
											<div className='text-2xl font-semibold leading-none text-gray-900'>
												{formatNumber(eventInfo.peakMeetingSpaceRequired || 0)}
											</div>
											<div className='text-xs font-medium text-gray-500'>
												ft<sup>2</sup> on peak
											</div>
										</div>
									</div>
								</div>
								<div className='flex w-1/3 gap-4'>
									<div className='border-r' />
									<div className='flex flex-col gap-1'>
										<div className='text-sm font-bold text-gray-500'>
											Largest Rooms
										</div>
										<div className='flex items-end gap-2'>
											<div className='text-2xl font-semibold leading-none text-gray-900'>
												{formatNumber(
													eventInfo.meetingSpaceRequests
														?.filter(r => !r.excludeFromTotals)
														.reduce((a, c) => Math.max(a, c.areaTotal || 0), 0)
												)}
											</div>
											<div className='text-xs font-medium text-gray-500'>
												ft<sup>2</sup>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				) : null}
			</div>
			{eventInfo?.meetingSpaceRequired ? (
				<>
					<div className='px-6 pt-4'>
						<div className='card !bg-gray-50 p-4'>
							<div className='flex gap-4'>
								<div className='flex flex-1 flex-col gap-1'>
									<div className='flex items-center justify-between gap-4'>
										<div className='text-sm font-normal text-gray-700'>
											General Comment
										</div>
										<HSButton
											color='text'
											onClick={() => setShowEditComment(true)}
										>
											Edit
										</HSButton>
									</div>
									<div
										className='line-clamp-2 max-h-12 overflow-hidden text-sm font-normal text-gray-500'
										title={eventInfo.meetingSpaceComments ?? ''}
									>
										{eventInfo.meetingSpaceComments || 'No comments added'}
									</div>
								</div>
								<div className='border-l border-gray-300' />

								<div className='flex flex-col gap-2'>
									<div className='text-sm font-normal text-gray-700'>
										F&B Budget for Event
									</div>
									<div className='flex w-32'>
										<HSButton
											className='rounded-r-none border-r bg-gray-100'
											color='gray'
										>
											{currency.symbol}
										</HSButton>
										<HSTextField
											label=''
											mask={numberMask}
											groupPlacement='right'
											placeholder='Rate'
											name='meetingSpaceBudget'
											value={eventInfo.meetingSpaceBudget || ''}
											min={0}
											onChange={event =>
												onChange(null, {
													name: 'meetingSpaceBudget',
													value: event.target.value.replaceAll(',', ''),
													targetType: 'number'
												})
											}
										/>
									</div>
								</div>
								<div className='border-l border-gray-300' />
								<div className='flex items-start gap-2'>
									<HSCheckbox
										name='requireNamedMeetingSpace'
										checked={eventInfo.requireNamedMeetingSpace || false}
										onChange={event =>
											onChange(null, {
												name: 'requireNamedMeetingSpace',
												value: event.target.checked
											})
										}
									/>
									<div className='text-sm font-normal text-gray-700'>
										Require Named Meeting Space in Proposals{' '}
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className='flex flex-col'>
						<EventTable availableDates={availableDates} />
					</div>
				</>
			) : (
				<div className='flex h-96 items-center justify-center'>
					<div className='flex flex-col gap-1'>
						<div className='text-sm font-semibold text-primary-disabled'>
							Event Space is not needed
						</div>
						<HSButton
							color='light'
							onClick={() => {
								onChangeMeetingSpace({
									name: 'meetingSpaceRequired',
									targetType: 'checkbox',
									value: true
								})
							}}
							disabled={disableFields}
						>
							Request Event Space
						</HSButton>
					</div>
				</div>
			)}
			{showEditComment ? (
				<EditCommentDrawer
					value={eventInfo?.meetingSpaceComments ?? ''}
					onChange={value => {
						onChange(null, {
							name: 'meetingSpaceComments',
							value,
							targetType: 'text'
						})
					}}
					onClose={() => setShowEditComment(false)}
				/>
			) : null}
			{showShiftItemsContext.showModal ? (
				<ShiftItemModal
					setShowShiftItemsContext={setShowShiftItemsContext}
					showShiftItemsContext={showShiftItemsContext}
				/>
			) : null}
		</div>
	)
}

export default EventSpace
