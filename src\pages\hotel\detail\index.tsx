import headerStore from 'components/header/headerStore'
import HotelDetail from 'components/hotel/detail'
import hotelDetailStore from 'components/hotel/detail/store'
import { useEffect } from 'react'
import { useParams } from 'react-router-dom'

const HotelDetailPage = () => {
	const { venueId } = useParams()
	const { reset, setHide } = headerStore()
	const {
		setGoBackLabel,
		setGoBackUrl,
		setShowAvailability,
		clearStore,
		setShowAddToRfp
	} = hotelDetailStore()

	useEffect(() => {
		setHide(false)
		return () => {
			reset()
		}
	}, [reset, setHide])

	useEffect(() => {
		setGoBackLabel('Back to Preview')
		setGoBackUrl(`/hotel-profiles/hotel-info/${venueId}/preview`)
		setShowAvailability(false)
		setShowAddToRfp(false)
		return () => {
			clearStore()
		}
	}, [
		clearStore,
		setGoBackLabel,
		setGoBackUrl,
		setShowAddToRfp,
		setShowAvailability,
		venueId
	])

	return venueId ? (
		<div
			className='!overflow-auto p-6'
			style={{ maxHeight: 'calc(100vh - 6rem)' }}
		>
			<HotelDetail venueId={venueId} showCompHotelAddToRfp={false} />
		</div>
	) : null
}

export default HotelDetailPage
