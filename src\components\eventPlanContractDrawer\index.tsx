/* eslint-disable @typescript-eslint/no-unused-vars */
import { useEffect, useState } from 'react'
import HSBadge from 'components/badge'
import HSButton from 'components/button'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import type { EventPlanStatusKey } from 'lib/helpers/statusMaps'
import {
	EventPlanStatusMap,
	ProposalRequestStatusMap
} from 'lib/helpers/statusMaps'
import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import { sortProposalDates } from 'lib/common/proposalValues'
import {
	useGetEventDetail,
	useSignEventPlanContract
} from 'lib/services/eventPlans.service'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import Loader from 'components/loader'
import HSDrawer from 'components/drawer'
import HSTextArea from 'components/textarea'
import HSRadioButton from 'components/radioButton'
import { Drawer } from 'flowbite-react'

interface IEventPlanContractSignedDrawerProperties {
	showContractSignedDrawer: boolean
	onCancelMarkContractSigned: () => void
	onContractEventPlan?: () => void
	eventPlan: EventPlan
}

const defaultDeclinedReason = 'Group selected another location'

const EventPlanContractSignedDrawer = (
	properties: IEventPlanContractSignedDrawerProperties
) => {
	const {
		eventPlan,
		showContractSignedDrawer,
		onContractEventPlan,
		onCancelMarkContractSigned
	} = properties
	const { userProfile } = useUserProfileContext()
	const { mergeProperties, setObject, eventInfo } = eventInfoStore()
	const [venueId, setVenueId] = useState('')
	const [formInvalid, setFormInvalid] = useState(false)
	const [proposalRequestCount, setProposalRequestCount] = useState(0)
	const { mutateAsync: signContract, isPending: isSigningContract } =
		useSignEventPlanContract()
	const [signingProposalRequests, setSigningProposalRequests] = useState<
		EventPlan['proposalRequests']
	>([])
	const [contractedProposals, setContractedProposals] = useState<
		Record<
			string,
			{
				venueName: string
				selectedDateKey: string
				proposalDates: { key: string; declineToBid: boolean }[]
				selected: boolean
			}
		>
	>({})

	const [declineComment, setDeclineComment] = useState('')

	const { data: eventInfoData, isFetching: isFetchingEventInfo } =
		useGetEventDetail(eventPlan.id ?? '', !!eventPlan.id)

	useEffect(() => {
		if (eventInfoData) {
			setObject(eventInfoData, true)
		}
	}, [eventInfoData, setObject])

	useEffect(() => {
		setFormInvalid(
			Object.keys(contractedProposals).every(
				k => !contractedProposals[k].selected
			)
		)
	}, [contractedProposals])

	useEffect(() => {
		if (eventInfo?.proposalRequests?.length) {
			const filteredProposals = eventInfo.proposalRequests
				.filter(
					pr =>
						pr.status !== null &&
						['ClosedWon', 'ContractSigned'].includes(pr.status)
				)
				.map(pr => ({
					...pr,
					closedWon: pr.status === 'ClosedWon',
					contractIsSigned: pr.status === 'ContractSigned'
				}))

			setSigningProposalRequests(filteredProposals)

			setProposalRequestCount(filteredProposals.length)
			if (filteredProposals.length > 0) {
				setVenueId(filteredProposals[0].venueId)
				const proposalMap = Object.fromEntries(
					filteredProposals.map((c, index) => {
						const proposalDates =
							c.currentBid?.proposalDates
								?.sort(sortProposalDates)
								.filter(pd => pd.declineToBid === false) ?? []
						return [
							c.venueId,
							{
								venueName: c.venueName ?? '',
								selectedDateKey:
									proposalDates.length === 1
										? (proposalDates[0].key ?? '')
										: '',
								proposalDates: proposalDates.map(pd => ({
									key: pd.key ?? '',
									declineToBid: pd.declineToBid ?? false
								})),
								selected: index === 0 && filteredProposals.length === 1
							}
						]
					})
				)
				setContractedProposals(proposalMap)
			}
		}
	}, [eventInfo?.proposalRequests])

	const otherProposalRequests =
		eventInfo?.proposalRequests?.filter((pr: ProposalRequest) =>
			[
				ProposalRequestStatusMap.Active?.key,
				ProposalRequestStatusMap.New?.key,
				ProposalRequestStatusMap.Received?.key,
				ProposalRequestStatusMap.Reviewed?.key
			].includes(pr.status as string)
		) ?? []

	const renderRFPCard = () => (
		<div className='card flex flex-col gap-2 p-4'>
			<div className='flex items-center justify-between gap-2'>
				<div className='whitespace-nowrap text-sm font-medium text-gray-900'>
					RFP Name
				</div>
				<div className='text-sm font-normal text-gray-600'>
					{eventInfo?.name}
				</div>
			</div>
			<div className='flex items-center justify-between gap-2'>
				<div className='text-sm font-medium text-gray-900'>Status</div>
				<div>
					<HSBadge
						color={
							EventPlanStatusMap[eventInfo?.status as EventPlanStatusKey]
								?.color ?? 'gray'
						}
						className='w-fit p-1 text-center'
					>
						{EventPlanStatusMap[eventInfo?.status as EventPlanStatusKey]
							?.label ?? eventInfo?.status}
					</HSBadge>
				</div>
			</div>
		</div>
	)

	const handleChangeContractedProposal = (
		hotelId: string,
		selected: boolean
	) => {
		setContractedProposals(previousState => ({
			...previousState,
			[hotelId]: {
				...previousState[hotelId],
				selected
			}
		}))
	}

	const handleContractEventPlan = () => {
		const selectedContractedProposals: Record<string, string> = {}
		for (const hotelId of Object.keys(contractedProposals)) {
			if (contractedProposals[hotelId].selected) {
				selectedContractedProposals[hotelId] =
					contractedProposals[hotelId].selectedDateKey
			}
		}

		const declineOtherProposals = Object.fromEntries(
			otherProposalRequests.map(c => [
				c.venueId,
				{
					id: c.id,
					venueId: c.venueId,
					venueName: c.venueName,
					status: c.status,
					plannerDeclinedReasons: [defaultDeclinedReason],
					removedReason: 'Contract Signed with another hotel'
				}
			])
		)

		signContract({
			eventPlanId: eventPlan.id ?? '',
			contractData: {
				contractedProposals: selectedContractedProposals,
				declineOtherProposals,
				declineComment
			}
		})
			.then(response => {
				setObject(response)
				onContractEventPlan?.()
				onCancelMarkContractSigned()
			})
			.catch((error: unknown) => {
				console.error(error)
			})
	}

	return (
		<HSDrawer
			open={showContractSignedDrawer}
			onClose={onCancelMarkContractSigned}
			position='right'
			style={{ width: '500px' }}
			noPadding
		>
			<Drawer.Header
				title='Mark as Contract Signed'
				titleIcon={() => null}
				className='px-4 pt-4'
			/>
			{isFetchingEventInfo ? (
				<div className='flex !h-32 items-center justify-center p-5'>
					<Loader />
				</div>
			) : (
				<>
					<div
						className='flex flex-col gap-8 px-6'
						style={{ height: 'calc(100vh - 10rem)' }}
					>
						{proposalRequestCount === 1 ? (
							<div className='text-sm font-normal text-gray-500'>
								Please confirm that you have signed a contract with{' '}
								<span className='text-gray-700'>
									{contractedProposals[venueId].venueName || ''}.
								</span>
							</div>
						) : (
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-normal text-gray-500'>
									Please select which hotel(s) you have signed a contract with:
								</div>

								<div className='flex flex-col gap-2'>
									{Object.keys(contractedProposals).length > 0 ? (
										Object.keys(contractedProposals)
											.sort((a, b) =>
												contractedProposals[a].venueName >
												contractedProposals[b].venueName
													? 1
													: -1
											)
											.map(k => (
												<div key={k} className='flex items-center gap-2'>
													<input
														type='checkbox'
														id={`hotel-${k}`}
														checked={contractedProposals[k].selected}
														onChange={event =>
															handleChangeContractedProposal(
																k,
																event.target.checked
															)
														}
														className='h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500'
													/>
													<label
														htmlFor={`hotel-${k}`}
														className='text-sm font-normal text-gray-700'
													>
														{contractedProposals[k].venueName}
													</label>
												</div>
											))
									) : (
										<div className='italic text-gray-500'>
											No hotels available to select
										</div>
									)}
								</div>
							</div>
						)}
						{(() => {
							const otherProposalCounts = {
								remove: otherProposalRequests.filter(pr =>
									['New', 'Received'].includes(pr.status as string)
								).length,
								decline: otherProposalRequests.filter(pr =>
									['Active', 'Reviewed'].includes(pr.status as string)
								).length
							}

							return (
								<>
									{otherProposalCounts.remove > 0 ? (
										<div className='rounded-md bg-blue-50 p-4 text-blue-700'>
											<div className='text-sm font-medium'>
												Hotels have not responded and will be removed from the
												RFP:
											</div>
											<div className='mt-1 text-sm font-normal'>
												{otherProposalRequests
													.filter(pr =>
														['New', 'Received'].includes(pr.status as string)
													)
													.map(pr => pr.venueName)
													.join(', ')}
											</div>
										</div>
									) : null}

									{otherProposalCounts.decline > 0 && (
										<div className='flex flex-col gap-2'>
											<div className='text-sm font-medium text-gray-900'>
												Provide comments to relay to the declined hotel
												{otherProposalRequests.length === 1 ? '' : 's'}:
											</div>
											<HSTextArea
												placeholder='Enter comment'
												value={declineComment}
												onChange={event =>
													setDeclineComment(event.target.value)
												}
												rows={8}
												color='light'
												disabled={otherProposalRequests.length === 0}
											/>
										</div>
									)}
								</>
							)
						})()}
						<div className='border-b' />

						{eventInfo?.id ? (
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-medium text-gray-900'>
									Stop Receiving New Proposals:
								</div>
								<div className='flex gap-8'>
									<HSRadioButton
										label='Yes'
										name='proposalStatus'
										value='yes'
										selectedValue={eventInfo.proposalsPaused ? 'yes' : 'no'}
										onChange={() =>
											mergeProperties({
												proposalsPaused: true,
												proposalsPausedBy: userProfile?.id,
												proposalsPausedAt: new Date().toISOString(),
												proposalsPausedComment: null
											})
										}
									/>
									<HSRadioButton
										label='No'
										name='proposalStatus'
										value='no'
										selectedValue={eventInfo.proposalsPaused ? 'yes' : 'no'}
										onChange={() =>
											mergeProperties({
												proposalsPaused: false,
												proposalsPausedBy: null,
												proposalsPausedAt: null,
												proposalsPausedComment: null
											})
										}
									/>
								</div>
							</div>
						) : null}
					</div>
					<div className='flex justify-center gap-4 p-5'>
						<HSButton
							className='w-full'
							color='light'
							onClick={onCancelMarkContractSigned}
							size='sm'
						>
							Cancel
						</HSButton>
						<HSButton
							className='w-full'
							color='primary'
							onClick={handleContractEventPlan}
							disabled={formInvalid || isSigningContract}
							size='sm'
						>
							{isSigningContract ? 'Processing...' : 'Mark as Contract Signed'}
						</HSButton>
					</div>
				</>
			)}
		</HSDrawer>
	)
}

export default EventPlanContractSignedDrawer
