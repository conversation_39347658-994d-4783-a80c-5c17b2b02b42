import HSModal from 'components/modal'
import HSButton from 'components/button'
import { Modal } from 'flowbite-react'
import type { ISupplierContact } from 'models/affiliateOrganizations'

interface DeleteRfpContactsProperties {
	open: boolean
	onClose: () => void
	onConfirm: () => void
	contact?: ISupplierContact
	selectedCount?: number
}

const DeleteRfpContacts = ({
	open,
	onClose,
	onConfirm,
	contact,
	selectedCount
}: DeleteRfpContactsProperties) => {
	const isMultipleDelete = selectedCount && selectedCount > 1
	const modalTitle = isMultipleDelete
		? `Delete ${selectedCount} RFP Contacts`
		: 'Delete RFP Contact'

	const modalMessage = isMultipleDelete
		? 'Are you sure you want to delete selected RFP Contacts?'
		: (contact
			? `Are you sure you want to delete ${contact.firstName} ${contact.lastName} (${contact.id})?`
			: '')

	return (
		<HSModal openModal={open} onClose={onClose} header={modalTitle} size='md'>
			<Modal.Body>
				<div className='flex flex-col gap-4'>
					<div className='text-lg text-gray-600'>{modalMessage}</div>
					<div className='text-gray-500'>This action cannot be undone.</div>
				</div>
			</Modal.Body>
			<Modal.Footer>
				<div className='flex w-full gap-3'>
					<HSButton color='light' onClick={onClose} className='flex-1'>
						Cancel
					</HSButton>
					<HSButton color='danger' onClick={onConfirm} className='flex-1'>
						{isMultipleDelete ? `Delete (${selectedCount})` : 'Delete'}
					</HSButton>
				</div>
			</Modal.Footer>
		</HSModal>
	)
}

export default DeleteRfpContacts
