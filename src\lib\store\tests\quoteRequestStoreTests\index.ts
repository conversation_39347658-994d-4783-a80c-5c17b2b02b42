/* eslint-disable import/prefer-default-export */
import type { QuoteRequest } from 'models/hotelier'

export const defaultQuoteRequest: QuoteRequest = {
	eventPlan: {
		id: 'event-123',
		roomBlocksRequired: false,
		meetingSpaceRequired: false,
		ownerName: null,
		responseRate: null,
		venueLocations: [],
		plannerName: '',
		chainIds: [],
		requestsResponded: null,
		requestsAwaiting: null,
		requestsProposed: null,
		requestsTotal: null,
		roomsValue: null,
		roomNights: null,
		foodAndBeverageValue: null,
		fastestResponseTimeTicks: null,
		totalResponseTimeTicks: null,
		proposalsByChain: [],
		eventPlanId: null,
		templateId: null,
		organizationId: null,
		organizationName: null,
		includeOrganizationDescription: null,
		itemType: null,
		rfpCode: null,
		isSiteSearch: null,
		name: null,
		contractSignatoryState: null,
		responsesDueDate: null,
		selectionDate: null,
		status: null,
		type: null,
		groupType: null,
		industryType: null,
		notes: null,
		description: null,
		groupProfile: null,
		successCriteria: null,
		dealBreakers: null,
		currencyCode: null,
		measurementSystemCode: null,
		platformSuggestions: null,
		planners: null,
		shortlists: null,
		location: null,
		latitude: null,
		longitude: null,
		startDate: '',
		endDate: null,
		startDayOfWeek: null,
		totalDays: null,
		alternateDates: null,
		datesAreFlexible: null,
		patternIsFlexible: null,
		allowDateSuggestions: null,
		selectedDateKey: null,
		patternDays: null,
		recurringEvent: null,
		reservationMethod: null,
		reservationMethods: null,
		paymentMethods: null,
		contractSigner: null,
		rfpOwner: null,
		shareEmailAddress: null,
		sharePhoneNumber: null,
		isSkipSearch: null,
		isSelfSearch: null,
		commissionable: false,
		rebateEligible: false,
		iataCode: null,
		iataAgency: null,
		isBookingFee: false,
		bookingFeeRate: null,
		estimatedSpend: null,
		estimatedCommissionRate: null,
		estimatedAttritionRate: null,
		estimatedCommission: null,
		estimatedCommissionDate: null,
		peakMeetingRoomsRequired: null,
		peakMeetingSpaceRequired: null,
		peakAttendees: null,
		largestMeetingSpaceRequired: null,
		physicalDistancingRequired: null,
		meetingSpaceComments: null,
		estimatedNumberOfAttendees: null,
		meetingSpaceBudget: null,
		requireNamedMeetingSpace: null,
		totalRoomsRequested: null,
		totalRoomTypes: null,
		peakRooms: null,
		totalRoomsBudget: null,
		estimatedNumberOfRooms: null,
		roomNightBudget: null,
		roomRateMax: null,
		roomRateMin: null,
		canRequestAttritionRate: null,
		requestAttritionRateDefault: null,
		requestAttritionRate: null,
		requestRebate: null,
		rebateRequestAmount: null,
		rebateRequestBasis: null,
		firstSubmitted: null,
		lastReportedResponseRate: null,
		lastReportedResponseRateDate: null,
		submitted: null,
		created: null,
		contracting: null,
		firstContractSigned: null,
		cancelled: null,
		cancelledReason: null,
		abandoned: null,
		abandonedReason: null,
		proposalsPaused: null,
		proposalsPausedAt: null,
		proposalsPausedBy: null,
		proposalsPausedComment: null,
		proposedValue: null,
		contractedValue: null,
		supportedBy: null,
		createdBy: null,
		lastModified: null,
		lastModifiedBy: null,
		deleted: null,
		deletedBy: null,
		isDeleted: null,
		latestBidDateTime: null,
		editingSentDateTime: null,
		editedSentDateTime: null,
		changeComments: null,
		eventHistories: null,
		eventHistoryComments: null,
		attachments: null,
		meetingSpaceRequests: null,
		foodAndBeverageRequests: null,
		roomBlockRequests: [
			{
				id: 'a438055c-e935-478b-bd3a-307d6c1466cc',
				name: 'Room Block #1',
				peakSize: 20,
				paymentMethod: 'individualsPay',
				allowPartialProposals: false,
				minimumPeakRooms: null,
				notes: null,
				roomTypeRequests: [
					{
						roomType: 0,
						budget: 0,
						budgetLow: null,
						roomNights: [
							{
								dayNumber: 0,
								roomsRequested: 10
							},
							{
								dayNumber: 1,
								roomsRequested: 20
							}
						],
						notes: null
					}
				],
				created: '2025-05-03T09:17:13+00:00'
			}
		],
		contractClauses: null,
		concessionRequests: null,
		proposalValues: null,
		proposalValuesAverage: null,
		siteSearch: null,
		supplierContacts: null,
		templateOptions: null,
		sourcingProfiles: null,
		attachmentContainer: null,
		proposalRequestStatuses: {},
		overdueForSelection: null,
		overdueForResponses: null,
		doNotCollectAttritionRate: null,
		doNotCollectCutOffDate: null,
		averageRoomRate: null,
		rfpValue: null,
		hasContractedValue: null,
		savingsValue: null,
		negotiatedValue: null,
		concessionsValue: null
	},
	organizationAddress: null,
	organizationAddress2: null,
	organizationCity: null,
	organizationState: null,
	organizationZip: null,
	organizationCountry: null,
	averageViews: null,
	averageResponseHours: null,
	averageRoomRate: null,
	averageFbRate: null,
	selectedProposal: {
		name: null,
		averageRoomRate: null,
		foodAndBeverage: null
	},
	totalSubmittedRequests: null,
	proposalRequest: null,
	summaryProposalRequest: null,
	totalRequests: null,
	totalNewRequests: null,
	totalActiveRequests: null,
	totalDeclinedRequests: null,
	firstOpened: null,
	lastOpened: null,
	totalViews: null,
	competingBids: null,
	currentBid: {
		rebateRequestApproved: false,
		proposalDates: [],
		roomRates: [],
		perRoomFees: 0,
		rebateAmount: 0,
		bidExpirationDate: '',
		cutOffDate: '',
		isPromotionApplied: false,
		rebateRequestAmount: 0,
		meetingRates: [],
		serviceChargeRate: 0
	},
	name: '',
	effectiveStatus: null,
	rfpCode: null,
	ownerName: null,
	venueName: null,
	rfpValue: null,
	eventPlanId: null,
	isAdmin: false
}
