import PageLoader from 'components/pageLoader'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { useParams } from 'react-router-dom'
import HotelDetail from 'components/hotel/detail'
import hotelDetailStore from 'components/hotel/detail/store'
import { useEffect } from 'react'

const PlannerComparableHotels = () => {
	const { infoType: venueId } = useParams()
	const { eventInfo } = eventInfoStore()
	const { setGoBackLabel, setGoBackUrl, clearStore, setShowAvailability } =
		hotelDetailStore()

	useEffect(() => {
		setGoBackLabel('Back to Comparable Hotels')
		setGoBackUrl(
			`/planner/event/${eventInfo?.id}/review-proposals/comparable-hotels`
		)
		setShowAvailability(true)
		return () => {
			clearStore()
		}
	}, [
		clearStore,
		eventInfo?.id,
		setGoBackLabel,
		setGoBackUrl,
		setShowAvailability
	])

	return (
		<div>
			{eventInfo ? (
				<div
					className='overflow-auto bg-gray-50'
					style={{
						maxHeight: 'calc(100vh - 12rem)'
					}}
				>
					{venueId ? <HotelDetail venueId={venueId} /> : null}
				</div>
			) : (
				<PageLoader />
			)}
		</div>
	)
}

export default PlannerComparableHotels
