/* eslint-disable unicorn/no-keyword-prefix */
import HSButton from 'components/button'
import <PERSON>STextField from 'components/textField'
import { Drawer } from 'flowbite-react'
import { isEmail } from 'lib/helpers'
import { addSupplierContactDelegates } from 'lib/services/supplierContactDelegates.service'
import useUserProfileStore from 'lib/store/userProfileStore'
import { useState } from 'react'
import { toast } from 'react-toastify'

export interface IAddTeamMember {
	openAddNewTeamMemberDrawer: boolean
	setOpenAddNewTeamMemberDrawer: (open: boolean) => void
	onComplete?: () => void
}
const AddTeamMember = (properties: IAddTeamMember) => {
	const {
		openAddNewTeamMemberDrawer,
		setOpenAddNewTeamMemberDrawer,
		onComplete
	} = properties
	const { userProfile } = useUserProfileStore()
	const [newTeamMember, setNewTeamMember] = useState<
		| {
				firstName: string | undefined
				lastName: string | undefined
				email: string | undefined
		  }
		| undefined
	>()

	const onInviteTeamMember = () => {
		addSupplierContactDelegates(userProfile.id ?? '', {
			email: newTeamMember?.email ?? '',
			firstName: newTeamMember?.firstName ?? '',
			lastName: newTeamMember?.lastName ?? ''
		})
			.catch((error: unknown) => console.error(error))
			.finally(() => {
				setOpenAddNewTeamMemberDrawer(false)
				toast.success('Team member added successfully')
				onComplete?.()
			})
	}

	const isAddButtonValid = () => {
		const isValid =
			!!newTeamMember?.email && (isEmail(newTeamMember.email) as boolean)

		return isValid
	}

	return (
		<Drawer
			position='right'
			onClose={() => setOpenAddNewTeamMemberDrawer(false)}
			open={openAddNewTeamMemberDrawer}
			className='w-1/4'
		>
			<div className='flex h-full flex-col justify-between gap-4'>
				<div>
					<Drawer.Header title='Add New Team Member' titleIcon={() => null} />
					<Drawer.Items>
						<div className='flex flex-col gap-4'>
							<div>
								<div className='text-sm font-medium text-gray-900'>
									First Name
								</div>
								<HSTextField
									placeholder='Enter First Name'
									title='First Name'
									value={newTeamMember?.firstName ?? ''}
									onChange={event =>
										setNewTeamMember({
											firstName: event.target.value,
											lastName: newTeamMember?.lastName ?? '',
											email: newTeamMember?.email ?? ''
										})
									}
								/>
							</div>
							<div>
								<div className='text-sm font-medium text-gray-900'>
									Last Name
								</div>
								<HSTextField
									placeholder='Enter Last Name'
									title='Last Name'
									value={newTeamMember?.lastName ?? ''}
									onChange={event =>
										setNewTeamMember({
											...newTeamMember,
											firstName: newTeamMember?.firstName ?? '',
											lastName: event.target.value,
											email: newTeamMember?.email ?? ''
										})
									}
								/>
							</div>
							<div>
								<div className='text-sm font-medium text-gray-900'>Email</div>
								<HSTextField
									placeholder='Enter Email Address'
									title='Email'
									value={newTeamMember?.email ?? ''}
									onChange={event =>
										setNewTeamMember({
											...newTeamMember,
											firstName: newTeamMember?.firstName ?? '',
											lastName: newTeamMember?.lastName ?? '',
											email: event.target.value
										})
									}
								/>
							</div>
						</div>
					</Drawer.Items>
				</div>
				<div className='mt-2 flex items-center justify-between gap-2'>
					<HSButton
						color='light'
						className='grow'
						onClick={() => setOpenAddNewTeamMemberDrawer(false)}
					>
						Cancel
					</HSButton>
					<HSButton
						color='primary'
						className='grow'
						onClick={() => onInviteTeamMember()}
						disabled={!isAddButtonValid()}
					>
						Add Member
					</HSButton>
				</div>
			</div>
		</Drawer>
	)
}

export default AddTeamMember
