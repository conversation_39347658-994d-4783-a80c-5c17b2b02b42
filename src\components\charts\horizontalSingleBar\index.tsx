/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable react/no-array-index-key */
import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import { startCase } from 'es-toolkit'
import type React from 'react'

interface BarConfig {
	dataKey: string
	fill: string
}

interface HorizontalSingleBarProperties {
	data: unknown[]
	bars: BarConfig[]
	height?: number
	width?: string
	cornerRadius?: number
	showLabel?: boolean
	totalValue: number
	displayType?: 'percentage' | 'value' | 'none'
	barHeight?: number
}

const getNestedValue = (object: unknown, path: string): number =>
	path.split('.').reduce((accumulator, key) => {
		if (accumulator && typeof accumulator === 'object' && key in accumulator) {
			return (accumulator as Record<string, unknown>)[key]
		}
		return null
	}, object) as number

const HorizontalSingleBar: React.FC<HorizontalSingleBarProperties> = ({
	data,
	bars,
	height = 40,
	width = '100%',
	cornerRadius = 8,
	showLabel = false,
	totalValue,
	displayType = 'value',
	barHeight = 24
}) => (
	<div style={{ width, height: `${height}px` }} className='space-y-4'>
		{data.map((item, index) => (
			<div key={index}>
				{/* Optional Label */}
				{showLabel ? (
					<div className='mb-2 text-sm font-medium'>
						{(item as { name?: string }).name ?? 'Category'}
					</div>
				) : null}
				{/* Stacked Bar */}
				<div
					className='relative w-full overflow-hidden bg-gray-200'
					style={{
						height: `${barHeight}px`,
						borderRadius: `${cornerRadius}px`
					}}
				>
					{bars.map((bar, barIndex) => {
						const barValue = getNestedValue(item, bar.dataKey) || 0
						const barWidth = `${((barValue / totalValue) * 100).toFixed(2)}%`

						return (
							<TooltipComponent
								key={barIndex}
								content={`${startCase(bar.dataKey)}: ${
									displayType === 'value'
										? `${((barValue / totalValue) * 100).toFixed(0)}%`
										: barValue
								}`}
								mouseTrail
							>
								<div
									className='absolute left-0 top-0 h-full'
									style={{
										width: barWidth,
										backgroundColor: bar.fill,
										left: `${bars
											.slice(0, barIndex)
											.reduce(
												(accumulator, previousBar) =>
													accumulator +
													((getNestedValue(item, previousBar.dataKey) || 0) /
														totalValue) *
														100,
												0
											)}%`
									}}
								>
									{displayType === 'none' || barValue === 0 ? null : (
										<span className='absolute inset-0 flex items-center justify-center text-xs font-medium text-black'>
											{displayType === 'percentage'
												? `${((barValue / totalValue) * 100).toFixed(0)}%`
												: barValue}
										</span>
									)}
								</div>
							</TooltipComponent>
						)
					})}
				</div>
			</div>
		))}
	</div>
)

export default HorizontalSingleBar
