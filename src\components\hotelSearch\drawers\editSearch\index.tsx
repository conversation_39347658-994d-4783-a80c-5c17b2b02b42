import { useState } from 'react'
import <PERSON><PERSON>rawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import <PERSON>ST<PERSON><PERSON><PERSON>ield from 'components/textField'
import HSTextArea from 'components/textarea'
import HSButton from 'components/button'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { updateEvent } from 'lib/services/event.service'

interface EditHotelSearchProperties {
	onClose: (eventPlan?: EventPlan | null) => void
	siteSearch: EventPlan
}

const EditHotelSearch = (properties: EditHotelSearchProperties) => {
	const { onClose, siteSearch } = properties
	const [name, setName] = useState(siteSearch.name ?? '')
	const [description, setDescription] = useState(siteSearch.description ?? '')

	const handleUpdate = async () => {
		let updatedEventPlan: EventPlan | null = null
		if (name !== siteSearch.name) {
			updatedEventPlan = await updateEvent(siteSearch.id ?? '', {
				action: { type: 'setProperty' },
				value: {
					name: 'name',
					value: name
				}
			})
		}
		if (description !== siteSearch.description) {
			updatedEventPlan = await updateEvent(siteSearch.id ?? '', {
				action: { type: 'setProperty' },
				value: {
					name: 'description',
					value: description
				}
			})
		}
		onClose(updatedEventPlan)
	}

	return (
		<HSDrawer
			open
			onClose={onClose}
			position='right'
			style={{ width: '30rem' }}
		>
			<Drawer.Header title='Edit Search' titleIcon={() => null} />

			<Drawer.Items
				className='flex flex-col gap-8 overflow-auto'
				style={{
					height: 'calc(100vh - 9rem)'
				}}
			>
				<div className='flex flex-col gap-2'>
					<HSTextField
						label='Search Name'
						placeholder='Enter search name'
						value={name}
						onChange={event_ => setName(event_.target.value)}
					/>
				</div>

				<div className='flex flex-col gap-2'>
					<HSTextArea
						label='Description'
						placeholder='Write description here...'
						value={description}
						onChange={event_ => setDescription(event_.target.value)}
						rows={6}
					/>
				</div>
			</Drawer.Items>

			<div className='flex items-center justify-end'>
				<div className='flex w-full gap-4'>
					<HSButton color='light' className='flex-1' onClick={() => onClose()}>
						Cancel
					</HSButton>
					<HSButton color='primary' className='flex-1' onClick={handleUpdate}>
						Apply
					</HSButton>
				</div>
			</div>
		</HSDrawer>
	)
}

export default EditHotelSearch
