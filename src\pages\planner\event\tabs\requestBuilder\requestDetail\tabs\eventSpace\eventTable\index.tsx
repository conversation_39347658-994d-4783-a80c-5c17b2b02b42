/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
/* eslint-disable unicorn/no-array-reduce */
import HSButton from 'components/button'
import HSDropdownButton from 'components/dropdown'
import {
	calculateSortIndex,
	updateSortIndex
} from 'components/event/planner/meetingSpaceRequests'
import HSSelect from 'components/select'
import HSTextField from 'components/textField'
import { format } from 'date-fns'
import { formatNumber, numberMask } from 'lib/helpers'
import { roomLayoutOptions } from 'lib/helpers/roomLayouts'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { renderTimeRange, renderDetails } from '../templates'
import type { IMeetingSpaceRequest } from 'models/proposalResponseMonitor'
import { useCallback, useState } from 'react'
import { getDefaultMeetingSpaceRequest } from 'components/event/defaults'
import analytics from 'lib/analytics/segment/load'
import useAttachmentsStore from 'lib/store/attachmentsStore'
import AddEditMeetingSpaceRequestDrawer from '../drawers/AddEditMeetingSpaceRequest'
import { v4 as uuid } from 'uuid'
import meetingSpaceRequestStore from '../drawers/AddEditMeetingSpaceRequest/store'
import useEventSpaceHooks from '../hooks/eventSpaceHooks'
import { Table } from 'flowbite-react'
import { tableTheme } from 'pages/hotelier/proposal/tabs/builder/tabs/roomRate/rateEditor'
import HSCheckbox from 'components/checkbox'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faGripDotsVertical } from '@fortawesome/pro-regular-svg-icons'
import { sortMeetingSpaceRequests } from 'lib/helpers/meetingSpaceRequests'
import './index.css'

interface EventTableProperties {
	availableDates: Date[]
}

interface ExtendedMeetingSpaceRequest extends IMeetingSpaceRequest {
	sortIndex: number | null
}

const copyTargets = {
	previous: -1,
	all: 0,
	following: 1
}

const EventTable = (properties: EventTableProperties) => {
	const { availableDates } = properties
	const { eventInfo, disableFields, setProperty, mergeProperties } =
		eventInfoStore()
	const { attachments } = useAttachmentsStore()
	const { setMeetingSpaceRequest } = meetingSpaceRequestStore()
	const { onChangeMsrTime, onChange, onCopyAttachments, onDeleteAttachments } =
		useEventSpaceHooks()

	const [selectedRequests, setSelectedRequests] = useState<
		IMeetingSpaceRequest[]
	>([])
	const [droppingDayNumber, setDroppingDayNumber] = useState(-1)
	const [droppingIndex, setDroppingIndex] = useState(-1)
	const [dragging, setDragging] = useState<{
		meetingSpaceRequestId?: string
		dayNumber?: number
		index?: number
	}>({})

	const [
		showAddEditMeetingSpaceRequestDrawer,
		setShowAddEditMeetingSpaceRequestDrawer
	] = useState<{
		show: boolean
		activeTabId?: number
	}>({
		show: false
	})

	const clearSelection = (dateIndex: number, msrId?: string) => {
		if (selectedRequests.length > 0) {
			const updatedSelectedRequests = selectedRequests.filter(
				ssr => ssr.dayNumber !== dateIndex && ssr.id !== msrId
			)
			setSelectedRequests(updatedSelectedRequests)
		}
	}

	const deleteSelectedRequests = (sourceDayNumber: number) => {
		if (selectedRequests.length > 0 && eventInfo) {
			const ids = new Set(selectedRequests.map(ssr => ssr.id))

			mergeProperties({
				meetingSpaceRequests: eventInfo.meetingSpaceRequests?.filter(
					msr => !ids.has(msr.id)
				),
				foodAndBeverageRequests: eventInfo.foodAndBeverageRequests?.filter(
					fbr => !ids.has(fbr.meetingSpaceRequestId)
				)
			})
			setSelectedRequests([
				...selectedRequests.slice(0, sourceDayNumber),
				...selectedRequests.slice(sourceDayNumber + 1)
			])
			onDeleteAttachments(
				attachments.filter(a => ids.has(a.meetingSpaceRequestId)),
				true
			)
			analytics.track(`Meeting Space Requests Removed`)
			clearSelection(sourceDayNumber)
		}
	}

	const isRequestSelected = (msr: IMeetingSpaceRequest) =>
		selectedRequests.some(ssr => ssr.id === msr.id)

	const toggleSelectedRequest = (
		msr: IMeetingSpaceRequest | IMeetingSpaceRequest[]
	) => {
		const requests = Array.isArray(msr) ? msr : [msr]

		let updatedSelectedRequests = [...selectedRequests]

		for (const request of requests) {
			const { dayNumber, id } = request
			if (dayNumber !== null) {
				updatedSelectedRequests = isRequestSelected(request)
					? updatedSelectedRequests.filter(item => item.id !== id)
					: [...updatedSelectedRequests, { ...request }]
			}
		}
		setSelectedRequests(updatedSelectedRequests)
	}

	const addRequest = (dateIndex_: number) => {
		if (eventInfo) {
			setMeetingSpaceRequest(
				getDefaultMeetingSpaceRequest(
					dateIndex_,
					eventInfo.meetingSpaceRequests?.filter(
						msr => msr.dayNumber === dateIndex_
					).length ?? 0
				) as IMeetingSpaceRequest
			)
			setShowAddEditMeetingSpaceRequestDrawer({
				show: true
			})
		}
	}

	const copySelectedRequestsToDay = (
		sourceDayNumber: number,
		targetDayNumber: number | undefined,
		copyTarget?: number
	) => {
		if (eventInfo && selectedRequests.length > 0) {
			let copyIdMap: Record<string, string> = {}
			const msrCopies = []
			const fbrCopies = []
			if (targetDayNumber === undefined) {
				let d = 0
				while (d < availableDates.length) {
					const isPrevious = d < sourceDayNumber
					const isFollowing = d > sourceDayNumber
					if (
						(isPrevious && copyTarget !== copyTargets.following) ||
						(isFollowing && copyTarget !== copyTargets.previous)
						// Remove the condition that was copying to the same day
						// when "Copy to all other days" was selected
					) {
						const targetDay = d
						const thisDayCopyIdMap = selectedRequests.reduce(
							(p: Record<string, string>, ssr) => {
								p[ssr.id ?? ''] = uuid()
								return p
							},
							{}
						)
						msrCopies.push(
							...selectedRequests.map(ssr => ({
								...ssr,
								id: thisDayCopyIdMap[ssr.id ?? ''],
								dayNumber: targetDay,
								sortIndex: updateSortIndex(
									sourceDayNumber,
									ssr.sortIndex ?? 0,
									targetDay
								)
							}))
						)
						fbrCopies.push(
							...selectedRequests.flatMap(ssr =>
								eventInfo.foodAndBeverageRequests
									?.filter(fbr => fbr.meetingSpaceRequestId === ssr.id)
									.map(fbr => ({
										...fbr,
										id: uuid(),
										dayNumber: targetDay,
										meetingSpaceRequestId: thisDayCopyIdMap[ssr.id ?? '']
									}))
							)
						)
						copyIdMap = {
							...copyIdMap,
							...thisDayCopyIdMap
						}
					}
					d += 1
				}
			} else {
				copyIdMap = selectedRequests.reduce(
					(p: Record<string, string>, ssr) => {
						p[ssr.id ?? ''] = uuid()
						return p
					},
					{}
				)
				msrCopies.push(
					...selectedRequests.map(ssr => ({
						...ssr,
						id: copyIdMap[ssr.id ?? ''],
						dayNumber: targetDayNumber,
						sortIndex: updateSortIndex(
							sourceDayNumber,
							ssr.sortIndex ?? 0,
							targetDayNumber
						)
					}))
				)
				fbrCopies.push(
					...selectedRequests.flatMap(ssr =>
						eventInfo.foodAndBeverageRequests
							?.filter(fbr => fbr.meetingSpaceRequestId === ssr.id)
							.map(fbr => ({
								...fbr,
								id: uuid(),
								dayNumber: targetDayNumber,
								meetingSpaceRequestId: copyIdMap[ssr.id ?? '']
							}))
					)
				)
			}

			mergeProperties(
				{
					meetingSpaceRequests: [
						...(eventInfo.meetingSpaceRequests ?? []),
						...msrCopies
					],
					foodAndBeverageRequests: [
						...(eventInfo.foodAndBeverageRequests ?? []),
						...fbrCopies.filter(Boolean)
					]
				},
				false,
				{ meetingSpaceRequests: true, foodAndBeverageRequests: true }
			)
			onCopyAttachments(
				Object.keys(copyIdMap).map(k => ({
					sourceId: k,
					targetId: copyIdMap[k]
				}))
			)
			setSelectedRequests([
				...selectedRequests.slice(0, sourceDayNumber),
				...selectedRequests.slice(sourceDayNumber + 1)
			])
			analytics.track(`Meeting Space Requests Copied`)
			clearSelection(sourceDayNumber)
		}
	}

	const tableData = useCallback(
		(dateIndex: number) =>
			eventInfo?.meetingSpaceRequests
				?.filter(msr => msr.dayNumber === dateIndex)
				.sort((c, n) => (c.sortIndex ?? 0) - (n.sortIndex ?? 0)) ?? [],
		[eventInfo?.meetingSpaceRequests]
	)

	const onDragOver = (
		event:
			| React.DragEvent<HTMLTableSectionElement>
			| React.DragEvent<HTMLTableRowElement>,
		dayNumber: number,
		index: number
	) => {
		setDroppingDayNumber(dayNumber)
		setDroppingIndex(index)
		event.preventDefault()
	}

	const onMsrDrop = (
		event:
			| React.DragEvent<HTMLTableSectionElement>
			| React.DragEvent<HTMLTableRowElement>,
		dayNumber: number,
		index: number
	) => {
		if (eventInfo) {
			setDroppingDayNumber(-1)
			setDroppingIndex(-1)
			event.preventDefault()

			let calcIndex = index
			if (dragging.dayNumber === dayNumber) {
				if (dragging.index === index || dragging.index === index - 1) return

				calcIndex =
					(dragging.index ?? -1) > index || index === 0 ? index : index - 1
			}

			console.log({ from: dragging, to: { dayNumber, index, calcIndex } })
			// update sortIndexes
			const newMsr = {
				...eventInfo.meetingSpaceRequests?.find(
					msr => msr.id === dragging.meetingSpaceRequestId
				),
				dayNumber,
				sortIndex: calculateSortIndex(dayNumber, calcIndex)
			}
			// track which day(s) to change
			const days =
				dayNumber === dragging.dayNumber
					? [dayNumber]
					: [dayNumber, dragging.dayNumber]
			const a = days.reduce((accumulator: ExtendedMeetingSpaceRequest[], d) => {
				accumulator.push(
					...(eventInfo.meetingSpaceRequests
						?.filter(msr => msr.dayNumber === d && msr.id !== newMsr.id)
						.sort(sortMeetingSpaceRequests)
						.map((msr, index_) => ({
							...msr,
							sortIndex:
								d === newMsr.dayNumber && index_ >= calcIndex
									? calculateSortIndex(d, index_ + 1)
									: calculateSortIndex(d ?? -1, index_)
						})) ?? [])
				)
				if (d === newMsr.dayNumber) {
					accumulator.push(newMsr)
				}
				return accumulator
			}, [])

			setProperty(
				'meetingSpaceRequests',
				[
					...(eventInfo.meetingSpaceRequests?.filter(
						msr => !days.includes(msr.dayNumber ?? -1)
					) ?? []),
					...a
				],
				undefined,
				{
					meetingSpaceRequests: true,
					roomBlockRequests: false
				}
			)
			analytics.track(`Meeting Space Dragged and Dropped`)
		}
	}

	const emptyEventSpaceTemplate = (dateIndex: number, index: number) => (
		<Table.Row
			onDragEnter={event => onDragOver(event, dateIndex, index + 1)}
			onDragOver={event => onDragOver(event, dateIndex, index + 1)}
			onDragLeave={event => onDragOver(event, -1, -1)}
			onDragExit={event => onDragOver(event, -1, -1)}
			onDrop={event => {
				onMsrDrop(event, dateIndex, index + 1)
			}}
		>
			<Table.Cell
				className={`drag-target w-full ${droppingDayNumber === dateIndex && droppingIndex === 1 ? 'dropping' : ''}`}
				colSpan={7}
			>
				<div className='flex h-16 items-center justify-center text-gray-400'>
					No Event Space
				</div>
			</Table.Cell>
		</Table.Row>
	)

	const renderHeader = (label: string) => (
		<th className='px-2 py-1.5 text-left text-sm font-semibold text-gray-500'>
			{label}
		</th>
	)

	return eventInfo
		? availableDates.map((date, dateIndex) => (
				<div className='flex flex-col gap-2 p-6' key={dateIndex}>
					<div className='flex items-center justify-between gap-4'>
						<div className='flex flex-col'>
							<div className='text-lg font-semibold text-gray-900'>
								Day {dateIndex + 1}
							</div>
							<div className='flex gap-2'>
								<div className='text-sm font-normal text-gray-500'>
									{format(date, 'MMM dd (eee)')}
								</div>
								<div className='border-r' />
								<div className='text-xs font-normal text-gray-400'>
									Total Rooms Required:
								</div>
								<div className='text-xs font-normal text-gray-600'>
									{formatNumber(
										eventInfo.meetingSpaceRequests?.filter(
											msr =>
												msr.dayNumber === dateIndex && !msr.excludeFromTotals
										).length
									)}
								</div>
								<div className='border-r' />
								<div className='text-xs font-normal text-gray-400'>
									Total Space Required:
								</div>
								<div className='text-xs font-normal text-gray-600'>
									{formatNumber(
										eventInfo.meetingSpaceRequests
											?.filter(
												msr =>
													msr.dayNumber === dateIndex && !msr.excludeFromTotals
											)
											.reduce((a, c) => a + (c.areaTotal || 0), 0)
									)}{' '}
									ft<sup>2</sup>
								</div>
							</div>
						</div>
						<div className='flex items-center gap-2'>
							<HSDropdownButton
								disabled={
									eventInfo.meetingSpaceRequests?.filter(
										msr => msr.dayNumber === dateIndex
									).length === 0
								}
								items={
									selectedRequests.some(sr => sr.dayNumber === dateIndex)
										? [
												dateIndex > 0
													? {
															id: '0',
															item: 'Copy to previous day',
															clickFunction: () => {
																copySelectedRequestsToDay(
																	dateIndex,
																	dateIndex - 1
																)
															}
														}
													: null,
												dateIndex > 1
													? {
															id: '1',
															item: 'Copy to all previous days',
															clickFunction: () => {
																copySelectedRequestsToDay(
																	dateIndex,
																	undefined,
																	copyTargets.previous
																)
															}
														}
													: null,
												{
													id: '2',
													item: 'Copy to same day',
													clickFunction: () => {
														copySelectedRequestsToDay(dateIndex, dateIndex)
													}
												},
												dateIndex < availableDates.length - 1
													? {
															id: '3',
															item: 'Copy to next day',
															clickFunction: () => {
																copySelectedRequestsToDay(
																	dateIndex,
																	dateIndex + 1
																)
															}
														}
													: null,
												dateIndex < availableDates.length - 1
													? {
															id: '4',
															item: 'Copy to all other days',
															clickFunction: () => {
																copySelectedRequestsToDay(
																	dateIndex,
																	undefined,
																	copyTargets.all
																)
															}
														}
													: null,
												{
													id: '5',
													item: 'Copy to all following days',
													clickFunction: () => {
														copySelectedRequestsToDay(
															dateIndex,
															undefined,
															copyTargets.following
														)
													}
												},
												{
													id: '6',
													item: 'Delete',
													clickFunction: () => {
														deleteSelectedRequests(dateIndex)
													}
												}
											]
										: [
												{
													id: '7',
													item: 'Select items below to enable Actions',
													clickFunction: () => {
														// Do nothing
													}
												}
											]
								}
								label='Actions'
								color='light'
							/>
							<HSButton
								onClick={() => addRequest(dateIndex)}
								disabled={disableFields}
							>
								+ Add Room to Day {dateIndex + 1}
							</HSButton>
						</div>
					</div>
					<div className='flex w-full flex-col rounded-lg border border-gray-200'>
						<Table theme={tableTheme}>
							<thead>
								<tr
									className={`drag-target bg-gray-50 px-2 py-1.5 ${droppingDayNumber === dateIndex && droppingIndex === 0 ? 'dropping' : ''}`}
									onDragEnter={event => onDragOver(event, dateIndex, 0)}
									onDragOver={event => onDragOver(event, dateIndex, 0)}
									onDragLeave={event => onDragOver(event, -1, -1)}
									onDragExit={event => onDragOver(event, -1, -1)}
									onDrop={event => onMsrDrop(event, dateIndex, 0)}
								>
									<th
										className='w-8 rounded-tl-lg text-left'
										aria-label='Drag handle'
									>
										<div />
									</th>
									{renderHeader('Time Range')}
									{renderHeader('Function')}
									{renderHeader('Layout')}
									{renderHeader('Capacity')}
									{renderHeader('Details')}

									<th className='rounded-tr-lg px-2 py-1.5 text-left'>
										<HSCheckbox
											checked={
												selectedRequests.some(
													sr => sr.dayNumber === dateIndex
												) &&
												selectedRequests.filter(
													sr => sr.dayNumber === dateIndex
												).length === tableData(dateIndex).length
											}
											onChange={event => {
												if (event.target.checked) {
													setSelectedRequests([
														...selectedRequests,
														...tableData(dateIndex)
													])
												} else {
													const updatedSelectedRequests =
														selectedRequests.filter(
															sr => sr.dayNumber !== dateIndex
														)
													setSelectedRequests(updatedSelectedRequests)
												}
											}}
										/>
									</th>
								</tr>
							</thead>
							<Table.Body>
								{tableData(dateIndex).length === 0
									? emptyEventSpaceTemplate(dateIndex, 0)
									: tableData(dateIndex).map((item, index) => (
											// eslint-disable-next-line react/no-array-index-key
											<Table.Row
												key={item.id}
												className={`drag-target ${
													isRequestSelected(item)
														? 'bg-gray-100 text-gray-900'
														: 'text-gray-700 hover:bg-gray-50'
												} cursor-pointer transition duration-300 ease-in-out ${droppingDayNumber === dateIndex && droppingIndex === index + 1 ? 'dropping' : ''}`}
												onClick={() => {
													toggleSelectedRequest(item)
												}}
												onDragEnter={event =>
													onDragOver(event, dateIndex, index + 1)
												}
												onDragOver={event =>
													onDragOver(event, dateIndex, index + 1)
												}
												onDragLeave={event => onDragOver(event, -1, -1)}
												onDragExit={event => onDragOver(event, -1, -1)}
												onDrop={event => {
													onMsrDrop(event, dateIndex, index + 1)
												}}
											>
												<Table.Cell
													className='drag-handle w-8 text-left'
													draggable
													onDragStart={() =>
														setDragging({
															meetingSpaceRequestId: item.id ?? '',
															dayNumber: dateIndex,
															index
														})
													}
													onDragEnd={() => setDragging({})}
												>
													<FontAwesomeIcon
														icon={faGripDotsVertical}
														size='lg'
													/>
												</Table.Cell>
												<Table.Cell>
													{renderTimeRange(item, onChangeMsrTime, onChange)}
												</Table.Cell>
												<Table.Cell>
													<HSTextField
														value={item.name ?? ''}
														onChange={event => {
															onChange(item.id, {
																name: 'name',
																value: event.target.value,
																targetType: 'text'
															})
														}}
														isInvalid={!item.name}
														required
														placeholder='Enter Function'
													/>
												</Table.Cell>
												<Table.Cell>
													<HSSelect
														value={item.layoutStyle ?? ''}
														onChange={event => {
															onChange(item.id, {
																name: 'layoutStyle',
																value: event.target.value,
																targetType: 'text'
															})
														}}
														isInvalid={!item.layoutStyle}
														required
														name='layoutStyle'
													>
														<option value=''>Please choose...</option>
														{Object.entries(roomLayoutOptions)
															.toSorted((c, n) =>
																(c[1]?.name ?? '') > (n[1]?.name ?? '') ? 1 : -1
															)
															.filter(Boolean)
															.map(option => (
																<option key={option[0]} value={option[0]}>
																	{option[1]?.name ?? ''}
																</option>
															))}
													</HSSelect>
												</Table.Cell>
												<Table.Cell>
													<div className='flex w-36 flex-col gap-1'>
														<HSTextField
															value={formatNumber(item.capacity, '')}
															name='capacity'
															onChange={event =>
																onChange(item.id, {
																	name: 'capacity',
																	value: event.target.value.replaceAll(',', ''),
																	targetType: 'number'
																})
															}
															isInvalid={!item.capacity}
															disabled={disableFields}
															min={2}
															color={item.capacity ? 'light' : 'failure'}
															placeholder='Capacity'
															groupPlacement='left'
															groupItem={
																<div
																	className={`text-xs font-medium text-gray-600 ${
																		item.capacity
																			? 'text-gray-600'
																			: 'text-white'
																	}`}
																>
																	People
																</div>
															}
														/>
														{roomLayoutOptions[item.layoutStyle ?? '']
															?.additionalCapacityType ? (
															<HSTextField
																mask={numberMask}
																name='additionalCapacity'
																value={item.additionalCapacity || ''}
																disabled={disableFields}
																min={2}
																placeholder='Capacity'
																onChange={event =>
																	onChange(item.id, {
																		name: event.target.name,
																		value: event.target.value.replaceAll(
																			',',
																			''
																		),
																		targetType: 'number'
																	})
																}
																groupItem={
																	<div className='text-xs font-normal text-gray-600'>
																		{
																			roomLayoutOptions[item.layoutStyle ?? '']
																				?.additionalCapacityType
																		}
																	</div>
																}
																groupPlacement='left'
															/>
														) : null}
													</div>
												</Table.Cell>
												<Table.Cell>
													{renderDetails(
														item,
														attachments,
														() => {
															setShowAddEditMeetingSpaceRequestDrawer({
																show: true
															})
															setMeetingSpaceRequest(item)
														},
														eventInfo.foodAndBeverageRequests ?? [],
														activeTab => {
															setMeetingSpaceRequest(item)
															setShowAddEditMeetingSpaceRequestDrawer({
																show: true,
																activeTabId: activeTab
															})
														}
													)}
												</Table.Cell>
												<Table.Cell>
													<HSCheckbox
														checked={selectedRequests
															.filter(sr => sr.dayNumber === dateIndex)
															.some(msr => msr.id === item.id)}
														onChange={event => {
															if (event.target.checked) {
																setSelectedRequests([...selectedRequests, item])
															} else {
																setSelectedRequests(
																	selectedRequests.filter(
																		msr => msr.id !== item.id
																	)
																)
															}
														}}
													/>
												</Table.Cell>
											</Table.Row>
										))}
							</Table.Body>
						</Table>
					</div>

					{showAddEditMeetingSpaceRequestDrawer.show ? (
						<AddEditMeetingSpaceRequestDrawer
							onClose={() => {
								setShowAddEditMeetingSpaceRequestDrawer({
									show: false,
									activeTabId: undefined
								})
								setMeetingSpaceRequest(null)
							}}
							activeTabId={showAddEditMeetingSpaceRequestDrawer.activeTabId}
						/>
					) : null}
				</div>
			))
		: null
}

export default EventTable
