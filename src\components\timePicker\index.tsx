/* eslint-disable unicorn/no-nested-ternary */
import { faClock } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import type React from 'react'

export interface TimePickerProperties {
	value?: string
	onChange?: (value: string) => void
	placeholder?: string
	disabled?: boolean
	invalidMessage?: string // Optional invalid message
	isInvalid?: boolean // Explicit invalid state
}

const HSTimePicker: React.FC<TimePickerProperties> = (
	properties: TimePickerProperties
) => {
	const { onChange, invalidMessage, isInvalid, ...rest } = properties
	const showInvalid = Boolean(isInvalid)
	const message =
		showInvalid &&
		typeof invalidMessage === 'string' &&
		invalidMessage.length > 0
			? invalidMessage
			: showInvalid
				? 'Invalid time'
				: ''

	return (
		<div className='relative'>
			<div className='pointer-events-none absolute right-4 top-2.5'>
				<FontAwesomeIcon
					icon={faClock}
					className={`text-gray-600 transition-colors duration-150 ${showInvalid ? 'text-red-500' : ''}`}
				/>
			</div>
			<input
				type='time'
				id='time'
				className={`block w-full rounded-lg border p-2 text-sm leading-none focus:border-gray-500 focus:ring-gray-500 ${showInvalid ? 'border-red-500 bg-red-50 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 bg-gray-50 text-gray-900'}`}
				required
				onChange={event => {
					if (onChange) {
						onChange(event.target.value)
					}
				}}
				{...rest}
			/>
			{showInvalid && message ? (
				<p className='mt-1 text-xs text-red-600'>{message}</p>
			) : null}
		</div>
	)
}

export default HSTimePicker
