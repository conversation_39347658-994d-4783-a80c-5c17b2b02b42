import type { IConcessionEvent } from 'models/proposalResponseMonitor'
import { create } from 'zustand'

interface ConcessionRequestState {
	concessionRequests: IConcessionEvent[]
	saving: boolean
}

interface ConcessionRequestAction {
	setObject: (concessionRequests: IConcessionEvent[]) => void
	setSaving: (saving: boolean) => void
	addToArray: (concessionRequest: IConcessionEvent) => void
	removeFromArray: (concessionRequest: IConcessionEvent) => void
	replaceInArray: (concessionRequest: IConcessionEvent) => void
}

const concessionRequestStore = create<
	ConcessionRequestState & ConcessionRequestAction
>(set => ({
	concessionRequests: [],
	saving: false,
	setSaving: (saving: boolean) => set({ saving }),
	setObject: (concessionRequests: IConcessionEvent[]) => {
		set({ concessionRequests })
	},
	addToArray: value => {
		set(state => {
			const concessionRequests = [...state.concessionRequests, value]
			return { concessionRequests }
		})
	},
	removeFromArray: value => {
		set(state => {
			const concessionRequests = state.concessionRequests.filter(
				(item: IConcessionEvent) =>
					item.concessionRequest.id !== value.concessionRequest.id
			)
			return { concessionRequests }
		})
	},
	replaceInArray: value => {
		set(state => {
			const concessionRequests = state.concessionRequests.map(
				(item: IConcessionEvent) =>
					item.concessionRequest.id === value.concessionRequest.id
						? value
						: item
			)
			return { concessionRequests }
		})
	}
}))

export default concessionRequestStore
