/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable no-underscore-dangle */
/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable react/no-array-index-key */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable @typescript-eslint/max-params */
import { Table, Button } from 'flowbite-react'
import type { JSX } from 'react'
import {
	useMemo,
	useState,
	useRef,
	useEffect,
	forwardRef,
	useImperativeHandle,
	useCallback
} from 'react'
import type { GridColumn, GridRow, PaginationSetting } from './types'
import HSButton from 'components/button'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	faArrowDown,
	faArrowUp,
	faAngleDoubleLeft,
	faAngleLeft,
	faAngleRight,
	faAngleDoubleRight,
	faGripDotsVertical
} from '@fortawesome/pro-regular-svg-icons'
import HSDropdownButton from 'components/dropdown'
import HSCheckbox from 'components/checkbox'
import { faLock } from '@fortawesome/pro-solid-svg-icons'

interface RowSelectEventArguments<T> {
	data: T | T[]
}

interface RowDeselectEventArguments<T> {
	data: T | T[]
}

interface HSTableProperties<T> {
	columns: GridColumn<T>[]
	rows: T[]
	paginationSetting?: PaginationSetting
	defaultSort?: {
		field: string
		direction: 'asc' | 'desc' | null
	}
	rowHeight?: number
	allowPaging?: boolean
	allowSelection?: boolean
	selectionPlacement?: 'left' | 'right'
	rowSelected?: (event: RowSelectEventArguments<T>) => void
	rowDeselected?: (event: RowDeselectEventArguments<T>) => void
	allRowsSelected?: (selected: boolean) => void
	emptyRecordTemplate?: JSX.Element | (() => JSX.Element) | null
	noDataTemplate?: JSX.Element | (() => JSX.Element) | null
	borderStyling?: string | null
	onRowsReordered?: (rows: T[]) => void
	allowDragAndDrop?: boolean
	isLocked?: boolean
	rowDataBound?: {
		condition: (data: T) => boolean
		className: string
	}[]
}

const HSTable = forwardRef(
	<T,>(
		properties: HSTableProperties<T>,
		reference: React.Ref<{ resetSelection: () => void }>
	) => {
		const {
			columns,
			rows = [],
			paginationSetting = {
				rowsPerPage: [5, 10, 15]
			},
			defaultSort,
			rowHeight,
			allowPaging = true,
			allowSelection = false,
			selectionPlacement = 'left',
			rowSelected,
			rowDeselected,
			allRowsSelected,
			emptyRecordTemplate = null,
			noDataTemplate = null,
			borderStyling = null,
			onRowsReordered,
			allowDragAndDrop = false,
			isLocked = false,
			rowDataBound = []
		} = properties

		const [currentPage, setCurrentPage] = useState(1)
		const [rowsPerPage, setRowsPerPage] = useState(
			paginationSetting.rowsPerPage[1]
		)
		const [sortConfig, setSortConfig] = useState<{
			field: string
			direction: 'asc' | 'desc' | null
		}>({ field: '', direction: null })
		const [showLeftShadow, setShowLeftShadow] = useState(false)
		const [showRightShadow, setShowRightShadow] = useState(false)
		const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set())
		const [allSelected, setAllSelected] = useState(false)
		const [columnWidths, setColumnWidths] = useState<Map<string, number>>(
			new Map()
		)
		const [draggedRowIndex, setDraggedRowIndex] = useState<number | null>(null)
		const [dragOverRowIndex, setDragOverRowIndex] = useState<number | null>(
			null
		)
		const [internalRows, setInternalRows] = useState<T[]>(rows)

		const tableReference = useRef<HTMLDivElement>(null)
		const headerReferences = useRef<Map<string, HTMLTableCellElement>>(
			new Map()
		)

		useEffect(() => {
			const handleScroll = () => {
				if (tableReference.current) {
					const { scrollLeft, scrollWidth, clientWidth } =
						tableReference.current
					setShowLeftShadow(scrollLeft > 0)
					setShowRightShadow(scrollLeft < scrollWidth - clientWidth - 1)
				}
			}

			const tableElement = tableReference.current
			if (tableElement) {
				tableElement.addEventListener('scroll', handleScroll)
				handleScroll()
			}

			if (defaultSort) {
				setSortConfig(defaultSort)
			}

			// Measure column widths after render
			const newWidths = new Map<string, number>()
			for (const [key, element] of headerReferences.current.entries()) {
				newWidths.set(key, element.offsetWidth)
			}
			setColumnWidths(newWidths)

			return () => {
				if (tableElement) {
					tableElement.removeEventListener('scroll', handleScroll)
				}
			}
		}, [defaultSort, columns])

		useEffect(() => {
			setInternalRows(rows)
		}, [rows])

		useImperativeHandle(reference, () => ({
			resetSelection: () => {
				setSelectedRows(new Set())
				setAllSelected(false)
			}
		}))

		const getRowDataBound = useCallback(
			(row: T): string => {
				const condition = rowDataBound.find(cond => cond.condition(row))
				return condition ? condition.className : ''
			},
			[rowDataBound]
		)

		const handleSort = (field: string) => {
			setSortConfig(previousSort => ({
				field,
				direction:
					previousSort.field === field && previousSort.direction === 'asc'
						? 'desc'
						: 'asc'
			}))
			setCurrentPage(1)
		}

		const handleDragStart = (rowIndex: number) => {
			setDraggedRowIndex(rowIndex)
		}

		const handleDragOver = (
			rowIndex: number,
			event: React.DragEvent<HTMLTableRowElement>
		) => {
			event.preventDefault()
			setDragOverRowIndex(rowIndex)
		}

		const handleDropRow = (rowIndex: number) => {
			if (draggedRowIndex === null || draggedRowIndex === rowIndex) return
			const updatedRows = [...internalRows]
			const [removed] = updatedRows.splice(draggedRowIndex, 1)
			updatedRows.splice(rowIndex, 0, removed)
			setInternalRows(updatedRows)
			setDraggedRowIndex(null)
			setDragOverRowIndex(null)
			if (onRowsReordered) {
				onRowsReordered(updatedRows)
			}
		}

		const handleDragEnd = () => {
			setDraggedRowIndex(null)
			setDragOverRowIndex(null)
		}

		const sortedRows = useMemo(() => {
			if (!sortConfig.direction || !sortConfig.field) return internalRows

			return [...internalRows].sort((a, b) => {
				const aValue = a[sortConfig.field as keyof T]
				const bValue = b[sortConfig.field as keyof T]

				if (typeof aValue === 'number' && typeof bValue === 'number') {
					return sortConfig.direction === 'asc'
						? aValue - bValue
						: bValue - aValue
				}

				const aString = String(aValue)
				const bString = String(bValue)
				if (sortConfig.direction === 'asc') {
					return aString.localeCompare(bString)
				}
				return bString.localeCompare(aString)
			})
		}, [internalRows, sortConfig.direction, sortConfig.field])

		const paginatedRows = useMemo(() => {
			if (!allowPaging) return sortedRows
			const startIndex = (currentPage - 1) * rowsPerPage
			const endIndex = startIndex + rowsPerPage
			return sortedRows.slice(startIndex, endIndex)
		}, [allowPaging, currentPage, rowsPerPage, sortedRows])

		const totalPages = useMemo(
			() => Math.ceil(sortedRows.length / rowsPerPage),
			[sortedRows.length, rowsPerPage]
		)

		const handleRowToggle = (rowIndex: number, row: T) => {
			const newSelectedRows = new Set(selectedRows)
			const globalIndex = (currentPage - 1) * rowsPerPage + rowIndex

			if (newSelectedRows.has(globalIndex)) {
				newSelectedRows.delete(globalIndex)
				rowDeselected?.({ data: row })
				setAllSelected(false)
			} else {
				newSelectedRows.add(globalIndex)
				rowSelected?.({ data: row })
				if (newSelectedRows.size === paginatedRows.length) {
					setAllSelected(true)
				}
			}
			setSelectedRows(newSelectedRows)
		}

		const handleSelectAll = () => {
			const newSelectedRows = new Set<number>()
			if (allSelected) {
				rowDeselected?.({ data: paginatedRows })
			} else {
				for (const [index] of paginatedRows.entries()) {
					const globalIndex = (currentPage - 1) * rowsPerPage + index
					newSelectedRows.add(globalIndex)
				}
				rowSelected?.({ data: paginatedRows })
			}
			setSelectedRows(newSelectedRows)
			setAllSelected(!allSelected)
			allRowsSelected?.(!allSelected)
		}

		const { leftFrozenColumns, rightFrozenColumns, regularColumns } =
			useMemo(() => {
				let cols = columns.filter(col => col.visible !== false)
				if (allowSelection) {
					const selectionColumn: GridColumn<T> = {
						field: 'selection',
						headerText: '',
						width: 40,
						render: (row: T, index?: number) => {
							const globalIndex =
								(currentPage - 1) * rowsPerPage + Number(index)
							return (
								<HSCheckbox
									color='primary'
									checked={selectedRows.has(globalIndex)}
									onChange={() => handleRowToggle(Number(index), row)}
									disabled={rows.length === 0}
								/>
							)
						}
					}

					cols =
						selectionPlacement === 'left'
							? [selectionColumn, ...cols]
							: [...cols, selectionColumn]
				}
				const result = cols.reduce(
					(accumulator, col) => {
						if (col.freeze === 'left') {
							accumulator.leftFrozenColumns.push(col)
						} else if (col.freeze === 'right') {
							accumulator.rightFrozenColumns.push(col)
						} else {
							accumulator.regularColumns.push(col)
						}
						return accumulator
					},
					{
						leftFrozenColumns: [] as GridColumn<T>[],
						rightFrozenColumns: [] as GridColumn<T>[],
						regularColumns: [] as GridColumn<T>[]
					}
				)

				return result
				// eslint-disable-next-line react-hooks/exhaustive-deps
			}, [
				columns,
				allowSelection,
				selectedRows,
				currentPage,
				rowsPerPage,
				allSelected
			])

		const getLeftPosition = (index: number) => {
			let totalWidth = 0
			for (let index_ = 0; index_ < index; index_ += 1) {
				const colKey = `${leftFrozenColumns[index_].field}-left-${index_}`
				totalWidth +=
					columnWidths.get(colKey) ||
					(leftFrozenColumns[index_].width
						? Number.parseInt(String(leftFrozenColumns[index_].width), 10)
						: 150)
			}
			return `${totalWidth}px`
		}

		const getRightPosition = (index: number) => {
			let totalWidth = 0
			for (let index_ = 0; index_ < index; index_ += 1) {
				const colKey = `${rightFrozenColumns[index_].field}-right-${index_}`
				totalWidth +=
					columnWidths.get(colKey) ||
					(rightFrozenColumns[index_].width
						? Number.parseInt(String(rightFrozenColumns[index_].width), 10)
						: 150)
			}
			return `${totalWidth}px`
		}

		const renderCell = (
			row: T,
			column: GridColumn<T>,
			index: number,
			position: 'left' | 'right' | 'regular',
			rowIndex: number
		) => {
			const baseClasses =
				'text-sm font-normal text-gray-600 dark:text-gray-100 whitespace-nowrap relative group-hover:bg-gray-100 dark:group-hover:bg-gray-700 transition-colors'
			const stickyClasses =
				position === 'regular'
					? ''
					: `sticky bg-white dark:bg-gray-900 z-30 border-b-2 border-gray-100 last:border-b-0 ${
							position === 'left' && showLeftShadow
								? 'grid-shadow-right'
								: position === 'right' && showRightShadow
									? 'grid-shadow-left'
									: ''
						}`

			const { render, field, width, clipMode = 'none' } = column

			const cellContent = render
				? render(row, rowIndex)
				: (row as GridRow)[field]

			const highlightRow = getRowDataBound(row)

			return (
				<td
					key={`${position}-${index}`}
					className={`${baseClasses} ${stickyClasses} ${highlightRow} `}
					style={{
						...(width ? { width } : {}),
						...(position === 'left' && { left: getLeftPosition(index) }),
						...(position === 'right' && { right: getRightPosition(index) }),
						...(rowHeight && { height: rowHeight })
					}}
				>
					<div
						className={`${clipMode === 'ellipsis' ? 'w-[inherit]' : 'w-full'} px-2 py-[10px]`}
					>
						{cellContent as React.ReactNode}
					</div>
				</td>
			)
		}

		const renderHeaderCell = (
			column: GridColumn<T>,
			index: number,
			position: 'left' | 'right' | 'regular'
		) => {
			const {
				headerAlign = 'left',
				width,
				field,
				sortable = false,
				headerText,
				clipMode = 'none',
				headerTemplate
			} = column
			const baseClasses =
				'px-2 py-1 text-left text-xs text-gray-500 uppercase tracking-wider relative'
			let stickyClasses = 'sticky top-0 bg-gray-50 dark:bg-gray-800'

			stickyClasses +=
				position === 'regular'
					? ' z-20'
					: ` z-40 ${
							position === 'left' && showLeftShadow
								? 'grid-shadow-right'
								: position === 'right' && showRightShadow
									? 'grid-shadow-left'
									: ''
						}`

			const colKey = `${field}-${position}-${index}`

			return (
				<Table.HeadCell
					ref={element => {
						if (element) headerReferences.current.set(colKey, element)
						else headerReferences.current.delete(colKey)
					}}
					key={`${position}-${index}`}
					className={`${baseClasses} ${stickyClasses}`}
					style={{
						...(width ? { width } : {}),
						...(position === 'left' && { left: getLeftPosition(index) }),
						...(position === 'right' && { right: getRightPosition(index) })
					}}
				>
					{field === 'selection' ? (
						<HSCheckbox
							color='primary'
							checked={allSelected}
							onChange={handleSelectAll}
							disabled={rows.length === 0}
						/>
					) : (
						<div
							className={`flex ${clipMode === 'ellipsis' ? 'w-[inherit]' : 'w-full'} items-center gap-2`}
						>
							<HSButton
								color='text'
								size='xs'
								onClick={() => sortable && handleSort(field)}
								className={`block w-full cursor-pointer justify-start font-medium uppercase ${
									sortConfig.field === field ? 'text-gray-500' : 'text-gray-500'
								}`}
							>
								<div className='flex w-full flex-1 items-center justify-between gap-1'>
									<div
										className={`w-full whitespace-nowrap text-${headerAlign} font-semibold text-gray-500`}
									>
										{headerTemplate
											? typeof headerTemplate === 'function'
												? headerTemplate()
												: headerTemplate
											: headerText}
									</div>
									{sortable && sortConfig.field === field ? (
										<FontAwesomeIcon
											icon={
												sortConfig.direction === 'asc' ? faArrowUp : faArrowDown
											}
										/>
									) : null}
								</div>
							</HSButton>
						</div>
					)}
				</Table.HeadCell>
			)
		}

		// Pagination Navigation
		const handleFirstPage = () => setCurrentPage(1)
		const handleLastPage = () => setCurrentPage(totalPages)
		const handlePreviousPage = () =>
			setCurrentPage(previous => Math.max(previous - 1, 1))
		const handleNextPage = () =>
			setCurrentPage(previous => Math.min(previous + 1, totalPages))
		const handlePageChange = (page: number) => setCurrentPage(page)

		return (
			<div
				className={`flex w-full flex-col ${borderStyling ?? 'rounded-lg'} border border-gray-200`}
			>
				{/* Table Container */}
				<div
					className={`relative w-full ${isLocked ? 'overflow-hidden' : 'overflow-auto'}`}
					ref={tableReference}
				>
					<Table className='w-full text-sm'>
						<Table.Head>
							{allowDragAndDrop ? (
								<Table.HeadCell className='relative w-2 px-2 py-1 text-left text-xs uppercase tracking-wider text-gray-500'>
									<div />
								</Table.HeadCell>
							) : null}
							{leftFrozenColumns.map((column, index) =>
								renderHeaderCell(column, index, 'left')
							)}
							{regularColumns.map((column, index) =>
								renderHeaderCell(column, index, 'regular')
							)}
							{rightFrozenColumns.map((column, index) =>
								renderHeaderCell(column, index, 'right')
							)}
						</Table.Head>
						<Table.Body className='relative'>
							{paginatedRows.length > 0 ? (
								paginatedRows.map((row, rowIndex) => {
									const globalIndex = (currentPage - 1) * rowsPerPage + rowIndex
									const highlightClass = getRowDataBound(row)
									return (
										<tr
											key={rowIndex}
											className={`group border-b-2 border-gray-100 transition-colors last:border-b-0 ${
												selectedRows.has(globalIndex)
													? 'bg-gray-100 dark:bg-gray-700'
													: ''
											} ${dragOverRowIndex === rowIndex ? 'ring-2 ring-primary-500' : ''} ${highlightClass}`}
											draggable={allowDragAndDrop}
											onDragStart={() => handleDragStart(rowIndex)}
											onDragOver={event => handleDragOver(rowIndex, event)}
											onDrop={() => handleDropRow(rowIndex)}
											onDragEnd={handleDragEnd}
										>
											{allowDragAndDrop ? (
												<td className='relative items-center justify-center whitespace-nowrap px-2 py-[10px] text-sm font-normal text-gray-600 transition-colors group-hover:bg-gray-100 dark:text-gray-100 dark:group-hover:bg-gray-700'>
													<div>
														<FontAwesomeIcon
															icon={faGripDotsVertical}
															size='lg'
														/>
													</div>
												</td>
											) : null}
											{leftFrozenColumns.map((column, index) =>
												renderCell(row, column, index, 'left', rowIndex)
											)}
											{regularColumns.map((column, index) =>
												renderCell(row, column, index, 'regular', rowIndex)
											)}
											{rightFrozenColumns.map((column, index) =>
												renderCell(row, column, index, 'right', rowIndex)
											)}
										</tr>
									)
								})
							) : (
								<tr>
									<td
										colSpan={columns.length + (allowSelection ? 1 : 0)}
										className={`px-4 py-3 text-center text-gray-500 dark:text-gray-400 ${isLocked ? 'h-40' : ''}`}
									>
										{isLocked ? (
											<div className='absolute inset-0 flex items-center justify-center bg-opacity-50'>
												<div className='flex flex-col items-center justify-center gap-2'>
													<FontAwesomeIcon
														icon={faLock}
														className='text-2xl text-gray-500'
													/>
													<div className='text-sm text-gray-500'>
														Upgrade to unlock
													</div>
												</div>
											</div>
										) : noDataTemplate ? (
											typeof noDataTemplate === 'function' ? (
												noDataTemplate()
											) : (
												noDataTemplate
											)
										) : typeof emptyRecordTemplate === 'function' ? (
											emptyRecordTemplate()
										) : (
											emptyRecordTemplate || <div>No records found</div>
										)}
									</td>
								</tr>
							)}
						</Table.Body>
					</Table>
				</div>

				{allowPaging ? (
					<div className='flex items-center justify-between rounded-b-lg border-t border-gray-200 px-4 py-2 dark:border-gray-700 dark:bg-gray-800'>
						<div className='flex items-center space-x-1'>
							<Button.Group className='w-full'>
								<HSButton
									size='xxs'
									color='page'
									onClick={handleFirstPage}
									disabled={currentPage === 1}
									className='rounded-none rounded-l-sm'
								>
									<FontAwesomeIcon
										className='h-2 w-2 p-1'
										icon={faAngleDoubleLeft}
									/>
								</HSButton>
								<HSButton
									onClick={handlePreviousPage}
									disabled={currentPage === 1}
									size='xxs'
									color='page'
									className='rounded-none border-l-0'
								>
									<FontAwesomeIcon className='h-2 w-2 p-1' icon={faAngleLeft} />
								</HSButton>
								{(() => {
									const maxPagesToShow = 5
									let startPage = Math.max(
										1,
										currentPage - Math.floor(maxPagesToShow / 2)
									)
									const endPage = Math.min(
										totalPages,
										startPage + maxPagesToShow - 1
									)

									if (endPage - startPage + 1 < maxPagesToShow) {
										startPage = Math.max(1, endPage - maxPagesToShow + 1)
									}

									const pageButtons = []
									for (let page = startPage; page <= endPage; page += 1) {
										pageButtons.push(
											<HSButton
												key={page}
												onClick={() => handlePageChange(page)}
												className='rounded-none border-l-0 px-2 py-1 text-sm'
												size='xxs'
												color={currentPage === page ? 'primary' : 'page'}
											>
												{page}
											</HSButton>
										)
									}
									return pageButtons
								})()}
								<HSButton
									size='xxs'
									color='page'
									onClick={handleNextPage}
									disabled={currentPage === totalPages}
									className='rounded-none border-x-0'
								>
									<FontAwesomeIcon
										className='h-2 w-2 p-1'
										icon={faAngleRight}
									/>
								</HSButton>
								<HSButton
									size='xxs'
									color='page'
									onClick={handleLastPage}
									disabled={currentPage === totalPages}
									className='rounded-none rounded-r-sm'
								>
									<FontAwesomeIcon
										className='h-2 w-2 p-1'
										icon={faAngleDoubleRight}
									/>
								</HSButton>
							</Button.Group>
						</div>

						<div className='flex items-center gap-2 pr-10'>
							<span className='text-xs text-[#6b7280] dark:text-gray-400'>
								{`${currentPage} of ${totalPages} pages`}
							</span>
							<div className='h-8 border-l border-gray-200' />
							<div className='flex items-center gap-1'>
								<HSDropdownButton
									label={
										<div className='text-primary-600'>{rowsPerPage} Rows</div>
									}
									dismissOnClick
									color='text'
									size='xs'
									items={paginationSetting.rowsPerPage.map(value => ({
										id: value.toString(),
										item: value,
										clickFunction: () => {
											setRowsPerPage(Number(value))
											setCurrentPage(1)
										}
									}))}
									arrowClassName='text-primary-600'
								/>
								<div className='text-xs text-[#6b7280]'>Per Page</div>
							</div>
						</div>
					</div>
				) : null}
			</div>
		)
	}
) as <T>(
	properties: HSTableProperties<T> & {
		ref?: React.Ref<{ resetSelection: () => void }>
	}
) => JSX.Element

export default HSTable
