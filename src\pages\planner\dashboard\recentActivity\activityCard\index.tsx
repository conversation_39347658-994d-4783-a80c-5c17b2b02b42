/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import HSBadge from 'components/badge'
import HSButton from 'components/button'
import CopyText from 'components/copyText'
import HSTooltipWithEllipsis from 'components/tooltipEllipsis'
import { format, parseISO } from 'date-fns'
import { EventPlanStatusMap } from 'lib/helpers/statusMaps'
import type { StatusTransition } from 'models/hotels'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { useNavigate } from 'react-router-dom'

interface ActivityCardProperties {
	eventPlan: EventPlan
	statusTransition: StatusTransition
}

const ActivityCard = (properties: ActivityCardProperties) => {
	const { eventPlan, statusTransition } = properties
	const navigate = useNavigate()

	const getName = () => {
		if (statusTransition.createdByFirstName) {
			if (!statusTransition.createdByLastName) {
				return statusTransition.createdByFirstName
			}
			return `${statusTransition.createdByFirstName.charAt(0).toUpperCase()}. ${statusTransition.createdByLastName}`
		}
		return statusTransition.createdBy
	}

	return (
		<div className='flex flex-col gap-2'>
			<div>
				<div className='flex justify-between'>
					<HSButton
						color='text'
						className='text-left'
						onClick={async () => {
							if (!eventPlan.isDeleted) {
								navigate(`/planner/event/${eventPlan.eventPlanId}`)
							}
						}}
					>
						<div className='flex items-center justify-between gap-2'>
							<div className='w-60 overflow-hidden text-ellipsis'>
								<HSTooltipWithEllipsis
									className='text-xs font-bold text-primary-700 underline'
									content={
										eventPlan.name ||
										`New RFP Created ${format(parseISO(eventPlan.created ?? ''), 'PP')}`
									}
								/>
							</div>
							{eventPlan.isDeleted ? (
								<HSBadge color='failure'>Deleted</HSBadge>
							) : null}
						</div>
					</HSButton>
				</div>

				<div className='flex items-center gap-2'>
					<div className='text-xs font-normal text-gray-400'>Code:</div>
					<CopyText value={eventPlan.rfpCode} textSize='xs' />
				</div>
				<div className='flex justify-between gap-2 pt-2'>
					<div className='flex flex-col gap-2 pt-1'>
						<div className='flex items-center gap-2'>
							<div className='text-xs font-medium text-gray-700'>
								Status changed to
							</div>
							<HSBadge
								color={
									EventPlanStatusMap[statusTransition.toStatus]?.color ?? 'gray'
								}
								className='w-fit p-1 text-center'
							>
								{EventPlanStatusMap[statusTransition.toStatus]?.label ??
									statusTransition.toStatus}
							</HSBadge>
						</div>
						<div>
							<div className='flex items-center gap-2'>
								<span className='text-xs font-normal text-gray-400'>On:</span>
								<span className='whitespace-nowrap text-xs font-normal text-gray-600'>
									{statusTransition.created
										? format(statusTransition.created, 'PP')
										: ''}
								</span>
								<span className='text-xs font-normal text-gray-400'>
									(
									{statusTransition.created
										? format(statusTransition.created, 'EEEE')
										: ''}
									)
								</span>
							</div>
							<div className='flex items-center gap-2'>
								<span className='text-xs font-normal text-gray-400'>At:</span>
								<span className='text-xs font-normal text-gray-600'>
									{statusTransition.created
										? format(statusTransition.created, 'p')
										: ''}
								</span>
							</div>
						</div>
						<div className='flex items-center gap-2'>
							<span className='text-xs font-normal text-gray-400'>By:</span>
							<span className='text-xs font-normal text-gray-600'>
								<div className='w-fit 2xl:w-80'>
									<HSTooltipWithEllipsis content={getName()} />
								</div>
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}

export default ActivityCard
