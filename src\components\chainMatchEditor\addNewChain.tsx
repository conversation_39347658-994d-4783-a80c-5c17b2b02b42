import HSButton from 'components/button'
import H<PERSON>rawer from 'components/drawer'
import MultiSelectDropdownWithFilter from 'components/multiSelectDropdown'
import { Drawer } from 'flowbite-react'
import { useGetChains } from 'lib/services/chains.service'

interface IAddNewChainDrawer {
	show: boolean
	onClose: () => void
	onUpdate: (
		selectedChains: {
			label: string
			value: string
		}[]
	) => void
	selectedItems: string[]
	setSelectedItems: React.Dispatch<React.SetStateAction<string[]>>
}

const AddNewChainDrawer = (properties: IAddNewChainDrawer) => {
	const { onClose, show, onUpdate, selectedItems, setSelectedItems } =
		properties
	const { data: chains } = useGetChains()

	const chainOptions =
		chains?.map(chain => ({
			value: chain.id ?? '',
			label: chain.name ?? ''
		})) ?? []

	const onCreateClick = () => {
		const selectedChains = chainOptions
			.filter(option => selectedItems.includes(option.value))
			.map(option => ({ label: option.label, value: option.value }))
		onUpdate(selectedChains)
		onClose()
	}
	return (
		<div>
			<HSDrawer
				position='right'
				onClose={onClose}
				open={show}
				style={{ width: '400px' }}
			>
				<Drawer.Header titleIcon={() => null} title='Add New Chain' />
				<Drawer.Items
					style={{ height: 'calc(100vh - 10rem)' }}
					className='overflow-auto'
				>
					<MultiSelectDropdownWithFilter
						options={chainOptions}
						selectedItems={selectedItems}
						setSelectedItems={setSelectedItems}
						label='Select from existing'
					/>
				</Drawer.Items>
				<Drawer.Items>
					<div className='flex items-center gap-2'>
						<HSButton className='grow' color='light' onClick={onClose}>
							Cancel
						</HSButton>
						<HSButton className='grow' onClick={() => onCreateClick()}>
							Add Chains
						</HSButton>
					</div>
				</Drawer.Items>
			</HSDrawer>
		</div>
	)
}

export default AddNewChainDrawer
