import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { useState } from 'react'
import { suggestHotel } from 'lib/services/hotels.service'
import type { ProposalRequest } from 'models/proposalResponseMonitor'
import type { SiteSearch } from 'models/reporting'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import type { AxiosError } from 'axios'
import { toast } from 'react-toastify'
import { removeHotelFromEventPlan } from 'lib/services/event.service'

const handleRfpError = (error: AxiosError) => {
	let message = 'Error'
	if (error.response?.status) {
		switch (error.response.status) {
			case 409: {
				message =
					error.response.data === 'premium feature'
						? 'You must have an active paid subscription to add more hotels to this RFP.'
						: (error.response.data as string)
				break
			}

			default: {
				message = 'Authorization error.'
				break
			}
		}
		toast.error(message)
	}
}

const useRfpBuilder = () => {
	const { eventInfo, mergeProperties } = eventInfoStore()
	const [addBackProposalRequests, setAddBackProposalRequests] = useState<
		ProposalRequest[]
	>([])
	const [showAddBackProposalRequestModal, setShowAddBackProposalRequestModal] =
		useState(false)
	const [removeProposalRequests, setRemoveProposalRequests] = useState<
		ProposalRequest[]
	>([])
	const [showRemoveProposalRequestModal, setShowRemoveProposalRequestModal] =
		useState(false)

	const addRemoveProposalRequests = async (
		suggestHotels: ProposalRequest[],
		callback: () => void,
		isAdmin: boolean
	) => {
		if (suggestHotels[0].status === 'Removed') {
			setAddBackProposalRequests(suggestHotels)
			setShowAddBackProposalRequestModal(true)
		}

		let shouldAdd = false
		if (
			eventInfo?.proposalRequests?.find(
				pr => pr.venueId === suggestHotels[0].venueId
			) === undefined
		) {
			shouldAdd = true
		}

		if (shouldAdd || suggestHotels[0].status === 'Pending' || isAdmin) {
			if (
				eventInfo?.proposalRequests?.find(
					pr => pr.venueId === suggestHotels[0].venueId
				) === undefined
			) {
				suggestHotel(eventInfo?.id ?? '', suggestHotels)
					.then(
						async (response: {
							data: {
								proposalRequests: ProposalRequest[]
								siteSearch: SiteSearch | null
								supplierContacts: ISupplierContact[]
							}
						}) => {
							mergeProperties({
								proposalRequests: [...response.data.proposalRequests],
								siteSearch: { ...response.data.siteSearch },
								supplierContacts: [...response.data.supplierContacts]
							})
							if (typeof callback === 'function') {
								callback()
							}
						}
					)
					.then(() => toast.success('Successfully added to RFP'))
					.catch((error: unknown) => handleRfpError(error as AxiosError))
			} else {
				await removeHotelFromEventPlan(
					eventInfo.id ?? '',
					suggestHotels[0].venueId
				)
				mergeProperties({
					proposalRequests: eventInfo.proposalRequests.filter(
						pr => pr.venueId !== suggestHotels[0].venueId
					)
				})
			}
		} else {
			setRemoveProposalRequests(suggestHotels)
			setShowRemoveProposalRequestModal(true)
		}
	}

	return {
		addBackProposalRequests,
		addRemoveProposalRequests,
		showAddBackProposalRequestModal,
		removeProposalRequests,
		setRemoveProposalRequests,
		showRemoveProposalRequestModal,
		setShowRemoveProposalRequestModal
	}
}

export default useRfpBuilder
