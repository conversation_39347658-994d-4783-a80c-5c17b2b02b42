/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/max-params */
import { useMutation, useQuery } from '@tanstack/react-query'
import { intentSource } from 'lib/auth/config'
import api from 'lib/interceptor/axios.interceptor'
import type {
	ContractClauseEvent,
	IContractClauses
} from 'models/organizations'
import type { Member } from 'models/propertyPartner'
import type {
	EventPlan,
	IConcessionEvent,
	ITrailLimitStatus
} from 'models/proposalResponseMonitor'
import type { IRfpsWithEligibleHotel, VenueDetail } from 'models/venue'

const updatedSupportedBy = async (id: string, value: string) => {
	const response = await api.put(`/sourcing/api/events/plans/${id}/intent`, {
		action: { type: 'setProperty' },
		value: { name: 'supportedBy', value },
		source: intentSource
	})
	return response.data as EventPlan
}

export const updateEvent = async (
	eventPlanId: string,
	intent: { value: unknown; action: { type: string } }
) => {
	const response = await api.put(
		`/sourcing/api/events/plans/${eventPlanId}/intent`,
		{
			...intent,
			source: 'https://hopskip.com/web-client/2.0',
			value:
				intent.action.type === 'mergeProperties'
					? Object.keys(intent.value).map(k => ({
							name: k,
							value: intent.value[k]
						}))
					: intent.value
		}
	)
	return response.data as EventPlan
}

const createEventPlan = async (
	defaultEvent: EventPlan,
	meetingSpaceRequired: boolean,
	roomBlocksRequired: boolean
) => {
	const response = await api.post('/sourcing/api/events/plans', {
		...defaultEvent,
		meetingSpaceRequired,
		roomBlocksRequired
	})
	return response.data as EventPlan
}

const updateEventStatus = async (
	eventPlanId: string,
	status: string,
	data: {
		changeComment: string
		sendNotification: boolean
		plannerMessage: string
		hotelMessage: string
	}
) => {
	const response = await api.put(
		`/sourcing/api/events/admin/changeStatus/${eventPlanId}/${status}`,
		data
	)
	return response.data as EventPlan
}

const getActiveRfpsWithEligibleHotel = async (
	filter: Record<string, boolean | string> | null
) => {
	const response = await api.get(
		`/sourcing/api/events/admin/active${
			filter && Object.keys(filter).length > 0
				? `?${Object.keys(filter)
						.map(k => `${k}=${filter[k]}`)
						.join('&')}`
				: ''
		}`
	)

	const data = response.data as {
		eligibleMatches: Record<string, VenueDetail[]>
		eventPlans: EventPlan[]
	}

	return data.eventPlans.map((d: EventPlan) => ({
		...d,
		hotels: d.proposalRequests,
		eligibleMatches: data.eligibleMatches[d.id ?? '']
	})) as IRfpsWithEligibleHotel[]
}

const getConcessionsById = async (eventId: string) => {
	const response = await api.get(
		`/sourcing/api/events/concessionRequests/${eventId}`
	)
	return response.data as IConcessionEvent[]
}

const getTemplates = async (userProfileId: string) => {
	const response = await api.get(
		`/sourcing/api/events/templates/${userProfileId}`
	)
	return response.data as EventPlan[]
}

const deleteTemplate = async (templateId: string) => {
	const response = await api.delete(`/sourcing/api/events/plans/${templateId}`)
	return response.data as EventPlan
}

const getMyTemplates = async () => {
	const response = await api.get(`/sourcing/api/events/templates/me`)
	return response.data as EventPlan[]
}

const getTrailLimitStatus = async () => {
	const response = await api.get(`/sourcing/api/events/getlimitstatus`)
	return response.data as ITrailLimitStatus
}

const getEvents = async (filterText: string) => {
	const response = await api.get(
		`/sourcing/api/events/plans${filterText ? `?$filter=${filterText}` : ''}`
	)
	return response.data as EventPlan[]
}

const getItemsCount = async (templateId: string) => {
	const response = await api.get(
		`/sourcing/api/events/plans/${templateId}/itemCounts`
	)
	return response.data as Record<string, number>
}

const createEventCopy = async (
	templateId: string,
	operation: string,
	from: string,
	to: string,
	data: Record<string, Date | boolean | string | null>
) => {
	const response = await api.post(
		`/sourcing/api/events/plans/${templateId}/${operation}/${from}/${to}`,
		data
	)
	return response.data as EventPlan
}

const transferOwnership = async (
	eventPlanId: string,
	organizationId: string,
	transferContext: {
		owner: Member & { owner: true }
	}
) => {
	const response = await api.put(
		`/sourcing/api/events/admin/transferToOrganization/${eventPlanId}/${organizationId}`,
		{ data: transferContext }
	)
	return response.data as EventPlan
}

const assignOwnership = async (
	eventPlanId: string,
	assignOwnerContext: {
		newOwner: Member
	}
) => {
	const response = await api.post(
		`/sourcing/api/events/plans/${eventPlanId}/assignowner`,
		assignOwnerContext
	)
	return response.data as EventPlan
}

export const useCreateEventPlan = () =>
	useMutation({
		mutationKey: ['create-events/plan'],
		mutationFn: async ({
			defaultEvent,
			meetingSpaceRequired,
			roomBlocksRequired
		}: {
			defaultEvent: EventPlan
			meetingSpaceRequired: boolean
			roomBlocksRequired: boolean
		}) =>
			createEventPlan(defaultEvent, meetingSpaceRequired, roomBlocksRequired)
	})
export const useGetEventPlansById = () =>
	useMutation({
		mutationKey: ['events/plans'],
		mutationFn: async ({ id, value }: { id: string; value: string }) =>
			updatedSupportedBy(id, value)
	})

export const useUpdateEventStatus = () =>
	useMutation({
		mutationKey: ['admin/changeStatus'],
		mutationFn: async ({
			eventPlanId,
			status,
			data
		}: {
			eventPlanId: string
			status: string
			data: {
				changeComment: string
				sendNotification: boolean
				plannerMessage: string
				hotelMessage: string
			}
		}) => updateEventStatus(eventPlanId, status, data)
	})

export const useActiveRfpsWithEligibleHotel = (
	filter: Record<string, boolean | string> | null
) =>
	useQuery({
		queryKey: ['events/admin/active', filter],
		queryFn: async () => getActiveRfpsWithEligibleHotel(filter)
	})

export const useGetConcessionsByEventId = (eventId: string, enabled = true) =>
	useQuery({
		queryKey: ['concessionRequests-event', eventId],
		queryFn: async () => getConcessionsById(eventId),
		enabled
	})

export const useGetTemplates = (userProfileId: string, enabled = true) =>
	useQuery({
		queryKey: ['templates', userProfileId],
		queryFn: async () => getTemplates(userProfileId),
		enabled
	})

export const useDeleteTemplate = () =>
	useMutation({
		mutationKey: ['delete-template'],
		mutationFn: async (templateId: string) => deleteTemplate(templateId)
	})

export const useGetTrailLimitStatus = (enabled = true) =>
	useQuery({
		queryKey: ['trial-limit'],
		queryFn: async () => getTrailLimitStatus(),
		enabled
	})

export const useGetEvents = (filters?: string, enabled = true) =>
	useQuery({
		queryKey: ['events', filters],
		queryFn: async () => getEvents(filters ?? ''),
		enabled
	})

export const useGetMyTemplates = (enabled = true) =>
	useQuery({
		queryKey: ['my-templates'],
		queryFn: async () => getMyTemplates(),
		enabled
	})

export const useGetItemCount = (templateId: string, enabled = true) =>
	useQuery({
		queryKey: ['item-count', templateId],
		queryFn: async () => getItemsCount(templateId),
		enabled
	})

export const useCreateEventCopy = () =>
	useMutation({
		mutationKey: ['create-copy'],
		mutationFn: async ({
			templateId,
			operation,
			from,
			to,
			data
		}: {
			templateId: string
			operation: string
			from: string
			to: string
			data: Record<string, Date | boolean | string | null>
		}) => createEventCopy(templateId, operation, from, to, data)
	})

export const addExistingContractClauseToEvent = async (
	eventPlanId: string,
	contractClauseId: string,
	contractClause: IContractClauses
) => {
	const response = await api.put(
		`/sourcing/api/events/contractClauses/${eventPlanId}${contractClauseId ? `/${contractClauseId}` : ''}`,
		contractClause
	)
	return response.data as ContractClauseEvent
}

export const addNewContractClauseToEvent = async (
	eventPlanId: string,
	contractClause: IContractClauses
) => {
	const response = await api.post(
		`/sourcing/api/events/contractClauses/${eventPlanId}`,
		contractClause
	)
	return response.data as ContractClauseEvent
}

export const removeContractClauseFromEvent = async (
	eventPlanId: string,
	contractClauseId: string
) => {
	const response = await api.delete(
		`/sourcing/api/events/contractClauses/${eventPlanId}${contractClauseId ? `/${contractClauseId}` : ''}`
	)
	return response.data as ContractClauseEvent
}

const getContractClausesForEvent = async (eventId: string) => {
	const response = await api.get(
		`/sourcing/api/events/contractClauses/${eventId}`
	)
	return response.data as ContractClauseEvent[]
}

export const useGetContractClausesForEvent = (
	eventId: string,
	enabled = true
) =>
	useQuery({
		queryKey: ['contractClauses', eventId],
		queryFn: async () => getContractClausesForEvent(eventId),
		enabled
	})

export const removeHotelFromEventPlan = async (
	eventPlanId: string,
	venueId: string
) => {
	await api.delete(`/sourcing/api/events/plans/${eventPlanId}/${venueId}`)
}

export const removeEventPlan = async (eventPlanId: string) =>
	api.delete(`/sourcing/api/events/plans/${eventPlanId}`)

export const useTransferOwnership = () =>
	useMutation({
		mutationKey: ['transfer-ownership'],
		mutationFn: async ({
			eventPlanId,
			organizationId,
			data
		}: {
			eventPlanId: string
			organizationId: string
			data: {
				owner: Member & { owner: true }
			}
		}) => transferOwnership(eventPlanId, organizationId, data)
	})

export const useAssignOwnership = () =>
	useMutation({
		mutationKey: ['assign-ownership'],
		mutationFn: async ({
			eventPlanId,
			data
		}: {
			eventPlanId: string
			data: {
				newOwner: Member
			}
		}) => assignOwnership(eventPlanId, data)
	})

const getRateSet = async (
	eventPlanId: string,
	venueId: string,
	set: 'proposed' | 'contracted'
) => {
	const response = await api.get(
		`/sourcing/api/events/plans/${eventPlanId}/rateSets/${venueId}/${set}`
	)
	return response.data
}

export const useGetRateSet = (
	eventPlanId: string,
	venueId: string,
	set: 'proposed' | 'contracted',
	enabled = true
) =>
	useQuery({
		queryKey: ['rateSet', eventPlanId, venueId, set],
		queryFn: async () => getRateSet(eventPlanId, venueId, set),
		enabled
	})
