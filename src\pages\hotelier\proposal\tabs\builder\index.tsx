/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable unicorn/no-nested-ternary */
import type { TabsRef } from 'flowbite-react'
import { Tabs } from 'flowbite-react'
import { getTabs } from './helper'
import { useCallback, useEffect, useRef, useState } from 'react'
import type { ITab } from 'components/tab'
import { customTabComponentTheme } from 'components/tab'
import useQuoteRequestStore from 'lib/store/quoteRequestStore'
import useQuestionStore from 'lib/store/questionsStore'
import useConcessionRequestStore from 'lib/store/concessionRequests'
import useContractClauseStore from 'lib/store/contractClauses'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCircleExclamation } from '@fortawesome/pro-duotone-svg-icons'
import { UnitedStatesDollar, type ICurrency } from 'lib/helpers'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import HSCurrencyPicker from 'components/currencySelector'
import { faCheck } from '@fortawesome/pro-regular-svg-icons'
import HSBadge from 'components/badge'
import { format, parseISO } from 'date-fns'
import { useNavigate, useParams } from 'react-router-dom'
import HSTooltip from 'components/tooltip'

const ProposalBuilder = () => {
	const tabReference = useRef<TabsRef | null>(null)
	const { tab: tabPath, eventId, venueId, chapterName, infoType } = useParams()
	const navigate = useNavigate()
	const {
		errors,
		quoteRequest,
		setProperty,
		isSummary: isMarketAvailability
	} = useQuoteRequestStore()
	const { errors: questionErrors } = useQuestionStore()
	const { errors: concessionRequestErrors } = useConcessionRequestStore()
	const { errors: contractClauseErrors } = useContractClauseStore()
	const [activeTabId, setActiveTabId] = useState(0)
	const { currencies } = useCurrencyContext()
	const proposalRequest = isMarketAvailability
		? quoteRequest.summaryProposalRequest
		: quoteRequest.proposalRequest
	const onChange = useCallback(
		async (event: {
			target: {
				name: string
				value: string | number | boolean | null
				type: string
			}
		}) => {
			if (
				['Pending', 'New', 'Received'].includes(proposalRequest?.status ?? '')
			) {
				let v =
					event.target.type === 'number'
						? event.target.value || event.target.value === '0'
							? Number(event.target.value)
							: null
						: event.target.value
				if (
					event.target.name === 'isMeetingRateDependentOnDate' &&
					event.target.value === true
				) {
					const meetingRates = [
						...(proposalRequest?.currentBid?.meetingRates ?? [])
					]
					const missingMeetingRates =
						proposalRequest?.currentBid?.proposalDates
							?.filter(pd => !meetingRates.some(mr => mr.key === pd.key))
							.map(pd => ({
								key: format(parseISO(pd.startDate ?? ''), 'yyyy.MM.dd'),
								serviceChargeRate:
									proposalRequest.currentBid?.serviceChargeRate,
								meetingSpaceOnHoldOption: null
							})) ?? []

					await setProperty('currentBid', {
						...proposalRequest?.currentBid,
						[event.target.name]: v,
						meetingRates: [...meetingRates, ...missingMeetingRates]
					})
				} else if (
					event.target.name === 'rebateRequestApproved' &&
					typeof v === 'boolean'
				) {
					const updatedCurrentBid = {
						...proposalRequest?.currentBid,
						rebateRequestApproved: v,
						rebateAmount: v
							? (proposalRequest?.currentBid?.rebateRequestAmount ?? 0)
							: 0
					}
					await setProperty('currentBid', updatedCurrentBid)
				} else {
					if (event.target.name === 'attritionRate') {
						if (Number(v ?? 0) < 0) v = 0
						if (Number(v ?? 0) > 100) {
							v = proposalRequest?.currentBid?.attritionRate ?? 0
						}
					}

					const currentBid = {
						...proposalRequest?.currentBid,
						[event.target.name]: v
					}
					await setProperty('currentBid', currentBid)
				}
			}
		},
		[proposalRequest?.currentBid, proposalRequest?.status, setProperty]
	)

	const tabData = useCallback(
		() =>
			getTabs(
				{
					...errors,
					question: questionErrors,
					concessionRequest: concessionRequestErrors,
					contractClause: contractClauseErrors
				},
				quoteRequest,
				onChange,
				isMarketAvailability
			).filter(tab => tab.visible),
		[
			concessionRequestErrors,
			contractClauseErrors,
			errors,
			isMarketAvailability,
			onChange,
			questionErrors,
			quoteRequest
		]
	)

	const renderTitle = (tab: ITab & { error: string[] | null }) => {
		const { title, error } = tab
		return (
			<div className='flex items-center justify-between'>
				<div>{title}</div>
				<div className='w-4'>
					{error && error.length > 0 ? (
						<HSTooltip
							content={error.map(item => (
								<div className='text-left' key={item}>
									{item}
								</div>
							))}
						>
							<FontAwesomeIcon
								className='ml-2 text-red-400'
								icon={faCircleExclamation}
							/>
						</HSTooltip>
					) : (
						<HSTooltip content='All Good'>
							<HSBadge color='green' className='ml-2 rounded-full !p-1'>
								<FontAwesomeIcon
									icon={faCheck}
									className='flex items-center justify-center text-green-800'
									size='xs'
								/>
							</HSBadge>
						</HSTooltip>
					)}
				</div>
			</div>
		)
	}

	useEffect(() => {
		if (tabPath) {
			const tabIndex = tabData().findIndex(tab => tab.path === tabPath)
			if (tabIndex !== -1) {
				tabReference.current?.setActiveTab(tabIndex)
			}
		}
	}, [tabData, tabPath])

	return (
		<>
			<div className='flex items-center justify-between border-b px-6 py-4'>
				<div className='text-xl font-semibold'>
					{isMarketAvailability ? 'Availability Response' : 'Create Proposal'}
				</div>

				<div className='flex items-center gap-2'>
					<div className='text-sm font-normal text-gray-500'>
						Default Currency:
					</div>
					<HSCurrencyPicker
						value={
							proposalRequest?.currencyCode
								? currencies[proposalRequest.currencyCode]
								: UnitedStatesDollar
						}
						onChange={async (value: ICurrency) => {
							await setProperty('currencyCode', value.code)
						}}
					/>
				</div>
			</div>
			<div className='flex gap-4 px-6 py-2'>
				<Tabs
					ref={tabReference}
					className='w-full'
					variant='underline'
					theme={customTabComponentTheme('horizontal', true)}
					onActiveTabChange={tabId => {
						setActiveTabId(tabId)
						navigate(
							`/hotelier/proposal${isMarketAvailability ? '-summary' : ''}/${eventId}/${venueId}/${chapterName}/${infoType}/${tabData()[tabId].path}`
						)
					}}
				>
					{tabData().map((tab, key) => {
						const TabComponent = tab.children
						return (
							<Tabs.Item key={tab.key} title={renderTitle(tab)}>
								{activeTabId === key ? TabComponent : null}
							</Tabs.Item>
						)
					})}
				</Tabs>
			</div>
		</>
	)
}

export default ProposalBuilder
