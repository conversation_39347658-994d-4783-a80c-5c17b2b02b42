import {
	ColumnsDirective,
	ColumnDirective,
	Inject,
	Sort,
	Page
} from '@syncfusion/ej2-react-grids'
import { WarningBanner } from 'components/warningBanner'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { useFeatureContext } from 'lib/providers/feature.provider'
import type { SubscriptionInfo } from 'models/organizations'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
	actionTemplate,
	expiryDateTemplate,
	startDateTemplate
} from './templates'
import { addDays, addYears, formatISO, isValid, parseISO } from 'date-fns'
import {
	useAddSubscription,
	useGetHotelier,
	useUpdateSubscription
} from 'lib/services/hotels.service'
import type { Hotelier, IHotelierResponse } from 'models/hotelier'
import type { IUserProfile } from 'models/userProfiles'
import type { ChangedEventArgs } from '@syncfusion/ej2-react-calendars'
import DataGrid from 'components/dataGrid'
import useHotelStore from 'lib/store/hotelStore'
import useSubscriptionStore from 'lib/store/subscriptionStore'
import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import HSBadge from 'components/badge'
import HSRadioButton from 'components/radioButton'
import { defaultLicenseTier } from './helper'
import HSToggleSwitch from 'components/toggleSwitch'
import HSDatePicker from 'components/datePicker'

const Subscriptions = () => {
	const { venue: hotel } = useHotelStore()
	const { subscriptions, setObject: setSubscriptions } = useSubscriptionStore()
	const { userProfile } = useUserProfileContext()
	const { getFeatureByKey } = useFeatureContext()
	const navigate = useNavigate()
	const { refetch: getHoteliers } = useGetHotelier(hotel.id ?? '', false)

	const [paidFeature, setPaidFeature] = useState(
		getFeatureByKey('ACCTMGR', userProfile?.isAdmin ? null : hotel)
	)
	const [selectedSubscription, setSelectedSubscription] =
		useState<Partial<SubscriptionInfo> | null>(null)
	const [showAddEditSubscriptionModal, setShowAddEditSubscriptionModal] =
		useState(false)
	const [orgAdmins, setOrgAdmins] = useState<(Hotelier & IUserProfile)[]>([])

	const { mutateAsync: addSubscription } = useAddSubscription()
	const { mutateAsync: updateSubscription } = useUpdateSubscription()

	const onSubscriptionAdded = (subscription: SubscriptionInfo) => {
		addSubscription({ venueId: hotel.id ?? '', subscription })
			.then(response => {
				setSubscriptions([...(subscriptions ?? []), response])
			})
			.catch((error: unknown) => console.error(error))
	}

	const onSubscriptionUpdated = (subscription: SubscriptionInfo) => {
		updateSubscription({ venueId: hotel.id ?? '', subscription })
			.then(response => {
				setSubscriptions([
					...(subscriptions?.filter(d => d.id !== subscription.id) ?? []),
					response
				])
			})
			.catch((error: unknown) => console.error(error))
	}

	const onEditSubscriptionClick = (subscription: SubscriptionInfo) => {
		setSelectedSubscription(subscription)
		setShowAddEditSubscriptionModal(true)
	}

	const onCloseAddEditSubscriptionModal = () => {
		setShowAddEditSubscriptionModal(false)
	}

	const onChangeTrial = (checked: boolean) => {
		setSelectedSubscription({
			...selectedSubscription,
			type: `Hotelier ${selectedSubscription?.tier}${checked ? ' Trial' : ''}`
		})
	}

	const onAddSubscriptionClick = () => {
		getHoteliers()
			.then((response: { data: IHotelierResponse[] | undefined }) => {
				if (response.data) {
					setOrgAdmins(
						response.data
							.filter(d => d.userProfile.organizationRoles?.isOrgAdmin)
							.map(d => ({ ...d.hotelier, ...d.userProfile }))
					)
					setSelectedSubscription({
						propertyId: hotel.id,
						type: 'Hotelier Pro Trial',
						tier: 'Pro',
						startDate: formatISO(new Date()),
						expiryDate: formatISO(addYears(new Date(), 1))
					})
					setShowAddEditSubscriptionModal(true)
				}
			})
			.catch((error: unknown) => console.error(error))
	}

	const onClickAddEdit = () => {
		if (selectedSubscription?.id) {
			onSubscriptionUpdated({ ...selectedSubscription } as SubscriptionInfo)
		} else {
			onSubscriptionAdded({ ...selectedSubscription } as SubscriptionInfo)
		}
		setShowAddEditSubscriptionModal(false)
	}

	const onChangeDate = (name: string, date: Date) => {
		let v: Date | null = date
		if (!isValid(v)) {
			v = null
		}
		setSelectedSubscription({
			...selectedSubscription,
			[name]: formatISO(v as Date, { representation: 'date' })
		})
	}

	useEffect(() => {
		if (hotel.id) {
			setPaidFeature(
				getFeatureByKey(
					'ACCTMGR',
					userProfile?.isAdmin
						? null
						: { currentSubscriptionInfo: hotel.currentSubscriptionInfo }
				)
			)
		}
	}, [
		userProfile?.isAdmin,
		hotel.id,
		hotel.currentSubscriptionInfo,
		getFeatureByKey
	])

	return (
		<>
			<div className='flex items-center justify-between border-b'>
				<div className='px-6 py-4 text-xl font-semibold'>
					Subscriptions & Billing
				</div>
			</div>
			{paidFeature ? null : (
				<div className='flex justify-end px-4 pt-4'>
					<WarningBanner
						variant='Warning'
						heading='Upgrade'
						message='Click the link to view a list of paid features'
						ctaPrompt='View Features'
						onClick={async () => navigate('/payment/checkout')}
						hideIcon
					/>
				</div>
			)}
			<div
				className='overflow-y-auto p-6'
				style={{ maxHeight: 'calc(100vh - 14rem)' }}
			>
				<div className='mb-2 text-lg font-semibold text-gray-900'>
					Subscriptions History
				</div>
				<div className='flex items-center justify-between'>
					<div>{Number(subscriptions?.length)} Subscription</div>
					<div className='flex flex-row gap-[10px]'>
						{userProfile?.isAdmin ? (
							<HSButton color='primary' onClick={onAddSubscriptionClick}>
								Add New Subscription (ADMIN)
							</HSButton>
						) : null}
					</div>
				</div>
				<div className='my-4'>
					<DataGrid
						filterSettings={{ type: 'Menu' }}
						dataSource={subscriptions?.sort((a, b) =>
							(a.id ?? '').localeCompare(b.id ?? '')
						)}
						loadingIndicator={{ indicatorType: 'Shimmer' }}
						allowSorting
						allowMultiSorting
						allowTextWrap
						autoFit
						allowPaging
					>
						<ColumnsDirective>
							<ColumnDirective field='type' headerText='Type' />
							<ColumnDirective field='tier' headerText='Tier' />
							<ColumnDirective
								field='startDate'
								headerText='Start Date'
								template={(item: SubscriptionInfo) => startDateTemplate(item)}
							/>
							<ColumnDirective
								field='expiryDate'
								headerText='Expiration Date'
								template={(item: SubscriptionInfo) => expiryDateTemplate(item)}
							/>
							{userProfile?.isAdmin ? (
								<ColumnDirective
									field='id'
									headerText='Actions'
									headerTextAlign='Center'
									textAlign='Center'
									template={(item: SubscriptionInfo) =>
										actionTemplate(item, onEditSubscriptionClick)
									}
								/>
							) : null}
						</ColumnsDirective>
						<Inject services={[Sort, Page]} />
					</DataGrid>
				</div>

				<HSDrawer
					id='modalDialog'
					onClose={onCloseAddEditSubscriptionModal}
					open={selectedSubscription !== null && showAddEditSubscriptionModal}
					position='right'
					style={{ width: '400px' }}
				>
					<div className='flex h-[calc(100vh-5rem)] flex-col'>
						<Drawer.Header
							title={`${selectedSubscription?.id ? 'Edit' : 'Add New'} Subscription`}
							titleIcon={() => null}
						/>
						<div className='flex flex-col gap-5'>
							<div className='mb-2 text-sm text-gray-500'>
								Fill in Subscription Details
							</div>
							<div className='flex flex-col gap-1'>
								<div className='text-sm font-semibold'>License Tier</div>
								<div className='flex flex-col gap-2'>
									{defaultLicenseTier.map(license => (
										<div
											className='flex items-center gap-2'
											key={license.label}
										>
											<HSRadioButton
												name='licenseTier'
												labelComponent={
													<HSBadge color={license.color}>
														<div className='p-1'>{license.label}</div>
													</HSBadge>
												}
												onChange={event => {
													setSelectedSubscription(s => ({
														...s,
														type: `Hotelier ${event.target.value} Trial`,
														tier: event.target.value
													}))
												}}
												value={license.label}
												label={license.label}
												selectedValue={
													defaultLicenseTier.find(
														item => item.label === selectedSubscription?.tier
													)?.label ?? ''
												}
											/>
										</div>
									))}
								</div>
							</div>
							<div className='border-b' />
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-medium text-gray-900'>Trial</div>
								<div className='flex items-center gap-2'>
									<HSToggleSwitch
										label=''
										checked={
											Number(selectedSubscription?.type?.indexOf('Trial')) > 0
										}
										onChange={checked => onChangeTrial(checked)}
									/>
									<HSBadge color='orange'>Trial</HSBadge>
								</div>
							</div>
							<div className='border-b' />
							<div className='flex gap-4'>
								<div className='flex flex-col gap-1'>
									<div className='text-sm font-semibold'>Start Date</div>
									<div>
										<HSDatePicker
											disabled={!userProfile?.isAdmin}
											value={
												selectedSubscription?.startDate
													? parseISO(selectedSubscription.startDate)
													: undefined
											}
											format='yyyy-MM-dd'
											change={(event: ChangedEventArgs) =>
												onChangeDate('startDate', event.value as Date)
											}
										/>
									</div>
								</div>
								<div className='flex flex-col gap-1'>
									<div className='text-sm font-semibold'>Expiry Date</div>
									<div>
										<HSDatePicker
											disabled={!userProfile?.isAdmin}
											value={
												selectedSubscription?.expiryDate
													? parseISO(selectedSubscription.expiryDate)
													: undefined
											}
											format='yyyy-MM-dd'
											min={
												selectedSubscription?.startDate
													? addDays(parseISO(selectedSubscription.startDate), 1)
													: undefined
											}
											change={(event: ChangedEventArgs) =>
												onChangeDate('expiryDate', event.value as Date)
											}
										/>
									</div>
								</div>
							</div>
							<div className='border-b' />
							{orgAdmins.length === 0 ? (
								<div className='text-sm text-red-500'>
									There are no Org Admins setup for this hotel. A welcome email
									will not be sent.
								</div>
							) : (
								<div>
									<div className='text-sm text-gray-500'>
										Org Admins to receive the{' '}
										<a
											className='font-medium text-primary-700 underline'
											href='https://app.intercom.com/a/apps/tbzm7jeb/outbound/email/31788736'
										>
											Welcome to {selectedSubscription?.tier ?? 'Pro'} email
										</a>
										:
									</div>
									{orgAdmins.map(oa => (
										<div className='text-sm text-gray-500' key={oa.id}>
											{oa.firstName} {oa.lastName} ({oa.id})
										</div>
									))}
								</div>
							)}
						</div>
					</div>
					<div className='flex gap-4'>
						<HSButton
							color='light'
							className='grow'
							onClick={() => onCloseAddEditSubscriptionModal()}
						>
							Cancel
						</HSButton>
						<HSButton
							className='grow'
							onClick={() => {
								onClickAddEdit()
							}}
						>
							{selectedSubscription?.id ? 'Save' : 'Add'} Subscription
						</HSButton>
					</div>
				</HSDrawer>
			</div>
		</>
	)
}

export default Subscriptions
