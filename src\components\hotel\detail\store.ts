import { create } from 'zustand'

interface HotelDetailState {
	goBackLabel: string | null
	goBackUrl: string | null
	showAvailability: boolean
	showAddToRfp: boolean
}

interface HotelDetailAction {
	setGoBackLabel: (label: string) => void
	setGoBackUrl: (url: string) => void
	clearStore: () => void
	setShowAvailability: (show: boolean) => void
	setShowAddToRfp: (show: boolean) => void
}

const hotelDetailStore = create<HotelDetailState & HotelDetailAction>(set => ({
	goBackLabel: null,
	goBackUrl: null,
	showAvailability: false,
	showAddToRfp: false,

	setGoBackLabel: label => set({ goBackLabel: label }),
	setGoBackUrl: url => set({ goBackUrl: url }),
	setShowAvailability: show => set({ showAvailability: show }),
	clearStore: () =>
		set({ goBackLabel: null, goBackUrl: null, showAvailability: false }),
	setShowAddToRfp: show => set({ showAddToRfp: show })
}))

export default hotelDetailStore
