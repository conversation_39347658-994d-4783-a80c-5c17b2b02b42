import type { IContractClauses } from 'models/organizations'
import HSDrawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import HSTable from 'components/table'

interface IContractClauseModal {
	onClose: () => void
	isEdit: boolean
	isProfile: boolean
	editContractClause?: IContractClauses | undefined
	userProfileId: string
	contractClauses: IContractClauses[] | undefined
	onAddUpdate: (value: IContractClauses) => void
	showAddFromToolbox?: boolean
}

interface RoomTypeData {
	rate: number
	proposed: number
	contracted: number
	savings: number
	savingsincludingtax: number
}

const ContractClauseModal = (properties: IContractClauseModal) => {
	const { onClose } = properties

	const roomTypeData: RoomTypeData[] = [
		{
			rate: 174,
			proposed: 200,
			contracted: 104,
			savings: 200,
			savingsincludingtax: 506
		},
		{
			rate: 158,
			proposed: 0,
			contracted: 14,
			savings: 132,
			savingsincludingtax: 0
		}
	]

	return (
		<div className='control-pane'>
			<div id='targetElement'>
				<HSDrawer
					id='add-edit-contract-clause-drawer'
					position='right'
					onClose={onClose}
					open
					style={{ width: '50vw' }}
				>
					<Drawer.Header
						title='Contracted Guestrooms Cost'
						titleIcon={() => null}
					/>

					<Drawer.Items style={{ height: 'calc(100vh - 10rem)' }}>
						<div className='flex flex-col gap-8'>
							<div className='gray-200 rounded-lg border p-4'>
								<div className='mb-2 text-sm font-medium text-gray-900'>
									Comments
								</div>
								<div className='text-sm font-normal text-gray-600'>
									This proposal stands out for its thoughtful cost-saving
									measures, which could significantly optimize our RFP budget
									without compromising on quality or service. Their ability to
									align competitive pricing with value-added offerings reflects
									a clear understanding of our priorities
								</div>
							</div>

							<div className='gap-2'>
								<HSTable
									allowPaging={false}
									rows={roomTypeData}
									columns={[
										{
											field: 'rate',
											headerText: 'Rate',
											width: 120
										},
										{
											field: 'proposed',
											headerText: 'Proposed',
											width: 120
										},
										{
											field: 'contracted',
											headerText: 'Contracted',
											width: 120
										},
										{
											field: 'savings',
											headerText: 'Savings',
											width: 120
										},
										{
											field: 'savingsincludingtax',
											headerText: 'Savings Including Tax',
											width: 120
										}
									]}
								/>
							</div>
						</div>
					</Drawer.Items>
				</HSDrawer>
			</div>
		</div>
	)
}

export default ContractClauseModal
