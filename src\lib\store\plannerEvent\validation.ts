/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable unicorn/no-array-reduce */
import {
	parseISO,
	isAfter,
	isBefore,
	differenceInDays,
	addBusinessDays,
	subBusinessDays
} from 'date-fns'
import type { EventPlan, PatternDays } from 'models/proposalResponseMonitor'
import type { Attachment } from '../attachmentsStore'
import { supplierContactRoles } from 'lib/helpers/supplierContacts'
import { useMemo } from 'react'
import type { IUserProfile } from 'models/userProfiles'

interface ValidationErrors {
	total: number
	general: string[]
	contacts: string[]
	mespace: string[]
	fandb: string[]
	roomblocks: string[]
	contractTerms: string[]
	contractClauses: string[]
	history: string[]
	hotels: string[]
	dmos: string[]
	send: string[]
	generalErrors: string[]
	dateErrors: string[]
	responsesDueDate: string[]
	selectionDate: string[]
}

const initializeErrors = (): ValidationErrors => ({
	total: 0,
	general: [],
	contacts: [],
	mespace: [],
	fandb: [],
	roomblocks: [],
	contractTerms: [],
	contractClauses: [],
	history: [],
	hotels: [],
	dmos: [],
	send: [],
	generalErrors: [],
	dateErrors: [],
	responsesDueDate: [],
	selectionDate: []
})

const validateContractingStatus = (
	status: string | undefined,
	errors: ValidationErrors
): ValidationErrors | null => {
	if (['Contracting', 'Contracted', 'CheckedOut'].includes(status ?? '')) {
		const updatedErrors = { ...errors }
		updatedErrors.send.push(
			"You cannot send an RFP to more hotels once you've started contracting with a hotel."
		)
		updatedErrors.total = 1
		return updatedErrors
	}
	return null
}

const validateGeneralInfo = (
	eventPlan: EventPlan,
	errors: ValidationErrors,
	userProfile: IUserProfile | undefined
): void => {
	if (!eventPlan.name) {
		errors.general.push('Request Name is required.')
		errors.generalErrors.push('Request Name is required.')
	}

	if (eventPlan.status !== 'New') return

	if (!eventPlan.groupType) {
		errors.general.push('Please select a Group Type.')
		errors.generalErrors.push('Please select a Group Type.')
	}

	const now = new Date()
	const startDate = eventPlan.startDate ? parseISO(eventPlan.startDate) : null
	const endDate = eventPlan.endDate ? parseISO(eventPlan.endDate) : null

	if (startDate && isAfter(now, startDate)) {
		errors.general.push('Start date must be in the future.')
		errors.dateErrors.push('Start date must be in the future.')
	}
	if (endDate && isAfter(now, endDate)) {
		errors.general.push('End date must be in the future.')
		errors.dateErrors.push('End date must be in the future.')
	}

	if (startDate && endDate && !isAfter(endDate, startDate)) {
		errors.general.push('End date must be after start date.')
		errors.dateErrors.push('End date must be after start date.')
	} else if (eventPlan.itemType === 'eventPlan') {
		if (!eventPlan.startDate) {
			errors.general.push('Start Date is required.')
			errors.dateErrors.push('Start Date is required.')
		}
		if (!eventPlan.endDate) {
			errors.general.push('End Date is required.')
			errors.dateErrors.push('End Date is required.')
		}
	}

	if (eventPlan.allowDateSuggestions == null) {
		errors.general.push(
			'Please specify whether hotels can suggest alternate dates.'
		)
		errors.dateErrors.push(
			'Please specify whether hotels can suggest alternate dates.'
		)
	}

	if (
		eventPlan.patternIsFlexible &&
		Object.values(eventPlan.patternDays as PatternDays).every(day => !day)
	) {
		errors.general.push(
			'Please select at least one alternate start day under Flexible Pattern.'
		)
		errors.dateErrors.push(
			'Please select at least one alternate start day under Flexible Pattern.'
		)
	}

	const responsesDueDate = eventPlan.responsesDueDate
		? parseISO(eventPlan.responsesDueDate)
		: null

	let messageR: string | null = null

	if (responsesDueDate === null) {
		messageR = 'Responses Due Date is required.'
	} else if (
		isBefore(
			responsesDueDate,
			addBusinessDays(new Date(), userProfile?.minimumResponsesDueDays ?? 4)
		)
	) {
		messageR = `Responses Due Date must be at least ${userProfile?.minimumResponsesDueDays} business day${userProfile?.minimumResponsesDueDays === 1 ? '' : 's'} from today.`
	} else if (
		isAfter(
			responsesDueDate,
			subBusinessDays(parseISO(eventPlan?.startDate?.split('T')[0] ?? ''), 1)
		)
	) {
		messageR = 'Responses Due Date must be before the event start date.'
	}

	if (messageR) {
		errors.general.push(messageR)
		errors.generalErrors.push(messageR)
		errors.responsesDueDate.push(messageR)
	}

	const selectionDate = eventPlan.selectionDate
		? parseISO(eventPlan.selectionDate)
		: null
	let messageS: string | null = null

	if (selectionDate === null) {
		messageS = 'Decision Date is required.'
	} else if (
		eventPlan?.responsesDueDate &&
		isBefore(
			selectionDate,
			addBusinessDays(
				parseISO(eventPlan.responsesDueDate.split('T')[0] ?? ''),
				1
			)
		)
	) {
		messageS = `Estimated Decision Date after Responses Due Date.`
	} else if (
		isAfter(
			selectionDate,
			subBusinessDays(parseISO(eventPlan?.startDate?.split('T')[0] ?? ''), 1)
		)
	) {
		messageS =
			'Estimated Decision Due Date must be before the event start date.'
	}

	if (messageS) {
		errors.general.push(messageS)
		errors.generalErrors.push(messageS)
		errors.selectionDate.push(messageS)
	}
}

const validateAlternateDates = (
	eventPlan: EventPlan,
	errors: ValidationErrors
): void => {
	if (!eventPlan.alternateDates?.length) return

	const now = new Date()
	for (const ad of eventPlan.alternateDates) {
		const startDate = ad.startDate ? parseISO(ad.startDate) : null
		const endDate = ad.endDate ? parseISO(ad.endDate) : null

		if (!startDate || !endDate) {
			errors.general.push(
				'Please select an alternate date range, or remove it.'
			)
			errors.dateErrors.push(
				'Please select an alternate date range, or remove it.'
			)
		}

		if (ad.startDate && isAfter(now, Number(startDate))) {
			errors.general.push('Alternate start date must be in the future.')
			errors.dateErrors.push('Alternate start date must be in the future.')
		}
		if (ad.endDate && isAfter(now, Number(endDate))) {
			errors.general.push('Alternate end date must be in the future.')
			errors.dateErrors.push('Alternate end date must be in the future.')
		}
		if (
			ad.startDate &&
			ad.endDate &&
			!isAfter(Number(endDate), Number(startDate))
		) {
			errors.general.push('Alternate end date must be after start date.')
			errors.dateErrors.push('Alternate end date must be after start date.')
		}
	}
}

const validateContacts = (
	eventPlan: EventPlan,
	errors: ValidationErrors
): void => {
	if (
		(!eventPlan.proposalRequests || eventPlan.proposalRequests.length === 0) &&
		(!eventPlan.supplierContacts || eventPlan.supplierContacts.length === 0)
	) {
		errors.contacts.push(
			'Please add at least one Hotel, DMO, or Industry Contact.'
		)
		errors.hotels.push(
			'Please add at least one Hotel, DMO, or Industry Contact.'
		)
		return
	}

	if (
		eventPlan.proposalRequests?.length === 0 &&
		eventPlan.supplierContacts?.every(sc => !sc.requestSuggestions)
	) {
		errors.contacts.push(
			'At least one Industry Contact must be allowed to make suggestions if you are not selecting any hotels.'
		)
		errors.hotels.push(
			'At least one Industry Contact must be allowed to make suggestions if you are not selecting any hotels.'
		)
	}

	if (
		eventPlan.supplierContacts?.some(
			sc =>
				sc.role !== supplierContactRoles.hot?.key &&
				!sc.systemAdded &&
				!sc.isDelegate &&
				(sc.allowMessaging == null || sc.requestSuggestions == null)
		)
	) {
		errors.contacts.push(
			'Please configure settings for all of your Industry Contacts.'
		)
	}
}

const validateFoodAndBeverage = (
	eventPlan: EventPlan,
	errors: ValidationErrors
): void => {
	if (
		eventPlan.foodAndBeverageRequests?.some(fbr => !fbr.type || !fbr.quantity)
	) {
		errors.fandb.push('Each F&B request must include a type and quantity.')
	}
}

const validateMeetingSpaces = (
	eventPlan: EventPlan,
	attachments: Attachment[],
	errors: ValidationErrors
): void => {
	if (!eventPlan.meetingSpaceRequired) return

	if (!eventPlan.meetingSpaceRequests?.length) {
		errors.mespace.push('Please add at least one meeting space request.')
	}

	if (
		eventPlan.meetingSpaceRequests?.some(
			msr => !msr.name || !msr.layoutStyle || !msr.capacity
		)
	) {
		errors.mespace.push(
			'Please fill out all fields for each meeting space request.'
		)
	}

	if (
		eventPlan.meetingSpaceRequests?.some(
			msr =>
				msr.layoutStyle === 'Custom' &&
				!(
					msr.notes ||
					attachments.some(
						a => a.venueId === null && a.meetingSpaceRequestId === msr.id
					)
				)
		)
	) {
		errors.mespace.push(
			'All Custom room layouts must include either notes or an attachment.'
		)
	}

	const invalidTimes = eventPlan.meetingSpaceRequests?.filter(
		msr =>
			Number(msr.startTime) * 100 + Number(msr.startMinutes) >=
			Number(msr.endTime) * 100 + Number(msr.endMinutes)
	).length
	if (invalidTimes) {
		errors.mespace.push(
			`End time is before start time on ${invalidTimes} meeting space${invalidTimes === 1 ? '' : 's'}.`
		)
	}
}

const validateRoomBlocks = (
	eventPlan: EventPlan,
	errors: ValidationErrors
): void => {
	if (!eventPlan.roomBlocksRequired) return

	if (!eventPlan.roomBlockRequests?.length) {
		errors.roomblocks.push('Please add at least one room block request.')
	}

	if (eventPlan.roomBlockRequests?.some(rbr => !rbr.name)) {
		errors.roomblocks.push('Please enter a unique name for each room block.')
	}

	if (
		eventPlan.roomBlockRequests?.some(rbr =>
			rbr.roomTypeRequests.some(rtr => rtr.roomType < 0)
		)
	) {
		errors.roomblocks.push('Please select a room type for each request.')
	}

	if (
		eventPlan.roomBlockRequests?.some(
			rbr => Number(rbr.minimumPeakRooms) >= rbr.peakSize
		)
	) {
		errors.roomblocks.push(
			'If specifying minimum peak rooms per proposal, it must be less than the total peak rooms requested.'
		)
	}

	const totalRoomsRequested = eventPlan.roomBlockRequests
		?.flatMap(rbr =>
			rbr.roomTypeRequests.flatMap(rtr =>
				rtr.roomNights.flatMap(rn => rn.roomsRequested || 0)
			)
		)
		.reduce((accumulator, rr) => accumulator + (rr || 0), 0)
	if (Number(totalRoomsRequested) < 10) {
		errors.roomblocks.push(
			'The total number of room nights requested across all room blocks must be at least 10.'
		)
	}

	const startDate = eventPlan.startDate ? parseISO(eventPlan.startDate) : null
	const endDate = eventPlan.endDate ? parseISO(eventPlan.endDate) : null
	if (startDate && endDate && isBefore(startDate, endDate)) {
		const minRoomNight = Math.min(
			...(eventPlan.roomBlockRequests || [])
				.flatMap(rbr => rbr.roomTypeRequests)
				.flatMap(rtr => rtr.roomNights)
				.reduce(
					(accumulator, rn) => {
						accumulator[rn.dayNumber] += rn.roomsRequested
						return accumulator
					},
					Array.from({ length: differenceInDays(endDate, startDate) }, () => 0)
				)
		)
		if (minRoomNight === 0) {
			errors.roomblocks.push('Each night must have at least 1 room requested.')
		}
	}

	if (eventPlan.roomBlockRequests?.some(rbr => !rbr.paymentMethod)) {
		errors.roomblocks.push(
			'Please select a payment method for each room block.'
		)
	}

	if (
		eventPlan.roomBlockRequests?.some(rbr =>
			rbr.roomTypeRequests.some(
				rtr => rtr.budgetLow && rtr.budget && rtr.budgetLow > rtr.budget
			)
		)
	) {
		errors.roomblocks.push('Budget High must be greater than Budget Low.')
	}

	if (
		eventPlan.reservationMethods &&
		Object.values(eventPlan.reservationMethods).every(v => !v)
	) {
		errors.contractTerms.push('Please select at least one reservation method.')
	}

	if (eventPlan.requestRebate && !eventPlan.rebateRequestAmount) {
		errors.roomblocks.push(
			'If you are requesting a rebate, please enter an amount per room.'
		)
	}
}

const validateContractTerms = (
	eventPlan: EventPlan,
	errors: ValidationErrors
): void => {
	if (eventPlan.meetingSpaceRequired) {
		const paymentMethods = [
			{ key: 'meetingSpace', label: 'Meeting Space' },
			{ key: 'foodAndBeverage', label: 'Food and Beverage' },
			{ key: 'audioVisual', label: 'Audio Visual' }
		]
		for (const { key, label } of paymentMethods) {
			if (
				eventPlan.paymentMethods &&
				!eventPlan.paymentMethods[key as keyof typeof eventPlan.paymentMethods]
			) {
				errors.contractTerms.push(
					`Please select a payment method for ${label}.`
				)
			}
		}
	}

	if (
		eventPlan.contractSigner &&
		(
			[
				'name',
				'title',
				'address',
				'city',
				'state',
				'postalCode',
				'country'
			] as const
		).some(key => !eventPlan.contractSigner?.[key])
	) {
		errors.contractTerms.push(
			'Please provide contract signatory name, title, and address.'
		)
	}

	if (
		eventPlan.rfpOwner &&
		(['name', 'state', 'country'] as (keyof typeof eventPlan.rfpOwner)[]).some(
			key => eventPlan.rfpOwner && !eventPlan.rfpOwner[key]
		)
	) {
		errors.contractTerms.push(
			'Please provide point of contact name, state, and country.'
		)
	}
}

const calculateTotalErrors = (errors: ValidationErrors): void => {
	const updatedErrors = { ...errors }
	updatedErrors.total = Object.keys(errors)
		.filter(key => key !== 'total')
		.reduce((sum, key) => {
			const value = errors[key as keyof ValidationErrors]
			return sum + (Array.isArray(value) ? value.length : 0)
		}, 0)
	Object.assign(errors, updatedErrors)
}

const getEventPlanValidationErrors = (
	eventPlan: EventPlan | null,
	attachments: Attachment[],
	userProfile: IUserProfile | undefined
): ValidationErrors => {
	const errors = initializeErrors()

	if (!eventPlan) return errors

	if (validateContractingStatus(eventPlan.status ?? '', errors)) {
		return errors
	}

	if (eventPlan.status !== 'New') {
		return errors
	}

	validateGeneralInfo(eventPlan, errors, userProfile)
	validateAlternateDates(eventPlan, errors)
	validateContacts(eventPlan, errors)
	validateFoodAndBeverage(eventPlan, errors)
	validateMeetingSpaces(eventPlan, attachments, errors)
	validateRoomBlocks(eventPlan, errors)
	validateContractTerms(eventPlan, errors)
	calculateTotalErrors(errors)

	return errors
}

interface UseEventPlanValidationErrorsArguments {
	eventPlan: EventPlan | null
	attachments: Attachment[]
	userProfile: IUserProfile | undefined
	status?: string
}

const useEventPlanValidationErrors = ({
	eventPlan,
	attachments,
	userProfile
}: UseEventPlanValidationErrorsArguments): ValidationErrors => {
	const validationErrors = useMemo<ValidationErrors>(
		() => getEventPlanValidationErrors(eventPlan, attachments, userProfile),
		[eventPlan, attachments, userProfile]
	)

	return validationErrors
}

export default useEventPlanValidationErrors
