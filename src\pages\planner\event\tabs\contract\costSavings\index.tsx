import { useState } from 'react'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import EditContractRates from 'components/editContractRates'
import {
	costSavingsStore,
	viewModes
} from 'lib/store/plannerEvent/costSavingsStore'
import CostSavingDashboard from './views/costSavingDashboard'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import { UnitedStatesDollar } from 'lib/helpers'

const CostSavings = () => {
	const { eventInfo: eventPlan } = eventInfoStore()
	const { currencies } = useCurrencyContext()
	const { viewMode } = costSavingsStore()
	const [showEditRates, setShowEditRates] = useState(false)

	return eventPlan ? (
		<div>
			{viewMode.key === viewModes.view.key ? (
				<CostSavingDashboard
					currency={
						currencies[eventPlan.currencyCode ?? ''] ?? UnitedStatesDollar
					}
				/>
			) : null}

			{showEditRates ? (
				<EditContractRates
					onClose={() => setShowEditRates(false)}
					isEdit={false}
					isProfile={false}
					userProfileId={eventPlan.id ?? ''}
					contractClauses={[]}
					onAddUpdate={() => {}}
				/>
			) : null}
		</div>
	) : null
}

export default CostSavings
