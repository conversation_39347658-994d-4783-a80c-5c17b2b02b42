import { faMessageLines, faTrashXmark } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSTooltip from 'components/tooltip'
import { formatNumber } from '../../../../lib/helpers/index'

export interface IHotelList {
	venueName: string
	venueCity: string
	venueState: string
	guestRoomQuantity: number
	meetingRoomQuantity: number
	meetingSpaceSquareFeet: number
	largestMeetingSpaceSquareFeet: number
	venueImageUrl: string | null
}
export const renderRooms = (item: number) => (
	<div className='flex items-center gap-1'>
		<div className='text-sm font-normal text-gray-600'>
			{formatNumber(item)}
		</div>
		<div className='text-xs font-medium text-gray-400'>
			room{item > 1 ? 's' : ''}
		</div>
	</div>
)

export const renderSpace = (item: number) => (
	<div className='flex items-center gap-1'>
		<div className='text-sm font-normal text-gray-600'>
			{formatNumber(item)}
		</div>
		<div className='text-xs font-medium text-gray-400'>
			ft<sup>2</sup>
		</div>
	</div>
)

export const renderAction = (
	item: IHotelList,
	handleNote: () => void,
	handleDelete: () => void
) => (
	<div className='flex items-center gap-2'>
		<HSTooltip content='Notes'>
			<HSButton color='light' size='xs' onClick={handleNote}>
				<FontAwesomeIcon icon={faMessageLines} size='lg' />
			</HSButton>
		</HSTooltip>
		<HSTooltip content='Delete'>
			<HSButton color='light' size='xs' onClick={handleDelete}>
				<FontAwesomeIcon
					icon={faTrashXmark}
					size='lg'
					className='text-red-600'
				/>
			</HSButton>
		</HSTooltip>
	</div>
)
