import HSButton from 'components/button'
import headerStore from 'components/header/headerStore'
import { useEffect, useMemo } from 'react'
import usePlannerDashboardData from './data'
import plannerDashboardStore from './dataStore'
import ActiveLeads from './activeLeads'
import RfpDetail from './rfpDetail'
import RFPDue from './rfpDue'
import OpportunityAwardedComparison from './opportunityAwarded'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons'
import RecentActivity from './recentActivity'
import RFPBoard from './rfps'
import { isSameMonth, parseISO } from 'date-fns'
import PageLoader from 'components/pageLoader'

const PlannerDashboard = () => {
	const { resetData } = usePlannerDashboardData()
	const { isLoading, events } = plannerDashboardStore()
	const { setLeftComponent, reset, setHide } = headerStore()

	useEffect(() => {
		resetData()
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	const leftComponent = useMemo(
		() => (
			<div className='text-xs font-medium leading-none text-gray-900'>
				My RFP Dashboard
			</div>
		),
		[]
	)

	useEffect(() => {
		setHide(false)
		setLeftComponent(leftComponent)
		return () => {
			reset()
		}
	}, [leftComponent, reset, setHide, setLeftComponent])

	const rfpsThisMonth = useMemo(
		() =>
			events.filter(event => isSameMonth(parseISO(event.created), new Date())),
		[events]
	)

	if (isLoading) {
		return <PageLoader />
	}

	return (
		<div
			className='!overflow-auto'
			style={{ maxHeight: 'calc(100vh - 3.3rem)' }}
		>
			<div className='flex items-center justify-between border border-y-gray-200 bg-white px-2 py-1'>
				{rfpsThisMonth.length > 0 ? (
					<div className='text-sm font-normal leading-none text-gray-700'>
						You’ve created {rfpsThisMonth.length} RFPs this month. Keep going!
					</div>
				) : null}
				<div className='ml-auto'>
					<HSButton size='xs'>Create RFP</HSButton>
				</div>
			</div>
			<div>
				<div className='bg-gray-50 p-4'>
					<div className='flex flex-col gap-6'>
						<div className='flex gap-4'>
							<div className='basis-1/5'>
								<ActiveLeads />
							</div>
							<div className='basis-1/5'>
								<RfpDetail />
							</div>
							<div className='basis-1/5'>
								<RFPDue />
							</div>
							<div className='basis-2/5'>
								<OpportunityAwardedComparison />
							</div>
						</div>
						<div className='flex gap-6'>
							<div className='!w-4/5'>
								<div className='card'>
									<RFPBoard />
								</div>
							</div>
							<div className='!w-1/5'>
								<div className='card'>
									<div className='border-b p-4'>
										<div className='flex items-center gap-2'>
											<div className='font-medium text-gray-700'>
												Recent Activity
											</div>
											<TooltipComponent content='View status updates from your last 10 RFP activities, including actions taken by both planners and hotels'>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-700'
												/>
											</TooltipComponent>
										</div>
										<div className='text-xs font-normal text-gray-500'>
											Last 10 Actions
										</div>
									</div>
									<div>
										<RecentActivity />
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}

export default PlannerDashboard
