import HSButton from 'components/button'
import HSTable from 'components/table'
import { costSavingsStore } from 'lib/store/plannerEvent/costSavingsStore'

const NegotiatedRates = () => {
	// const { eventInfo: eventPlan } = eventInfoStore()
	const { onToggleViewMode } = costSavingsStore()

	console.log('NegotiatedRates component rendered')

	return (
		<div className='card'>
			<div className='flex flex-col'>
				<div className='flex items-center justify-between border-b px-6 py-4'>
					<div className='flex flex-col'>
						<div className='text-xl font-semibold text-gray-900'>
							Edit Negotiated Rates
						</div>
						<span className='text-sm font-normal text-gray-600'>
							Edit your contracted rates below to track and measure your cost
							savings on your cost savings dashboard at any time.
						</span>
					</div>
					<HSButton color='light' onClick={onToggleViewMode}>
						Back to Dashboard
					</HSButton>
				</div>
				<div className='flex flex-col gap-4 px-4 py-6'>
					<div className='flex items-center gap-3'>
						<div className='text-lg font-semibold text-gray-900'>
							Hotel Name
						</div>
						<div className='text-sm font-normal text-gray-500'>
							Proposal Date Range
						</div>
					</div>
					<HSTable
						rows={[]}
						columns={[
							{
								field: 'rateType',
								headerText: 'Rate Type'
							},
							{
								field: 'proposedRate',
								headerText: 'Proposed Rate'
							},
							{
								field: 'contractedRate',
								headerText: 'Contracted Rate'
							},
							{
								field: 'savings',
								headerText: 'Savings'
							},
							{
								field: 'edit',
								headerText: 'Edit'
							}
						]}
					/>
				</div>
			</div>
		</div>
	)
}

export default NegotiatedRates
