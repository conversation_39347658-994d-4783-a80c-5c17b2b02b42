/* eslint-disable unicorn/no-nested-ternary */
import {
	faBuildingCircleCheck,
	faBuilding,
	faBedFront,
	faHandshake,
	faPeopleGroup,
	faRulerTriangle
} from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { InfoWindow, useAdvancedMarkerRef } from '@vis.gl/react-google-maps'
import HSButton from 'components/button'
import HotelMarker from 'components/googleMapV2/components/hotelMarker'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { formatImageUrl, formatNumber } from 'lib/helpers'
import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import type { Venue } from 'models/venue'

interface IHotelInfoMarkerProperties {
	venue: Venue
	setSelectedVenue: (venue: Venue | null) => void
	isAdded: boolean
	eventInfo?: EventPlan | undefined
	memoizedAddRemoveProposalRequests: (
		pr: Partial<ProposalRequest>[],
		callback: () => void,
		isAdmin: boolean
	) => Promise<void>
	isSelected?: boolean
	onClickDetails?: (venueId: string) => void
	setShowSiteSearchDrawer: (show: boolean) => void
}

const HotelInfoMarker = (properties: IHotelInfoMarkerProperties) => {
	const {
		venue,
		setSelectedVenue,
		isAdded,
		eventInfo,
		memoizedAddRemoveProposalRequests,
		isSelected,
		onClickDetails,
		setShowSiteSearchDrawer
	} = properties
	const [markerReference, marker] = useAdvancedMarkerRef()
	const { userProfile } = useUserProfileContext()

	const renderInfoMarker = () => (
		<InfoWindow
			anchor={marker}
			onCloseClick={() => {
				setSelectedVenue(null)
			}}
			className='info-window'
			style={{
				padding: 0
			}}
			headerContent={
				<div className='text-base font-semibold text-gray-700'>
					{venue.name}
				</div>
			}
		>
			<div className='flex gap-4'>
				<div className='w-1/3'>
					<img
						className='w-32 rounded-lg shadow-lg'
						src={
							venue.imageUrl?.includes('coming-soon') || !venue.imageUrl
								? '/images/hotel-coming-soon.png'
								: formatImageUrl(venue.imageUrl, venue.id ?? '')
						}
						alt='hotel'
					/>
				</div>
				<div className='w-2/3'>
					<div className='flex gap-4'>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-normal text-gray-700'>
								{venue.address}
							</div>
							<div className='flex items-center gap-2'>
								<div className='flex flex-wrap gap-x-4 gap-y-2'>
									<div className='flex items-center gap-2'>
										<FontAwesomeIcon
											className='w-4 text-gray-500'
											icon={faBedFront}
										/>
										<div className='text-sm font-normal text-gray-600'>
											{formatNumber(venue.guestRoomQuantity)}
										</div>
									</div>
									<div className='flex items-center gap-2'>
										<FontAwesomeIcon
											className='w-6 text-gray-500'
											icon={faHandshake}
										/>
										<div className='flex items-center gap-1'>
											<div className='text-sm font-normal text-gray-600'>
												{formatNumber(venue.meetingSpaceSquareFeet)}
											</div>
											<div className='text-sm font-normal text-gray-500'>
												Ft<sup>2</sup>
											</div>
										</div>
									</div>
									<div className='flex items-center gap-2'>
										<FontAwesomeIcon
											className='w-6 text-gray-500'
											icon={faPeopleGroup}
										/>
										<div className='text-sm font-normal text-gray-600'>
											{formatNumber(venue.meetingRoomQuantity)}
										</div>
									</div>
									<div className='flex gap-2'>
										<FontAwesomeIcon
											className='w-4 text-gray-500'
											icon={faRulerTriangle}
										/>
										<div className='flex items-center gap-1'>
											<div className='text-sm font-normal text-gray-600'>
												{formatNumber(venue.largestMeetingSpaceSquareFeet)}
											</div>
											<div className='text-sm font-normal text-gray-500'>
												Ft2
											</div>
										</div>
									</div>
								</div>
							</div>
							<div className='flex items-center justify-between gap-2'>
								<HSButton
									color='light'
									size='sm'
									className='grow'
									onClick={() => onClickDetails?.(venue.id ?? '')}
								>
									Detail
								</HSButton>
								<HSButton
									color={isAdded ? 'failure' : 'primary'}
									outline
									size='sm'
									className='grow'
									onClick={() => {
										if (eventInfo?.id) {
											if (userProfile) {
												memoizedAddRemoveProposalRequests(
													[
														{
															status: 'Pending',
															selected: false,
															venueId: venue.id ?? '',
															venueName: venue.name,
															venueLocation: `${venue.city}, ${venue.state}`,
															geolocation: venue.geolocation,
															destinations: venue.destinations,
															propertySellers: venue.propertySellers,
															createdBy: userProfile.isAdmin
																? (eventInfo.planners?.[0]?.id ??
																	eventInfo.createdBy ??
																	'')
																: userProfile.id,
															createdByOrganizationId: userProfile.isAdmin
																? (eventInfo.organizationId ?? '')
																: userProfile.organizationId,
															createdByOrganizationName: userProfile.isAdmin
																? (eventInfo.organizationName ?? '')
																: (userProfile.organizationName ?? '')
														}
													],
													() => {},
													userProfile.isAdmin
												).catch((error: unknown) => console.error(error))
											}
										} else {
											setSelectedVenue(venue)
											setShowSiteSearchDrawer(true)
										}
									}}
								>
									{isAdded
										? 'Remove'
										: eventInfo?.itemType === 'eventPlan'
											? 'Add to RFP'
											: 'Add to List'}
								</HSButton>
							</div>
						</div>
					</div>
				</div>
			</div>
			{/* <HotelPreview venue={venue} eventInfo={eventInfo} isSearch showAddToRFP /> */}
		</InfoWindow>
	)

	return (
		<>
			<HotelMarker
				ref={markerReference}
				position={{
					lat: Number(venue.latitude),
					lng: Number(venue.longitude)
				}}
				onClick={() => {
					setSelectedVenue(venue)
				}}
				icon={isAdded ? faBuildingCircleCheck : faBuilding}
				backgroundColor={isAdded ? null : '#FFF'}
				title={venue.name}
				iconColor={isAdded ? null : 'text-primary-600'}
			/>
			{isSelected ? renderInfoMarker() : null}
		</>
	)
}

export default HotelInfoMarker
