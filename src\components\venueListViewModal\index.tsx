import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import type { IVenueList } from 'models/organizations'

interface IVenueListViewModalProperties {
	open: boolean
	onClose: () => void
	selectedVenueList: IVenueList
}

const VenueListViewModal = (properties: IVenueListViewModalProperties) => {
	const { open, onClose, selectedVenueList } = properties

	return (
		<HSDrawer
			open={open}
			position='right'
			style={{ width: '30rem' }}
			onClose={onClose}
			noPadding
		>
			<Drawer.Header
				title='Hotels Included in Collection'
				titleIcon={() => null}
				className='p-4'
			/>
			<div
				className='overflow-auto px-6 pb-4'
				style={{ height: 'calc(100vh - 10rem)' }}
			>
				<div className='flex flex-col gap-6 pt-4'>
					{selectedVenueList.venues.map(venue => {
						const address = [
							venue.properties.address,
							venue.properties.city,
							venue.properties.state,
							venue.properties.zip,
							venue.properties.country
						]
							.filter(Boolean)
							.join(', ')

						return (
							<div key={venue.id} className='flex flex-col gap-1'>
								<div className='text-sm font-semibold text-gray-900'>
									{venue.name}
								</div>
								{address ? <div className='text-sm font-normal text-gray-600'>
										{address}
									</div> : null}
							</div>
						)
					})}
				</div>
			</div>
			<div className='flex flex-1 items-center justify-end gap-4 border-t p-4'>
				<HSButton color='light' onClick={onClose}>
					Close
				</HSButton>
			</div>
		</HSDrawer>
	)
}

export default VenueListViewModal
