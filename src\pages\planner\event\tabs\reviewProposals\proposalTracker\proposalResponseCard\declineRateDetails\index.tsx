import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import HSBadge from 'components/badge'
import HSDon<PERSON><PERSON>hart from 'components/charts/donutChart'
import HSLegend from 'components/charts/legends'
import HSDrawer from 'components/drawer'
import HSTable from 'components/table'
import HSTooltipWithEllipsis from 'components/tooltipEllipsis'
import { Drawer } from 'flowbite-react'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { ProposalRequestStatusMap } from 'lib/helpers/statusMaps'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { ProposalRequest } from 'models/proposalResponseMonitor'
import { useEffect, useState } from 'react'

interface IDeclineRateDetails {
	showDetails: boolean
	setDetails: React.Dispatch<React.SetStateAction<boolean>>
	chartData: {
		dataKey: string
		fill: string
		value: number
		reason: string
	}[]
	filterTrackedProposalRequests: (
		proposalRequest: ProposalRequest,
		isAdmin: boolean
	) => boolean
}

interface IDeclineRate {
	venueName: string | null
	declineReasons: string[]
	comment: string | null
	venueLocation: string | null
}

const DeclineRateDetails = (properties: IDeclineRateDetails) => {
	const { showDetails, setDetails, chartData, filterTrackedProposalRequests } =
		properties
	const { eventInfo: eventPlan } = eventInfoStore()
	const { userProfile } = useUserProfileContext()
	const [declineData, setDeclineData] = useState<IDeclineRate[]>([])

	useEffect(() => {
		if (eventPlan?.id && eventPlan.proposalRequests) {
			const proposalRequests = eventPlan.proposalRequests.filter(
				pr =>
					userProfile && filterTrackedProposalRequests(pr, userProfile.isAdmin)
			)
			const proposalRequestsNotRemoved = proposalRequests.filter(
				pr => pr.status !== ProposalRequestStatusMap.Removed?.key
			)

			setDeclineData(
				proposalRequestsNotRemoved
					.filter(pr => ProposalRequestStatusMap.Declined?.key === pr.status)
					.map(pr => ({
						venueName: pr.venueName ?? '',
						declineReasons: pr.venueDeclinedReasons,
						comment: pr.venueDeclinedComment ?? '-',
						venueLocation: pr.venueLocation ?? '-'
					}))
			)
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [eventPlan?.id, eventPlan?.proposalRequests, userProfile])

	const formatDeclineReasons = (item: IDeclineRate) => (
		<div className='flex gap-1'>
			{item.declineReasons.length > 0 && (
				<div className='text-gray-600'>{item.declineReasons[0]}</div>
			)}
			{item.declineReasons.length > 1 && (
				<div>
					<TooltipComponent content={item.declineReasons.slice(1).join(', ')}>
						<HSBadge color='gray'>{`+${item.declineReasons.length - 1}`}</HSBadge>
					</TooltipComponent>
				</div>
			)}
		</div>
	)

	const formatVenue = (item: IDeclineRate) => (
		<div className='flex flex-col'>
			<div className='text-sm font-normal text-gray-600'>{item.venueName}</div>
			<div className='text-xs font-medium text-gray-400'>
				{item.venueLocation}
			</div>
		</div>
	)

	const formatComment = (item: IDeclineRate) => (
		<div>
			<span className='text-overflow'>
				<HSTooltipWithEllipsis content={item.comment} />
			</span>
		</div>
	)

	return (
		<div>
			<HSDrawer
				position='right'
				onClose={() => {
					setDetails(false)
				}}
				open={showDetails}
				style={{ width: '800px' }}
			>
				<Drawer.Header titleIcon={() => null} title='Decline Rate' />
				<Drawer.Items
					style={{ height: 'calc(100vh - 10rem)' }}
					className='overflow-auto'
				>
					<div className='flex flex-col gap-8 px-4'>
						<div className='card flex items-center'>
							<div className='flex items-center gap-4 p-4'>
								<HSDonutChart
									data={chartData.map(item => ({
										name: item.dataKey,
										value: item.value,
										color: item.fill
									}))}
									size={150}
									labelSize='text-3xl'
									showTooltip
									label={`${chartData.reduce((accumulator, item) => accumulator + item.value, 0)}`}
								/>
								<div className='flex flex-col gap-2'>
									<div className='text-sm font-medium text-gray-900'>
										Top-5 Decline Reasons:
									</div>
									<div className='flex flex-col gap-1'>
										<HSLegend
											data={chartData
												.sort((a, b) => b.value - a.value)
												.slice(0, 5)
												.map(item => ({
													label: `${item.reason} (${item.value})`,
													color: item.fill
												}))}
										/>
									</div>
								</div>
							</div>
						</div>
						<div className='card flex h-fit flex-col'>
							<div className='flex items-center p-4 text-base font-medium text-gray-700'>
								{declineData.length} Hotel{declineData.length > 1 ? 's' : ''}{' '}
								that Declined RFP
							</div>
							<div className='overflow-auto'>
								<div className='disabled-rounded-border border-t'>
									<HSTable
										rows={declineData}
										allowPaging={false}
										columns={[
											{
												field: 'venueName',
												headerText: 'Hotel Name',
												width: 200,
												sortable: true,
												render: formatVenue
											},

											{
												field: 'declineReasons',
												headerText: 'Decline Reason(s)',
												width: 200,
												sortable: true,
												render: formatDeclineReasons
											},
											{
												field: 'comment',
												headerText: 'Hotel’s comment',
												width: 250,
												sortable: true,
												render: formatComment,
												clipMode: 'ellipsis'
											}
										]}
									/>
								</div>
							</div>
						</div>
					</div>
				</Drawer.Items>
			</HSDrawer>
		</div>
	)
}

export default DeclineRateDetails
