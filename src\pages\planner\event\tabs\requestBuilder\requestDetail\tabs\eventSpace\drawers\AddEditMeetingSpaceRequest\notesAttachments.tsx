/* eslint-disable react/no-array-index-key */
import HSDropzone from 'components/dropzone'
import HSTextArea from 'components/textarea'
import meetingSpaceRequestStore from './store'

interface NotesAttachmentsProperties {
	setAcceptedFiles: React.Dispatch<React.SetStateAction<File[]>>
	acceptedFiles: File[]
}

const NotesAttachments = (properties: NotesAttachmentsProperties) => {
	const { acceptedFiles, setAcceptedFiles } = properties
	const { meetingSpaceRequest, setMeetingSpaceRequest } =
		meetingSpaceRequestStore()

	return meetingSpaceRequest ? (
		<div className='flex flex-col gap-8'>
			<div className='flex flex-col gap-2'>
				<div className='text-sm font-medium text-gray-900'>Room Notes</div>
				<HSTextArea
					rows={6}
					placeholder='Add notes for hotels about this meeting space that you would like to share with hotels for this specific meeting space request'
					value={meetingSpaceRequest.notes ?? ''}
					onChange={event =>
						setMeetingSpaceRequest({
							...meetingSpaceRequest,
							notes: event.target.value
						})
					}
				/>
			</div>
			<div className='flex flex-col gap-2'>
				<div className='text-sm font-medium text-gray-900'>
					Room Attachments
				</div>

				<div className='flex flex-col gap-1'>
					<div className='text-sm font-normal text-gray-700'>
						Upload room-specific documents for hotels
					</div>
					<HSDropzone
						setAcceptedFiles={setAcceptedFiles}
						acceptedFiles={acceptedFiles}
						type='document'
						subText='SVG, PNG, JPG or GIF (MAX. 800x400px)'
					/>
				</div>
			</div>
		</div>
	) : null
}

export default NotesAttachments
