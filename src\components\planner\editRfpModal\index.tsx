import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import api from 'lib/interceptor/axios.interceptor'
import HSModal from 'components/modal'
import HSButton from 'components/button'
import HSToggleSwitch from 'components/toggleSwitch'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTriangleExclamation } from '@fortawesome/pro-regular-svg-icons'

interface IEditRfpModalProperties {
	show: boolean
	onClose: () => void
	eventId: string
	setEventInfo: (action: {
		action: { type: string; clientOnly?: boolean }
		value: unknown
	}) => void
}

const EditRfpModal = ({
	show,
	onClose,
	eventId,
	setEventInfo
}: IEditRfpModalProperties) => {
	const [requiresUpdate, setRequiresUpdate] = useState(false)
	const [loading, setLoading] = useState(false)
	const navigate = useNavigate()

	const handleConfirm = () => {
		setLoading(true)

		if (requiresUpdate) {
			api
				.put<{ data: unknown }>(
					`/sourcing/api/events/plans/${eventId}/editSent`
				)
				.then(response => {
					setEventInfo({
						action: { type: 'setObject', clientOnly: true },
						value: response.data
					})
					onClose()
				})
				.catch((error: unknown) => {
					console.error(error)
				})
				.finally(() => setLoading(false))
		} else {
			onClose()
			navigate(`/planner/event/${eventId}/builder/add-hotels`)
		}
	}

	return (
		<HSModal openModal={show} onClose={onClose} header='Edit RFP' size='xl'>
			<div className='flex flex-col gap-4 p-5'>
				<p className='text-base font-normal text-gray-600'>
					Please confirm what you would like to update so we know whether to
					notify hotels who may be working on proposals
				</p>

				<div className='flex items-start gap-2'>
					<FontAwesomeIcon
						icon={faTriangleExclamation}
						className='mt-0.5 text-orange-600'
					/>
					<p className='text-sm font-medium text-orange-600'>
						Use this option only if you&apos;re changing details that would
						affect space, rates, or booking dates
					</p>
				</div>

				<p className='text-base font-normal text-gray-500'>
					You do not need to use this option if you&apos;re adding hotels,
					industry contacts, or editing other details that have no effect on
					space or room availability.
				</p>

				<div className='mt-2 flex items-start gap-2'>
					<HSToggleSwitch
						checked={requiresUpdate}
						onChange={checked => setRequiresUpdate(checked)}
					/>
					<div>
						<div className='text-sm font-medium'>
							I need to edit specific criteria including event dates, room
							counts, event space, food and beverage, questions, and
							concessions.
						</div>
						<p className='mt-1 text-xs font-normal text-gray-500'>
							This option will require hotels to update and resubmit their
							proposals.
						</p>
					</div>
				</div>

				<div className='mt-4 border-t border-gray-200' />

				<div className='flex justify-between gap-4 p-4'>
					<HSButton
						color='light'
						onClick={onClose}
						className='w-1/2'
						disabled={loading}
					>
						Cancel
					</HSButton>
					<HSButton
						color='primary'
						onClick={handleConfirm}
						className='w-1/2'
						disabled={loading}
					>
						Edit RFP
					</HSButton>
				</div>
			</div>
		</HSModal>
	)
}

export default EditRfpModal
