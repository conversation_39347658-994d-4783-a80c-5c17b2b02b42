/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable react/jsx-handler-names */
import { ROLE_ADMIN, ROLE_PLANNER } from 'lib/helpers/roles'
import NotificationSettings from '../tabs/notificationSettings'
import AdminActivity from '../tabs/adminActivity'
// import { MultiSelectComponent } from '@syncfusion/ej2-react-dropdowns'
import NotificationHistory from 'components/notificationHistory'
import { targetTypes } from 'models/notificationQueueMessage'
import AdminMatchingCriteria from '../tabs/adminMatchingCriteria'
import ConcessionRequests from '../tabs/concessionRequests'
import Questions from '../tabs/questions'
import { useState } from 'react'
import { rfpImportSourceFileTypes } from 'lib/helpers/importSourceFileTypes'
import ImportTemplates from 'components/importTemplates'
import ImportTemplatesExcel from 'components/ImportTemplatesExcel'
import Templates from 'components/templates'
import useUserProfileStore from 'lib/store/userProfileStore'
import type { ITab } from 'components/tab'
import ProfileOverviewCommon from '../tabs/overviewCommon'
import CustomSetting from '../tabs/customSetting'
import type { IUserProfile } from 'models/userProfiles'
import type { INewDestination } from 'models/destinations'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { useLocation, useParams } from 'react-router-dom'

// eslint-disable-next-line import/prefer-default-export
export const useLoadProfile = () => {
	const { userProfile, setProperty, mergeProperties } = useUserProfileStore()
	const { userProfile: loggedInUser } = useUserProfileContext()
	const { id } = userProfile
	const { userId: routeUserId } = useParams()
	const location = useLocation()
	const [rfpImportSourceFileType, setRfpImportSourceFileType] = useState<
		keyof typeof rfpImportSourceFileTypes | null
	>(null)

	const tabs = [
		{
			header: 'My Profile',
			template: (
				<div className='flex flex-col flex-wrap'>
					<ProfileOverviewCommon />
				</div>
			),
			roles: ['*'],
			path: 'profile',
			key: 0
		},

		{
			header: 'Notification Settings',
			template: <NotificationSettings />,
			roles: ['*'],
			path: 'notification-settings',
			key: 1
		},
		{
			header: 'My Templates',
			template: (
				<div>
					{rfpImportSourceFileType === rfpImportSourceFileTypes.Pdf ? (
						<ImportTemplates
							// template={undefined}
							// onClose={() => {}}
							// type='pdf'
							onComplete={() => setRfpImportSourceFileType(null)}
						/>
					) : rfpImportSourceFileType === rfpImportSourceFileTypes.Excel ? (
						<ImportTemplatesExcel
							onComplete={() => setRfpImportSourceFileType(null)}
						/>
					) : (
						<Templates
							onClickImportTemplates={(
								fileType: keyof typeof rfpImportSourceFileTypes
							) => setRfpImportSourceFileType(fileType)}
						/>
					)}
				</div>
			),
			roles: [ROLE_PLANNER, ROLE_ADMIN],
			path: 'my-templates',
			key: 2
		},
		{
			header: 'My Questions',
			template: <Questions userProfile={userProfile} />,
			roles: [ROLE_PLANNER, ROLE_ADMIN],
			path: 'my-questions',
			key: 3
		},
		{
			header: 'My Concession Requests',
			template: <ConcessionRequests userProfile={userProfile} />,
			roles: [ROLE_PLANNER, ROLE_ADMIN],
			path: 'my-concession-requests',
			key: 4
		},
		// {
		// 	header: 'My Hotel Reviews',
		// 	template: <PlannerReviews />,
		// 	roles: [ROLE_PLANNER]
		// },
		{
			header: 'Custom Settings (ADMIN)',
			template: <CustomSetting />,
			roles: [ROLE_ADMIN],
			path: 'custom-settings',
			key: 5
		},
		{
			header: 'Property Matching (ADMIN)',
			template: (
				<AdminMatchingCriteria
					profile={userProfile}
					setProperty={setProperty}
					mergeProperties={(
						profile: Partial<IUserProfile> | Partial<INewDestination>
					) => mergeProperties(profile as Partial<IUserProfile>)}
				/>
			),
			roles: [ROLE_ADMIN],
			path: 'property-matching',
			key: 6
		},
		{
			header: 'App Activity (ADMIN)',
			template: <AdminActivity />,
			roles: [ROLE_ADMIN],
			path: 'app-activity',
			key: 7
		},
		{
			header: 'Notification History (ADMIN)',
			template: (
				<NotificationHistory
					targetType={targetTypes.UserProfile as keyof typeof targetTypes}
					targetId={id ?? ''}
				/>
			),
			roles: [ROLE_ADMIN],
			path: 'notification-history',
			key: 8
		}
	]

	const filteredTabs: ITab[] = tabs
		.filter(
			tab =>
				tab.roles.includes('*') || tab.roles.includes(loggedInUser?.role ?? '')
		)
		.map((tab, index) => ({
			title: tab.header,
			children: tab.template,
			key: index,
			path: location.pathname.includes('admin-tools')
				? `/admin-tools/general/user-profile/${tab.path}/${routeUserId}`
				: `/my-profile/${tab.path}`
		}))

	return filteredTabs
}
