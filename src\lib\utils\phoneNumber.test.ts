import isValidPhoneNumber from './phoneNumber'

describe('isValidPhoneNumber', () => {
	it('returns true for a valid US number', () => {
		expect(isValidPhoneNumber('+14155552671')).toBe(true)
	})

	it('returns true for a valid UK number', () => {
		expect(isValidPhoneNumber('+************')).toBe(true)
	})

	it('returns false for an invalid number', () => {
		expect(isValidPhoneNumber('12345')).toBe(false)
	})

	it('returns false for an empty string', () => {
		expect(isValidPhoneNumber('')).toBe(false)
	})

	it('returns false for a string with letters', () => {
		expect(isValidPhoneNumber('abc123')).toBe(false)
	})

	it('returns false for a valid number with missing plus sign', () => {
		expect(isValidPhoneNumber('14155552671')).toBe(false)
	})

	it('returns true for a valid Indian number', () => {
		expect(isValidPhoneNumber('+************')).toBe(true)
	})

	it('returns false for a too short number', () => {
		expect(isValidPhoneNumber('+1415')).toBe(false)
	})

	it('returns false for a too long number', () => {
		expect(isValidPhoneNumber('+14155552671123456789')).toBe(false)
	})
})
