/* eslint-disable @typescript-eslint/consistent-return */
/* eslint-disable consistent-return */

import { useEffect } from 'react'

interface TransitLayerProperties {
	map: google.maps.Map | null
}

const TransitLayer = (properties: TransitLayerProperties) => {
	const { map } = properties

	useEffect(() => {
		if (!map) return

		const transitLayer = new google.maps.TransitLayer()
		transitLayer.setMap(map)
		return () => {
			transitLayer.setMap(null)
		}
	}, [map])

	return <div />
}

export default TransitLayer
