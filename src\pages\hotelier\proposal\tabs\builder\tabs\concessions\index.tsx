/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable react/no-array-index-key */
/* eslint-disable unicorn/no-array-reduce */
import { useCurrencyContext } from 'lib/contexts/currency.context'
import { formatCurrency, UnitedStatesDollar } from 'lib/helpers'
import {
	requestGroups,
	sortRequestGroups,
	sortRequests
} from 'lib/helpers/requestGroups'
import useConcessionRequestStore from 'lib/store/concessionRequests'
import useQuoteRequestStore from 'lib/store/quoteRequestStore'
import { useCallback, useEffect, useState } from 'react'
import type { IGroup } from '../questions/models'
import ConcessionCard from './card'
import HSProgress from 'components/progressBar'
import { Accordion } from 'flowbite-react'
import HSBadge from 'components/badge'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCircle, faCircleCheck } from '@fortawesome/pro-light-svg-icons'
import type { IConcessionRequest } from 'models/eventPlans'
import type { IConcessionEventResponse } from 'models/proposalResponseMonitor'
import { useParams } from 'react-router-dom'
import HSButton from 'components/button'

const Concessions = () => {
	const [groups, setGroups] = useState<IGroup[]>([])
	const { currencies } = useCurrencyContext()
	const [currency, setCurrency] = useState(UnitedStatesDollar)
	const [totalValue, setTotalValue] = useState(0)

	const { quoteRequest } = useQuoteRequestStore()
	const { concessionRequests, setConcessionResponse } =
		useConcessionRequestStore()
	const { venueId } = useParams()
	const [currentPage, setCurrentPage] = useState(1)

	useEffect(() => {
		setCurrency(currencies[quoteRequest.proposalRequest?.currencyCode ?? ''])
	}, [currencies, quoteRequest.proposalRequest?.currencyCode])

	useEffect(() => {
		const updatedConcessionGroups = concessionRequests
			.reduce((a: IGroup[], c) => {
				if (
					!a.some(g => g.id === (c.concessionRequest.requestGroupId || 'none'))
				) {
					a.push(
						c.concessionRequest.requestGroupId
							? {
									id: c.concessionRequest.requestGroupId,
									name:
										requestGroups[c.concessionRequest.requestGroupId]?.name ??
										'',
									sortIndex:
										requestGroups[c.concessionRequest.requestGroupId]
											?.sortIndex ?? 0
								}
							: {
									id: 'none',
									name: 'No Grouping Specified',
									sortIndex: 0
								}
					)
				}
				return a
			}, [])
			.sort(sortRequestGroups)

		setGroups(updatedConcessionGroups)
		setTotalValue(
			concessionRequests.reduce((a, c) => {
				if (c.response.responses?.some(rr => rr.text === 'Yes'))
					return a + Number(c.response.concessionValueProposed ?? 0)
				return a
			}, 0)
		)
	}, [concessionRequests])

	const onChange = (
		cr: {
			concessionRequest: IConcessionRequest
			response: IConcessionEventResponse | null
		},
		name: string,
		value:
			| {
					text: string
					score: number
					requireComment?: boolean | null
			  }[]
			| string,
		selected: boolean
	) => {
		if (name === 'responses') {
			if (!selected) {
				value = []
			}
		} else if (name === 'concessionValueProposed' && value === '') {
			value = null
		}
		if (cr.response) {
			const updatedResponse = {
				...cr.response,
				concessionRequestId: cr.concessionRequest.id,
				eventPlanId: cr.concessionRequest.eventPlanId,
				createdByType: 'Hotelier',
				[name]: value
			}
			setConcessionResponse(
				cr.concessionRequest?.eventPlanId ?? '',
				cr.concessionRequest?.venueId ?? venueId ?? '',
				cr.concessionRequest?.id ?? '',
				updatedResponse
			)
		}
	}

	const getQuestionGroupInfo = useCallback(
		(requestGroupId: string) => {
			const group = concessionRequests.filter(
				item =>
					(item.concessionRequest.requestGroupId || 'none') === requestGroupId
			)

			const answered = group.filter(item => {
				const concessionIsApproved =
					item?.response.responses && item.response.responses[0]?.score > 0

				return (
					(concessionIsApproved &&
						item.response.concessionValueProposed !== null) ||
					(item.response?.response?.trim().length ?? 0) > 0 ||
					item.response?.responses?.[0]?.score === -1
				)
			}).length
			const total = group.length

			return { answered, total }
		},
		[concessionRequests]
	)
	return (
		<div>
			<div className='flex flex-col gap-6'>
				<div className='flex justify-between gap-6'>
					<div className='flex flex-col gap-2'>
						<div className='text-xl font-semibold text-gray-900'>
							Requested Concessions
						</div>
						<div className='text-sm font-normal text-gray-500'>
							Please respond to each of the planner&apos;s requests below. Your
							responses will save automatically and will be sent to the planner
							when you submit your proposal.
						</div>
					</div>
					{concessionRequests.length > 0 ? (
						<div className='w-64'>
							<div className='card h-full items-start justify-center bg-primary-50 p-2'>
								<div className='flex flex-col'>
									<div className='text-sm font-medium text-gray-900'>
										Total Concessions Value
									</div>
									<div className='text-2xl font-semibold text-green-500'>
										{formatCurrency(totalValue, currency)}
									</div>
								</div>
							</div>
						</div>
					) : null}
				</div>
				{concessionRequests.length > 0 ? (
					<div className='flex flex-row gap-6'>
						<div className='card !w-2/3'>
							<ConcessionCard
								concessionGroup={concessionRequests.sort((c, n) =>
									sortRequests(c.concessionRequest, n.concessionRequest)
								)}
								onChange={onChange}
								currency={currency}
								currentPage={currentPage}
								setCurrentPage={setCurrentPage}
							/>
						</div>
						<div className='card flex !w-1/3 flex-col'>
							<div className='flex flex-col gap-2 p-4'>
								<div className='flex items-center justify-between'>
									<div className='font-medium text-gray-700'>
										Completion status
									</div>
									<div className='text-2xl leading-none'>
										<span className='font-semibold text-gray-900'>
											{
												concessionRequests.filter(item => {
													const concessionIsApproved =
														item?.response.responses &&
														item.response.responses[0]?.score > 0

													return (
														(concessionIsApproved &&
															item.response.concessionValueProposed !== null) ||
														(item.response?.response?.trim().length ?? 0) > 0 ||
														item.response?.responses?.[0]?.score === -1
													)
												}).length
											}
										</span>
										<span className='font-normal text-gray-500'>
											/{concessionRequests.length}
										</span>
									</div>
								</div>
								<div>
									<HSProgress
										color='primary'
										progress={
											(concessionRequests.filter(
												item =>
													(item.response?.response?.length ?? 0) > 0 ||
													(item.response?.responses?.length ?? 0) > 0
											).length /
												concessionRequests.length) *
											100
										}
									/>
								</div>
							</div>
							<div className='border-b border-gray-200' />
							<div className='accordion'>
								<Accordion className='!border-none'>
									{groups.map((requestGroup, requestGroupIndex) => (
										<Accordion.Panel key={requestGroupIndex}>
											<Accordion.Title>
												<div className='flex flex-row items-center gap-2'>
													<div className='text-gray-900'>
														{requestGroup.name}
													</div>
													<div>
														<HSBadge
															color={
																getQuestionGroupInfo(requestGroup.id)
																	.answered ===
																getQuestionGroupInfo(requestGroup.id).total
																	? 'success'
																	: 'red'
															}
														>
															{getQuestionGroupInfo(requestGroup.id).answered}/
															{getQuestionGroupInfo(requestGroup.id).total}
														</HSBadge>
													</div>
												</div>
											</Accordion.Title>
											<Accordion.Content>
												{concessionRequests
													.filter(
														item =>
															(item.concessionRequest.requestGroupId ||
																'none') === requestGroup.id
													)
													.map((concession, concessionIndex) => {
														const concessionIsApproved =
															concession?.response.responses &&
															concession.response.responses[0]?.score > 0

														const answered =
															(concessionIsApproved &&
																concession.response.concessionValueProposed !==
																	null) ||
															(concession.response?.response?.trim().length ??
																0) > 0 ||
															concession.response?.responses?.[0]?.score === -1

														const isSelectedConcession =
															currentPage ===
															concessionRequests.findIndex(
																q =>
																	q.concessionRequest.id ===
																	concession.concessionRequest.id
															) +
																1
														return (
															<div
																key={concessionIndex}
																className={`${answered ? '!text-green-600' : 'text-red-600'} ${
																	isSelectedConcession ? 'bg-primary-50' : ''
																} cursor-normal group flex justify-between !py-2 pl-7 pr-4 text-sm font-normal text-gray-600 ${isSelectedConcession ? 'hover:bg-primary-100' : 'hover:bg-gray-50'}`}
															>
																<div className='flex items-center gap-2'>
																	<div>
																		<FontAwesomeIcon
																			icon={answered ? faCircleCheck : faCircle}
																		/>
																	</div>
																	<div>Concession {concessionIndex + 1}</div>
																</div>

																<div className='hidden group-hover:block'>
																	<HSButton
																		color='text'
																		onClick={() => {
																			setCurrentPage(
																				concessionRequests.findIndex(
																					q =>
																						q.concessionRequest.id ===
																						concession.concessionRequest.id
																				) + 1
																			)
																		}}
																		size='xs'
																	>
																		View
																	</HSButton>
																</div>
															</div>
														)
													})}
											</Accordion.Content>
										</Accordion.Panel>
									))}
								</Accordion>
							</div>
						</div>
					</div>
				) : (
					<div className='flex h-64 items-center justify-center'>
						<div className='text-sm font-normal text-primary-disabled'>
							Concessions Has Not Been Requested
						</div>
					</div>
				)}
			</div>
		</div>
	)
}

export default Concessions
