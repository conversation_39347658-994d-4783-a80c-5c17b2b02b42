import { customTabComponentTheme } from 'components/tab'
import { useLoadProfile } from './helper'
import type { TabsRef } from 'flowbite-react'
import { Tabs } from 'flowbite-react'
import { useNavigate, useParams } from 'react-router-dom'
import { useEffect, useRef } from 'react'

const Profile = () => {
	const tabData = useLoadProfile()
	const tabReference = useRef<TabsRef | null>(null)
	const navigate = useNavigate()
	const { activeTab } = useParams()

	useEffect(() => {
		if (tabReference.current) {
			if (activeTab) {
				const active =
					tabData.find(view =>
						view.path?.toLowerCase().includes(activeTab.toLowerCase())
					)?.key ?? 0
				tabReference.current.setActiveTab(Number(active))
			} else {
				tabReference.current.setActiveTab(Number(0))
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [activeTab])

	return (
		<Tabs
			ref={tabReference}
			theme={customTabComponentTheme('vertical')}
			onActiveTabChange={tab => {
				navigate(tabData[tab]?.path ?? '')
			}}
		>
			{tabData.map((tab, index) => (
				<Tabs.Item key={tab.key} title={tab.title}>
					<div className='card'>
						{(tabData.find(view =>
							view.path?.toLowerCase().includes(activeTab?.toLowerCase() ?? '')
						)?.key ?? 0) === index
							? tab.children
							: null}
					</div>
				</Tabs.Item>
			))}
		</Tabs>
	)
}

export default Profile
