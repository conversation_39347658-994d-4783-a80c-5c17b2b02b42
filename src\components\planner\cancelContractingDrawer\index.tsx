/* eslint-disable @typescript-eslint/no-unused-vars */
import HSButton from 'components/button'
import type { EventPlan } from 'models/proposalResponseMonitor'
import Loader from 'components/loader'
import HSDrawer from 'components/drawer'
import HSTextArea from 'components/textarea'
import HSRadioButton from 'components/radioButton'
import { Drawer } from 'flowbite-react'
import { useState } from 'react'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { toast } from 'react-toastify'

interface IEventPlanContractSignedDrawerProperties {
	showContractSignedDrawer: boolean
	onCancelMarkContractSigned: () => void
	onContractEventPlan?: (cancelContext: {
		cancelRfp: boolean | null
		editRfp: boolean | null
		internalNotes: string
		cancelHotelNotes: string
		stopProposals: boolean
	}) => void
	eventPlan: EventPlan
	setEventInfo?: (info: EventPlan) => void
}

const CancelContractingDrawer = (
	properties: IEventPlanContractSignedDrawerProperties
) => {
	const {
		showContractSignedDrawer,
		onCancelMarkContractSigned,
		onContractEventPlan,
		eventPlan
	} = properties
	const { userProfile } = useUserProfileContext()
	const { mergeProperties, eventInfo } = eventInfoStore()
	const [cancelRfp, setCancelRfp] = useState<boolean | null>(null)
	const [editRfp, setEditRfp] = useState<boolean | null>(null)
	const [internalNotes, setInternalNotes] = useState('')
	const [hotelNote, setHotelNote] = useState('')
	const [stopProposals, setStopProposals] = useState('yes')
	const [isFetchingEventInfo, setIsFetchingEventInfo] = useState(false)

	const onSubmit = () => {
		if (!cancelRfp && editRfp === null) {
			toast.warning(
				'Please select whether to use existing proposals or edit this RFP.'
			)
			return
		}

		const cancelContext = {
			cancelRfp,
			editRfp,
			internalNotes,
			cancelHotelNotes: hotelNote,
			stopProposals: stopProposals === 'yes'
		}

		onContractEventPlan?.(cancelContext)
	}

	return (
		<HSDrawer
			open={showContractSignedDrawer}
			onClose={onCancelMarkContractSigned}
			position='right'
			style={{ width: '700px' }}
			noPadding
		>
			<Drawer.Header
				title='Cancel Contracting'
				titleIcon={() => null}
				className='px-4 pt-4'
			/>
			{isFetchingEventInfo ? (
				<div className='flex !h-32 items-center justify-center p-5'>
					<Loader />
				</div>
			) : (
				<>
					<div
						className='flex flex-col gap-6 overflow-auto px-4'
						style={{ height: 'calc(100vh - 10rem)' }}
					>
						<div className='text-sm font-medium text-gray-900'>
							Please select why you&apos;re not moving forward with this hotel.
						</div>
						<div className='flex flex-col gap-2 text-sm font-normal'>
							<HSRadioButton
								name='cancelReason'
								value='cancelEvent'
								label='We are canceling this event'
								selectedValue={cancelRfp === true ? 'cancelEvent' : ''}
								onChange={() => {
									setCancelRfp(true)
									setEditRfp(null)
								}}
							/>
							<HSRadioButton
								name='cancelReason'
								value='cancelContracting'
								label='I am canceling the contracting process with this hotel'
								selectedValue={cancelRfp === false ? 'cancelContracting' : ''}
								onChange={() => {
									setCancelRfp(false)
									setEditRfp(null)
								}}
							/>

							{cancelRfp === false && (
								<div className='flex flex-col gap-2 pl-4'>
									<HSRadioButton
										name='editRfp'
										value='useExisting'
										label='I will begin contracting with another hotel based on an existing proposal'
										selectedValue={editRfp === false ? 'useExisting' : ''}
										onChange={() => setEditRfp(false)}
									/>
									<HSRadioButton
										name='editRfp'
										value='editRfp'
										label='I need to make changes to the RFP and request new proposals'
										selectedValue={editRfp === true ? 'editRfp' : ''}
										onChange={() => setEditRfp(true)}
									/>
									{editRfp !== null && (
										<div className='text-xs text-gray-700'>
											{editRfp
												? 'This RFP will be in edit mode. You can modify hotels and resend the RFP.'
												: 'This RFP will remain active. You can start contracting with another proposal.'}
										</div>
									)}
								</div>
							)}
						</div>

						<div className='border-b' />

						<div className='flex flex-col gap-2'>
							<div className='text-sm font-medium text-gray-900'>
								Internal Notes (not visible to hotels):
							</div>
							<HSTextArea
								placeholder='Enter comment'
								value={internalNotes}
								onChange={event => setInternalNotes(event.target.value)}
								rows={4}
							/>
						</div>

						<div className='flex flex-col gap-2'>
							<div className='text-sm font-medium text-gray-900'>
								Note to {eventPlan.proposalRequests?.[0]?.venueName ?? 'Hotel'}:
							</div>
							<HSTextArea
								placeholder='Enter comment'
								value={hotelNote}
								onChange={event => setHotelNote(event.target.value)}
								rows={4}
							/>
						</div>

						<div className='border-b' />

						{eventInfo?.id ? (
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-medium text-gray-900'>
									Stop Receiving New Proposals:
								</div>
								<div className='flex gap-8'>
									<HSRadioButton
										label='Yes'
										name='proposalStatus'
										value='yes'
										selectedValue={eventInfo.proposalsPaused ? 'yes' : 'no'}
										onChange={() =>
											mergeProperties({
												proposalsPaused: true,
												proposalsPausedBy: userProfile?.id,
												proposalsPausedAt: new Date().toISOString(),
												proposalsPausedComment: null
											})
										}
									/>
									<HSRadioButton
										label='No'
										name='proposalStatus'
										value='no'
										selectedValue={eventInfo.proposalsPaused ? 'yes' : 'no'}
										onChange={() =>
											mergeProperties({
												proposalsPaused: false,
												proposalsPausedBy: null,
												proposalsPausedAt: null,
												proposalsPausedComment: null
											})
										}
									/>
								</div>
							</div>
						) : null}
					</div>

					<div className='flex justify-between gap-4 p-4'>
						<HSButton
							className='w-full'
							color='light'
							size='sm'
							onClick={onCancelMarkContractSigned}
						>
							Cancel
						</HSButton>
						<HSButton
							className='w-full'
							color='primary'
							size='sm'
							onClick={onSubmit}
							disabled={!cancelRfp && editRfp === null}
						>
							Cancel Contract
						</HSButton>
					</div>
				</>
			)}
		</HSDrawer>
	)
}

export default CancelContractingDrawer


