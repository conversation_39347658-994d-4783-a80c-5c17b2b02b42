/* eslint-disable react/no-unused-prop-types */
import type { IconDefinition } from '@fortawesome/pro-regular-svg-icons'
import { faBed } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import type { AdvancedMarkerRef } from '@vis.gl/react-google-maps'
import { AdvancedMarker, Pin } from '@vis.gl/react-google-maps'
import HSTooltip from 'components/tooltip'
import { forwardRef, memo } from 'react'

interface IHotelMarkerProperties {
	position: google.maps.LatLngLiteral
	onClick: (event: google.maps.MapMouseEvent) => void
	backgroundColor?: string | null
	title?: string | null
	icon?: IconDefinition | null
	iconColor?: string | null
}

const HotelMarker = memo(
	forwardRef<AdvancedMarkerRef, IHotelMarkerProperties>(
		(
			properties: IHotelMarkerProperties,
			reference: React.ForwardedRef<AdvancedMarkerRef>
		) => {
			const { onClick, position, backgroundColor, title, iconColor, icon } =
				properties
			return (
				<div title={title ?? ''}>
					<HSTooltip
						content={title ?? ''}
						title={title ?? ''}
						placement='top'
						trigger='hover'
					>
						<AdvancedMarker
							position={position}
							onClick={onClick}
							ref={reference}
						>
							<Pin
								background={backgroundColor ?? '#0a587e'}
								borderColor='#1e89a1'
								scale={1.2}
							>
								<FontAwesomeIcon
									icon={icon ?? faBed}
									className={iconColor ?? 'text-white'}
									size='xl'
								/>
							</Pin>
						</AdvancedMarker>
					</HSTooltip>
				</div>
			)
		}
	)
)

export default HotelMarker
