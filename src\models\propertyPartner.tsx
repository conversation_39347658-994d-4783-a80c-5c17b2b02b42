import type { LicenseInfo, SubscriptionInfo } from './organizations'
import type { OrganizationRoles } from './userProfiles'

export interface Member {
	id: string | null
	firstName: string | null
	lastName: string | null
	sendAllRFPs: boolean
	createdBy: string | null
	created: string | null
	organizationRoles?: OrganizationRoles | null
	licenseInfo?: LicenseInfo
}

export interface PropertyPartnerSeller {
	id: string | null
	propertyId: string | null
	itemType: string | null
	name: string | null
	address: string | null
	address2: string | null
	city: string | null
	state: string | null
	zip: string | null
	country: string | null
	phone: string | null
	paymentCustomerId: string | null
	currentSubscriptionInfo: SubscriptionInfo
	members: Member[]
	firstAccountCreated: string | null
	receivedRfp: string | null
	respondedToRfp: string | null
	wonRfp: string | null
	addMembersWithEmailDomain: string | null
	memberEmailDomain: string | null
	created: string | null
	createdBy: string | null
	deleted: string | null
	deletedBy: string | null
	isDeleted: boolean
	modifiedBy: string | null
	modified: string | null
	include?: boolean | null
	logoImageUrl: string | null
}
