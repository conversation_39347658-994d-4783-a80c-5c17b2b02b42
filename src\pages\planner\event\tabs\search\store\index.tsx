import type { IDestinationLocation, INewDestination } from 'models/destinations'
import { create } from 'zustand'
import { sortModes } from '../common'
import type { EventPlan } from 'models/proposalResponseMonitor'
import type { MapLocation } from 'pages/hotel/platformSuggestions/helper'

export interface RangeFilters {
	guestRoomQuantity: [number | null, number | null]
	meetingRoomQuantity: [number | null, number | null]
	meetingSpaceSquareFeet: [number | null, number | null]
	largestMeetingSpaceSquareFeet: [number | null, number | null]
	averageRating: [number | null, number | null]
}

export interface ChoiceFilters {
	chainId: string[]
	brandId: string[]
	hotelTypes: string[]
	propertySellerId: string[]
}

interface Airports {
	filter: boolean
	types: string[]
	distanceMiles: number
}
export type SortMode = (typeof sortModes)[keyof typeof sortModes]

interface SearchParametersState {
	searching: boolean
	pageIndex: number
	pageSize: number
	sort: SortMode
	location: IDestinationLocation
	locationBounds: MapLocation[]
	rangeFilters: RangeFilters
	choiceFilters: ChoiceFilters
	airports: Airports
	radiusMiles: number
	rankedOnly: boolean
	includePrevious: boolean
	addedVenueIds: string[]
	comparableDestination: INewDestination
}

interface SearchParametersActions {
	setSearching: (searching: boolean) => void
	setPageIndex: (pageIndex: number) => void
	setPageSize: (pageSize: number) => void
	setSort: (sort: SortMode) => void
	setLocation: (location: IDestinationLocation) => void
	setLocationBounds: (locationBounds: MapLocation[]) => void
	setRangeFilter: <K extends keyof RangeFilters>(
		filterKey: K,
		value: RangeFilters[K]
	) => void
	setChoiceFilter: <K extends keyof ChoiceFilters>(
		filterKey: K,
		value: ChoiceFilters[K]
	) => void
	setAirports: (airports: Airports) => void
	setRadiusMiles: (radiusMiles: number) => void
	setRankedOnly: (rankedOnly: boolean) => void
	setIncludePrevious: (includePrevious: boolean) => void
	addVenueId: (venueId: string) => void
	removeVenueId: (venueId: string) => void
	setComparableDestination: (comparableDestination: INewDestination) => void
	onMapBoundsChanged: (bounds: google.maps.LatLngBounds) => void
	resetFilters: () => void
}

type SearchParametersStore = SearchParametersState & SearchParametersActions

// Initialize with the event data
const initializeStore = (event: EventPlan) => {
	const hasAlternateLocations =
		Number(event.siteSearch?.alternateLocations.length) > 0

	const initialState: SearchParametersState = {
		searching: hasAlternateLocations,
		pageIndex: 0,
		pageSize: 10,
		sort: sortModes.profileScore,
		location: hasAlternateLocations
			? { ...event.siteSearch?.alternateLocations[0] }
			: ({} as IDestinationLocation),
		locationBounds: [],
		rangeFilters: {
			guestRoomQuantity: [null, null],
			meetingRoomQuantity: [null, null],
			meetingSpaceSquareFeet: [null, null],
			largestMeetingSpaceSquareFeet: [null, null],
			averageRating: [null, null]
		},
		choiceFilters: {
			chainId: [],
			brandId: [],
			hotelTypes: [],
			propertySellerId: []
		},
		airports: {
			filter: false,
			types: ['large_airport'],
			distanceMiles: 10
		},
		radiusMiles: 10,
		includePrevious: false,
		rankedOnly: false,
		addedVenueIds: [],
		comparableDestination: {} as INewDestination
	}

	return initialState
}

export const useSearchParametersStore = create<SearchParametersStore>(set => ({
	...initializeStore({
		siteSearch: {
			alternateLocations: []
		}
	}),

	// Actions to update the state
	setSearching: searching => set({ searching }),
	setPageIndex: pageIndex => set({ pageIndex }),
	setPageSize: pageSize => set({ pageSize }),
	setSort: sort => set({ sort }),
	setLocation: location => set({ location }),
	setLocationBounds: locationBounds => set({ locationBounds }),

	setRangeFilter: (filterKey, value) =>
		set(state => ({
			rangeFilters: {
				...state.rangeFilters,
				[filterKey]: value
			}
		})),

	setChoiceFilter: (filterKey, value) =>
		set(state => ({
			choiceFilters: {
				...state.choiceFilters,
				[filterKey]: value
			}
		})),

	setAirports: airports => set({ airports }),
	setRadiusMiles: radiusMiles => set({ radiusMiles }),
	setRankedOnly: rankedOnly => set({ rankedOnly }),
	setIncludePrevious: includePrevious => set({ includePrevious }),

	addVenueId: venueId =>
		set(state => ({
			addedVenueIds: [...state.addedVenueIds, venueId]
		})),

	removeVenueId: venueId =>
		set(state => ({
			addedVenueIds: state.addedVenueIds.filter(id => id !== venueId)
		})),

	setComparableDestination: comparableDestination =>
		set({ comparableDestination }),
	resetFilters: () =>
		set(state => ({
			pageIndex: 0,
			rangeFilters: {
				guestRoomQuantity: [null, null],
				meetingRoomQuantity: [null, null],
				meetingSpaceSquareFeet: [null, null],
				largestMeetingSpaceSquareFeet: [null, null],
				averageRating: [null, null]
			},
			choiceFilters: {
				chainId: [],
				brandId: [],
				hotelTypes: [],
				propertySellerId: []
			},
			airports: {
				...state.airports,
				filter: false
			},
			radiusMiles: 10,
			rankedOnly: false,
			includePrevious: false
		})),
	onMapBoundsChanged: mapBounds =>
		set({
			locationBounds: [
				{
					lng: mapBounds.getNorthEast().lng(),
					lat: mapBounds.getNorthEast().lat()
				},
				{
					lng: mapBounds.getSouthWest().lng(),
					lat: mapBounds.getNorthEast().lat()
				},
				{
					lng: mapBounds.getSouthWest().lng(),
					lat: mapBounds.getSouthWest().lat()
				},
				{
					lng: mapBounds.getNorthEast().lng(),
					lat: mapBounds.getSouthWest().lat()
				},
				{
					lng: mapBounds.getNorthEast().lng(),
					lat: mapBounds.getNorthEast().lat()
				}
			]
		})
}))

// Initialize the store with event data
export const initializeSearchParametersStore = (event: EventPlan) => {
	useSearchParametersStore.setState(initializeStore(event))
}
