import HSBadge from 'components/badge'
import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import HSTextArea from 'components/textarea'
import { Drawer } from 'flowbite-react'
import type { EventPlanStatusKey } from 'lib/helpers/statusMaps'
import { EventPlanStatusMap } from 'lib/helpers/statusMaps'
import type { EventPlan } from 'models/proposalResponseMonitor'

interface IEventPlanCancelDrawerProperties {
	onCancelCancel: () => void
	onCancelEvent: () => void
	setHotelMessage: (message: string) => void
	setChangeComment: (comment: string) => void
	hotelMessage: string
	changeComment: string
	eventPlan: EventPlan
}

const EventPlanCancelDrawer = (
	properties: IEventPlanCancelDrawerProperties
) => {
	const {
		eventPlan,
		changeComment,
		hotelMessage,
		onCancelCancel,
		onCancelEvent,
		setChangeComment,
		setHotelMessage
	} = properties

	const renderRFPCard = () => (
		<div className='card flex flex-col gap-2 p-4'>
			<div className='flex items-center justify-between gap-2'>
				<div className='whitespace-nowrap text-sm font-medium text-gray-900'>
					RFP Name
				</div>
				<div className='text-sm font-normal text-gray-600'>
					{eventPlan.name}
				</div>
			</div>
			<div className='flex items-center justify-between gap-2'>
				<div className='text-sm font-medium text-gray-900'>Status</div>
				<div>
					<HSBadge
						color={
							EventPlanStatusMap[eventPlan.status as EventPlanStatusKey]
								?.color ?? 'gray'
						}
						className='w-fit p-1 text-center'
					>
						{EventPlanStatusMap[eventPlan.status as EventPlanStatusKey]
							?.label ?? eventPlan.status}
					</HSBadge>
				</div>
			</div>
		</div>
	)
	return (
		<HSDrawer
			data-testid='eventCancelModal'
			onClose={onCancelCancel}
			open
			position='right'
			style={{ width: '500px', height: '100%' }}
		>
			<Drawer.Header
				title='Cancel RFP and notify hotels'
				titleIcon={() => null}
			/>
			<Drawer.Items
				style={{ height: 'calc(100vh - 9rem)' }}
				className='overflow-auto'
			>
				<div className='flex flex-col gap-8'>
					<div className='flex flex-col gap-4'>
						<div className='text-base font-normal text-gray-500'>
							Are you sure you want to cancel this RFP? Your hotels will be
							notified via email.
						</div>
						<div>{renderRFPCard()}</div>
					</div>
					<div className='flex flex-col gap-2'>
						<div>
							<HSTextArea
								label='Reason for cancellation'
								placeholder='Provide the reason for cancellation to your hotel partners...'
								color='light'
								rows={5}
								isInvalid={
									hotelMessage.trim().length === 0 ||
									hotelMessage.trim().length <= 3
								}
								value={hotelMessage}
								onChange={event => setHotelMessage(event.target.value)}
							/>
						</div>
					</div>
					<div className='border-b border-gray-200' />
					<div className='flex flex-col gap-4'>
						<div>
							<HSTextArea
								label='Internal comment'
								placeholder='Enter internal comment here...'
								color='light'
								rows={5}
								value={changeComment}
								onChange={event => setChangeComment(event.target.value)}
							/>
						</div>
						<div className='text-base font-normal text-gray-500'>
							You will be able to reactivate this RFP by opening the Cancelled
							RFP list from the RFP Board.
						</div>
					</div>
				</div>
			</Drawer.Items>
			<div className='flex gap-4'>
				<HSButton className='flex-1' color='light' onClick={onCancelCancel}>
					Don&apos;t cancel RFP
				</HSButton>
				<HSButton
					className='flex-1'
					color='danger'
					onClick={() => onCancelEvent()}
					disabled={
						hotelMessage.trim().length === 0 || hotelMessage.trim().length <= 3
					}
				>
					Cancel RFP
				</HSButton>
			</div>
		</HSDrawer>
	)
}

export default EventPlanCancelDrawer
