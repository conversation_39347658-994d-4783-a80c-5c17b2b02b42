/* eslint-disable react/no-array-index-key */
/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable react/no-danger */
import { faCommentLines } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSDrawer from 'components/drawer'
import { Drawer, List, ListItem } from 'flowbite-react'
import { requestGroups } from 'lib/helpers/requestGroups'
import type { IQuestionEvent } from 'models/questions'

interface QuestionResponsesProperties {
	question: IQuestionEvent
	onClose: () => void
}

const QuestionResponseDrawer = (properties: QuestionResponsesProperties) => {
	const { question, onClose } = properties

	const responses = question.responses
		.filter(
			r =>
				!['New', 'Pending', 'Received', 'Declined'].includes(
					r.proposalRequestStatus ?? ''
				)
		)
		.sort((c, n) => ((c.venueName ?? '') > (n.venueName ?? '') ? 1 : -1))

	const awaitingResponses = question.responses
		.filter(r =>
			['New', 'Pending', 'Received'].includes(r.proposalRequestStatus ?? '')
		)
		.sort((c, n) => ((c.venueName ?? '') > (n.venueName ?? '') ? 1 : -1))

	const declinedResponses = question.responses
		.filter(r => r.proposalRequestStatus === 'Declined')
		.sort((c, n) => ((c.venueName ?? '') > (n.venueName ?? '') ? 1 : -1))

	return (
		<HSDrawer
			onClose={onClose}
			open
			style={{ width: '400px' }}
			position='right'
		>
			<Drawer.Header title='Question Details' titleIcon={() => null} />
			<Drawer.Items>
				<div className='flex flex-col gap-6'>
					<div className='flex flex-col gap-1'>
						<div className='text-sm font-bold text-gray-700'>
							{requestGroups[question.question.requestGroupId ?? '']?.name}
						</div>
						<div className='text-base font-normal text-gray-500'>
							{question.question.text}
						</div>
					</div>
					<div className='card'>
						<div className='border-b bg-gray-200 p-2'>
							<div className='text-sm font-medium text-gray-700'>
								Responses ({responses.length})
							</div>
						</div>
						{responses.map(response => (
							<div
								key={`qa-res-${response.venueId}-${response.id}`}
								className='flex flex-col gap-2 border-b p-2 last:border-b-0'
							>
								<div className='text-sm font-medium text-primary-600 underline'>
									{response.venueName ?? ''}
								</div>
								<div className='text-sm font-normal text-gray-500'>
									{question.question.responseType === 'text' ? (
										<div>
											<div
												dangerouslySetInnerHTML={{
													__html: response.response ?? ''
												}}
											/>
										</div>
									) : (
										<div>
											{question.question.responseType === 'singleChoice' ? (
												<div>
													{response.responses
														? response.responses[0].text
														: null}
												</div>
											) : question.question.responseType ===
											  'multipleChoice' ? (
												<List>
													{response.responses?.map((res, resIndex) => (
														<ListItem key={resIndex}>{res.text}</ListItem>
													))}
												</List>
											) : (
												<div
													dangerouslySetInnerHTML={{
														__html: response.response ?? ''
													}}
												/>
											)}
										</div>
									)}
								</div>
								<div className='flex items-start gap-2 text-wrap'>
									<FontAwesomeIcon icon={faCommentLines} />
									<div className='text-sm font-normal text-gray-500'>
										{response.comment ?? 'None'}
									</div>
								</div>
							</div>
						))}
					</div>
					<div className='card'>
						<div className='border-b bg-gray-200 p-2'>
							<div className='text-sm font-medium text-gray-700'>
								Awaiting Responses ({awaitingResponses.length})
							</div>
						</div>
						{awaitingResponses.map(response => (
							<div
								key={`qa-res-${response.venueId}-${response.id}`}
								className='flex flex-col gap-2 border-b p-2 last:border-b-0'
							>
								<div className='text-sm font-medium text-primary-600 underline'>
									{response.venueName ?? ''}
								</div>
							</div>
						))}
					</div>
					<div className='card'>
						<div className='border-b bg-gray-200 p-2'>
							<div className='text-sm font-medium text-gray-700'>
								Declined to submit Proposal ({declinedResponses.length})
							</div>
						</div>
						{declinedResponses.map(response => (
							<div
								key={`qa-res-${response.venueId}-${response.id}`}
								className='flex flex-col gap-2 border-b p-2 last:border-b-0'
							>
								<div className='text-sm font-medium text-primary-600 underline'>
									{response.venueName ?? ''}
								</div>
							</div>
						))}
					</div>
				</div>
			</Drawer.Items>
		</HSDrawer>
	)
}

export default QuestionResponseDrawer
