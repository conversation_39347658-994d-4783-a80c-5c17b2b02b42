/* eslint-disable react/no-array-index-key */
import type { CustomFlowbiteTheme } from 'flowbite-react'
import { Breadcrumb } from 'flowbite-react'
import { getBreadCrumbName } from './helper'
import customText from './helper/customText'

interface IBreadcrumbProperties {
	replace?: Record<string, string>
}

const HSBreadCrumb = (properties: IBreadcrumbProperties) => {
	const { replace } = properties
	const { pathname } = globalThis.location
	const pathArray = pathname
		.split('/')
		.map(path => ({
			path: replace?.[path] ?? path,
			ignore: !!replace?.[path]
		}))
		.filter(item => item.path)

	const customTheme: CustomFlowbiteTheme['breadcrumb'] = {
		root: {
			base: '',
			list: 'flex items-center'
		},
		item: {
			base: 'group flex items-center',
			chevron: 'mx-1 h-4 w-4 text-gray-400 group-first:hidden md:mx-2',
			href: {
				off: 'flex items-center text-sm font-medium text-gray-500 dark:text-gray-400 hover:no-underline',
				on: 'flex items-center text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white hover:no-underline'
			},
			icon: 'mr-2 h-4 w-4'
		}
	}

	if (pathArray.length <= 1) return null

	return (
		<Breadcrumb
			aria-label='breadcrumb'
			className='w-fit'
			theme={customTheme.root}
		>
			{pathArray.map((path, index, array) => (
				<Breadcrumb.Item
					key={index}
					// href={
					// 	index === pathArray.length - 1
					// 		? undefined
					// 		: `/${pathArray.slice(0, index + 1).join('/')}`
					// }
					className='hover:no-underline'
					theme={customTheme.item}
				>
					<span
						title={getBreadCrumbName(path)}
						className={`max-w-48 cursor-default overflow-hidden text-ellipsis whitespace-nowrap ${index === array.length - 1 ? '!text-gray-500' : '!text-gray-700'}`}
					>
						{customText[getBreadCrumbName(path).toLowerCase()] ??
							getBreadCrumbName(path)}
					</span>
				</Breadcrumb.Item>
			))}
		</Breadcrumb>
	)
}

export default HSBreadCrumb
