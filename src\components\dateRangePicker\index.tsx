import { DateRangePickerComponent } from '@syncfusion/ej2-react-calendars'
import './index.css'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons'
import { memo } from 'react'

interface IHSDateRangePicker {
	placeholder?: string
	onChange: (event: { value: Date[] | null }) => void
	value: Date[] | undefined
	format?: string
	min?: Date | undefined
	max?: Date | undefined
	children?: React.ReactNode
	isInValid?: boolean
	id?: string
	disabled?: boolean
	showClearButton?: boolean
	dayHeaderFormat?: string
	cleared?: () => void
	enabled?: boolean
}

const HSDateRangePicker = memo((properties: IHSDateRangePicker) => {
	const { children, isInValid } = properties
	return (
		<div className='relative flex items-center justify-center'>
			<DateRangePickerComponent {...properties}>
				{children}
			</DateRangePickerComponent>
			{isInValid ? (
				<FontAwesomeIcon
					className='absolute right-[32px] text-red-600'
					icon={faInfoCircle}
				/>
			) : null}
		</div>
	)
})

export default HSDateRangePicker
