/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable @typescript-eslint/no-floating-promises */
import {
	PresetDirective,
	PresetsDirective
} from '@syncfusion/ej2-react-calendars'
import type {
	SelectEventArgs,
	RemoveEventArgs
} from '@syncfusion/ej2-react-dropdowns'
import { MultiSelectComponent } from '@syncfusion/ej2-react-dropdowns'

import Loader from 'components/loader'
import {
	ROLE_ADMIN,
	ROLE_ADMIN_HOTEL,
	ROLE_SALES_OPS,
	ROLE_SUPPLIER
} from 'lib/helpers/roles'
import { PropertyPartnerTypes } from 'lib/helpers/propertyPartner'
import { useGetChains } from 'lib/services/chains.service'
import { useGetPropertyPartners } from 'lib/services/propertyPartner.service'
import { useGetUserProfilesByRawQuery } from 'lib/services/userProfile.service'
import { useEffect, useMemo, useState } from 'react'
import { calculatePeriod, periodTypes } from 'lib/helpers'
import HSDropdownButton from 'components/dropdown'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	faDownload,
	faFilter,
	faPalette
} from '@fortawesome/pro-light-svg-icons'
import HSTextField from 'components/textField'
import HSIcon from 'components/HSIcon'
import HSButton from 'components/button'
import { AccordionPanel } from 'flowbite-react'
import HSToggleSwitch from 'components/toggleSwitch'
import HSBadge from 'components/badge'
import HSAccordion from 'components/accordion'
import filterStore from './filterStore'
import GridSearch from './gridSearch'
import HSRadioButton from 'components/radioButton'
import HSPopover from 'components/popover'
import { debounce } from 'es-toolkit'
import AddUser from './addUser'
import { exportCollection } from 'lib/services/report.service'
import headerStore from 'components/header/headerStore'
import PageLoader from 'components/pageLoader'
import UserProfileGrid from './grid'
import HSDateRangePicker from 'components/dateRangePicker'

const UserProfiles = () => {
	const {
		licenseTier,
		setLicenseTier,
		setUserNames,
		setUserRoles,
		userNames,
		userRoles,
		showDeletedUser,
		setShowDeletedUser,
		searchText,
		setSearchText,
		trials,
		setTrials,
		companyNames,
		setCompanyNames,
		clearAll,
		dateFilter,
		setDateFilter,
		neverSignedUp,
		setNeverSignedUp,
		filterNameSearch,
		setFilterNameSearch,
		getQuery
	} = filterStore()
	const { setHide, reset } = headerStore()

	const [queryParameters, setQueryParameters] = useState({
		condition: 'c.isDeleted = false',
		top: '50',
		skip: '0',
		orderBy: 'c.created desc'
	})
	const [selectedRole, setSelectedRole] = useState<string>('')
	const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
	const [isExporting, setIsExporting] = useState<boolean>(false)

	const { data: userProfiles, isFetching: isLoadingProfiles } =
		useGetUserProfilesByRawQuery(queryParameters)

	const { isFetching: isLoadingChains } = useGetChains()
	const { isFetching: isLoadingPropertySellers } = useGetPropertyPartners(
		PropertyPartnerTypes.seller.itemType ?? ' '
	)

	useEffect(() => {
		setHide(false)
		return () => {
			reset()
		}
	}, [reset, setHide])

	useEffect(() => {
		const allTimeFilter = calculatePeriod(periodTypes.allTime.key)
		setDateFilter(
			neverSignedUp
				? undefined
				: [new Date(allTimeFilter.startDate), new Date(allTimeFilter.endDate)]
		)
	}, [neverSignedUp, setDateFilter])

	const onRoleSelection = (value: string) => {
		setSelectedRole(value)
		setIsModalOpen(true)
	}

	const resetModal = () => {
		setSelectedRole('')
		setIsModalOpen(false)
	}

	const onModalClose = () => {
		setIsModalOpen(false)
		setSelectedRole('')
	}

	const popOverContent = (
		<div className='max-h-96 w-80 overflow-y-auto'>
			<div className='flex flex-col gap-2 p-2'>
				<div className='flex items-center justify-between'>
					<div className='text-sm font-medium text-gray-900'>Filters</div>
					<div className='flex gap-2'>
						<HSButton size='xs'>Save View</HSButton>
						<HSButton size='xs' onClick={() => clearAll()}>
							Clear All
						</HSButton>
					</div>
				</div>
				<div className='flex flex-row items-center gap-2'>
					<HSTextField
						icon={HSIcon(faPalette)}
						placeholder='Search Filter Name'
						value={filterNameSearch}
						onChange={event => setFilterNameSearch(event.target.value)}
					/>
				</div>
			</div>
			<div className='border-b' />
			<HSAccordion alwaysOpen>
				{'user'.includes(filterNameSearch.toLowerCase()) ? (
					<AccordionPanel>
						<HSAccordion.Title>User</HSAccordion.Title>
						<HSAccordion.Content>
							<div className='flex max-h-72 flex-col gap-2'>
								<MultiSelectComponent
									id='customelement'
									dataSource={[]}
									mode='Box'
									placeholder='Enter names'
									allowCustomValue
									value={userNames}
									select={(event: SelectEventArgs) => {
										const currentUserNames = userNames
										setUserNames([
											...currentUserNames,
											event.itemData.text as string
										])
									}}
									removed={(event: RemoveEventArgs) => {
										const currentUserNames = userNames
										setUserNames(
											currentUserNames.filter(
												name => name !== event.itemData.text
											)
										)
									}}
									immediateRender
								/>
								<HSToggleSwitch
									checked={showDeletedUser}
									onChange={(checked: boolean) => {
										setShowDeletedUser(checked)
									}}
									label='Show deleted user profiles'
								/>
							</div>
						</HSAccordion.Content>
					</AccordionPanel>
				) : (
					<> </>
				)}
				{'user role'.includes(filterNameSearch.toLowerCase()) ? (
					<AccordionPanel>
						<HSAccordion.Title>User Role</HSAccordion.Title>
						<HSAccordion.Content>
							<div className='flex flex-col gap-2'>
								{userRoles.map(role => (
									<div className='flex items-center gap-2' key={role.label}>
										<HSRadioButton
											name='user-role'
											value={role.label}
											label={role.label}
											selectedValue={
												userRoles.find(item => item.checked)?.label ?? ''
											}
											onClick={event => {
												console.log(event)
											}}
											onChange={event => {
												const isAlreadySelected = userRoles.find(
													t => t.checked && t.label === event.target.value
												)

												if (isAlreadySelected === undefined) {
													const updatedUserRoles = userRoles.map(l => {
														if (l.label === event.target.value) {
															return { ...l, checked: true }
														}
														return { ...l, checked: false }
													})
													setUserRoles(updatedUserRoles)
												} else {
													const updatedUserRoles = userRoles.map(l => {
														if (l.label === event.target.value) {
															return { ...l, checked: false }
														}
														return { ...l, checked: false }
													})
													setUserRoles(updatedUserRoles)
												}
											}}
										/>
									</div>
								))}
							</div>
						</HSAccordion.Content>
					</AccordionPanel>
				) : (
					<> </>
				)}
				{'company'.includes(filterNameSearch) ? (
					<AccordionPanel>
						<HSAccordion.Title>Company</HSAccordion.Title>
						<HSAccordion.Content>
							<div className='flex flex-col gap-2'>
								<MultiSelectComponent
									id='customelement'
									dataSource={[]}
									mode='Box'
									placeholder='Start typing to add Company'
									allowCustomValue
									value={companyNames}
									select={(event: SelectEventArgs) => {
										setCompanyNames([
											...companyNames,
											event.itemData.text as string
										])
									}}
									removed={(event: RemoveEventArgs) => {
										const updatedCompanyNames = companyNames.filter(
											name => name !== event.itemData.text
										)
										setCompanyNames(updatedCompanyNames)
									}}
									immediateRender
								/>
							</div>
						</HSAccordion.Content>
					</AccordionPanel>
				) : (
					<> </>
				)}
				{'license tier'.includes(filterNameSearch) ? (
					<AccordionPanel>
						<HSAccordion.Title>License Tier</HSAccordion.Title>
						<HSAccordion.Content>
							<div className='flex flex-col gap-2'>
								{licenseTier.map(license => (
									<div className='flex items-center gap-2' key={license.label}>
										<HSRadioButton
											name='licenseTier'
											labelComponent={
												<HSBadge color={license.color}>
													<div className='p-1'>{license.label}</div>
												</HSBadge>
											}
											onChange={event => {
												const isAlreadySelected = licenseTier.find(
													t => t.checked && t.label === event.target.value
												)
												if (isAlreadySelected === undefined) {
													const updatedLicenseTier = licenseTier.map(l => {
														if (l.label === event.target.value) {
															return { ...l, checked: true }
														}
														return { ...l, checked: false }
													})
													setLicenseTier(updatedLicenseTier)
												} else {
													const updatedLicenseTier = licenseTier.map(l => {
														if (l.label === event.target.value) {
															return { ...l, checked: false }
														}
														return { ...l, checked: false }
													})
													setLicenseTier(updatedLicenseTier)
												}
											}}
											value={license.label}
											label={license.label}
											selectedValue={
												licenseTier.find(item => item.checked)?.label ?? ''
											}
										/>
									</div>
								))}
							</div>
						</HSAccordion.Content>
					</AccordionPanel>
				) : (
					<> </>
				)}
				{'trial'.includes(filterNameSearch) ? (
					<AccordionPanel>
						<HSAccordion.Title>Trial</HSAccordion.Title>
						<HSAccordion.Content>
							<div className='flex flex-col gap-2'>
								{trials.map(trial => (
									<div className='flex items-center gap-2' key={trial.value}>
										<HSToggleSwitch
											checked={trial.checked}
											onChange={(checked: boolean) => {
												setTrials(
													trials.map(t => {
														if (t.label === trial.label) {
															return { ...t, checked }
														}
														return t
													})
												)
											}}
										/>
										<HSBadge color={trial.color}>
											<div className='p-1'>{trial.label}</div>
										</HSBadge>
									</div>
								))}
							</div>
						</HSAccordion.Content>
					</AccordionPanel>
				) : (
					<> </>
				)}
			</HSAccordion>
		</div>
	)

	const debouncedSearch = debounce(() => {
		const query = getQuery()
		setQueryParameters(q => ({ ...q, condition: query }))
	}, 500)

	useEffect(() => {
		debouncedSearch()
		return () => {
			debouncedSearch.cancel()
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [
		userNames,
		userRoles,
		licenseTier,
		userProfiles,
		showDeletedUser,
		companyNames,
		trials,
		neverSignedUp,
		dateFilter,
		searchText,
		getQuery
	])

	const filterCount =
		userNames.length +
		userRoles.filter(role => role.checked).length +
		licenseTier.filter(license => license.checked).length +
		(showDeletedUser ? 1 : 0) +
		companyNames.length

	const presetDirective = useMemo(
		() => (
			<PresetsDirective>
				{Object.keys(periodTypes).map(period => {
					const {
						type: { label, key },
						startDate,
						endDate
					} = calculatePeriod(period)
					return (
						<PresetDirective
							key={key}
							label={label}
							start={new Date(startDate)}
							end={new Date(endDate)}
						/>
					)
				})}
			</PresetsDirective>
		),
		[]
	)

	return (
		<div>
			{/* <div className='flex flex-col gap-2 border bg-white p-6'>
				<HSBreadCrumb />
				<div className='mt-1 text-2xl font-semibold'>User Profiles</div>
			</div> */}
			<div className='bg-gray-50 p-6'>
				<div className='flex flex-col justify-between rounded-lg border bg-white'>
					<div className='flex flex-row items-center justify-between p-4'>
						<div className='text-xl font-semibold text-gray-900'>
							HopSkip User Profiles
						</div>
						<div className='flex w-80 flex-row items-center gap-2'>
							<GridSearch
								value={searchText}
								onSearch={(value: string) => {
									setSearchText(value)
								}}
							/>
						</div>
					</div>
					<div className='border-b' />

					<div>
						<div className='flex items-center justify-between gap-4 px-4 py-2'>
							<div className='text-sm font-medium text-gray-900'>
								{userProfiles?.length} Users
							</div>
							<div className='flex items-center justify-end'>
								<div className='flex items-center gap-5'>
									<HSToggleSwitch
										id='neverSignedUp'
										checked={neverSignedUp}
										onChange={(checked: boolean) => setNeverSignedUp(checked)}
									/>
									<label
										htmlFor='neverSignedUp'
										className='text-sm font-medium leading-tight text-gray-900'
									>
										Never Signed Up
									</label>
									<div className='flex gap-5'>
										<div className='w-64'>
											<HSDateRangePicker
												placeholder='Signed Up:'
												onChange={({ value }: { value: Date[] | null }) =>
													setDateFilter(value)
												}
												dayHeaderFormat='Short'
												showClearButton
												cleared={() => setDateFilter([])}
												enabled={!neverSignedUp}
												value={dateFilter ?? []}
												// format='MMM dd, yyyy'
											>
												{presetDirective}
											</HSDateRangePicker>
										</div>
									</div>
									<div>
										<HSDropdownButton
											data-testid='exportDropdown'
											size='sm'
											items={[
												{
													item: 'Excel',
													id: 'excel',
													clickFunction: async () => {
														setIsExporting(true)
														exportCollection(
															'user-profiles',
															'userProfile',
															() => {
																setIsExporting(false)
															}
														)
													}
												}
												// {
												// 	item: 'PDF',
												// 	id: 'pdf',
												// 	clickFunction: async () => {
												// 		await gridInstance?.pdfExport()
												// 	}
												// }
											]}
											color='light'
											label={
												<div className='flex items-center gap-2 text-gray-900'>
													{isExporting ? (
														<Loader size='sm' />
													) : (
														<FontAwesomeIcon icon={faDownload} />
													)}
												</div>
											}
											showDropdownIcon={false}
											className='gap-2 text-sm font-normal text-gray-900'
										/>
									</div>
									<div>
										<HSPopover
											content={popOverContent}
											aria-labelledby='filter'
											placement='bottom'
											arrow={false}
										>
											<div className='relative inline-block'>
												<HSButton color='light' size='sm'>
													<div className='flex items-center gap-2'>
														<FontAwesomeIcon icon={faFilter} />
														Filter
													</div>
												</HSButton>
												{filterCount > 0 ? (
													<HSBadge
														color='failure'
														title='3 Filters Applied'
														className='absolute right-0 top-0 z-10 -translate-y-1/2 translate-x-1/2 transform rounded-full bg-red-600'
													>
														<div className='text-md flex p-1 font-semibold text-white'>
															{filterCount}
														</div>
													</HSBadge>
												) : null}
											</div>
										</HSPopover>
									</div>
									<div>
										<HSDropdownButton
											size='sm'
											items={[
												{
													item: 'SalesOPS',
													id: 'SalesOPS',
													clickFunction: () => onRoleSelection(ROLE_SALES_OPS)
												},
												{
													item: 'NSO / GSO',
													id: 'NSO / GSO',
													clickFunction: () => onRoleSelection(ROLE_SUPPLIER)
												},
												{
													item: 'Admin',
													id: 'Admin',
													clickFunction: () => {
														onRoleSelection(ROLE_ADMIN)
													}
												},
												{
													item: 'Hotel Admin',
													id: 'Hotel Admin',
													clickFunction: () => {
														onRoleSelection(ROLE_ADMIN_HOTEL)
													}
												}
											]}
											label='Create New...'
											className='gap-2 font-medium text-white'
										/>
									</div>
								</div>
							</div>
						</div>
						<div
							style={{ maxHeight: 'calc(100vh - 15rem)' }}
							className='overflow-auto p-4'
						>
							{isLoadingChains ||
							isLoadingPropertySellers ||
							isLoadingProfiles ? (
								<PageLoader />
							) : userProfiles ? (
								<UserProfileGrid userProfiles={userProfiles} />
							) : null}
						</div>
						{isModalOpen ? (
							<AddUser
								isModalOpen={isModalOpen}
								onModalClose={onModalClose}
								reset={resetModal}
								selectedRole={selectedRole}
							/>
						) : null}
					</div>
				</div>
			</div>
		</div>
	)
}

export default UserProfiles
