/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-floating-promises */
/* eslint-disable @typescript-eslint/no-unnecessary-condition, no-await-in-loop */
import { useEffect, useState } from 'react'
import HSCheckbox from 'components/checkbox'
import { downloadReport } from 'lib/utils'
import { ROLE_PLANNER } from 'lib/helpers/roles'
import useQuoteRequestStore from 'lib/store/quoteRequestStore'
import { useLoggedInUserProfileContext } from 'lib/contexts/loggedInUserProfile.context'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import HSButton from 'components/button'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faDownload, faPenToSquare } from '@fortawesome/pro-light-svg-icons'
import CustomizeReportPdf from 'components/customizeReportModal/customizeReportPdfModal'
import CustomizeReportExcel from 'components/customizeReportModal/customizeReportExcelModal'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'

interface Report {
	title: string
	subTitle: string
	template: string
	disabled: boolean
	disabledMessage: string
	fileType: 'PDF' | 'Excel' | 'PowerPoint'
	selected: boolean
	id: string
	combine: boolean
}

const getFileTypeBadgeColor = (fileType: string) => {
	switch (fileType) {
		case 'PDF': {
			return 'bg-red-100 text-red-800 rounded-full'
		}
		case 'Excel': {
			return 'bg-green-100 text-green-800 rounded-full'
		}
		case 'PowerPoint': {
			return 'bg-orange-100 text-orange-800 rounded-full'
		}
		default: {
			return 'bg-gray-100 text-gray-800'
		}
	}
}

const RFPReport = () => {
	const { userProfile: loggedInUser } = useLoggedInUserProfileContext()
	const { userProfile } = useUserProfileContext()
	const [reports, setReports] = useState<Report[]>([])
	const [showCustomizeExcelModal, setShowCustomizeExcelModal] = useState(false)
	const [showCustomizePdfModal, setShowCustomizePdfModal] = useState(false)
	const [selectedReport, setSelectedReport] = useState<Report | null>(null)
	const [selectedOptions, setSelectedOptions] = useState<
		Record<string, string[]>
	>({})
	const { eventInfo: eventPlan, setProperty } = eventInfoStore()

	useEffect(() => {
		const initialReports: Report[] = [
			{
				id: 'rfp',
				title: 'RFP',
				subTitle: 'Your RFP in a PDF format',
				template: 'rfpsummary',
				disabled: false,
				disabledMessage: '',
				fileType: 'PDF',
				selected: false,
				combine: false
			},
			{
				id: 'hotel-proposal-comparison',
				title: 'Hotel Proposal Comparison',
				subTitle:
					'Compare the key criteria across all of the proposals you received from hotels',
				template: 'proposalcomparison',
				disabled: false,
				disabledMessage: '',
				fileType: 'Excel',
				selected: false,
				combine: true
			},
			{
				id: 'proposal-financials',
				title: 'Proposal Financials',
				subTitle:
					'Your estimated total proposal cost for each hotel broken down by the largest cost areas',
				template: 'proposalfinancials',
				disabled: false,
				disabledMessage: '',
				fileType: 'Excel',
				selected: false,
				combine: true
			},
			{
				id: 'meeting-agenda',
				title: 'Meeting Agenda',
				subTitle:
					'Your meeting agenda that was generated from your meeting space request',
				template: 'meetingagenda',
				disabled: false,
				disabledMessage: '',
				fileType: 'Excel',
				selected: false,
				combine: true
			},
			{
				id: 'meeting-space-proposals',
				title: 'Meeting Space Proposals',
				subTitle:
					'A breakdown of each hotels meeting space proposal showing details of the hotel meeting rooms assigned to each meeting room request',
				template: 'meetingspaceproposals',
				disabled: false,
				disabledMessage: '',
				fileType: 'Excel',
				selected: false,
				combine: true
			},
			{
				id: 'food-and-beverage-proposals',
				title: 'Food & Beverage Proposals',
				subTitle:
					'A breakdown of each hotels F&B proposal showing details of the hotel F&B quantities and rates',
				template: 'fnbproposals',
				disabled: false,
				disabledMessage: '',
				fileType: 'Excel',
				selected: false,
				combine: true
			},
			{
				id: 'presentation',
				title: 'Presentation',
				subTitle:
					'Prepare a PowerPoint presentation based on proposals received',
				template: 'presentation',
				disabled: false,
				disabledMessage: '',
				fileType: 'PowerPoint',
				selected: false,
				combine: false
			}
		]

		setReports(initialReports)

		setSelectedOptions({
			rfpsummary: [
				'summary',
				'profile',
				'programinfo',
				'rooms',
				'meeting',
				'fandb',
				'questions',
				'concessionRequests',
				'contract',
				'clauses',
				'attachments',
				'notes'
			],
			proposalcomparison: [
				'responseComments',
				'displayAttrition',
				'hotelNotes',
				'noresponse',
				'hotelturneddown',
				'hotelContacts',
				'notes'
			]
		})
	}, [])

	const onClickReport = async (template: string, sections?: string[]) => {
		if (userProfile && loggedInUser) {
			try {
				await downloadReport({
					openWindow: true,
					reportPath: 'excel',
					eventPlanId: eventPlan?.id ?? '',
					templates: template,
					params: {
						options: sections ? sections.join(',') : '',
						audience: ROLE_PLANNER,
						currencyCode: eventPlan?.currencyCode ?? ''
					},
					impersonate:
						loggedInUser.id === userProfile.id ? null : userProfile.id
				})
			} catch (error) {
				console.error('Error downloading report:', error)
			}
		}
	}

	const toggleReport = (reportId: string) => {
		setReports(previousReports =>
			previousReports.map(report =>
				report.id === reportId
					? { ...report, selected: !report.selected }
					: report
			)
		)
	}

	const downloadSelected = async () => {
		const selectedReports = reports.filter(report => report.selected)
		if (selectedReports.length === 0) return

		const combinableReports = selectedReports.filter(r => r.combine)
		const nonCombinableReports = selectedReports.filter(r => !r.combine)

		try {
			if (combinableReports.length > 0) {
				await downloadReport({
					openWindow: false,
					reportPath: 'excel',
					eventPlanId: eventPlan?.id ?? '',
					templates: combinableReports.map(r => r.template).join(','),
					params: {
						options: combinableReports
							.flatMap(r => selectedOptions[r.template] || [])
							.join(','),
						audience: ROLE_PLANNER,
						currencyCode: eventPlan?.currencyCode ?? ''
					},
					impersonate:
						loggedInUser?.id === userProfile?.id
							? null
							: (userProfile?.id ?? null)
				})
			}

			for (const report of nonCombinableReports) {
				await downloadReport({
					openWindow: report.fileType === 'PDF',
					reportPath: report.fileType.toLowerCase() as
						| 'excel'
						| 'pdf'
						| 'powerpoint',
					eventPlanId: eventPlan?.id ?? '',
					templates: report.template,
					params: {
						options: selectedOptions[report.template]?.join(',') || '',
						audience: ROLE_PLANNER,
						currencyCode: eventPlan?.currencyCode ?? ''
					},
					impersonate:
						loggedInUser?.id === userProfile?.id
							? null
							: (userProfile?.id ?? null)
				})
			}
		} catch (error) {
			console.error('Error downloading reports:', error)
		}
	}

	const handleEditClick = (report: Report) => {
		setSelectedReport(report)
		if (report.title === 'RFP') {
			setShowCustomizePdfModal(true)
		} else if (report.title === 'Hotel Proposal Comparison') {
			setShowCustomizeExcelModal(true)
		}
	}

	const handleCustomizeModalClose = () => {
		setShowCustomizeExcelModal(false)
		setShowCustomizePdfModal(false)
		setSelectedReport(null)
	}

	const handleCustomizeModalSave = (selectedSections: string[]) => {
		if (!selectedReport) return

		setSelectedOptions(previous => ({
			...previous,
			[selectedReport.template]: selectedSections
		}))

		if (selectedReport.template === 'rfpsummary') {
			onClickReport(selectedReport.template, selectedSections)
		} else if (selectedReport.template === 'proposalcomparison') {
			onClickReport(selectedReport.template, selectedSections)
		}

		handleCustomizeModalClose()
	}

	return (
		<div>
			<div className='border-b px-6 py-4'>
				<div className='flex items-center justify-between'>
					<div className='text-xl font-semibold text-gray-900'>My Reports</div>
					<HSButton
						onClick={downloadSelected}
						disabled={!reports.some(report => report.selected)}
						color='light'
						className='bg-gray-200'
					>
						Download Selected
					</HSButton>
				</div>
			</div>
			<div className='p-6'>
				<div className='flex max-w-5xl flex-col'>
					{reports.map(report => (
						<div
							key={report.id}
							className='mb-2 rounded-lg border border-gray-200 bg-white px-6 py-4 shadow-sm'
						>
							<div className='flex items-start justify-between gap-6'>
								<div className='flex items-start gap-4'>
									<HSCheckbox
										checked={report.selected}
										onChange={() => toggleReport(report.id)}
										disabled={report.disabled}
									/>
									<div className='flex flex-col gap-1'>
										<div className='flex items-center gap-2'>
											<span className='text-lg font-medium'>
												{report.title}
											</span>
											<span
												className={`rounded px-2 py-0.5 text-xs font-medium ${getFileTypeBadgeColor(report.fileType)}`}
											>
												{report.fileType}
											</span>
										</div>
										<span className='max-w-xl text-sm text-gray-500'>
											{report.subTitle}
										</span>
									</div>
								</div>
								<div className='flex gap-3'>
									{(report.title === 'RFP' ||
										report.title === 'Hotel Proposal Comparison') && (
										<HSButton
											color='light'
											size='xs'
											onClick={() => handleEditClick(report)}
										>
											<FontAwesomeIcon icon={faPenToSquare} />
										</HSButton>
									)}
									<HSButton
										color='light'
										size='xs'
										onClick={() => {
											const options = selectedOptions[report.template] || []
											onClickReport(
												report.template,
												options.length > 0 ? options : undefined
											)
										}}
									>
										<FontAwesomeIcon
											icon={faDownload}
											className='text-green-600'
										/>
									</HSButton>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>

			<CustomizeReportExcel
				show={showCustomizeExcelModal}
				onClose={handleCustomizeModalClose}
				onSave={handleCustomizeModalSave}
			/>

			<CustomizeReportPdf
				show={showCustomizePdfModal}
				onClose={handleCustomizeModalClose}
				onSave={handleCustomizeModalSave}
			/>
		</div>
	)
}

export default RFPReport
