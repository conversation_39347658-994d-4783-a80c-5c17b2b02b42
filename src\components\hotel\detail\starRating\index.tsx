import type { Venue } from 'models/venue'
import HSRating from 'components/rating'
import { ratingSystems } from 'lib/helpers/venueFeatures'

interface HotelStarRatingProperties {
	hotel: Venue
}

const HotelStarRating = (properties: HotelStarRatingProperties) => {
	const { hotel } = properties
	return (
		<div>
			{hotel.venueRatings
				? Object.entries(hotel.venueRatings).map(rating =>
						rating[1] ? (
							<div className='flex items-center gap-2' key={rating[0]}>
								<span className='text-sm font-normal text-gray-400'>
									{rating[0]}:
								</span>
								<HSRating
									value={rating[1].rating ?? 0}
									icon={ratingSystems[rating[0]]?.icon}
									completionColor={ratingSystems[rating[0]]?.completionColor}
									readOnly
								/>
							</div>
						) : null
					)
				: null}
		</div>
	)
}

export default HotelStarRating
