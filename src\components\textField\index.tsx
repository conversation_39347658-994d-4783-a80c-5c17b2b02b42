/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable react/boolean-prop-naming */
/* eslint-disable unicorn/no-keyword-prefix */
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons'
import { faX } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import WhatsThis from 'components/whatsThis'
import type { TextInputProps } from 'flowbite-react'
import { Label, TextInput } from 'flowbite-react'
import type { ChangeEvent, JSX, ReactNode } from 'react'
import type { Mask } from 'react-text-mask'
import MaskedInput from 'react-text-mask'

interface IHSTextFieldProperties extends TextInputProps {
	groupPlacement?: 'left' | 'right'
	label?: string | JSX.Element
	tooltipTitle?: string
	tooltipContent?: string
	groupItem?: ReactNode
	groupColor?: string
	mask?: Mask | ((value: string) => Mask)
	ignoreGroupStyle?: boolean
	showClearButton?: boolean
	isInvalid?: boolean
}

const HSTextField = (properties: IHSTextFieldProperties) => {
	const {
		type,
		className,
		placeholder,
		groupPlacement,
		required,
		label = null,
		disabled,
		tooltipTitle = '',
		tooltipContent = '',
		groupItem,
		groupColor,
		onChange,
		value,
		mask,
		ignoreGroupStyle,
		showClearButton,
		color,
		isInvalid,
		...rest
	} = properties

	const renderClear = () => (
		<HSButton
			size='sm'
			className='pr-3 text-gray-500'
			color='text'
			onClick={() =>
				onChange?.({ target: { value: '' } } as ChangeEvent<HTMLInputElement>)
			}
		>
			<FontAwesomeIcon icon={faX} />
		</HSButton>
	)

	const renderError = () => (
		<FontAwesomeIcon icon={faInfoCircle} className='mr-4 text-red-600' />
	)

	return (
		<div className='w-full'>
			{label ? (
				typeof label === 'string' ? (
					<div className='mb-2 flex gap-2 truncate text-center text-sm font-medium'>
						<Label
							className={disabled ? 'text-gray-500' : 'text-gray-900'}
							value={label}
						/>
						{tooltipTitle || tooltipContent ? (
							<WhatsThis title={tooltipTitle} content={tooltipContent} />
						) : null}
					</div>
				) : (
					label
				)
			) : null}
			<div
				className={groupPlacement === 'left' ? 'flex' : 'flex flex-row-reverse'}
			>
				{mask ? (
					<MaskedInput
						{...rest}
						mask={mask}
						type={type || 'text'}
						className={`w-full rounded-lg border-gray-300 !bg-white p-2.5 text-sm hover:border-gray-300 hover:outline-none hover:ring-0 focus:border-gray-300 focus:outline-none focus:ring-0 ${disabled ? 'bg-gray-50 !text-gray-400' : '!bg-white !text-gray-900'} ${groupPlacement === 'right' ? '!rounded-l-none' : groupPlacement === 'left' ? '!rounded-r-none' : ''}`}
						placeholder={placeholder}
						required={required}
						disabled={disabled}
						value={value}
						onChange={event_ =>
							onChange &&
							onChange({
								...event_,
								target: {
									...event_.target,
									value: event_.target.value.replaceAll(',', '')
								}
							})
						}
					/>
				) : (
					<TextInput
						type={type || 'text'}
						className={className}
						theme={{
							base: 'flex-1',
							field: {
								input: {
									base: `focus:ring-0 focus:outline-none focus:border-gray-300 text-sm w-full ${disabled ? 'bg-gray-50 !text-gray-400' : '!bg-white !text-gray-900'} ${groupPlacement === 'right' ? '!rounded-l-none !border-l-0' : groupPlacement === 'left' ? '!rounded-r-none' : ''}`,
									colors: {
										gray: 'border-gray-300 bg-gray-50 text-gray-900 focus:border-gray-300 focus:ring-0 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-0',
										light: 'border-gray-300'
									}
								}
							}
						}}
						placeholder={placeholder}
						required={required}
						disabled={disabled}
						value={value}
						onChange={onChange}
						rightIcon={
							showClearButton
								? renderClear
								: isInvalid
									? renderError
									: undefined
						}
						color={color || 'light'}
						{...rest}
					/>
				)}
				{groupItem ? (
					ignoreGroupStyle ? (
						groupItem
					) : (
						<div
							style={{
								backgroundColor: groupColor || ''
							}}
							className={`flex cursor-default items-center rounded-lg ${groupPlacement === 'left' ? '!rounded-l-none border-l-0' : '!rounded-r-none border-r-0 !pr-0'} border ${color === 'failure' ? 'border-red-500 bg-red-700 hover:bg-red-800' : 'border-gray-300 bg-gray-100'} px-3 py-2 text-sm font-medium text-gray-900 ${disabled ? 'bg-gray-50 !text-gray-400' : ''}`}
						>
							{groupItem}
						</div>
					)
				) : null}
			</div>
		</div>
	)
}

export default HSTextField
