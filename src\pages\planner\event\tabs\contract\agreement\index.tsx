import { useState, useEffect } from 'react'
import { parseISO, format } from 'date-fns'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { ProposalRequest, EventPlan } from 'models/proposalResponseMonitor'
import HSButton from 'components/button'
import HSDatePicker from 'components/datePicker'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faEye, faPlus, faCheck } from '@fortawesome/pro-regular-svg-icons'
import EventPlanContractSignedDrawer from 'components/eventPlanContractDrawer'
import CancelContractingDrawer from 'components/planner/cancelContractingDrawer'
import { Link } from 'react-router-dom'
import { useCancelContracting } from 'lib/services/planner.service'
import { toast } from 'react-toastify'
import HSDrawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import HSDropzone from 'components/dropzone'
import { getAccessToken } from 'lib/interceptor/axios.interceptor'
import {
	useAddAttachments,
	useGetEventDetail
} from 'lib/services/eventPlans.service'
import useAttachmentsStore from 'lib/store/attachmentsStore'
import HSBadge from 'components/badge'

const Agreement = () => {
	const { eventInfo: eventPlan } = eventInfoStore()
	const { attachments } = useAttachmentsStore()
	const { refetch: refetchEventPlan } = useGetEventDetail(
		eventPlan?.id ?? '',
		!!eventPlan?.id
	)
	const [selectedProposals, setSelectedProposals] = useState<ProposalRequest[]>(
		[]
	)
	const [showContractSignedDrawer, setShowContractSignedDrawer] =
		useState(false)
	const [showCancelContractingDrawer, setShowCancelContractingDrawer] =
		useState(false)
	const [selectedProposal, setSelectedProposal] =
		useState<ProposalRequest | null>(null)
	const [showUploadDrawer, setShowUploadDrawer] = useState(false)
	const [acceptedFiles, setAcceptedFiles] = useState<File[]>()
	const { mutateAsync: addAttachments } = useAddAttachments()
	const { mutateAsync: cancelContractingMutation } = useCancelContracting()

	useEffect(() => {
		refetchEventPlan()
			.then(response => {
				if (response.data) {
					eventInfoStore.setState({ eventInfo: response.data })
				}
			})
			.catch((error: unknown) => {
				console.error('Error fetching event plan:', error)
			})
	}, [refetchEventPlan])

	useEffect(() => {
		const proposalRequests =
			eventPlan?.proposalRequests?.filter(
				pr => pr.status && ['ClosedWon', 'ContractSigned'].includes(pr.status)
			) || []
		setSelectedProposals(proposalRequests)
	}, [eventPlan?.proposalRequests])

	const handleMarkContractSigned = (proposal: ProposalRequest) => {
		setSelectedProposal(proposal)
		setShowContractSignedDrawer(true)
	}

	const handleCloseContractDrawer = () => {
		setShowContractSignedDrawer(false)
		setSelectedProposal(null)
	}

	const handleContractEventPlan = () => {
		setShowContractSignedDrawer(false)
		setSelectedProposal(null)
	}

	const handleCancelContracting = (cancelContext: {
		cancelRfp: boolean | null
		editRfp: boolean | null
		internalNotes: string
		cancelHotelNotes: string
		stopProposals: boolean
	}) => {
		if (!selectedProposal) return

		cancelContractingMutation({
			eventPlanId: eventPlan?.id ?? '',
			venueId: selectedProposal.venueId || '',
			cancelContext: {
				cancelRfp: cancelContext.cancelRfp,
				editRfp: cancelContext.editRfp,
				internalNotes: cancelContext.internalNotes,
				cancelHotelNotes: cancelContext.cancelHotelNotes
			}
		})
			.then(() => {
				setShowCancelContractingDrawer(false)
				setSelectedProposal(null)
				toast.success('Contracting has been successfully canceled')
			})
			.catch((error: unknown) => {
				console.error('Error canceling contracting:', error)
				toast.error('Failed to cancel contracting')
			})
	}

	const closeDrawerAndRefetch = (closeFunction: () => void) => {
		closeFunction()
		refetchEventPlan()
			.then(response => {
				if (response.data) {
					eventInfoStore.setState({ eventInfo: response.data })
				}
			})
			.catch((error: unknown) =>
				console.error('Refetch failed after drawer close', error)
			)
	}

	return (
		<div className='flex flex-col gap-2'>
			<div className='flex items-center justify-between border-b p-4'>
				<div>
					{selectedProposals.length > 0 ? (
						<div className='text-xl font-semibold text-gray-900'>
							Finalize your agreement with the hotel
						</div>
					) : (
						<div className='text-xl font-semibold text-gray-900'>
							Hotel Agreement
						</div>
					)}

					<div className='text-sm font-normal text-gray-500'>
						Upload and view your signed agreements here. After awarding the
						booking, mark your RFP as &apos;Contract Signed&apos; to finalize
						the process. This will ensure your booking is recorded in your
						sourcing history and analytics.
					</div>
				</div>
			</div>

			{selectedProposals.length > 0 ? (
				selectedProposals.map(pr => {
					const proposalDate =
						pr.currentBid?.proposalDates?.find(pd => pd.contracted) ||
						pr.currentBid?.proposalDates?.[0]
					return (
						<div className='flex flex-col p-4' key={pr.venueId || pr.id}>
							<div
								key={pr.venueId}
								className='gap-4 rounded-lg border shadow-sm'
							>
								<div className='flex items-center justify-between border-b border-gray-200 p-4'>
									<div className='text-lg font-semibold'>{pr.venueName}</div>
									<div className='flex gap-4'>
										<HSButton
											color='danger'
											size='sm'
											outline
											onClick={() => {
												setShowCancelContractingDrawer(true)
												setSelectedProposal(pr)
											}}
										>
											Cancel Contracting
										</HSButton>
										<HSButton
											color='primary'
											size='sm'
											onClick={() => handleMarkContractSigned(pr)}
										>
											<div className='flex items-center gap-2'>
												<FontAwesomeIcon icon={faCheck} />I have a signed
												contract
											</div>
										</HSButton>
									</div>
								</div>

								<div className='grid grid-cols-4 gap-4 p-4'>
									<div className='col-span-1 border-r border-gray-200 pr-4'>
										<div className='mb-2 text-sm font-medium'>Event Dates</div>
										<div className='text-sm'>
											<div>
												Start Date:{' '}
												{proposalDate?.startDate
													? format(
															parseISO(proposalDate.startDate),
															'MMM d, yyyy'
														)
													: 'Not Set'}
												<span className='ml-1 text-gray-400'>
													{proposalDate?.startDate
														? format(parseISO(proposalDate.startDate), 'EEEE')
														: ''}
												</span>
											</div>
											<div>
												End Date:{' '}
												{proposalDate?.endDate
													? format(
															parseISO(proposalDate.endDate),
															'MMM d, yyyy'
														)
													: 'Not Set'}
												<span className='ml-1 text-gray-400'>
													{proposalDate?.endDate
														? format(parseISO(proposalDate.endDate), 'EEEE')
														: ''}
												</span>
											</div>
										</div>
									</div>

									<div className='col-span-2 flex gap-4 border-r border-gray-200 px-4'>
										<div className='flex-1'>
											<div className='mb-2 text-sm font-medium'>
												Contracting Started
											</div>
											<HSDatePicker
												placeholder='Select Date'
												format='MMM dd, yyyy'
												showClearButton={false}
												value={
													pr.won && typeof pr.won === 'string'
														? parseISO(pr.won)
														: undefined
												}
											/>
										</div>

										<div className='flex-1'>
											<div className='mb-2 text-sm font-medium'>
												Contract Signed
											</div>
											<HSDatePicker
												placeholder='Select Date'
												format='MMM dd, yyyy'
												showClearButton={false}
												value={
													pr.status === 'ContractSigned' && pr.signed
														? parseISO(pr.signed)
														: undefined
												}
											/>
										</div>
									</div>

									<div className='col-span-1 pl-4'>
										<div className='mb-2 flex items-center justify-between'>
											<div className='text-sm font-medium'>Contract</div>
											<HSButton
												size='sm'
												color='text'
												onClick={() => {
													setShowUploadDrawer(true)
													setSelectedProposal(pr)
												}}
											>
												<div className='flex items-center gap-2'>
													<FontAwesomeIcon icon={faPlus} />
													Upload
												</div>
											</HSButton>
										</div>
										{(() => {
											const proposalAttachments = attachments.filter(
												a => a.venueId === pr.venueId
											)

											return proposalAttachments.length > 0 ? (
												<div className='relative grid grid-cols-3 text-sm text-gray-700'>
													{proposalAttachments
														.slice(0, 6)
														.map((attachment, index) => (
															<div key={attachment.id || `attachment-${index}`}>
																{attachment.displayName || attachment.name}
																{attachment.extension
																	? `.${attachment.extension}`
																	: ''}
															</div>
														))}
													{proposalAttachments.length > 6 && (
														<HSBadge
															color='light'
															className='absolute bottom-0 right-0'
														>
															+{proposalAttachments.length - 6}
														</HSBadge>
													)}
												</div>
											) : (
												<div className='flex h-8 items-center justify-center text-sm text-gray-500'>
													No attachments
												</div>
											)
										})()}
									</div>
								</div>

								<div className='flex items-center justify-between border-t border-gray-200 p-4'>
									<div
										className={`text-sm font-normal ${pr.status === 'ContractSigned' ? 'text-green-600' : 'text-red-600'}`}
									>
										{pr.status === 'ContractSigned'
											? 'Contract Signed'
											: 'Contract Not Signed'}
									</div>
									<HSButton color='text'>
										<div className='flex items-center gap-2 text-sm font-semibold text-primary-600'>
											<FontAwesomeIcon icon={faEye} />
											<Link
												to={`/event/${pr.eventPlanId}/proposal/${pr.venueId}`}
											>
												View Proposal
											</Link>
										</div>
									</HSButton>
								</div>
							</div>
						</div>
					)
				})
			) : (
				<div className='m-4 flex flex-col items-center justify-center p-8'>
					<div className='max-w-lg'>
						<div className='mt-8'>
							<h3 className='mb-2 text-base font-medium text-gray-700'>
								Ready to award your booking?
							</h3>
							<div className='text-sm text-gray-500'>
								<p className='mb-2 text-sm font-normal'>
									To begin contracting:
								</p>
								<ol className='list-decimal space-y-1 pl-6'>
									<li>
										<Link
											className='text-sm font-semibold underline'
											to={`/planner/event/${eventPlan?.id}/review-proposals/dashboard`}
										>
											Go to the Proposal Dashboard
										</Link>
									</li>
									<li>Click into the proposal details of your chosen venue</li>
									<li>
										Select &quot;Begin Contracting&quot; to award the booking
									</li>
								</ol>
								<p className='mt-4'>
									This will notify your selected hotel that they&apos;ve won
									your business and give you the option to inform other
									properties that you won&apos;t be moving forward with them
								</p>
							</div>
						</div>
					</div>
				</div>
			)}

			{showContractSignedDrawer && selectedProposal ? (
				<EventPlanContractSignedDrawer
					showContractSignedDrawer={showContractSignedDrawer}
					onCancelMarkContractSigned={handleCloseContractDrawer}
					onContractEventPlan={handleContractEventPlan}
					eventPlan={eventPlan as EventPlan}
				/>
			) : null}

			{showCancelContractingDrawer && selectedProposal ? (
				<CancelContractingDrawer
					showContractSignedDrawer={showCancelContractingDrawer}
					onCancelMarkContractSigned={() => {
						setShowCancelContractingDrawer(false)
						setSelectedProposal(null)
					}}
					onContractEventPlan={handleCancelContracting}
					eventPlan={eventPlan as EventPlan}
				/>
			) : null}

			{showUploadDrawer && selectedProposal ? (
				<HSDrawer
					id='add-contract-document-dialog'
					onClose={() =>
						closeDrawerAndRefetch(() => {
							setShowUploadDrawer(false)
							setAcceptedFiles(undefined)
						})
					}
					open={showUploadDrawer}
					position='right'
					style={{ width: '400px' }}
				>
					<div className='flex h-[calc(100vh-2rem)] flex-col justify-between'>
						<div className='flex flex-col gap-8'>
							<Drawer.Header
								title='Upload Attachments'
								titleIcon={() => null}
							/>

							<div className='text-sm font-medium text-gray-900'>
								Upload a copy of your signed contract to keep with your RFP
								records
							</div>
							<div className='text-xs font-normal text-gray-500'>
								Documents you upload below are automatically saved to your
								organization profile
							</div>

							<HSDropzone
								acceptedFiles={acceptedFiles}
								setAcceptedFiles={setAcceptedFiles}
								type='document'
								acceptedFileTypes={{
									'application/pdf': [],
									'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
										[],
									'image/jpeg': [],
									'image/png': []
								}}
								subText='PDF, DOCX, JPG, and PNG.'
							/>
						</div>
						<div className='flex gap-4'>
							<HSButton
								color='light'
								className='grow'
								onClick={() => {
									setShowUploadDrawer(false)
									setAcceptedFiles(undefined)
								}}
							>
								Cancel
							</HSButton>
							<HSButton
								className='grow'
								onClick={() => {
									if (!acceptedFiles || acceptedFiles.length === 0) {
										toast.warn('Please select a file to upload')
										return
									}

									toast.info(`Uploading ${acceptedFiles.length} files`)
									const formData = new FormData()
									let f = 0
									for (const file of acceptedFiles) {
										formData.append(`file${f}`, file, file.name)
										formData.append(
											`props${f}`,
											JSON.stringify({
												signatureRequired: true,
												documentContext: 'contract',
												visibleToVenues: true
											})
										)
										f += 1
									}

									getAccessToken()
										.then(() => {
											addAttachments({
												venueId: selectedProposal.venueId || '',
												eventId: eventPlan?.id || '',
												meetingSpaceRequestIds: null,
												data: formData
											})
												.then(response => {
													toast.success(
														`${acceptedFiles.length} files uploaded`
													)

													if (eventPlan) {
														const updatedProposalRequests =
															eventPlan.proposalRequests?.map(pr => {
																if (pr.venueId === selectedProposal.venueId) {
																	return {
																		...pr,
																		attachments: response
																	}
																}
																return pr
															})

														eventInfoStore.setState({
															eventInfo: {
																...eventPlan,
																proposalRequests: updatedProposalRequests
															}
														})
													}

													closeDrawerAndRefetch(() => {
														setShowUploadDrawer(false)
														setAcceptedFiles(undefined)
													})
												})
												.catch((error: unknown) => {
													toast.error('Failed to upload files')
													console.error('Failed to upload files:', error)
												})
										})
										.catch((error: unknown) => {
											console.error('Failed to get token:', error)
										})
								}}
								disabled={(acceptedFiles?.length ?? 0) <= 0}
							>
								Upload
							</HSButton>
						</div>
					</div>
				</HSDrawer>
			) : null}
		</div>
	)
}

export default Agreement
