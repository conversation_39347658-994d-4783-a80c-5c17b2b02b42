/* eslint-disable react/no-array-index-key */
import { faTrashXmark } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSAccordion from 'components/accordion'
import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import HSSlider from 'components/rangeSlider'
import HSTextField from 'components/textField'
import type { AccordionPanelProps } from 'flowbite-react'
import { Drawer } from 'flowbite-react'
import { chainDisplayOptions, useGetChains } from 'lib/services/chains.service'
import { useSearchParametersStore } from 'pages/planner/event/tabs/search/store'
import type { ReactElement } from 'react'
import { useEffect, useState } from 'react'
import { rangeFilters as rangeFiltersList } from 'pages/planner/event/tabs/search/common'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { airportTypes } from 'lib/common/hotel'
import HSCheckbox from 'components/checkbox'
import { hotelTypes } from 'lib/helpers/venueFeatures'
import MultiSelectDropdown from 'components/multiSelect'

const renderAccordionTitle = (title: string) => (
	<HSAccordion.Title>
		<div className='text-sm font-medium text-gray-900 dark:text-white'>
			{title}
		</div>
	</HSAccordion.Title>
)

interface IFilterDrawer {
	showFilterDrawer: boolean
	hideFilterDrawer: () => void
}

const FilterDrawer = (properties: IFilterDrawer) => {
	const { hideFilterDrawer, showFilterDrawer } = properties

	const { data: chains = [] } = useGetChains(
		chainDisplayOptions.searchFilter.key
	)
	const { userProfile } = useUserProfileContext()

	const {
		rangeFilters,
		choiceFilters,
		airports,
		setAirports,
		setChoiceFilter,
		setRangeFilter,
		resetFilters
	} = useSearchParametersStore()

	const [canApplyFilters, setCanApplyFilters] = useState(false)
	const [canClearFilters, setCanClearFilters] = useState(false)
	const [localRadiusMilesFilter, setLocalRadiusMilesFilter] =
		useState<number>(0)
	const [localRangeFilterState, setLocalRangeFilterState] = useState({
		guestRoomQuantity: [...rangeFilters.guestRoomQuantity],
		meetingRoomQuantity: [...rangeFilters.meetingRoomQuantity],
		meetingSpaceSquareFeet: [...rangeFilters.meetingSpaceSquareFeet],
		largestMeetingSpaceSquareFeet: [
			...rangeFilters.largestMeetingSpaceSquareFeet
		],
		averageRating: [...rangeFilters.averageRating]
	})

	const [localChoiceFilterState, setLocalChoiceFilterState] = useState({
		chainId: [...choiceFilters.chainId],
		brandId: [...choiceFilters.brandId],
		hotelTypes: [...choiceFilters.hotelTypes],
		propertySellerId: [...choiceFilters.propertySellerId]
	})

	const [localAirportFilter, setLocalAirportFilter] = useState({
		...airports
	})

	useEffect(() => {
		setCanClearFilters(
			Object.keys(rangeFilters).some(k =>
				rangeFilters[k as keyof typeof rangeFilters].some(Boolean)
			) ||
				Object.keys(choiceFilters).some(k =>
					choiceFilters[k as keyof typeof choiceFilters].some(Boolean)
				)
		)
	}, [rangeFilters, choiceFilters])

	const onLocalRangeFilterChanged = (
		filterKey: string,
		min: number | null,
		max: number | null
	) => {
		setLocalRangeFilterState(p => ({ ...p, [filterKey]: [min, max] }))
		setCanApplyFilters(true)
	}

	const onLocalChoiceFilterChanged = (
		filterKey: string,
		choice: string,
		selected: boolean
	) => {
		setLocalChoiceFilterState(p => ({
			...p,
			[filterKey as keyof typeof localChoiceFilterState]: selected
				? [...p[filterKey as keyof typeof localChoiceFilterState], choice]
				: p[filterKey as keyof typeof localChoiceFilterState].filter(
						c => c !== choice
					)
		}))
		setCanApplyFilters(true)
	}

	const onLocalAirportFilterChanged = ({
		distanceMiles,
		types
	}: {
		distanceMiles: number
		types: string[]
	}) => {
		setLocalAirportFilter(p => ({
			...p,
			distanceMiles,
			types,
			filter: types.length > 0
		}))
		setCanApplyFilters(true)
	}

	const onLocalRadiusMilesFilterChanged = (radiusMiles: number) => {
		setLocalRadiusMilesFilter(radiusMiles)
		setCanApplyFilters(true)
	}

	// const onClickApplyAllFilters = () => {
	// 	onFiltersChanged({
	// 		rangeFilters: { ...localRangeFilterState },
	// 		choiceFilters: { ...localChoiceFilterState },
	// 		airports: { ...localAirportFilter },
	// 		radiusMiles: localRadiusMilesFilter
	// 	})
	// 	setCanApplyFilters(false)
	// }

	const onClickApplyAllFilters = () => {
		// onFiltersChanged({
		// 	rangeFilters: { ...localRangeFilterState },
		// 	choiceFilters: { ...localChoiceFilterState },
		// 	airports: { ...localAirportFilter },
		// 	radiusMiles: localRadiusMilesFilter
		// })
		// Update each range filter individually
		for (const key of Object.keys(localRangeFilterState)) {
			const filterValues =
				localRangeFilterState[key as keyof typeof localRangeFilterState]
			const valueTuple: [number | null, number | null] = [
				filterValues[0] ?? null,
				filterValues[1] ?? null
			]
			setRangeFilter(key as keyof typeof localRangeFilterState, valueTuple)
		}
		setAirports(localAirportFilter)
		setChoiceFilter('chainId', localChoiceFilterState.chainId)
		setChoiceFilter('brandId', localChoiceFilterState.brandId)
		setChoiceFilter('hotelTypes', localChoiceFilterState.hotelTypes)
		setChoiceFilter('propertySellerId', localChoiceFilterState.propertySellerId)
		setChoiceFilter('hotelTypes', localChoiceFilterState.hotelTypes)
		hideFilterDrawer()
		setCanApplyFilters(false)
	}

	return (
		<HSDrawer
			position='right'
			open={showFilterDrawer}
			style={{ width: '400px' }}
			onClose={hideFilterDrawer}
			noPadding
		>
			<div className='flex h-full flex-col'>
				<Drawer.Header
					title='Filters'
					titleIcon={() => null}
					className='px-6 pt-6'
				/>
				<div className='flex flex-1 flex-col gap-2 bg-gray-50 px-6 pb-6 pt-2'>
					<HSButton
						color='text'
						className='w-full'
						onClick={resetFilters}
						disabled={!canClearFilters}
					>
						<div className='flex items-center justify-center gap-2 text-red-800'>
							<div>
								<FontAwesomeIcon icon={faTrashXmark} />
							</div>
							<div>Clear All Filters</div>
						</div>
					</HSButton>
					<div>
						<HSAccordion collapseAll className='border-none' alwaysOpen>
							<HSAccordion.Panel>
								{renderAccordionTitle('Search Radius')}
								<HSAccordion.Content className='px-0'>
									<div className='flex flex-col'>
										<div className='flex w-1/2 justify-end'>
											<HSTextField
												value={localRadiusMilesFilter}
												onChange={event =>
													onLocalRadiusMilesFilterChanged(
														Number(event.target.value)
													)
												}
												groupPlacement='left'
												groupItem='miles'
											/>
										</div>
										<HSSlider
											value={localRadiusMilesFilter}
											onChange={value =>
												onLocalRadiusMilesFilterChanged(Number(value))
											}
											min={5}
											max={100}
											step={10}
										/>
									</div>
								</HSAccordion.Content>
							</HSAccordion.Panel>
							<div className='border-b' />
							{
								rangeFiltersList.map((rf, rfIndex) => (
									<HSAccordion.Panel key={rfIndex.toString()}>
										{renderAccordionTitle(rf.title)}
										<HSAccordion.Content>
											<div className='flex items-center gap-4'>
												<HSTextField
													groupPlacement='left'
													groupItem={
														<div className='text-xs font-medium text-gray-900'>
															min
														</div>
													}
													value={
														localRangeFilterState[
															rf.filterKey as keyof typeof localRangeFilterState
														][0] ?? undefined
													}
													type='number'
													onChange={event => {
														if (event.target.valueAsNumber) {
															onLocalRangeFilterChanged(
																rf.filterKey,
																event.target.valueAsNumber || null,
																localRangeFilterState[
																	rf.filterKey as keyof typeof localRangeFilterState
																][1]
															)
														}
													}}
												/>
												<HSTextField
													type='number'
													groupPlacement='left'
													groupItem={
														<div className='text-xs font-medium text-gray-900'>
															max
														</div>
													}
													value={
														localRangeFilterState[
															rf.filterKey as keyof typeof localRangeFilterState
														][1] ?? undefined
													}
													onChange={event => {
														if (event.target.valueAsNumber) {
															onLocalRangeFilterChanged(
																rf.filterKey,
																localRangeFilterState[
																	rf.filterKey as keyof typeof localRangeFilterState
																][0],
																event.target.valueAsNumber || null
															)
														}
													}}
												/>
											</div>
										</HSAccordion.Content>
									</HSAccordion.Panel>
								)) as unknown as ReactElement<AccordionPanelProps>
							}

							<HSAccordion.Panel>
								{renderAccordionTitle('Hotel Chain(s)')}
								<HSAccordion.Content>
									<div className='flex flex-col gap-2'>
										<MultiSelectDropdown
											options={chains.map(c => c.name ?? '')}
											label='Hotel Type'
											selectedOptions={localChoiceFilterState.chainId}
											onChange={(option: string, checked: boolean) => {
												if (checked) {
													onLocalChoiceFilterChanged('chainId', option, true)
												} else {
													onLocalChoiceFilterChanged('chainId', option, false)
												}
											}}
										/>
									</div>
								</HSAccordion.Content>
							</HSAccordion.Panel>
							<HSAccordion.Panel>
								{renderAccordionTitle('Hotel Brand(s)')}
								<HSAccordion.Content>
									<div className='flex flex-col gap-2'>
										<MultiSelectDropdown
											options={chains.map(c => c.name ?? '')}
											label='Hotel Type'
											selectedOptions={localChoiceFilterState.brandId}
											onChange={(option: string, checked: boolean) => {
												if (checked) {
													onLocalChoiceFilterChanged('brandId', option, true)
												} else {
													onLocalChoiceFilterChanged('brandId', option, false)
												}
											}}
										/>
									</div>
								</HSAccordion.Content>
							</HSAccordion.Panel>
							{userProfile?.previewExperiences?.SEARCHFILTERHOTELTYPE
								? ((
										<HSAccordion.Panel>
											{renderAccordionTitle('Hotel Type(s)')}
											<HSAccordion.Content>
												<div className='flex flex-col gap-2'>
													<MultiSelectDropdown
														options={Object.keys(hotelTypes)}
														label='Hotel Type'
														showHelperText
														selectedOptions={localChoiceFilterState.hotelTypes}
														onChange={(option: string, checked: boolean) => {
															if (checked) {
																onLocalChoiceFilterChanged(
																	'hotelTypes',
																	option,
																	true
																)
															} else {
																onLocalChoiceFilterChanged(
																	'hotelTypes',
																	option,
																	false
																)
															}
														}}
													/>
												</div>
											</HSAccordion.Content>
										</HSAccordion.Panel>
									) as unknown as ReactElement<AccordionPanelProps>)
								: (undefined as unknown as ReactElement<AccordionPanelProps>)}
							<HSAccordion.Panel>
								{renderAccordionTitle('Distance from Airport')}
								<HSAccordion.Content>
									<div className='flex flex-col gap-2'>
										<div className='flex w-32 items-center justify-end'>
											<HSTextField
												value={localAirportFilter.distanceMiles}
												groupPlacement='left'
												groupItem='miles'
												onChange={event =>
													onLocalAirportFilterChanged({
														...localAirportFilter,
														distanceMiles: Number(event.target.value)
													})
												}
											/>
										</div>
										<HSSlider
											value={localAirportFilter.distanceMiles}
											min={5}
											max={60}
											step={5}
											onChange={value =>
												onLocalAirportFilterChanged({
													...localAirportFilter,
													distanceMiles: Number(value)
												})
											}
										/>
									</div>
								</HSAccordion.Content>
							</HSAccordion.Panel>
							<HSAccordion.Panel>
								{renderAccordionTitle('Airport Tier')}
								<HSAccordion.Content>
									<div className='flex flex-col gap-3.5'>
										{airportTypes.map((at, atIndex) => (
											<div className='flex items-start gap-2' key={atIndex}>
												<HSCheckbox
													checked={localAirportFilter.types.includes(at.value)}
													onChange={event =>
														onLocalAirportFilterChanged({
															...localAirportFilter,
															types: event.target.checked
																? [...localAirportFilter.types, at.value]
																: localAirportFilter.types.filter(
																		t => t !== at.value
																	)
														})
													}
												/>
												<div className='flex flex-col'>
													<div className='text-sm font-normal leading-none text-gray-700'>
														{at.title}
													</div>
													<div className='text-xs font-normal text-gray-500'>
														{at.subtitle}
													</div>
												</div>
											</div>
										))}
									</div>
								</HSAccordion.Content>
							</HSAccordion.Panel>
						</HSAccordion>
					</div>
				</div>
				<div className='p-4'>
					<div className='flex justify-end gap-2'>
						<HSButton color='light' onClick={hideFilterDrawer} className='grow'>
							Cancel
						</HSButton>
						<HSButton
							onClick={onClickApplyAllFilters}
							className='grow'
							disabled={!canApplyFilters}
						>
							Save View
						</HSButton>
					</div>
				</div>
			</div>
		</HSDrawer>
	)
}

export default FilterDrawer
