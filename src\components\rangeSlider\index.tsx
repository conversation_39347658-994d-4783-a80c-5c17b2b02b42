import type { SliderChangeEventArgs } from '@syncfusion/ej2-react-inputs'
import { SliderComponent } from '@syncfusion/ej2-react-inputs'
import './index.css'

export interface IRangeSliderProperties {
	value: number
	onChange: (value: number) => void
	max?: number
	step?: number
	min?: number
}
const HSSlider = (properties: IRangeSliderProperties) => {
	const { onChange, value, max, min, step } = properties

	return (
		<SliderComponent
			value={value}
			change={(event: SliderChangeEventArgs) => onChange(Number(event.value))}
			type='MinRange'
			max={max}
			step={step}
			min={min}
		/>
	)
}

export default HSSlider
