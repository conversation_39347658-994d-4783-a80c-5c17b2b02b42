/* eslint-disable @typescript-eslint/promise-function-async */
import { getHopSkipConfig } from 'lib/auth/auth.config'
import { PostHogProvider } from 'posthog-js/react'
import type { PropsWithChildren } from 'react'

const postHogKey = 'phc_NbOjcjUhuCMdENtXGjwNsaJGD4oFmSVbjsD4N267Wkt'
const postHogHost = 'https://us.i.posthog.com'

const PostHogProviderWrapper = ({ children }: PropsWithChildren) => {
	const config = getHopSkipConfig()
	const isProduction = config.api.environment === 'Prod'
	return isProduction ? (
		<PostHogProvider
			apiKey={postHogKey}
			options={{
				api_host: postHogHost
			}}
		>
			{children}
		</PostHogProvider>
	) : (
		children
	)
}

export default PostHogProviderWrapper
