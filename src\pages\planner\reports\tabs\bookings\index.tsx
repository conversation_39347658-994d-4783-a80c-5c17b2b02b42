/* eslint-disable react/no-array-index-key */
/* eslint-disable @typescript-eslint/no-unnecessary-type-assertion */
/* eslint-disable @typescript-eslint/dot-notation */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-confusing-void-expression */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable unicorn/no-array-reduce */
import {
	faColumns3,
	faDownload,
	faInfoCircle,
	faSackDollar
} from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import type { EventPlan } from 'models/proposalResponseMonitor'
import HSTooltip from 'components/tooltip'
import HSCurrencyPicker from 'components/currencySelector'
import {
	calculatePeriod,
	formatCurrency,
	formatNumber,
	periodTypes,
	type ICurrency
} from 'lib/helpers'
import HSProgress from 'components/progressBar'
import HSTable from 'components/table'
import {
	dateTemplate,
	rfpNameTemplate,
	statusTemplate,
	currencyTemplate
} from '../openPipeline/templates'
import CustomizeColumnsModal from 'components/planner/customizeColumnsModal'
import { columnOptions } from 'components/planner/customizeColumnsModal/columnConfig'
import { useEffect, useState } from 'react'
import { differenceInDays, parseISO } from 'date-fns'
import { calculateProposalValue } from 'lib/helpers/proposalValues'
import { ProposalRequestStatusMap } from 'lib/helpers/statusMaps'
import { useGetChains } from 'lib/services/chains.service'
import {
	faChevronDown,
	faClock,
	faLocationCrosshairs
} from '@fortawesome/pro-light-svg-icons'
import dateFilterStore from '../common/dateFilterStore'
import LocationMapMarker from 'components/googleMapV2/components/locationMapMarker'
import GoogleMapsV2 from 'components/googleMapV2'
import { InfoWindow } from '@vis.gl/react-google-maps'
import type { ISetFilters } from '../../helper'
import BookingsGrid from './bookingsGrid'
import {
	PresetsDirective,
	PresetDirective
} from '@syncfusion/ej2-react-calendars'
import Loader from 'components/loader'
import FilterPopover from '../common/filterHelper'
import { packageWorkbook } from 'lib/services/report.service'
import { toast } from 'react-toastify'
import rawDataFieldsForPlanner from '../columns/columnDefinition'
import MetricCard from 'components/metric/card'
import HSDateRangePicker from 'components/dateRangePicker'
import { Link } from 'react-router-dom'
import HSBadge from 'components/badge'
import type { IHotel } from '../openPipeline'

interface BookingsProperties {
	eventPlans: EventPlan[]
	currency: ICurrency
	setFilters: React.Dispatch<React.SetStateAction<ISetFilters>>
	filters: ISetFilters
	filteredChainIds: string[]
	isFeatureLocked: boolean
	hotelsListFilterFunction: (hotel: { status: string }) => boolean
}
interface ProposalValue {
	value: number
	type: string
	period?: string
	quantity?: number
}
interface ContractLocation {
	venueLocation: string
	value: number
	lat?: number
	lng?: number
	venueName?: string
	eventPlanName?: string
	geolocation: {
		coordinates: [number, number]
	}
	proposalValues?: ProposalValue[]
}
interface AggregatedLocation {
	venueLocation: string
	value: number
	count: number
}
interface ChainValue {
	chainId: string
	name: string
	count: number
	value: number
}
interface BrandValue {
	brandId: string
	name: string
	count: number
	value: number
}
interface Metrics {
	contracted: number
	totalContractValue: number
	averageContractValue: number
	averageDaysToContractSigned: number
	valuesByChain: ChainValue[]
	valuesByBrand: BrandValue[]
	contractLocations: ContractLocation[]
	daysToContractSigned: number
}

const Bookings = (properties: BookingsProperties) => {
	const {
		eventPlans,
		currency,
		setFilters,
		filters,
		filteredChainIds,
		isFeatureLocked,
		hotelsListFilterFunction
	} = properties
	const [showColumnsModal, setShowColumnsModal] = useState(false)
	const [visibleColumns, setVisibleColumns] = useState<string[]>(columnOptions)
	const { dateFilter, setDateFilter } = dateFilterStore()
	const [metrics, setMetrics] = useState<Metrics>({
		contracted: 0,
		totalContractValue: 0,
		averageContractValue: 0,
		averageDaysToContractSigned: 0,
		valuesByChain: [],
		valuesByBrand: [],
		contractLocations: [],
		daysToContractSigned: 0
	})
	const contractLocations = metrics.contractLocations as ContractLocation[]
	const { data: chains } = useGetChains()
	const [isDownloading, setIsDownloading] = useState(false)

	const onDateChangeFilter = (dates: Date[] | null) => {
		if (dates) {
			setFilters(previous => ({
				...previous,
				dateFilter: {
					...previous.dateFilter, // Keep the previous filters type
					startDate: new Date(dates[0]).toISOString().split('.')[0],
					endDate: new Date(dates[1]).toISOString().split('.')[0]
				}
			}))

			setDateFilter({
				startDate: new Date(dates[0]).toISOString().split('.')[0],
				endDate: new Date(dates[1]).toISOString().split('.')[0]
			})
		}
	}

	const onClickExport = () => {
		setIsDownloading(true)
		toast.info(
			<div className='flex gap-2'>
				<FontAwesomeIcon
					icon={faClock}
					className='rounded-full bg-yellow-100 p-1 text-yellow-700'
					size='lg'
				/>
				Downloading...
			</div>
		)

		const exportConfiguration = {
			workbookId: 'clientexport',
			packageData: {
				workbookName: 'Bookings',
				sheets: [
					{
						name: 'Bookings',
						title: 'Bookings',
						rows: eventPlans
							.sort((a, b) => ((a.name ?? '') > (b.name ?? '') ? 1 : -1))
							.map(item => ({
								values: Object.fromEntries(
									rawDataFieldsForPlanner['bookings']
										.filter(column => column.header !== '')
										.map(field => [
											field.header,
											{
												value:
													typeof field.exportFormat === 'function'
														? field.exportFormat(item)
														: item[field.name as keyof EventPlan],
												formatString: field.exportFormatString ?? ''
											}
										])
								)
							}))
					}
				]
			}
		}
		packageWorkbook(exportConfiguration)
			.catch((error: unknown) => console.log(error))
			.finally(() => {
				setIsDownloading(false)
			})
	}

	useEffect(() => {
		const totals = eventPlans.reduce<Metrics>(
			(accumulator, plan) => {
				const updated = { ...accumulator }

				updated.contracted += 1
				updated.totalContractValue += plan.rfpValue ?? 0

				if (plan.firstContractSigned && plan.firstSubmitted) {
					updated.daysToContractSigned += differenceInDays(
						parseISO(plan.firstContractSigned),
						parseISO(plan.firstSubmitted)
					)
				}

				const contractStatus =
					plan.proposalRequestStatuses?.[
						ProposalRequestStatusMap?.ContractSigned?.key ?? ''
					]
				if (!contractStatus) return updated

				for (const proposalRequest of contractStatus) {
					const chainId = proposalRequest.chainId || 'INDEPENDENT'
					const proposalValue = calculateProposalValue(
						proposalRequest.proposalValues ?? []
					)

					const existingChainItem = updated.valuesByChain.find(
						item => item.chainId === chainId
					)
					if (existingChainItem) {
						existingChainItem.count += 1
						existingChainItem.value += proposalValue
					} else {
						const chain = chains?.find(chainItem => chainItem.id === chainId)
						const chainName = chain?.name ?? 'Independent'
						updated.valuesByChain.push({
							chainId,
							name: chainName,
							count: 1,
							value: proposalValue
						})
					}

					if (chainId !== 'INDEPENDENT' && proposalRequest.brandId) {
						const existingBrandItem = updated.valuesByBrand.find(
							item => item.brandId === proposalRequest.brandId
						)
						if (existingBrandItem) {
							existingBrandItem.count += 1
							existingBrandItem.value += proposalValue
						} else {
							const chain = chains?.find(chainItem => chainItem.id === chainId)
							const brand = chain?.brands.find(
								brandItem => brandItem.id === proposalRequest.brandId
							)
							const brandName = brand?.name ?? 'Independent'
							updated.valuesByBrand.push({
								brandId: proposalRequest.brandId,
								name: brandName,
								count: 1,
								value: proposalValue
							})
						}
					}

					if (proposalRequest.geolocation?.coordinates) {
						updated.contractLocations.push({
							venueLocation: proposalRequest.venueLocation || '',
							value: proposalValue,
							lat: proposalRequest.geolocation.coordinates[1],
							lng: proposalRequest.geolocation.coordinates[0],
							venueName: proposalRequest.venueName || '',
							eventPlanName: plan.name || '',
							geolocation: proposalRequest.geolocation
						})
					}
				}

				return updated
			},
			{
				contracted: 0,
				totalContractValue: 0,
				valuesByChain: [],
				valuesByBrand: [],
				contractLocations: [],
				daysToContractSigned: 0,
				averageContractValue: 0,
				averageDaysToContractSigned: 0
			}
		)

		setMetrics({
			...totals,
			averageContractValue:
				totals.contracted > 0
					? totals.totalContractValue / totals.contracted
					: 0,
			averageDaysToContractSigned:
				totals.contracted > 0
					? totals.daysToContractSigned / totals.contracted
					: 0
		})
	}, [eventPlans, chains])

	const filterHotels = (hotel: IHotel) =>
		typeof hotelsListFilterFunction === 'function'
			? hotelsListFilterFunction(hotel) &&
				hotel.status === (ProposalRequestStatusMap.ContractSigned?.key ?? ' ')
			: hotel.status === (ProposalRequestStatusMap.ContractSigned?.key ?? ' ')

	const formatHotelsList = (
		item: EventPlan & {
			hotels?: IHotel[]
		}
	) => {
		const filteredHotels = (item.hotels || []).filter(hotel =>
			filterHotels(hotel)
		)
		const renderTooltip = () => (
			<div className='flex flex-col gap-1'>
				{filteredHotels.slice(1).map((hotel, index) => (
					<div key={index} className='text-sm font-medium text-gray-400'>
						<div className='flex flex-col'>{hotel.venueName}</div>
						<div className='text-sm font-medium text-gray-400'>
							{hotel.venueLocation}
						</div>
					</div>
				))}
			</div>
		)
		return (
			<div className='flex gap-1'>
				{filteredHotels.length > 0 && (
					<div>
						<Link to={`/planner/event/${item.id}`}>
							<div className='text-gray-600'>{filteredHotels[0].venueName}</div>
							<div className='text-sm text-gray-400'>
								{filteredHotels[0].venueLocation}
							</div>
						</Link>
					</div>
				)}
				{filteredHotels.length > 1 && (
					<div className='flex gap-2 text-sm'>
						<HSTooltip content={renderTooltip()}>
							<HSBadge color='gray'>{`+${filteredHotels.length - 1}`}</HSBadge>
						</HSTooltip>
					</div>
				)}
			</div>
		)
	}

	return (
		<>
			<div
				className='flex flex-col overflow-auto'
				style={{ maxHeight: 'calc(100vh - 8rem)' }}
			>
				<div className='border-b px-4 py-6'>
					<div className='flex items-center justify-between'>
						<div className='flex max-w-[600px] flex-col'>
							<div className='text-xl font-semibold text-gray-900'>
								Bookings Dashboard
							</div>
							<div className='text-sm font-normal text-gray-500'>
								Track performance, trends, and conversion metrics for all your
								contracted RFPs at a glance
							</div>
						</div>
						<div className='flex items-center gap-4'>
							<div className='flex items-center gap-2'>
								<HSCurrencyPicker
									value={currency}
									onChange={value => {
										setFilters(previous => ({
											...previous,
											currency: value
										}))
									}}
									color='light'
									icon={faChevronDown}
								/>
							</div>
							<FilterPopover
								filters={filters}
								setFilters={setFilters}
								chains={chains}
								filteredChainIds={filteredChainIds}
							/>
							<div className='w-80'>
								<HSDateRangePicker
									placeholder='Select Date Range'
									onChange={event => {
										if (event.value) {
											onDateChangeFilter(event.value)
										}
									}}
									value={[
										new Date(dateFilter.startDate),
										new Date(dateFilter.endDate)
									]}
									format='MMM dd, yyyy'
								>
									<PresetsDirective>
										{Object.keys(periodTypes).map(period => {
											const {
												type: { label, key },
												startDate,
												endDate
											} = calculatePeriod(period)
											return (
												<PresetDirective
													key={key}
													label={label}
													start={new Date(startDate)}
													end={new Date(endDate)}
												/>
											)
										})}
									</PresetsDirective>
								</HSDateRangePicker>
							</div>
						</div>
					</div>
				</div>
				<div className='px-4 py-6'>
					<div className='flex flex-col gap-6'>
						<div className='flex gap-4'>
							<div className='w-1/2'>
								<div className='card h-full p-4'>
									<div className='mb-4 text-base font-medium text-gray-700'>
										Contract Details
									</div>
									<div className='flex gap-8'>
										<div className='flex-1'>
											<div className='mb-2 flex items-center gap-2'>
												<div className='text-sm font-bold text-gray-500'>
													Total Contracted
												</div>
												<HSTooltip content='The total number of bookings that have been awarded across all your RFPs'>
													<FontAwesomeIcon
														icon={faInfoCircle}
														className='text-gray-400'
													/>
												</HSTooltip>
											</div>
											<MetricCard isLocked={isFeatureLocked} lockMessage=''>
												<div className='flex items-baseline gap-2'>
													<div className='text-2xl font-semibold text-gray-900'>
														{formatNumber(metrics.contracted)}
													</div>
													<div className='text-sm text-gray-500'>bookings</div>
												</div>
											</MetricCard>
										</div>
										<div className='border-l border-gray-200' />
										<div className='flex-1'>
											<div className='mb-2 flex items-center gap-2'>
												<div className='text-sm font-bold text-gray-500'>
													Avg Time To Contracted
												</div>
												<HSTooltip content='The average amount of time from sending an RFP to signing a contract with a hotel'>
													<FontAwesomeIcon
														icon={faInfoCircle}
														className='text-gray-400'
													/>
												</HSTooltip>
											</div>
											<MetricCard isLocked={isFeatureLocked} lockMessage=''>
												<div className='flex items-baseline gap-2'>
													<div className='text-2xl font-semibold text-gray-900'>
														{formatNumber(metrics.averageDaysToContractSigned)}
													</div>
													<div className='text-sm text-gray-500'>days</div>
												</div>
											</MetricCard>
										</div>
									</div>
								</div>
							</div>
							<div className='w-1/2'>
								<div className='card h-full p-4'>
									<div className='mb-4 text-base font-medium text-gray-700'>
										Contract Value
									</div>
									<div className='flex gap-4'>
										<div className='w-1/2'>
											<div className='mb-2 flex items-center gap-2'>
												<div className='text-sm font-bold text-gray-500'>
													Average
												</div>
												<HSTooltip content='The average contract value across all your bookings, calculated by dividing the total contracted value by the number of bookings'>
													<FontAwesomeIcon
														icon={faInfoCircle}
														className='text-gray-400'
													/>
												</HSTooltip>
											</div>
											<MetricCard isLocked={isFeatureLocked} lockMessage=''>
												<div className='flex items-center gap-1'>
													<div className='flex h-6 w-6 items-center justify-center rounded-lg bg-gray-100'>
														<FontAwesomeIcon
															icon={faSackDollar}
															className='text-gray-400'
														/>
													</div>
													<div className='text-2xl font-semibold text-gray-900'>
														{formatCurrency(
															metrics.averageContractValue,
															currency
														)}
													</div>
												</div>
											</MetricCard>
										</div>
										<div className='border-l border-gray-200' />
										<div className='w-1/2'>
											<div className='mb-2 flex items-center gap-2'>
												<div className='text-sm font-bold text-gray-500'>
													Total
												</div>
												<HSTooltip content='The sum of all contract values across all of your awarded bookings'>
													<FontAwesomeIcon
														icon={faInfoCircle}
														className='text-gray-400'
													/>
												</HSTooltip>
											</div>
											<MetricCard isLocked={isFeatureLocked} lockMessage=''>
												<div className='flex items-center gap-1'>
													<div className='flex h-6 w-6 items-center justify-center rounded-lg bg-green-100'>
														<FontAwesomeIcon
															icon={faSackDollar}
															className='text-green-600'
														/>
													</div>
													<div className='text-grey-900 text-2xl font-semibold'>
														{formatCurrency(
															metrics.totalContractValue,
															currency
														)}
													</div>
												</div>
											</MetricCard>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className='px-4'>
					<div className='flex gap-4'>
						<div className='w-3/4'>
							<div className='card h-full rounded-lg border border-gray-200'>
								<div className='flex items-center justify-between border-b border-gray-200 px-4 py-3'>
									<div className='flex items-center gap-2'>
										<div className='text-base font-medium text-gray-700'>
											Bookings by Location
										</div>
										<HSTooltip content='A distribution map of your bookings that details the number and spread of your awarded RFPs across different locations'>
											<FontAwesomeIcon
												icon={faInfoCircle}
												className='text-gray-400'
											/>
										</HSTooltip>
									</div>
								</div>
								<div className='flex divide-x divide-gray-200'>
									<div className='w-1/3 px-4 py-3'>
										<div className='mb-2 border-b border-gray-200 pb-1 text-sm font-bold text-gray-500'>
											Top 5 cities
										</div>
										<MetricCard isLocked={isFeatureLocked} lockMessage=''>
											<div className='flex flex-col gap-2'>
												{contractLocations
													.reduce<AggregatedLocation[]>(
														(accumulator, current) => {
															const existing = accumulator.find(
																loc =>
																	loc.venueLocation === current.venueLocation
															)
															if (existing) {
																existing.value += current.value
																existing.count += 1
															} else {
																accumulator.push({
																	venueLocation: current.venueLocation,
																	value: current.value,
																	count: 1
																})
															}
															return accumulator
														},
														[]
													)
													.sort((a, b) => b.value - a.value)
													.slice(0, 5)
													.map(location => (
														<div
															key={location.venueLocation}
															className='border-b border-gray-100 py-2 last:border-b-0'
														>
															<div className='mb-1 text-sm font-medium text-gray-900'>
																{location.venueLocation}
															</div>
															<div className='flex items-center gap-3 text-sm text-gray-500'>
																<span>${formatNumber(location.value)}</span>
																<span>
																	{formatNumber(location.count)} booking
																	{location.count === 1 ? '' : 's'}
																</span>
															</div>
														</div>
													))}
											</div>
										</MetricCard>
									</div>
									<div className='w-2/3 px-4 py-3'>
										<MetricCard
											isLocked={isFeatureLocked}
											lockMessage='Upgrade to Pro to unlock'
										>
											<div className='h-96 overflow-hidden'>
												<GoogleMapsV2
													defaultZoom={4}
													markerComponent={metrics.contractLocations.map(
														location => (
															<LocationMapMarker
																key={`${location.lat}-${location.lng}-${location.venueName}`}
																position={{
																	lat: location.lat ?? 0,
																	lng: location.lng ?? 0
																}}
																icon={faLocationCrosshairs}
																title={location.venueName}
																onClick={() => {}}
																backgroundColor='#0a587e'
															>
																<InfoWindow
																	position={{
																		lat: (location.lat ?? 0) + 0.005,
																		lng: location.lng ?? 0
																	}}
																>
																	<div className='z-[99999]'>
																		<div title={location.eventPlanName}>
																			{location.eventPlanName}
																		</div>
																		<div>{location.venueName}</div>
																		<div>
																			{formatCurrency(location.value, currency)}
																		</div>
																	</div>
																</InfoWindow>
															</LocationMapMarker>
														)
													)}
												/>
											</div>
										</MetricCard>
									</div>
								</div>
							</div>
						</div>
						<div className='w-1/4'>
							<div className='card h-full'>
								<BookingsGrid
									metrics={{
										valuesByChain: metrics.valuesByChain,
										valuesByBrand: metrics.valuesByBrand
									}}
									currency={currency}
								/>
							</div>
						</div>
					</div>
				</div>
				<div className='px-4 py-6'>
					<div className='flex items-center justify-between'>
						<div className='text-sm font-medium text-gray-900'>
							RFP Conversions
							<span className='ml-1 text-xs font-normal text-gray-400'>
								{eventPlans.length} RFP{eventPlans.length > 1 ? 's' : ''}
							</span>
						</div>
						<div className='flex items-center gap-4'>
							<HSButton
								color='light'
								onClick={onClickExport}
								disabled={!eventPlans?.length}
							>
								{isDownloading ? (
									<Loader size='sm' />
								) : (
									<FontAwesomeIcon icon={faDownload} />
								)}
							</HSButton>
							<HSButton color='light' onClick={() => setShowColumnsModal(true)}>
								<FontAwesomeIcon icon={faColumns3} />
							</HSButton>
						</div>
					</div>
				</div>
				<div className='px-4 pb-6'>
					<HSTable
						allowPaging
						rows={eventPlans}
						columns={[
							{
								field: 'name',
								headerText: 'RFP Name/Code',
								render: rfpNameTemplate,
								width: 250,
								sortable: true,
								clipMode: 'ellipsis',
								freeze: 'left',
								visible: visibleColumns.includes('RFP Name/Code')
							},
							{
								field: 'status',
								headerText: 'Status',
								render: statusTemplate,
								width: 200,
								sortable: true,
								clipMode: 'ellipsis',
								visible: visibleColumns.includes('Status')
							},
							{
								field: 'ownerName',
								headerText: 'Owner',
								width: 300,
								sortable: true,
								visible: visibleColumns.includes('Assigned')
							},
							{
								field: 'rfpValue',
								headerText: 'Contracted Value',
								render: (item: EventPlan) =>
									currencyTemplate(item.rfpValue, currency),
								sortable: true,
								clipMode: 'ellipsis',
								width: 150,
								visible: visibleColumns.includes('RFP Value')
							},
							{
								field: 'submitted',
								headerText: 'Sent',
								width: 150,
								render: (item: EventPlan) => dateTemplate(item.submitted),
								sortable: true,
								clipMode: 'ellipsis',
								visible: visibleColumns.includes('Sent')
							},
							{
								field: 'responsesDueDate',
								headerText: 'Response Due',
								render: (item: EventPlan) =>
									dateTemplate(item.responsesDueDate),
								width: 150,
								sortable: true,
								clipMode: 'ellipsis',
								visible: visibleColumns.includes('Responses due')
							},
							{
								field: 'responseRate',
								headerText: 'Response Rate',
								width: 250,
								sortable: true,
								clipMode: 'ellipsis',
								visible: visibleColumns.includes('Response rate'),
								render: (item: EventPlan) => (
									<div className='flex flex-col gap-1'>
										<div className='flex items-center justify-between'>
											<div className='text-sm font-medium text-gray-600'>
												{(item.requestsResponded && item.requestsTotal
													? (item.requestsResponded / item.requestsTotal) * 100
													: 0
												).toFixed(0)}
												%
											</div>
											<div className='text-xs font-normal text-gray-500'>
												{`${formatNumber(item.requestsResponded)}/${formatNumber(item.requestsTotal)} hotels`}
											</div>
										</div>
										<HSProgress
											progress={
												item.requestsResponded && item.requestsTotal
													? (item.requestsResponded / item.requestsTotal) * 100
													: 0
											}
											size='sm'
											color='primary'
										/>
									</div>
								)
							},
							{
								field: 'selectionDate',
								headerText: 'Decision Due',
								render: (item: EventPlan) => dateTemplate(item.selectionDate),
								width: 150,
								sortable: true,
								clipMode: 'ellipsis',
								visible: visibleColumns.includes('Decision due')
							},
							{
								field: 'averageRoomRate',
								headerText: 'AVG Room Rate',
								render: (item: EventPlan) =>
									currencyTemplate(item.averageRoomRate, currency),
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Average Room Rate')
							},
							{
								field: 'requestsTotal',
								headerText: 'Requested',
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Hotels Requested')
							},
							{
								field: 'requestsAwaiting',
								headerText: 'Awaiting',
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Awaiting')
							},
							{
								field: 'requestsResponded',
								headerText: 'Responses',
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Responses')
							},
							{
								field: 'hotelsList',
								headerText: 'Contracted Hotels',
								width: 300,
								render: formatHotelsList,
								visible: visibleColumns.includes('Contracted Hotels'),
								sortable: true,
								clipMode: 'ellipsis'
							}
						]}
					/>
				</div>
			</div>
			<CustomizeColumnsModal
				show={showColumnsModal}
				onClose={() => setShowColumnsModal(false)}
				context='bookings'
				selectedColumns={visibleColumns}
				onColumnsChange={setVisibleColumns}
			/>
		</>
	)
}

export default Bookings
