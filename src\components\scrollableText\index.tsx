/* eslint-disable unicorn/no-keyword-prefix */
import type React from 'react'

/**
 * <PERSON>ps interface for the ScrollableText component
 */
interface ScrollableTextProperties {
	/** The text content to display */
	content: string
	/** Number of visible rows (defaults to 5) */
	rows?: number
	/** Optional title for the card */
	title?: string
	/** Additional CSS classes */
	className?: string
}

/**
 * ScrollableText component displays text content with customizable height and scrolling
 *
 * @param props - Component props
 * @returns Scrollable text component
 */
const ScrollableText: React.FC<ScrollableTextProperties> = ({
	content,
	rows = 5,
	title,
	className = ''
}) => {
	// Calculate approximate height based on rows (1.5rem line height)
	const heightInRem = rows * 1.5

	return (
		<div className={`w-full ${className}`}>
			{title ? <h5 className='text-lg font-semibold'>{title}</h5> : null}
			<div
				className='prose max-w-none overflow-y-auto text-gray-700'
				style={{
					height: `${heightInRem}rem`,
					maxHeight: `${heightInRem}rem`
				}}
			>
				<div className='whitespace-pre-wrap'>{content}</div>
			</div>
		</div>
	)
}

export default ScrollableText
