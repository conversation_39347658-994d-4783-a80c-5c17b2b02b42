import type { GridComponent } from '@syncfusion/ej2-react-grids'
import {
	ColumnDirective,
	ColumnsDirective,
	Freeze,
	Inject,
	Page,
	Sort
} from '@syncfusion/ej2-react-grids'
import { calculatePeriod, periodTypes } from 'lib/helpers'
import { useGetMessageTemplates } from 'lib/services/notification.service'
import {
	useGetNotificationQueueMessages,
	useResendNotificationQueueMessage
} from 'lib/services/notificationQueueMessage.service'
import type { IMessageTemplate } from 'models/notification'
import type {
	IMappedNotificationQueueMessage,
	INotificationQueueMessage
} from 'models/notificationQueueMessage'
import { targetTypes } from 'models/notificationQueueMessage'
import { useEffect, useMemo, useState } from 'react'
import {
	actionTemplate,
	completedTemplate,
	dateTimeFormat,
	emailTemplate,
	recipientTemplate,
	renderModal,
	subjectTemplate,
	valueTemplate
} from './templates'
import {
	PresetDirective,
	PresetsDirective
} from '@syncfusion/ej2-react-calendars'
import { DialogComponent } from '@syncfusion/ej2-react-popups'
import DataGrid from 'components/dataGrid'
import DisabledProfileBanner from 'components/profile/common/disabledProfileBanner'
import Loader from 'components/loader'
import HSPopover from 'components/popover'
import { faFilter } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSBadge from 'components/badge'
import HSButton from 'components/button'
import filterStore from './filterStore'
import HSTextField from 'components/textField'
import headerStore from 'components/header/headerStore'
import HSDateRangePicker from 'components/dateRangePicker'

interface INotificationHistoryProperties {
	targetType: keyof typeof targetTypes
	targetId: string | null
}

const NotificationHistory = (properties: INotificationHistoryProperties) => {
	const { targetId, targetType } = properties
	let gridInstance: GridComponent | null = null
	const [gridData, setGridData] = useState<
		IMappedNotificationQueueMessage[] | undefined
	>([])
	const [messageTemplates, setMessageTemplates] = useState<
		Record<string, IMessageTemplate | null>
	>({})
	const [dateFilter, setDateFilter] = useState<{
		startDate: string
		endDate: string
	}>(calculatePeriod(periodTypes.today.key))
	const [resending, setResending] = useState<Record<string, boolean>>({})
	const [itemDetails, setItemDetails] = useState<{
		show: boolean
		item: IMappedNotificationQueueMessage | object
	}>({ show: false, item: {} })

	const { refetch: getNotificationQueueMessages, isFetching } =
		useGetNotificationQueueMessages(
			targetType,
			targetId ?? '',
			dateFilter,
			false
		)

	const { mutateAsync: resendNotificationQueueMessage } =
		useResendNotificationQueueMessage()

	const { refetch: getMessageTemplates } = useGetMessageTemplates(false)
	const { reset, setHide } = headerStore()

	const onClickViewDetails = (item: IMappedNotificationQueueMessage) => {
		setItemDetails({ show: true, item: { ...item } })
	}

	const {
		clearAll,
		subjectSearch,
		setSubjectSearch,
		targetRoleSearch,
		setTargetRoleSearch
	} = filterStore()

	const onClickResend = (item: IMappedNotificationQueueMessage) => {
		let target: keyof typeof targetTypes
		setResending(s => ({
			...s,
			[item.id ?? '']: true
		}))
		if (item.userProfileId) {
			target = 'UserProfile'
		} else if (item.propertyId) {
			target = 'Hotel'
		} else if (item.eventPlanId) {
			target = 'Rfp'
		} else {
			target = 'System'
		}
		resendNotificationQueueMessage({
			targetType: target,
			targetId:
				item.userProfileId ??
				item.propertyId ??
				item.eventPlanId ??
				item.id ??
				'',
			id: item.id ?? ''
		})
			.then(() => {
				getNotificationQueueMessages()
					.then(() => console.log(`Data fetched successfully`))
					.catch((error: unknown) => console.error(error))
				setResending(s => ({
					...s,
					[item.id ?? '']: false
				}))
			})
			.catch((error: unknown) => console.error(error))
	}

	const onDateChangeFilter = (dates: Date[] | null) => {
		if (dates)
			setDateFilter({
				startDate: new Date(dates[0]).toISOString().split('.')[0],
				endDate: new Date(dates[1]).toISOString().split('.')[0]
			})
	}

	useEffect(() => {
		setHide(false)
		return () => {
			reset()
		}
	}, [reset, setHide])

	useEffect(() => {
		const templates: Record<string, IMessageTemplate> = {}
		getMessageTemplates()
			.then((response: { data: IMessageTemplate[] | undefined }) => {
				if (response.data)
					for (const item of response.data) {
						templates[item.id ?? ''] = item
					}
				setMessageTemplates(templates)
			})
			.catch((error: unknown) => console.error(error))
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	useEffect(() => {
		if (targetId) {
			getNotificationQueueMessages()
				.then((response: { data: INotificationQueueMessage[] | undefined }) => {
					const mappedGridData = response.data?.map(n => ({
						...n,
						subject:
							n.messageResult?.notification?.subject ?? '(empty subject)',
						recipientEmails: (n.messageResult?.to.map(t => t.email) ?? []).join(
							';'
						),
						messageTemplateId: n.messageProperties.messageTemplateId,
						userRole:
							messageTemplates[n.messageProperties.messageTemplateId ?? '']
								?.userRole ?? 'N/A'
					}))

					setGridData(mappedGridData)
				})
				.catch((error: unknown) => console.error(error))
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [targetType, targetId, dateFilter])

	const filterCount = useMemo(
		() => (subjectSearch === '' ? 0 : 1) + (targetRoleSearch === '' ? 0 : 1),
		[subjectSearch, targetRoleSearch]
	)

	useEffect(() => {
		gridInstance?.clearFiltering()
		if (targetRoleSearch !== '') {
			gridInstance?.filterByColumn('userRole', 'contains', targetRoleSearch)
		}
		if (subjectSearch !== '') {
			gridInstance?.filterByColumn('subject', 'contains', subjectSearch)
		}
	}, [gridInstance, subjectSearch, targetRoleSearch])

	const popOverContent = (
		<div className='flex max-h-96 w-80 flex-col gap-4 overflow-y-auto p-4'>
			<div className='flex flex-col gap-2 p-2'>
				<div className='flex items-center justify-between'>
					<div className='text-sm font-medium text-gray-900'>Filters</div>
					<div className='flex gap-2'>
						<HSButton size='sm' color='text'>
							Save View
						</HSButton>
						<HSButton size='sm' color='text' onClick={() => clearAll()}>
							Clear All
						</HSButton>
					</div>
				</div>
			</div>

			<HSTextField
				label='Subject'
				placeholder='Start Typing Subject'
				value={subjectSearch}
				onChange={event => setSubjectSearch(event.target.value)}
			/>

			<HSTextField
				label='Target Role'
				placeholder='Start Typing Target Role'
				value={targetRoleSearch}
				onChange={event => setTargetRoleSearch(event.target.value)}
			/>
		</div>
	)

	return (
		<div className='p-4'>
			<div className='card'>
				<div className='flex items-center justify-between border-b p-4'>
					<div className='text-xl font-semibold text-gray-900'>
						Notification History (ADMIN)
					</div>
				</div>
				<DisabledProfileBanner />
				<div
					className='flex flex-col gap-2 overflow-auto p-4'
					style={{ maxHeight: 'calc(100vh - 15rem)' }}
				>
					<div className='flex items-center justify-between'>
						<div className='text-sm font-medium text-gray-900'>
							{gridData?.length} Notifications
						</div>
						<div className='flex items-center gap-4'>
							<HSDateRangePicker
								placeholder='Sent Date'
								onChange={({ value }: { value: Date[] | null }) =>
									onDateChangeFilter(value)
								}
								value={[
									new Date(dateFilter.startDate),
									new Date(dateFilter.endDate)
								]}
							>
								<PresetsDirective>
									{Object.keys(periodTypes).map(period => {
										const {
											type: { label, key },
											startDate,
											endDate
										} = calculatePeriod(period)
										return (
											<PresetDirective
												key={key}
												label={label}
												start={new Date(startDate)}
												end={new Date(endDate)}
											/>
										)
									})}
								</PresetsDirective>
							</HSDateRangePicker>
							<HSPopover
								content={popOverContent}
								aria-labelledby='filter'
								placement='bottom'
								arrow={false}
							>
								<div className='relative inline-block'>
									<HSButton color='light' size='sm'>
										<div className='flex items-center gap-2'>
											<FontAwesomeIcon icon={faFilter} />
											Filter
										</div>
									</HSButton>
									{filterCount > 0 ? (
										<HSBadge
											color='failure'
											title={`${filterCount} Filter/s Applied`}
											className='absolute right-0 top-0 z-10 -translate-y-1/2 translate-x-1/2 transform rounded-full bg-red-600'
										>
											<div className='text-md flex p-1 font-semibold text-white'>
												{filterCount}
											</div>
										</HSBadge>
									) : null}
								</div>
							</HSPopover>
						</div>
					</div>
					{isFetching ? (
						<Loader />
					) : (
						<DataGrid
							filterSettings={{ type: 'Menu' }}
							ref={r => {
								gridInstance = r
							}}
							dataSource={gridData}
							loadingIndicator={{ indicatorType: 'Shimmer' }}
							allowSorting
							allowMultiSorting
							// eslint-disable-next-line react/jsx-props-no-spreading
							{...(targetType === targetTypes.System
								? { allowPaging: true }
								: {})}
							allowTextWrap
							autoFit
							frozenColumns={1}
						>
							<ColumnsDirective>
								<ColumnDirective
									field='subject'
									headerText='Subject'
									template={(item: IMappedNotificationQueueMessage) =>
										subjectTemplate(item, onClickViewDetails)
									}
									width={300}
								/>
								<ColumnDirective
									field='recipientEmails'
									headerText='Recipient(s)'
									template={(item: IMappedNotificationQueueMessage) =>
										recipientTemplate(item)
									}
								/>
								<ColumnDirective
									field='created'
									headerText='Triggered'
									template={(item: IMappedNotificationQueueMessage) =>
										dateTimeFormat(item.created)
									}
									width={150}
								/>
								<ColumnDirective
									field='completed'
									headerText='Sent'
									template={(item: IMappedNotificationQueueMessage) =>
										completedTemplate(item, resending)
									}
									width={150}
								/>
								<ColumnDirective
									field='messageTemplateId'
									headerText='Email Template'
									template={(item: IMappedNotificationQueueMessage) =>
										emailTemplate(item)
									}
									autoFit
								/>
								<ColumnDirective
									field='userRole'
									headerText='Target Role'
									template={(item: IMappedNotificationQueueMessage) =>
										valueTemplate(item.userRole ?? 'N/A')
									}
								/>
								<ColumnDirective
									field='id'
									headerText='Resend'
									template={(item: IMappedNotificationQueueMessage) =>
										actionTemplate(item, onClickResend, resending)
									}
									freeze='Right'
									textAlign='Center'
									width={100}
								/>
							</ColumnsDirective>
							<Inject services={[Sort, Page, Freeze]} />
						</DataGrid>
					)}
				</div>

				{itemDetails.show ? (
					<DialogComponent
						visible
						showCloseIcon
						width='700px'
						close={() => setItemDetails({ show: false, item: {} })}
						target='#modal-dialog'
						isModal
					>
						{renderModal(itemDetails.item as IMappedNotificationQueueMessage)}
					</DialogComponent>
				) : null}
			</div>
		</div>
	)
}

export default NotificationHistory
