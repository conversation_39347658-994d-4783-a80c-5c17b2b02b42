/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-call */
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { isEmail } from 'lib/helpers'
import {
	ROLE_AFFILIATE,
	ROLE_DESTINATION_MANAGER,
	ROLE_HOTELIER,
	ROLE_SUPPLIER
} from 'lib/helpers/roles'
import {
	formatInvitationMessage,
	supplierContactRoles
} from 'lib/helpers/supplierContacts'
import { addHotelier, deleteHotelier } from 'lib/services/hotels.service'
import { saveSupplierContact } from 'lib/services/organizations.service'
import { getUserProfilesById } from 'lib/services/userProfile.service'
import type { IAddHotelier } from 'models/hotelier'
import type { AssociatedVenues, IUserProfile } from 'models/userProfiles'
import type { Venue } from 'models/venue'
import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import type { ISourcingProfile } from 'models/organizations'
import HSDrawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import HSButton from 'components/button'
import HSTextField from 'components/textField'
import HSToggleSwitch from 'components/toggleSwitch'
import HSDropdownButton from 'components/dropdown'
import HSTextArea from 'components/textarea'
import HotelSelectorTrimmed from 'components/hotelSelector/trimmed'
import { removeTemplate } from 'pages/hotel/benchmarking/compSet/templates'
import type { IDataSet } from 'pages/hotel/benchmarking/compSet'
import { startCase } from 'es-toolkit'
import { faInfoCircle, faPlus } from '@fortawesome/pro-light-svg-icons'
import HSTooltip from 'components/tooltip'
import { toast } from 'react-toastify'
import Loader from 'components/loader'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import HSPhoneNumber from 'components/phoneNumber'

const viewModes = {
	lookupEmail: 'lookupEmail',
	verifyEmail: 'verifyEmail',
	editContact: 'editContact'
}
interface ILookupResult {
	verify: boolean
	message: string
}
const defaultEmailLookupResult: ILookupResult = {
	verify: false,
	message: ''
}

export interface SupplierContactModalProperties {
	onClose: () => void
	isVisible: boolean
	isRFP: boolean
	isEdit: boolean
	isAddNew: boolean
	supplierContact: ISupplierContact | null
	onAddUpdate: () => Promise<void>
	setSupplierContact: React.Dispatch<
		React.SetStateAction<Partial<ISupplierContact> | null>
	>
}

const RfpContactModal = (properties: SupplierContactModalProperties) => {
	const {
		isVisible,
		isRFP,
		isAddNew,
		isEdit,
		onClose,
		supplierContact,
		onAddUpdate,
		setSupplierContact
	} = properties
	const { organizationId: orgId } = useParams()
	const { userProfile } = useUserProfileContext()
	const organizationId = orgId ?? userProfile?.organizationId

	const [canEditContact, setCanEditContact] = useState(!isRFP || isEdit)
	const [canEditPhoneNumber, setCanEditPhoneNumber] = useState(!isRFP || isEdit)
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const [emailLookupResult, setEmailLookupResult] = useState<ILookupResult>({
		...defaultEmailLookupResult
	})
	const [defaultInvitationMessage, setDefaultInvitationMessage] = useState('')
	const [selectedVenue, setSelectedVenue] = useState<Venue | undefined>()
	const [associatedVenues, setAssociatedVenues] = useState<
		AssociatedVenues[] | null
	>([])
	const [showHotelSelector, setShowHotelSelector] = useState<boolean>(false)
	const [addingVenues, setAddingVenues] = useState<Venue[]>([])
	const [isNameEditable, setIsNameEditable] = useState(false)
	const [viewMode, setViewMode] = useState(
		isAddNew ? viewModes.lookupEmail : viewModes.editContact
	)
	const [isFormValid, setIsFormValid] = useState(isEdit)
	const [isEmailVerified, setIsEmailVerified] = useState(isEdit)
	const [isEmailBeingVerified, setIsEmailBeingVerified] = useState(false)

	useEffect(() => {
		setIsNameEditable(!isEdit)
		setIsEmailVerified(isEdit)
	}, [canEditContact, isEdit])

	useEffect(() => {
		setViewMode(isAddNew ? viewModes.lookupEmail : viewModes.editContact)
		setCanEditContact(!isRFP || isEdit)
		setDefaultInvitationMessage('')
		setEmailLookupResult({ ...defaultEmailLookupResult })
	}, [isAddNew, isRFP, isEdit])

	useEffect(() => {
		if (
			viewMode === viewModes.editContact &&
			supplierContact?.role === supplierContactRoles.hot.key
		) {
			setAddingVenues([])
			getUserProfilesById(supplierContact.id ?? '')
				.then(r => setAssociatedVenues(r.associatedVenues))
				.catch((error: unknown) => console.log(error))
		} else {
			setAssociatedVenues([])
		}
	}, [viewMode, supplierContact?.id, supplierContact?.role])

	useEffect(() => {
		setDefaultInvitationMessage(
			formatInvitationMessage({
				supplierContact: {
					...supplierContact,
					firstName: supplierContact?.firstName ?? '',
					// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
					portfolioType: supplierContact?.role
						? supplierContactRoles[supplierContact.role].portfolioType
						: 'portfolio'
				},
				currentUser: { firstName: userProfile.firstName ?? '' }
			})
		)

		const valid =
			(!!supplierContact?.id &&
				!!supplierContact.firstName &&
				!!supplierContact.lastName &&
				!!supplierContact.role &&
				(!!supplierContact.organizationId || !!supplierContact.companyName) &&
				isEmail(supplierContact.id) &&
				supplierContact.id !== userProfile.id &&
				(supplierContact.role === supplierContactRoles.hot.key
					? true
					: supplierContact.requestSuggestions !== null) &&
				(supplierContact.role === supplierContactRoles.hot.key
					? true
					: supplierContact.allowMessaging !== null)) ??
			false
		setIsFormValid(valid)
	}, [supplierContact, userProfile.id, userProfile.firstName])

	const temporaryRemoveHotel = async (hotelToRemove: IDataSet) => {
		try {
			if (supplierContact?.id) {
				await deleteHotelier(hotelToRemove.id, supplierContact.id)
				setAssociatedVenues(
					associatedVenues?.filter(h => h.id !== hotelToRemove.id) ?? []
				)
				toast.success('Hotel removed successfully')
			} else {
				console.error('Supplier contact ID is missing')
				toast.error('Failed to remove hotel. Contact ID is missing.')
			}
		} catch (error) {
			console.error('Error removing hotel:', error)
			toast.error('Failed to remove hotel. Please try again.')
		}
	}

	const onClickLookupEmail = () => {
		setIsEmailBeingVerified(true)
		getUserProfilesById(supplierContact?.id ?? '')
			.then((response: IUserProfile) => {
				const {
					firstName,
					lastName,
					phoneNumber,
					isPaidSubscriber,
					role,
					organizationName
				} = response

				let roleKey

				switch (role) {
					case ROLE_HOTELIER: {
						roleKey = supplierContactRoles.hot.key
						break
					}
					case ROLE_DESTINATION_MANAGER: {
						roleKey = supplierContactRoles.dmo.key
						break
					}
					case ROLE_AFFILIATE: {
						roleKey = supplierContactRoles.soa.key
						break
					}
					default: {
						roleKey = supplierContactRoles.nso.key
						break
					}
				}
				const supplierContactObject: Partial<ISupplierContact> = {
					...response,
					firstName,
					lastName,
					role: roleKey,
					organizationId,
					companyName: organizationName,
					requestSuggestions: isPaidSubscriber ? true : null
				}
				setSupplierContact(supplierContactObject)
				if (
					[ROLE_AFFILIATE, ROLE_SUPPLIER, ROLE_DESTINATION_MANAGER].includes(
						role ?? ''
					) ||
					(!isRFP && role === ROLE_HOTELIER)
				) {
					setEmailLookupResult({
						verify: true,
						message: `Email verified. Click Next to add this contact to your ${
							isRFP ? 'RFP' : 'directory'
						}.`
					})
				} else if (isRFP && role === ROLE_HOTELIER) {
					setEmailLookupResult({
						verify: false,
						message: `${supplierContact?.id} exists in HopSkip as a ${role} and cannot be added here. You can add this person to one or more hotels from the Send RFP or Proposal Tracker tab.`
					})
				} else {
					setEmailLookupResult({
						verify: false,
						message: `${supplierContact?.id} exists in HopSkip as a ${role} and cannot be added as an Industry Contact.  Please try another email address.`
					})
				}
				setCanEditContact(false)
				setCanEditPhoneNumber(!phoneNumber)
			})
			.catch(() => {
				setEmailLookupResult({
					verify: true,
					message: `Email verified. Click Next to add this contact to your ${
						isRFP ? 'RFP' : 'directory'
					}.`
				})
				setCanEditContact(false)

				setCanEditPhoneNumber(true)
				setAddingVenues([])
			})
			.finally(() => {
				setIsEmailVerified(true)
				setViewMode(viewModes.verifyEmail)
				setIsEmailBeingVerified(false)
			})
	}

	// const onClickVerifyEmail = () => {
	// 	if (emailLookupResult.verify) {
	// 		setViewMode(viewModes.editContact)
	// 	} else {
	// 		setViewMode(viewModes.lookupEmail)
	// 	}
	// 	setEmailLookupResult({ ...defaultEmailLookupResult })
	// }

	const onAddUpdateClick = async (data: ISupplierContact) => {
		if (canEditContact || !isRFP) {
			try {
				if (addingVenues.length === 0) {
					await saveSupplierContact({
						organizationId: organizationId ?? '',
						contact: data,
						isEdit,
						isRFP,
						defaultInvitationMessage
					})
					await onAddUpdate()
				} else {
					await Promise.all(
						addingVenues.map(async (venue: Venue) => {
							const payload: IAddHotelier = {
								hotelId: venue.id ?? '',
								hotelier: {
									id: data.id ?? '',
									sendAllRFPs: false
								}
							}

							return addHotelier({ data: payload })
						})
					)

					await saveSupplierContact({
						organizationId: organizationId ?? '',
						contact: data,
						isEdit,
						isRFP,
						defaultInvitationMessage
					})

					await onAddUpdate()
				}

				toast.success('Contact updated successfully')
			} catch (error) {
				console.error('Error occurred:', error)
				toast.error('An error occurred while updating contact.')
			} finally {
				onClose()
			}
		}
	}

	const resetState = () => {
		setViewMode(isAddNew ? viewModes.lookupEmail : viewModes.editContact)
		setCanEditContact(!isRFP || isEdit)
		setDefaultInvitationMessage('')
		setEmailLookupResult({ ...defaultEmailLookupResult })
		isEmail('')
	}

	// const onClickChangeEmail = () => {
	// 	resetState()
	// 	setViewMode(viewModes.lookupEmail)
	// }

	const onChange = (
		name: string,
		value: ISourcingProfile[] | boolean | string | null | undefined
	) => {
		if (name === 'id') {
			// if email changes, user needs to look up again
			setCanEditContact(false)
		}
		setSupplierContact(s => ({
			...s,
			[name]: value
		}))
	}

	const onSelectVenue = (venue: Venue) => {
		setAddingVenues(s => [...s, venue])
		setSelectedVenue(undefined)
		setSupplierContact(s => ({
			...s,
			organizationId: venue.id,
			companyName: venue.name
		}))
	}

	const roleOptions = Object.keys(supplierContactRoles)
		.filter(r => {
			if (isRFP) {
				return r !== supplierContactRoles.hot.key
			}
			return true
		})
		.map(item => ({ value: item, text: supplierContactRoles[item].label }))

	return (
		<div className='mt-4' id='modal-dialog'>
			<HSDrawer
				open={isVisible}
				// buttons={buttons}
				onClose={onClose}
				position='right'
				style={{ width: '600px' }}
			>
				<Drawer.Header
					title={`${isEdit ? 'Edit ' : 'Add New '} Contact`}
					titleIcon={() => null}
				/>
				<Drawer.Items
					className='overflow-auto'
					style={{ height: 'calc(100vh - 11rem)' }}
				>
					<div className='flex flex-col gap-2'>
						<div className='flex flex-col gap-5'>
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-semibold text-gray-900'>
									Enter your contacts email address
								</div>
								<div className='flex items-center gap-2'>
									<HSTextField
										value={supplierContact?.id ?? ''}
										onChange={event => {
											if (event.target.value === '') {
												setIsEmailVerified(false)
											}
											setSupplierContact(previous => ({
												...previous,
												id: event.target.value.toLowerCase()
											}))
										}}
										color={
											((supplierContact?.id ?? '') !== '' &&
												isEmail(supplierContact?.id ?? '')) ||
											isEmailVerified
												? isEmailVerified
													? 'success'
													: 'light'
												: (supplierContact?.id ?? '') === ''
													? 'light'
													: 'failure'
										}
										readOnly={isEmailVerified}
										showClearButton={isNameEditable}
										key='email'
										placeholder='Enter email address'
									/>
									<HSButton
										outline
										onClick={() => onClickLookupEmail()}
										disabled={
											!isEmail(supplierContact?.id ?? '') || isEmailVerified
										}
										color={isEmailVerified ? 'gray' : 'light'}
									>
										<div>
											{isEmailBeingVerified ? <Loader size='sm' /> : 'Verify'}
										</div>
									</HSButton>
								</div>
								{isEmailVerified && supplierContact?.id !== '' ? (
									<div className='text-xs font-normal text-green-600'>
										Email Confirmed!
									</div>
								) : null}
								<div className='my-4 border-b' />
								<div className='flex gap-4'>
									<div className='flex flex-1 flex-col gap-2'>
										<div className='text-sm font-semibold text-gray-900'>
											Role
										</div>
										<div className='dropdown-select'>
											<HSDropdownButton
												items={roleOptions.map((item, index) => ({
													id: index.toString(),
													item: item.text,
													clickFunction: () => {
														setSupplierContact(s => ({
															...s,
															role: item.value,
															organizationId: null,
															companyName: null
														}))
													}
												}))}
												label={
													roleOptions.find(
														item => item.value === supplierContact?.role
													)?.text ?? 'Select Role'
												}
												color='light'
												className='w-full'
												value={supplierContact?.role ?? ''}
												size='sm'
												disabled={!isEmailVerified}
											/>
										</div>
									</div>
									<div className='flex flex-1 flex-col gap-2'>
										<div className='text-sm font-semibold text-gray-900'>
											Company
										</div>
										<div className='dropdown-select'>
											<HSDropdownButton
												items={[
													{
														id: '1',
														item: 'Select Company',
														clickFunction: () => {
															setSupplierContact(s => ({
																...s,
																companyName: 'Select Company'
															}))
														}
													}
												]}
												label='Select Company'
												color='light'
												className='w-full'
												value={supplierContact?.companyName ?? ''}
												size='sm'
												disabled={!isEmailVerified}
											/>
										</div>
									</div>
								</div>
								<div className='mt-4' />
								<div className='flex items-center gap-2'>
									<HSTextField
										label='First Name'
										value={supplierContact?.firstName ?? ''}
										onChange={event =>
											onChange('firstName', event.target.value)
										}
										sizing='md'
										disabled={!isEmailVerified}
										placeholder='Enter First Name'
									/>
									<HSTextField
										label='Last Name'
										onChange={event => onChange('lastName', event.target.value)}
										value={supplierContact?.lastName ?? ''}
										sizing='md'
										disabled={!isEmailVerified}
										placeholder='Enter Last Name'
									/>
								</div>

								<div className='flex gap-4'>
									<div className='flex flex-1 flex-col gap-2'>
										<div className='flex gap-1 text-sm font-semibold text-gray-900'>
											Title{' '}
											<span className='font-normal text-gray-600'>
												(optional)
											</span>
										</div>
										<HSTextField
											value={supplierContact?.title ?? ''}
											onChange={event => onChange('title', event.target.value)}
											color='light'
											sizing='md'
											placeholder='Enter Title'
											disabled={!isEmailVerified}
										/>
									</div>
									<div className='flex flex-1 flex-col gap-2'>
										<div className='text-sm font-semibold text-gray-900'>
											Phone Number
										</div>
										<HSPhoneNumber
											value={supplierContact?.phoneNumber ?? undefined}
											disabled={
												!(canEditContact || canEditPhoneNumber) ||
												!isEmailVerified
											}
											onChange={(phone, meta) =>
												onChange('phoneNumber', meta.inputValue)
											}
											placeholder='Enter Phone Number'
										/>
									</div>
								</div>
								{supplierContact?.role &&
								supplierContact.role !== supplierContactRoles.hot.key ? (
									<div className='flex w-1/2 flex-col gap-2'>
										<div className='text-sm font-semibold text-gray-900'>
											Company
										</div>
										<HSTextField
											value={supplierContact.companyName || ''}
											onChange={event =>
												onChange('companyName', event.target.value)
											}
											color='light'
											sizing='md'
											placeholder='Enter Company Name'
											disabled={!isEmailVerified}
										/>
									</div>
								) : null}

								<div className='mt-8 flex flex-col gap-2'>
									<div className='text-sm font-semibold text-gray-900'>
										Contact RFP Engagement
									</div>

									<div className='flex flex-col gap-3'>
										{/* Always include in all new RFPs */}
										<div className='flex items-center justify-between'>
											<div className='flex items-center gap-2'>
												<HSToggleSwitch
													checked={supplierContact?.includeByDefault ?? false}
													onChange={checked =>
														setSupplierContact(value => ({
															...value,
															includeByDefault: checked
														}))
													}
													sizing='sm'
												/>
												<div className='text-sm font-normal text-gray-700'>
													Always include in all new RFPs
												</div>
											</div>
										</div>

										{/* Proposal Access */}
										<div className='flex items-center justify-between'>
											<div className='flex items-center gap-2'>
												<HSToggleSwitch
													checked={supplierContact?.allowMessaging ?? false}
													onChange={checked =>
														setSupplierContact(value => ({
															...value,
															allowMessaging: checked
														}))
													}
													sizing='sm'
												/>
												<div className='flex items-center gap-2'>
													<span className='text-sm font-normal text-gray-700'>
														Proposal Access
													</span>
													<HSTooltip content='If inactive you have to copy this contact on RFPs and proposals'>
														<FontAwesomeIcon
															icon={faInfoCircle}
															className='text-gray-400'
														/>
													</HSTooltip>
												</div>
											</div>
										</div>

										{/* Suggest hotels */}
										<div className='flex items-center justify-between'>
											<div className='flex items-center gap-2'>
												<HSToggleSwitch
													checked={supplierContact?.requestSuggestions ?? false}
													onChange={checked =>
														setSupplierContact(value => ({
															...value,
															requestSuggestions: checked
														}))
													}
													sizing='sm'
												/>
												<div className='flex items-center gap-2'>
													<span className='text-sm font-normal text-gray-700'>
														Suggest hotels to receive this RFP?
													</span>
													<HSTooltip content='If inactive you have to reach out to this contact if you need anything'>
														<FontAwesomeIcon
															icon={faInfoCircle}
															className='text-gray-400'
														/>
													</HSTooltip>
												</div>
											</div>
										</div>
									</div>
								</div>

								<div className='my-4' />

								{/* Notes section */}
								<div className='flex flex-col gap-2'>
									<HSTextArea
										label='Personalized Message to this contact when receiving an RFP'
										rows={5}
										placeholder='Enter Message'
										color='light'
										value={supplierContact?.plannerNotes ?? ''}
										onChange={event =>
											onChange('plannerNotes', event.target.value)
										}
										disabled={!isEmailVerified}
									/>
									<div className='text-xs font-normal text-gray-600'>
										Changes to this message only apply to this RFP.
									</div>
								</div>
							</div>
							{supplierContact?.role === supplierContactRoles.hot.key ? (
								<div className='flex flex-col gap-2'>
									<div className='text-sm font-semibold text-gray-900'>
										Hotel(s)
									</div>
									<div className='card'>
										{associatedVenues && associatedVenues.length > 0
											? associatedVenues.map(hotelData => (
													<div
														key={hotelData.id}
														className='flex flex-col gap-2 border-b p-4'
													>
														<div className='flex items-center justify-between gap-3'>
															<div>
																{startCase(hotelData.name?.toLowerCase() ?? '')}
															</div>
															{removeTemplate(hotelData, temporaryRemoveHotel)}
														</div>
													</div>
												))
											: null}
										<div
											className={`w-full ${Number(associatedVenues?.length) > 0 ? 'rounded-t-none' : ''}`}
										>
											{showHotelSelector ? (
												<div className='flex items-center gap-2 border-b px-4 py-2'>
													<HotelSelectorTrimmed
														ignoreVenueIds={
															associatedVenues?.map(item => item.id ?? '') ?? []
														}
														className='w-full'
														selectedVenue={selectedVenue}
														venueSelected={venue => {
															setSelectedVenue(venue as Venue)
															onSelectVenue(venue as Venue)
															setShowHotelSelector(false)
															const updatedVenues = [
																...(associatedVenues ?? []),
																{
																	id: venue.id,
																	name: venue.name,

																	partitionKey: venue.id
																}
															]
															setAssociatedVenues(updatedVenues)
														}}
														asPopover
														popoverPosition={
															Number(associatedVenues?.length) > 5
																? 'bottom'
																: 'top'
														}
													/>
												</div>
											) : null}
											<div className='flex'>
												<HSButton
													color='text'
													className='px-4 py-2'
													onClick={() => setShowHotelSelector(true)}
												>
													<span className='flex w-full items-center gap-2'>
														<FontAwesomeIcon icon={faPlus} />
														<div>Add New</div>
													</span>
												</HSButton>
											</div>
										</div>
									</div>
								</div>
							) : null}

							{supplierContact?.role &&
							supplierContact.role !== supplierContactRoles.hot.key ? (
								<>
									<div className='border-b' />
									<div className='flex flex-col gap-2'>
										<div>
											<div className='text-sm font-medium text-gray-900'>
												Default Settings
											</div>
											<div className='text-xs font-normal text-gray-500'>
												You can change this setting when you add this contact to
												a specific RFP
											</div>
										</div>
										<div className='flex items-center gap-2'>
											<HSToggleSwitch
												checked={supplierContact.allowMessaging ?? false}
												onChange={checked =>
													setSupplierContact(value => ({
														...value,
														allowMessaging: checked
													}))
												}
												sizing='sm'
											/>
											<div className='flex items-center gap-2'>
												<div className='text-sm font-normal text-gray-700'>
													Initiate chat messages
												</div>
												<HSTooltip content='If inactive you have to copy this contact on RFPs and proposals'>
													<FontAwesomeIcon icon={faInfoCircle} />
												</HSTooltip>
											</div>
										</div>
										<div className='flex items-center gap-2'>
											<HSToggleSwitch
												checked={supplierContact.requestSuggestions ?? false}
												onChange={checked =>
													setSupplierContact(value => ({
														...value,
														requestSuggestions: checked
													}))
												}
												sizing='sm'
											/>
											<div className='flex items-center gap-2'>
												<div className='text-sm font-normal text-gray-700'>
													Suggest hotels for RFPs
												</div>
												<HSTooltip content='If inactive you have to reach out to this contact if you need anything'>
													<FontAwesomeIcon icon={faInfoCircle} />
												</HSTooltip>
											</div>
										</div>
										<div className='border-b' />
										<div className='flex flex-col gap-2'>
											<div className='text-sm font-medium text-gray-900'>
												Personalized Message to this contact when receiving an
												RFP
											</div>
											<div className='text-xs font-normal text-gray-500'>
												HopSkip will include simple directions to your contacts
												who are included on this RFP, but personalized messages
												are always helpful, especially when it&apos;s their
												first time using the HopSkip platform. You can edit the
												sample message below or use it as-is. Your first name
												will always be included automatically so it doesn&apos;t
												need to be included in the message.
											</div>
											<HSTextArea
												placeholder='Enter Message'
												rows={5}
												color='light'
												value={
													supplierContact.invitationMessage ||
													defaultInvitationMessage
												}
												onChange={event =>
													onChange('invitationMessage', event.target.value)
												}
											/>
										</div>
									</div>
								</>
							) : null}
						</div>
					</div>
				</Drawer.Items>
				<Drawer.Items>
					<div className='mb-4 flex items-center gap-2'>
						<HSToggleSwitch
							checked={supplierContact?.saveToIndustryContacts ?? false}
							onChange={checked =>
								setSupplierContact(value => ({
									...value,
									saveToIndustryContacts: checked
								}))
							}
							sizing='sm'
						/>
						<div className='text-sm font-normal text-gray-700'>
							Save to my industry contacts
						</div>
					</div>
					<div className='flex items-center gap-2'>
						<HSButton color='light' onClick={onClose} className='grow'>
							Cancel
						</HSButton>
						<HSButton
							onClick={async () => {
								if (supplierContact) await onAddUpdateClick(supplierContact)
								resetState()
							}}
							className='grow'
							disabled={
								!isFormValid ||
								!isEmailVerified ||
								(supplierContact?.role === supplierContactRoles.hot.key &&
									(associatedVenues?.length ?? 0) === 0)
							}
						>
							{isEdit ? 'Save Contact' : 'Add Contact'}
						</HSButton>
					</div>
				</Drawer.Items>
			</HSDrawer>
		</div>
	)
}

export default RfpContactModal
