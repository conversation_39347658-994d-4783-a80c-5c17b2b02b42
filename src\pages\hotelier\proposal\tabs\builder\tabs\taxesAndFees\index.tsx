/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable react/no-array-index-key */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import TaxesAndFee from './taxesAndFee'
import useQuoteRequestStore from 'lib/store/quoteRequestStore'
import HSToggleSwitch from 'components/toggleSwitch'
import { decimalMask, formatCurrency, UnitedStatesDollar } from 'lib/helpers'
import HSTextField from 'components/textField'
import FoodAndBeverageGrid from './taxesAndFee/fbEditableGrid'
import { useEffect, useState } from 'react'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import { ProposalRequestStatusMap } from 'lib/helpers/statusMaps'

interface TaxesAndFeesProperties {
	onChange: (event: {
		target: {
			name: string
			value: string | number | boolean | null
			type: string
		}
	}) => void
	isSummary?: boolean
}

const TaxesAndFees = (properties: TaxesAndFeesProperties) => {
	const { onChange, isSummary } = properties
	const { quoteRequest, setProperty } = useQuoteRequestStore()
	const { currencies } = useCurrencyContext()
	const [currency, setCurrency] = useState(UnitedStatesDollar)
	const proposalRequest = isSummary
		? quoteRequest.summaryProposalRequest
		: quoteRequest.proposalRequest

	useEffect(() => {
		if (proposalRequest?.currencyCode) {
			setCurrency(currencies[proposalRequest.currencyCode])
		}
	}, [currencies, proposalRequest?.currencyCode])

	useEffect(() => {
		if (
			(proposalRequest?.currentBid?.proposalDates?.filter(
				date => date.declineToBid === false
			)?.length ?? 0) <= 1 &&
			!isSummary &&
			!proposalRequest?.currentBid?.isMeetingRateDependentOnDate
		) {
			onChange({
				target: {
					name: 'isMeetingRateDependentOnDate',
					value: false,
					type: 'boolean'
				}
			})
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	const onChangeMeetingRate = async (
		name: string,
		value: string | number | boolean,
		type: string
	) => {
		const updatedCurrentBid = {
			...proposalRequest?.currentBid,
			[name]: type === 'number' ? Number(value) : value,
			meetingRates:
				proposalRequest?.currentBid?.meetingRates?.map(mr => ({
					...mr,
					[name]: type === 'number' ? Number(value) : value
				})) ?? []
		}
		await setProperty('currentBid', updatedCurrentBid)
	}

	return (
		<div>
			<div className='flex gap-12'>
				<div className='w-1/4'>
					<TaxesAndFee onChange={onChange} isSummary={isSummary} />
				</div>
				<div className='w-3/4'>
					<div className='flex flex-col gap-4'>
						<div className='flex flex-col gap-2'>
							<div className='text-lg font-semibold text-gray-900'>
								Event Space
							</div>
							<div className='card flex flex-col'>
								<div className='rounded-t-md border-b border-gray-200 bg-gray-50 px-4 py-2 text-base font-medium text-gray-700'>
									{isSummary ? 'F&B Rates' : 'Taxes'}
								</div>
								<div className='flex gap-1'>
									{isSummary ? (
										<div className='flex basis-4/5 items-center gap-3 p-4'>
											<div className='flex flex-col gap-2'>
												<div className='text-sm font-normal text-gray-700'>
													F&B Min. Rate
												</div>
												<div className='flex'>
													<div
														className='flex items-center justify-center rounded-l-lg rounded-r-none border border-gray-300 bg-gray-50 px-3'
														color='light'
													>
														{currency.symbol}
													</div>
													<HSTextField
														type='text'
														value={proposalRequest?.currentBid?.fbMinimum ?? ''}
														placeholder='Min.Rate'
														groupPlacement='right'
														color='light'
														onChange={async event =>
															onChangeMeetingRate(
																'fbMinimum',
																event.target.value,
																'string'
															)
														}
														disabled={
															quoteRequest.eventPlan?.meetingSpaceRequired ===
																false ||
															proposalRequest?.status ===
																ProposalRequestStatusMap.ClosedWon?.key ||
															proposalRequest?.status ===
																ProposalRequestStatusMap.Active?.key
														}
														mask={decimalMask as (RegExp | string)[]}
													/>
												</div>
											</div>
										</div>
									) : (
										<div className='flex basis-4/5 items-center gap-3 p-4'>
											<div className='basis-1/4'>
												<HSTextField
													type='text'
													label='Service Charge'
													value={
														proposalRequest?.currentBid?.serviceChargeRate ?? ''
													}
													disabled={
														quoteRequest.eventPlan?.meetingSpaceRequired ===
															false ||
														proposalRequest?.status ===
															ProposalRequestStatusMap.Active?.key
													}
													placeholder='Charge'
													groupItem={<div className='font-semibold'>%</div>}
													groupPlacement='left'
													color='light'
													onChange={async event => {
														await onChangeMeetingRate(
															'serviceChargeRate',
															event.target.value,
															'string'
														)
													}}
													mask={decimalMask as (RegExp | string)[]}
													tooltipContent='Service charge is a fee that is added to the bill in a hotel or restaurant to cover the cost of the service provided.'
												/>
											</div>
											<div className='basis-1/4'>
												<HSTextField
													type='text'
													label='Service Tax'
													value={
														proposalRequest?.currentBid?.serviceChargeTaxRate ??
														''
													}
													disabled={
														quoteRequest.eventPlan?.meetingSpaceRequired ===
															false ||
														proposalRequest?.status ===
															ProposalRequestStatusMap.Active?.key
													}
													onChange={async event => {
														await onChangeMeetingRate(
															'serviceChargeTaxRate',
															event.target.value,
															'string'
														)
													}}
													placeholder='Tax'
													groupItem={<div className='font-semibold'>%</div>}
													groupPlacement='left'
													color='light'
													mask={decimalMask as (RegExp | string)[]}
												/>
											</div>
											<div className='basis-1/4'>
												<HSTextField
													type='text'
													label='F&B Tax'
													value={
														proposalRequest?.currentBid
															?.foodAndBeverageTaxRate ?? ''
													}
													disabled={
														quoteRequest.eventPlan?.meetingSpaceRequired ===
															false ||
														proposalRequest?.status ===
															ProposalRequestStatusMap.Active?.key
													}
													placeholder='Tax'
													groupItem={<div className='font-semibold'>%</div>}
													groupPlacement='left'
													color='light'
													onChange={async event => {
														await onChangeMeetingRate(
															'foodAndBeverageTaxRate',
															event.target.value,
															'string'
														)
													}}
													mask={decimalMask as (RegExp | string)[]}
												/>
											</div>
											<div className='basis-1/4'>
												<HSTextField
													type='text'
													label='Room Rental Tax'
													value={
														proposalRequest?.currentBid?.roomRentalTaxRate ?? ''
													}
													disabled={
														quoteRequest.eventPlan?.meetingSpaceRequired ===
															false ||
														proposalRequest?.status ===
															ProposalRequestStatusMap.Active?.key
													}
													placeholder='Tax'
													groupItem={<div className='font-semibold'>%</div>}
													groupPlacement='left'
													color='light'
													onChange={async event => {
														await onChangeMeetingRate(
															'roomRentalTaxRate',
															event.target.value,
															'string'
														)
													}}
													mask={decimalMask as (RegExp | string)[]}
												/>
											</div>
										</div>
									)}
									<div className='flex basis-1/5 flex-col items-center justify-center border-l border-gray-200 bg-primary-50'>
										<div className='text-sm font-medium text-gray-900'>
											F&B Estimate
										</div>
										<div className='text-2xl font-semibold text-green-500'>
											{formatCurrency(
												Math.max(
													proposalRequest?.currentBid?.foodAndBeverageItems?.reduce(
														(a, c) => a + (c.quantity ?? 0) * (c.rate || 0),
														0
													) ?? 0,

													Number(proposalRequest?.currentBid?.fbMinimum ?? 0),
													...(proposalRequest?.currentBid?.meetingRates?.map(
														mr => Number(mr.fbMinimum ?? 0)
													) ?? [])
												),
												currency
											)}
										</div>
									</div>
								</div>
							</div>
						</div>
						{isSummary ? null : (
							<div>
								<div className='card flex flex-col'>
									<div className='flex items-center justify-between rounded-t-md border-b border-gray-200 bg-gray-50 px-4 py-2 text-base font-medium text-gray-700'>
										Rates
										<div className='flex items-center gap-2'>
											<HSToggleSwitch
												checked={
													proposalRequest?.currentBid
														?.isMeetingRateDependentOnDate ?? false
												}
												name='isMeetingRateDependentOnDate'
												disabled={
													quoteRequest.eventPlan?.meetingSpaceRequired ===
														false ||
													proposalRequest?.status === 'Active' ||
													(proposalRequest?.currentBid?.proposalDates?.filter(
														date => date.declineToBid === false
													)?.length ?? 0) <= 1
												}
												onChange={checked => {
													onChange({
														target: {
															name: 'isMeetingRateDependentOnDate',
															value: checked,
															type: 'boolean'
														}
													})
												}}
											/>
											<div className='text-sm font-normal text-gray-700'>
												Offer different rates for each date range?
											</div>
										</div>
									</div>

									<div className='flex gap-1'>
										<FoodAndBeverageGrid
											onChange={onChange}
											currency={currency}
										/>
									</div>
								</div>
							</div>
						)}
					</div>
				</div>
			</div>
		</div>
	)
}

export default TaxesAndFees
