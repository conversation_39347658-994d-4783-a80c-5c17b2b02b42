import type { Venue } from 'models/venue'

export type ProfileInfoAction =
	| {
			type: 'mergeProperties'
			value: Record<string, unknown>
			clientOnly?: boolean
	  }
	| {
			type: 'removeFromArray'
			name: string
			index: number
			clientOnly?: boolean
	  }
	| {
			type: 'replaceInArray'
			name: string
			index: number
			value: unknown
			clientOnly?: boolean
	  }
	| {
			type: 'setProperty'
			name: string
			value: {
				type?: string
				value: unknown
			}
			clientOnly?: boolean
	  }
	| { type: 'addToArray'; name: string; value: unknown; clientOnly?: boolean }
	| { type: 'setObject'; value: Venue; clientOnly?: boolean }

export const defaultMergeOptions = {
	meetingRooms: { label: 'Meeting Rooms', value: 'KeepTargetOnly' },
	photos: { label: 'Photos', value: 'KeepTargetOnly' },
	logo: { label: 'Logo', value: 'KeepTargetOnly' },
	hoteliers: { label: 'Hoteliers', value: 'KeepTargetOnly' },
	subscriptions: { label: 'Subscriptions', value: 'KeepTargetOnly' },
	currency: { label: 'Currency', value: 'KeepTargetOnly' },
	defaults: { label: 'Proposal Defaults', value: 'KeepTargetOnly' },
	declineReasons: { label: 'Custom Decline Reasons', value: 'KeepTargetOnly' },
	attachments: { label: 'Documents/Attachments', value: 'KeepTargetOnly' },
	proposalRequests: { label: 'Proposals', value: 'KeepBoth' }
	// engagementMetrics: { label: 'Engagement Metrics', value: 'KeepTargetOnly' },
	// reviews: { label: 'Planner Reviews', value: 'KeepTargetOnly' }
}
