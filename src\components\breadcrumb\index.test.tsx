import HSBreadCrumb from '.'
import { render, screen } from '@testing-library/react'

// Mock getBreadCrumbName function
// vi.mock('./helper', () => ({
// 	getBreadCrumbName: (path: string) => path
// }))

describe('HSBreadCrumb Component', () => {
	beforeEach(() => {
		// Mock globalThis.location before each test
		Object.defineProperty(globalThis, 'location', {
			configurable: true,
			value: {
				pathname: 'admin-tools/general/user-profile'
			}
		})
	})
	it('should render breadcrumb items based on pathname', () => {
		render(<HSBreadCrumb />)

		expect(screen.getByText('Admin Tools')).toBeInTheDocument()
		expect(screen.getByText('General')).toBeInTheDocument()
		expect(screen.getByText('User Profile')).toBeInTheDocument()
	})

	it('should apply the replace prop correctly', () => {
		const replace = {
			'admin-tools': 'Home',
			general: 'Library',
			'user-profile': 'Data'
		}
		render(<HSBreadCrumb replace={replace} />)

		expect(screen.getByText('Home')).toBeInTheDocument()
		expect(screen.getByText('Library')).toBeInTheDocument()
		expect(screen.getByText('Data')).toBeInTheDocument()
	})

	it('should render nothing if there is no breadcrumb path', () => {
		Object.defineProperty(globalThis, 'location', {
			configurable: true,
			value: {
				pathname: '/'
			}
		})

		render(<HSBreadCrumb />)
		const divElement = screen.queryByTestId('flowbite-breadcrumb-item')

		expect(divElement).not.toBeInTheDocument()
	})

	it.skip('should render correct hrefs for each breadcrumb item', async () => {
		render(<HSBreadCrumb />)
		const adminToolsLink = screen.getByText('Admin Tools').closest('a')
		const generalLink = screen.getByText('General').closest('a')
		const userProfileSpan = screen.getByText('User Profile').closest('span')

		expect(adminToolsLink).toHaveAttribute('href', '/admin-tools')
		expect(generalLink).toHaveAttribute('href', '/admin-tools/general')
		expect(userProfileSpan).not.toHaveAttribute('href')
	})
})
