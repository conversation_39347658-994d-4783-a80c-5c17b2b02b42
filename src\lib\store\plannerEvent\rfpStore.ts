/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable import/prefer-default-export */
import { updateEvent } from 'lib/services/event.service'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { toast } from 'react-toastify'
import { create } from 'zustand'
import { debounce } from 'es-toolkit'

const debouncedPutIntent = debounce(
	(
		eventPlanId: string,
		type:
			| 'removeFromArray'
			| 'addToArray'
			| 'setProperty'
			| 'replaceInArray'
			| 'clearArray'
			| 'mergeProperties'
			| 'roundTrip',
		value:
			| { name: string; index?: number; value?: unknown }
			| Partial<EventPlan>,
		setSaving: (value: boolean) => void,
		mergeProperties: (
			value: Partial<EventPlan>,
			clientOnly: boolean,
			roundTrip?: { meetingSpaceRequests: boolean; roomBlockRequests: boolean }
		) => void,
		roundTrip?: { meetingSpaceRequests: boolean; roomBlockRequests: boolean }
	) => {
		setSaving(true)
		updateEvent(eventPlanId, {
			action: { type },
			value
		})
			.then(response => {
				const o = {
					roomBlocksRequired: response.roomBlocksRequired,
					totalRoomTypes: response.totalRoomTypes,
					peakRooms: response.peakRooms,
					totalRoomsRequested: response.totalRoomsRequested,
					totalRoomsBudget: response.totalRoomsBudget,
					roomNightBudget: response.roomNightBudget,
					totalDays: response.totalDays,
					startDayOfWeek: response.startDayOfWeek,
					alternateDates: [...(response.alternateDates ?? [])],
					peakMeetingSpaceRequired: response.peakMeetingSpaceRequired,
					peakMeetingRoomsRequired: response.peakMeetingRoomsRequired,
					lastModified: response.lastModified,
					lastModifiedBy: response.lastModifiedBy
				}
				if (roundTrip?.meetingSpaceRequests) {
					o.meetingSpaceRequests = response.meetingSpaceRequests
				}
				if (roundTrip?.foodAndBeverageRequests) {
					o.foodAndBeverageRequests = response.foodAndBeverageRequests
				}
				if (roundTrip?.roomBlockRequests) {
					o.roomBlockRequests = response.roomBlockRequests
				}
				mergeProperties(o, true)
			})
			.catch((error: unknown) => {
				if (error.response && error.response.status === 401) {
					toast.error(
						'You do not have permission to edit this RFP. Use the 3-dot menu to request edit permission.'
					)
				} else {
					console.error('Error putting intent:', error)
					toast.error('Error updating Event Plan')
				}
			})
			.finally(() => {
				setSaving(false)
			})
	},
	450
)

interface EventInfoState {
	eventInfo: EventPlan | null
	saving: boolean
	errors: string[]
	disableFields: boolean
	currentUserIsEditor: boolean
}
interface EventInfoAction {
	setSaving: (saving: boolean) => void
	setErrors: (errors: string[]) => void
	setObject: (eventPlan: EventPlan, clientOnly?: boolean) => void
	setProperty: (
		name: string,
		value: unknown,
		type?: string,
		roundTrip?: { meetingSpaceRequests: boolean; roomBlockRequests: boolean }
	) => void
	mergeProperties: (
		value: Partial<EventPlan>,
		clientOnly?: boolean,
		roundTrip?: { meetingSpaceRequests: boolean; roomBlockRequests: boolean }
	) => void
	addToArray: (name: keyof EventPlan, value: object) => void
	removeFromArray: (name: keyof EventPlan, index: number) => void
	replaceInArray: (name: keyof EventPlan, value: object, index: number) => void
	clearArray: (name: keyof EventPlan) => void
	setDisableFields: (disableFields: boolean) => void
	setCurrentUserIsEditor: (currentUserIsEditor: boolean) => void
}

export const eventInfoStore = create<EventInfoState & EventInfoAction>(
	(set, get) => ({
		eventInfo: null,
		saving: false,
		errors: [],
		setSaving: (saving: boolean) => set({ saving }),
		setErrors: (errors: string[]) => set({ errors }),
		disableFields: false,
		currentUserIsEditor: false,

		setObject: (eventPlan: EventPlan, clientOnly = true) =>
			set(state => {
				const newState = { ...state, eventInfo: eventPlan }
				if (clientOnly) {
					return newState
				}
				return { ...newState, eventInfo: eventPlan }
			}),

		setProperty: (
			name: string,
			value: unknown,
			type?: string,
			roundTrip?: { meetingSpaceRequests: boolean; roomBlockRequests: boolean }
		) => {
			const { eventInfo, setSaving, mergeProperties } = get()
			if (eventInfo) {
				const updatedEventInfo = {
					...eventInfo,
					[name]: type === 'number' ? Number(value) : value
				}
				set({
					eventInfo: updatedEventInfo
				})

				debouncedPutIntent(
					eventInfo.id || '',
					'setProperty',
					{ name, value },
					setSaving,
					mergeProperties,
					roundTrip
				)
			}
		},

		mergeProperties: (
			value: Partial<EventPlan>,
			clientOnly = false,
			roundTrip?: { meetingSpaceRequests: boolean; roomBlockRequests: boolean }
		) => {
			const { eventInfo, setSaving, mergeProperties } = get()

			if (eventInfo) {
				set(state => ({
					...state,
					eventInfo: { ...eventInfo, ...value }
				}))
				if (!clientOnly) {
					debouncedPutIntent(
						eventInfo.id || '',
						'mergeProperties',
						value,
						setSaving,
						mergeProperties,
						roundTrip
					)
				}
			}
		},

		addToArray: (name: keyof EventPlan, value: object) => {
			const { setSaving, eventInfo, mergeProperties } = get()

			if (eventInfo) {
				const updatedEventInfo = {
					...eventInfo,
					[name]: Array.isArray(eventInfo[name])
						? [...eventInfo[name], value]
						: [value]
				}
				set({ eventInfo: updatedEventInfo })
				debouncedPutIntent(
					eventInfo.id ?? '',
					'addToArray',
					{
						name,
						value
					},
					setSaving,
					mergeProperties
				)
			}
		},

		removeFromArray: (name: string, index: number) => {
			const { setSaving, eventInfo, mergeProperties } = get()

			if (eventInfo) {
				const updatedEventInfo = {
					...eventInfo,
					[name]: [
						...eventInfo[name].slice(0, index),
						...eventInfo[name].slice(index + 1)
					]
				}
				set({ eventInfo: updatedEventInfo })

				debouncedPutIntent(
					eventInfo.id || '',
					'removeFromArray',
					{ name, index },
					setSaving,
					mergeProperties
				)
			}
		},

		replaceInArray: (name: string, value: object, index: number) => {
			const { setSaving, eventInfo, mergeProperties } = get()

			if (eventInfo) {
				const updatedEventInfo = {
					...eventInfo,
					[name]: [
						...eventInfo[name].slice(0, index),
						value,
						...eventInfo[name].slice(index + 1)
					]
				}
				set({ eventInfo: updatedEventInfo })

				debouncedPutIntent(
					eventInfo.id || '',
					'replaceInArray',
					{ name, index, value },
					setSaving,
					mergeProperties
				)
			}
		},
		clearArray: (name: string) => {
			const { setSaving, eventInfo, mergeProperties } = get()
			if (eventInfo) {
				const updatedEventInfo = {
					...eventInfo,
					[name]: []
				}
				set({ eventInfo: updatedEventInfo })

				debouncedPutIntent(
					eventInfo.id || '',
					'clearArray',
					{ name },
					setSaving,
					mergeProperties
				)
			}
		},
		setDisableFields: (disableFields: boolean) => {
			set({ disableFields })
		},
		setCurrentUserIsEditor: (currentUserIsEditor: boolean) => {
			set({ currentUserIsEditor })
		}
	})
)
