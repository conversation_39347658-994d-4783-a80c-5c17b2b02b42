/* eslint-disable unicorn/no-nested-ternary */
import type { ITab } from 'components/tab'
import { customTabComponentTheme } from 'components/tab'
import type { TabsRef } from 'flowbite-react'
import { Tabs } from 'flowbite-react'
import { useCallback, useEffect, useMemo, useRef } from 'react'
import EventDates from './tabs/eventDates'
import EventProgramInfo from './tabs/programInfo'
import EventQuestions from './tabs/eventQuestions'
import EventAttachments from './tabs/attachments'
import EventConcessions from './tabs/eventConcessions'
import General from './tabs/general'
import ContractTerm from './tabs/contractTerm'
import ContractClause from './tabs/contractClause'
import { useParams, useNavigate } from 'react-router-dom'
import EventSpace from './tabs/eventSpace'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import useAttachmentsStore from 'lib/store/attachmentsStore'
import useEventPlanValidationErrors from 'lib/store/plannerEvent/validation'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSTooltip from 'components/tooltip'
import questionStore from 'lib/store/plannerEvent/questionStore'
import concessionRequestStore from 'lib/store/plannerEvent/concessionRequestStore'
import contractClauseStore from 'lib/store/plannerEvent/contractClauseStore'
import { faExclamation, faCheck } from '@fortawesome/pro-regular-svg-icons'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'

const tabData = (error?: {
	generalErrors: string[] | null
	dateErrors: string[] | null
	roomblocks: string[] | null
	mespace: string[] | null
	contractTerms: string[] | null
}): (ITab & { error?: string[] | null })[] => [
	{
		key: 0,
		title: 'General',
		children: <General />,
		path: 'general',
		error: error?.generalErrors ?? []
	},
	{
		key: 1,
		title: 'Dates',
		children: <EventDates />,
		path: 'dates',
		error: error?.dateErrors ?? []
	},
	{
		key: 2,
		title: 'Guest Rooms',
		children: <div>Dates</div>,
		path: 'room-blocks',
		error: error?.roomblocks ?? []
	},
	{
		key: 3,
		title: 'Event Space',
		children: <EventSpace />,
		path: 'event-space',
		error: error?.mespace ?? []
	},
	{
		key: 4,
		title: 'Contract Terms',
		children: <ContractTerm />,
		path: 'contract-terms',
		error: error?.contractTerms ?? []
	},
	{
		key: 5,
		title: 'Contract Clause',
		children: <ContractClause />,
		path: 'contract-clause'
	},
	{
		key: 6,
		title: 'Questions',
		children: <EventQuestions />,
		path: 'questions'
	},
	{
		key: 7,
		title: 'Concessions',
		children: <EventConcessions />,
		path: 'concessions'
	},
	{
		key: 8,
		title: 'Program Info',
		children: <EventProgramInfo />,
		path: 'program-info'
	},
	{
		key: 9,
		title: 'Attachments',
		children: <EventAttachments />,
		path: 'attachments'
	}
]

const RequestDetailTab = () => {
	const tabReference = useRef<TabsRef | null>(null)
	const { view, eventId } = useParams()
	const navigate = useNavigate()
	const { eventInfo } = eventInfoStore()
	const { attachments } = useAttachmentsStore()
	const { questions } = questionStore()
	const { concessionRequests } = concessionRequestStore()
	const { contractClauses } = contractClauseStore()
	const { userProfile } = useUserProfileContext()

	const { generalErrors, dateErrors, roomblocks, mespace, contractTerms } =
		useEventPlanValidationErrors({
			eventPlan: eventInfo,
			attachments,
			userProfile
		})

	const tabs = useMemo(
		() =>
			tabData({
				generalErrors,
				dateErrors,
				roomblocks,
				mespace,
				contractTerms
			}),
		[contractTerms, dateErrors, generalErrors, mespace, roomblocks]
	)

	const getActiveTabIndex = useCallback(() => {
		const activeTab = tabs.find(tab => tab.path === view)
		return activeTab ? activeTab.key : 0
	}, [tabs, view])

	useEffect(() => {
		if (tabReference.current) {
			const activeIndex = getActiveTabIndex()
			tabReference.current.setActiveTab(activeIndex)
		}
	}, [getActiveTabIndex, view])

	const handleTabChange = (index: number) => {
		if (tabReference.current) {
			const tabPath = tabs[index].path
			navigate(`/planner/event/${eventId}/request-detail/info/${tabPath}`)
		}
	}

	const renderTitle = (tab: ITab & { error?: string[] | null }) => {
		const { title, error } = tab
		return (
			<div className='flex items-center gap-2'>
				<div>{title}</div>
				<div>
					{title === 'Questions' ||
					title === 'Concessions' ||
					title === 'Contract Clause' ? (
						<div className='flex h-5 w-5 items-center justify-center rounded-full bg-green-100'>
							<div className='text-xxs font-normal text-green-800'>
								{title === 'Questions'
									? questions.length
									: title === 'Concessions'
										? concessionRequests.length
										: contractClauses.length}
							</div>
						</div>
					) : error && error.length > 0 ? (
						<HSTooltip
							content={error.map(item => (
								<div className='text-left' key={item}>
									{item}
								</div>
							))}
						>
							<div className='flex h-5 w-5 items-center justify-center rounded-full bg-red-100'>
								<FontAwesomeIcon
									className='h-2.5 w-2.5 text-red-800'
									icon={faExclamation}
								/>
							</div>
						</HSTooltip>
					) : (
						<HSTooltip content='All Good'>
							<div className='flex h-5 w-5 items-center justify-center rounded-full bg-green-100'>
								<FontAwesomeIcon
									icon={faCheck}
									className='h-2.5 w-2.5 text-green-800'
								/>
							</div>
						</HSTooltip>
					)}
				</div>
			</div>
		)
	}

	return (
		<div className='flex gap-4'>
			<Tabs
				ref={tabReference}
				className='w-full'
				variant='underline'
				theme={customTabComponentTheme('horizontal', true, true)}
				onActiveTabChange={handleTabChange}
			>
				{tabs.map(tab => {
					const TabComponent = tab.children
					return (
						<Tabs.Item key={tab.key} title={renderTitle(tab)}>
							{TabComponent}
						</Tabs.Item>
					)
				})}
			</Tabs>
		</div>
	)
}

export default RequestDetailTab
