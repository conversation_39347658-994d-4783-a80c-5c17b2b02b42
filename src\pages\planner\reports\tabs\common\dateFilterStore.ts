import { calculatePeriod, periodTypes } from 'lib/helpers'
import { create } from 'zustand'

interface IDateFilterStore {
	dateFilter: { startDate: string; endDate: string }
	setDateFilter: (dateFilter: { startDate: string; endDate: string }) => void
}

const dateFilterStore = create<IDateFilterStore>(set => ({
	dateFilter: calculatePeriod(periodTypes.quarterToDate.key),
	setDateFilter: dateFilter => set({ dateFilter })
}))

export default dateFilterStore
