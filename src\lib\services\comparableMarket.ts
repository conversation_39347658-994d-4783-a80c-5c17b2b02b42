/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable import/prefer-default-export */
import type { ICurrency } from 'lib/helpers'
import { sleepingRoomTypeOptions } from 'lib/helpers/sleepingRoomType'
import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import { suggestHotel } from './hotels.service'
import { toast } from 'react-toastify'
import { comparableHotelSources } from 'lib/helpers/common'
import { addSummaryProposalRequestToRfp } from './planner.service'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { useGetEventDetail } from './eventPlans.service'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import type { SiteSearch } from 'models/reporting'

export const summarizeAvailability = ({
	summaryProposalRequest,
	proposalCurrency
}: {
	summaryProposalRequest: ProposalRequest
	proposalCurrency: ICurrency | undefined
}) => ({
	roomTypesOffered: summaryProposalRequest.currentBid?.roomRates
		?.filter(rr => (rr.offered ?? 0) > 0)
		.reduce(
			(
				a: {
					roomType: (typeof sleepingRoomTypeOptions)[number]
					rate: number
					rateCount: number
				}[],
				c
			) => {
				const roomType = sleepingRoomTypeOptions.find(
					o => o.value === c.roomType
				)
				// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
				if (roomType?.value !== null) {
					const existingRoomType = a.find(
						rto => rto.roomType.value === roomType?.value
					)
					if (existingRoomType) {
						existingRoomType.rate =
							(existingRoomType.rate * existingRoomType.rateCount +
								(c.rate ?? 0)) /
							(existingRoomType.rateCount + 1)
						existingRoomType.rateCount += 1
					} else if (roomType) {
						a.push({ roomType, rate: c.rate ?? 0, rateCount: 1 })
					}
				}
				return a
			},
			[]
		)
		.sort((a, b) => a.roomType.sort - b.roomType.sort),
	meetingSpaceAvailable:
		summaryProposalRequest.currentBid?.meetingSpaceAvailable,
	proposalCurrency
})

export const convertSummaryProposalRequestToRfp = async (
	summaryProposalRequest: ProposalRequest,
	eventPlan: EventPlan
) => {
	try {
		const payload = [
			{
				status: 'Pending',
				selected: false,
				venueId: summaryProposalRequest.venueId,
				venueName: summaryProposalRequest.venueName ?? '',
				venueLocation: `${summaryProposalRequest.venueCity}, ${summaryProposalRequest.venueState}`,
				geolocation: summaryProposalRequest.geolocation ?? '',
				comparableHotelSource: comparableHotelSources.MarketAvailability,
				destinations: summaryProposalRequest.destinations,
				propertySellers: summaryProposalRequest.propertySellers ?? [],
				currentBid: summaryProposalRequest.currentBid,
				createdBy: eventPlan.planners?.[0]?.id ?? eventPlan.createdBy ?? '',
				createdByOrganizationId: eventPlan.organizationId,
				createdByOrganizationName: eventPlan.organizationName
			}
		]
		await suggestHotel(eventPlan.id ?? '', payload)

		toast.success('Hotel added to RFP. Remember to send your RFP to the hotel.')
	} catch (error) {
		console.error('Error suggesting hotels', error)
		toast.error('Error suggesting hotels')
	}
}

export const useHandleAddToRfp = () => {
	const { mergeProperties, setObject, eventInfo: eventPlan } = eventInfoStore()

	const { refetch: loadEventInfo } = useGetEventDetail(
		eventPlan?.id ?? '',
		!!eventPlan?.id
	)

	const handleAddToRfp = async (summaryProposalRequest: ProposalRequest) => {
		if (eventPlan) {
			convertSummaryProposalRequestToRfp(summaryProposalRequest, eventPlan)
				.then(async () => {
					const r: {
						data?: {
							proposalRequests?: ProposalRequest[] | null
							siteSearch?: SiteSearch | null
							supplierContacts?: ISupplierContact[] | null
						}
					} = { data: undefined }
					await addSummaryProposalRequestToRfp(
						eventPlan.id ?? '',
						summaryProposalRequest.venueId,
						summaryProposalRequest.id ?? ''
					)
						.then(response => {
							const summaryProposalRequests = Array.isArray(response)
								? response
								: []

							mergeProperties({
								summaryProposalRequests: [...summaryProposalRequests]
							})
						})
						.then(() => {
							const proposalRequests = Array.isArray(r.data?.proposalRequests)
								? r.data.proposalRequests
								: []
							const siteSearch = r.data?.siteSearch || {}
							const supplierContacts = Array.isArray(r.data?.supplierContacts)
								? r.data.supplierContacts
								: []

							mergeProperties({
								proposalRequests: [...proposalRequests],
								siteSearch: { ...siteSearch },
								supplierContacts: [...supplierContacts]
							})
						})
						.then(() => {
							loadEventInfo()
								.then(response => {
									if (response.data) {
										setObject(response.data, true)
									}
								})
								.catch(() => {
									console.log('Error loading event info')
								})
						})
				})
				.catch((error: unknown) => {
					console.error('Error suggesting hotels', error)
				})
		}
	}

	return { handleAddToRfp }
}
