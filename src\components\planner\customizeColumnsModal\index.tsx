/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import { Drawer } from 'flowbite-react'
import HSDrawer from 'components/drawer'
import HSButton from 'components/button'
import HSToggleSwitch from 'components/toggleSwitch'
import { columnOptions } from 'components/planner/customizeColumnsModal/columnConfig'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faGripDotsVertical } from '@fortawesome/pro-light-svg-icons'

interface CustomizeColumnsModalProperties {
	show: boolean
	onClose: () => void
	context: 'bidHistory' | 'bookings' | 'openPipeline' | 'costSavings'
	selectedColumns: string[]
	onColumnsChange: (columns: string[]) => void
}

const CustomizeColumnsModal = ({
	show,
	onClose,
	context,
	selectedColumns,
	onColumnsChange
}: CustomizeColumnsModalProperties) => {
	const toggleColumn = (column: string) => {
		if (selectedColumns.includes(column)) {
			onColumnsChange(selectedColumns.filter(col => col !== column))
		} else {
			onColumnsChange([...selectedColumns, column])
		}
	}

	const getColumnLabel = (column: string) => {
		if (column === 'RFP Value') {
			return context === 'bookings' ? 'Contracted Value' : 'RFP Value'
		}
		if (column === 'Assigned') {
			return context === 'costSavings' || context === 'bookings'
				? 'Owner'
				: 'Assigned'
		}
		return column
	}

	const visibleColumns = columnOptions.filter(column => {
		if (context === 'bidHistory') {
			return ![
				'Peak Room Nights',
				'Peak Meeting Space',
				'Contract Signed',
				'Proposed',
				'Negotiated',
				'Concessions',
				'Contracted',
				'Savings',
				'Contracted Hotels'
			].includes(column)
		}
		if (context === 'bookings') {
			return ![
				'Peak Room Nights',
				'Peak Meeting Space',
				'Contract Signed',
				'Proposed',
				'Negotiated',
				'Requested',
				'Concessions',
				'Contracted',
				'Savings'
			].includes(column)
		}
		if (context === 'openPipeline') {
			return ![
				'Peak Room Nights',
				'Peak Meeting Space',
				'Contract Signed',
				'Proposed',
				'Negotiated',
				'Concessions',
				'Contracted',
				'Savings',
				'Contracted Hotels'
			].includes(column)
		}
		if (context === 'costSavings') {
			return ![
				'Average Room Rate',
				'Peak Room Nights',
				'Peak Meeting Space',
				'Requested',
				'Responses due',
				'Response rate',
				'Hotels Requested',
				'Awaiting',
				'Responses',
				'Decision due',
				'RFP Value',
				'Contracted Hotels'
			].includes(column)
		}
		return true
	})

	return (
		<HSDrawer
			position='right'
			onClose={onClose}
			open={show}
			style={{
				width: '500px'
			}}
			noPadding
		>
			<Drawer.Header
				title='Customize Columns'
				titleIcon={() => null}
				className='px-6 py-4'
			/>
			<Drawer.Items
				className='overflow-auto bg-gray-50'
				style={{ height: 'calc(100vh - 10rem)' }}
			>
				<div className='px-6 py-4'>
					<div className='flex flex-col gap-4'>
						<div className='text-sm font-medium text-gray-900'>
							Customize View
						</div>
						{visibleColumns.map(column => (
							<div key={column} className='flex items-center gap-4'>
								<div className='flex items-center'>
									<FontAwesomeIcon icon={faGripDotsVertical} />
								</div>
								<HSToggleSwitch
									checked={selectedColumns.includes(column)}
									onChange={() => toggleColumn(column)}
								/>
								<div className='text-sm font-medium text-gray-900'>
									{getColumnLabel(column)}
								</div>
							</div>
						))}
					</div>
				</div>
			</Drawer.Items>
			<Drawer.Items>
				<div className='flex grow items-center justify-between gap-4 px-6 py-4'>
					<HSButton color='light' onClick={() => onClose()} className='grow'>
						Cancel
					</HSButton>
					<HSButton color='primary' onClick={onClose} className='grow'>
						Apply View
					</HSButton>
				</div>
			</Drawer.Items>
		</HSDrawer>
	)
}

export default CustomizeColumnsModal
