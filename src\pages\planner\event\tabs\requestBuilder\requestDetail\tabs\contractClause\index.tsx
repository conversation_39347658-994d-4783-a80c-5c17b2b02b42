import {
	faInfoCircle,
	faPlus
	// faToolbox
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSTable from 'components/table'
import HSTooltip from 'components/tooltip'
import contractClauseStore from 'lib/store/plannerEvent/contractClauseStore'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { renderActions, renderDescription, renderName } from './templates'
import { useState } from 'react'
import ContractClauseModal from 'components/contractClauseModal'
import type {
	ContractClauseEvent,
	IContractClauses
} from 'models/organizations'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
// import AddContractClauseFromToolboxDrawer from 'components/event/planner/drawers/addContractClauseFromToolboxDrawer'
import {
	addExistingContractClauseToEvent,
	addNewContractClauseToEvent,
	removeContractClauseFromEvent,
	useGetContractClausesForEvent
} from 'lib/services/event.service'
import PageLoader from 'components/pageLoader'

const ContractClause = () => {
	const { eventInfo } = eventInfoStore()
	const {
		contractClauses,
		removeFromArray,
		setObject: setContractClause
	} = contractClauseStore()
	const { refetch: getContractClause } = useGetContractClausesForEvent(
		eventInfo?.id ?? '',
		!!eventInfo?.id
	)

	const { userProfile } = useUserProfileContext()

	const [showAddClauseModal, setShowAddClauseModal] = useState(false)
	// const [showAddClauseFromToolboxDrawer, setShowAddClauseFromToolboxDrawer] =
	// 	useState(false)
	const [isLoading, setIsLoading] = useState(false)

	const [contractClauseToEdit, setContractClauseToEdit] =
		useState<ContractClauseEvent>()

	const handleRemove = (item: ContractClauseEvent) => {
		setIsLoading(true)
		removeContractClauseFromEvent(
			eventInfo?.id ?? '',
			item.contractClause.id ?? ''
		)
			.then(() => {
				removeFromArray(item)
			})
			.catch((error: unknown) => {
				console.error('Error removing contract clause:', error)
			})
			.finally(() => {
				setIsLoading(false)
			})
	}

	const handleEdit = (item: ContractClauseEvent) => {
		setContractClauseToEdit(item)
		setShowAddClauseModal(true)
	}

	const reloadContractClause = () => {
		getContractClause()
			.then(response => {
				setContractClause(response.data ?? [])
			})
			.catch(() => {
				console.log('Error loading contract clauses')
			})
	}

	const onAddUpdate = (contractClause: IContractClauses) => {
		if (contractClause.id) {
			addExistingContractClauseToEvent(
				eventInfo?.id ?? '',
				contractClause.id,
				contractClause
			)
				.then(() => {
					reloadContractClause()
					setShowAddClauseModal(false)
				})
				.catch((error: unknown) => {
					console.error('Error adding contract clause:', error)
				})
				.finally(() => {
					setContractClauseToEdit(undefined)
				})
		} else {
			addNewContractClauseToEvent(eventInfo?.id ?? '', contractClause)
				.then(() => {
					reloadContractClause()
					setShowAddClauseModal(false)
				})
				.catch((error: unknown) => console.log(error))
				.finally(() => {
					setContractClauseToEdit(undefined)
				})
		}
	}

	return (
		<div className='px-6 pb-6 pt-4'>
			{isLoading ? <PageLoader /> : null}
			<div className='flex flex-col gap-2'>
				<div className='flex items-center gap-2'>
					<div className='text-lg font-semibold text-gray-900'>
						Contract Clause
					</div>
					<HSTooltip content='Add specific terms you want included in your hotel agreement - either choose from your saved clauses or create new ones. Hotels will review and respond to these requests in their proposals'>
						<FontAwesomeIcon icon={faInfoCircle} className='text-gray-600' />
					</HSTooltip>
				</div>
				<div className='flex items-center justify-between gap-4'>
					<div className='text-sm font-medium text-gray-900'>
						{contractClauses.length} Contract Clauses added to your RFP
					</div>
					<div className='flex items-center gap-4'>
						{/* <HSButton
							color='text'
							onClick={() => setShowAddClauseFromToolboxDrawer(true)}
						>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faToolbox} />
								Add From Toolbox
							</div>
						</HSButton> */}
						<HSButton onClick={() => setShowAddClauseModal(true)}>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faPlus} />
								Add New Clause
							</div>
						</HSButton>
					</div>
				</div>
				<HSTable
					rows={contractClauses}
					emptyRecordTemplate={
						<div className='flex h-32 items-center justify-center'>
							<div className='text-sm font-normal text-gray-600'>
								No Contract Clause
							</div>
						</div>
					}
					columns={[
						{
							field: 'contractClause.title',
							headerText: 'Contract Clause NAME',
							render: renderName,
							freeze: 'left'
						},
						{
							field: 'description',
							headerText: 'Contract Clause Details',
							render: renderDescription
						},
						{
							field: 'actions',
							headerText: 'Actions',
							headerAlign: 'center',
							render: item => renderActions(item, handleRemove, handleEdit),
							freeze: 'right',
							width: 100
						}
					]}
					allowPaging
				/>
			</div>
			{showAddClauseModal ? (
				<ContractClauseModal
					onClose={() => {
						setShowAddClauseModal(false)
						setContractClauseToEdit(undefined)
					}}
					isEdit={contractClauseToEdit !== undefined}
					editContractClause={contractClauseToEdit?.contractClause}
					isProfile={false}
					userProfileId={userProfile?.organizationId ?? ''}
					contractClauses={contractClauses.map(item => item.contractClause)}
					onAddUpdate={onAddUpdate}
					showAddFromToolbox
				/>
			) : null}
			{/* {showAddClauseFromToolboxDrawer && eventInfo ? (
				<AddContractClauseFromToolboxDrawer
					onClose={() => {
						setShowAddClauseFromToolboxDrawer(false)
						setContractClauseToEdit(undefined)
					}}
					eventPlan={eventInfo}
					onContractClauseAdded={() => reloadContractClause()}
					existingContractClauses={contractClauses.map(
						item => item.contractClause
					)}
				/>
			) : null} */}
		</div>
	)
}

export default ContractClause
