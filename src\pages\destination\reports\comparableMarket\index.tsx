/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable unicorn/no-array-reduce */
import { customTabComponentTheme } from 'components/tab'
import type { TabsRef } from 'flowbite-react'
import { Tabs } from 'flowbite-react'
import { useEffect, useRef, useState } from 'react'
import { getComparableMarketTabs, mapProposalsToConversionData } from './helper'
import {
	PresetDirective,
	PresetsDirective
} from '@syncfusion/ej2-react-calendars'
import dateFilterStore from '../dateFilterStore'
import type { ICurrency } from 'lib/helpers'
import { calculatePeriod, periodTypes } from 'lib/helpers'
import { WarningBanner } from 'components/warningBanner'
import { Link } from 'react-router-dom'
import type {
	DestinationProposalRequest,
	INewDestination
} from 'models/destinations'
import type { IEngagementMetric } from 'lib/services/contentEngagement.service'
import { useGetEngagementMetrics } from 'lib/services/contentEngagement.service'
import { useQueryCompetingMarkets } from 'lib/services/destinations.service'
import Loader from 'components/loader'
import HSDateRangePicker from 'components/dateRangePicker'

interface IDMOComparableMarket {
	destination: INewDestination
	proposals: DestinationProposalRequest
	currency: ICurrency
	isFeatureLocked: boolean
}

export interface IConversionData {
	declined: number | null
	id: string | null
	lost: number | null
	name: string | null
	notWon: number | null
	opportunities: number | null
	opportunitiesAverageValue: number | null
	rate: number | null
	responseTimes: {
		average?: {
			days: number
			hours: number
			minutes: number
			totalMinutes: number
		}
		minimum?: {
			days: number
			hours: number
			minutes: number
			totalMinutes: number
		}
	} | null
	won: number | null
	bookingCount: number | null
	bookingTotalValue: number | null
	bookingAverageValue: number | null
	leadSource: Record<string, number | null> | null
}

export interface IMetrics {
	won: {
		total: number | null
		totalValue: number | null
		averageValue: number | null
	}
	opp: {
		total: number | null
		averageValue: number | null
	}
	conversionMaxX: number | null
	conversionRank: number | null
	conversionData: IConversionData[] | null
	averageResponseTimeRank: number | null
}

const ComparableMarket = (properties: IDMOComparableMarket) => {
	const { isFeatureLocked, destination, proposals, currency } = properties
	const tabReference = useRef<TabsRef | null>(null)
	const { dateFilter, setDateFilter } = dateFilterStore()

	const [competingMarkets, setCompetingMarkets] = useState<
		Record<string, DestinationProposalRequest> | undefined
	>()
	const [metrics, setMetrics] = useState<IMetrics>({
		won: {
			total: 0,
			totalValue: 0,
			averageValue: 0
		},
		opp: {
			total: 0,
			averageValue: 0
		},
		conversionMaxX: 0,
		conversionRank: 0,
		conversionData: [],
		averageResponseTimeRank: 0
	})
	const [searchData, setSearchData] = useState<
		{
			id: string | null
			name: string | null
			engagementMetrics: Record<string, IEngagementMetric> | undefined
		}[]
	>([])

	const tabData = getComparableMarketTabs(
		isFeatureLocked,
		searchData,
		destination,
		metrics,
		currency
	)

	const { isFetching, data: competingMarketsData } = useQueryCompetingMarkets(
		destination.id ?? '',
		`startDate=${dateFilter.startDate}&endDate=${dateFilter.endDate}`
	)
	const [destinationIds, setDestinationIds] = useState<string[]>()
	const { refetch: getEngagementMetrics, isLoading } = useGetEngagementMetrics(
		[...new Set(destinationIds)],
		'destination',
		false
	)

	useEffect(() => {
		const calculateMetrics = () => {
			const wonMetrics = proposals.won?.reduce(
				(a, c) => ({
					...a,
					count: a.count + 1,
					totalValue:
						a.totalValue +
						(c.proposalValues.foodAndBeverage ?? 0) +
						(c.proposalValues.roomCost ?? 0) +
						(c.proposalValues.roomRental ?? 0)
				}),
				{
					count: 0,
					totalValue: 0
				}
			)

			const notWonMetrics = [
				...(proposals.received ?? []),
				...(proposals.lost ?? []),
				...(proposals.declined ?? [])
			].reduce(
				(a, c) => ({
					...a,
					count: a.count + 1,
					totalValue:
						a.totalValue +
						(c.proposalValues.foodAndBeverage ?? 0) +
						(c.proposalValues.roomCost ?? 0) +
						(c.proposalValues.roomRental ?? 0)
				}),
				{
					count: 0,
					totalValue: 0
				}
			)

			const uniqueRfps = [
				...(proposals.won ?? []),
				...(proposals.received ?? []),
				...(proposals.lost ?? []),
				...(proposals.declined ?? [])
			].reduce((a: Record<string, number>, c) => {
				if (c.eventPlanId) {
					return {
						...a,
						[c.eventPlanId]: (a[c.eventPlanId] || 0) + 1
					}
				}
				return a
			}, {})

			const conversionData = [
				mapProposalsToConversionData(
					destination.id ?? '',
					destination.name ?? '',
					proposals
				),
				...Object.keys(competingMarkets ?? {}).map(k =>
					mapProposalsToConversionData(
						k,
						destination.comparableDestinations?.find(cd => (cd.id ?? '') === k)
							?.name ?? '',
						competingMarkets?.[k]
					)
				)
			]

			setMetrics({
				won: {
					total: wonMetrics?.count ?? 0,
					totalValue: wonMetrics?.totalValue ?? 0,
					averageValue:
						proposals.won && proposals.won.length > 0
							? (wonMetrics?.totalValue ?? 0) / proposals.won.length
							: null
				},
				opp: {
					total: Object.keys(uniqueRfps).length,
					averageValue:
						(wonMetrics?.count ?? 0) + notWonMetrics.count > 0
							? ((wonMetrics?.totalValue ?? 0) + notWonMetrics.totalValue) /
								((wonMetrics?.count ?? 0) + notWonMetrics.count)
							: null
				},
				conversionMaxX: Math.max(
					...conversionData.map(cd => cd.won + cd.notWon)
				),
				conversionRank:
					proposals.won && proposals.won.length > 0
						? conversionData
								.sort((c, n) => n.rate - c.rate)
								.findIndex(cd => cd.id === destination.id) + 1
						: null,
				conversionData,
				averageResponseTimeRank:
					conversionData
						.sort(
							(c, n) =>
								(n.responseTimes.average?.totalMinutes ?? 0) -
								(c.responseTimes.average?.totalMinutes ?? 0)
						)
						.findIndex(cd => cd.id === destination.id) + 1
			})
		}

		calculateMetrics()
	}, [proposals, destination, competingMarkets])

	useEffect(() => {
		const updateDestinationIds = () => {
			if (destination.id) {
				setDestinationIds(d => [...(d ?? []), destination.id ?? ''])
			}
			if (destination.comparableDestinations?.length) {
				// eslint-disable-next-line no-underscore-dangle, @typescript-eslint/naming-convention
				for (const _destination of destination.comparableDestinations) {
					setDestinationIds(d => [...(d ?? []), _destination.id ?? ''])
				}
			}
		}

		updateDestinationIds()
	}, [destination.id, destination.comparableDestinations])

	useEffect(() => {
		const fetchEngagementMetrics = async () => {
			if (
				new Set(destinationIds).size ===
				(destination.comparableDestinations?.length || 0) + 1
			) {
				try {
					const response = await getEngagementMetrics()
					if (response.data) {
						setSearchData(
							response.data.map(r_ => ({
								id: r_.id,
								name:
									[
										...(destination.comparableDestinations ?? []),
										destination
									].find(a => a.id === r_.id)?.name ?? '',
								engagementMetrics: r_.data
							}))
						)
					}
				} catch (error) {
					console.error(error)
				} finally {
					setDestinationIds([])
				}
			}
		}

		fetchEngagementMetrics()
			.then(() => {})
			.catch(() => {})
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [destinationIds, destination.comparableDestinations])

	useEffect(() => {
		if (competingMarketsData) setCompetingMarkets(competingMarketsData)
	}, [competingMarketsData])

	const onDateChangeFilter = (dates: Date[] | null) => {
		if (dates)
			setDateFilter({
				startDate: new Date(dates[0]).toISOString().split('.')[0],
				endDate: new Date(dates[1]).toISOString().split('.')[0]
			})
	}

	if (isLoading || isFetching) {
		return <Loader />
	}

	return (
		<div className='flex flex-col'>
			<div className='flex items-center justify-between border-b px-6 py-4'>
				<div className='text-xl font-semibold text-gray-900'>
					Comparable markets
				</div>
				<div className='w-80'>
					<HSDateRangePicker
						placeholder='Select Date Range'
						onChange={({ value }: { value: Date[] | null }) =>
							onDateChangeFilter(value)
						}
						value={[
							new Date(dateFilter.startDate),
							new Date(dateFilter.endDate)
						]}
						format='MMM dd, yyyy'
					>
						<PresetsDirective>
							{Object.keys(periodTypes).map(period => {
								const {
									type: { label, key },
									startDate,
									endDate
								} = calculatePeriod(period)
								return (
									<PresetDirective
										key={key}
										label={label}
										start={new Date(startDate)}
										end={new Date(endDate)}
									/>
								)
							})}
						</PresetsDirective>
					</HSDateRangePicker>
				</div>
			</div>
			<div
				className='flex h-fit flex-col gap-4 overflow-y-auto'
				style={{ maxHeight: ' calc(100vh - 12rem)' }}
			>
				{isFeatureLocked ? (
					<div className='p-4'>
						<WarningBanner
							variant='Warning'
							heading='Paid analytics features are locked'
							message={
								<p>
									Some analytics features are locked in the free version of
									HopSkip{' '}
									<Link
										to='/'
										className='font-semibold text-primary-700 underline'
									>
										Learn More
									</Link>
									.
								</p>
							}
							ctaPrompt='Upgrade Your Account'
							onClick={() => console.log('Upgrade Your Account')}
							hideIcon
						/>
					</div>
				) : null}

				<div className='px-6 py-2'>
					<Tabs
						ref={tabReference}
						className='w-full'
						variant='underline'
						theme={customTabComponentTheme('horizontal')}
					>
						{tabData.map(tab => {
							const TabComponent = tab.children
							return (
								<Tabs.Item key={tab.key} title={tab.title}>
									{TabComponent}
								</Tabs.Item>
							)
						})}
					</Tabs>
				</div>
			</div>
		</div>
	)
}

export default ComparableMarket
