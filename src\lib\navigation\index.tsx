import { Route, Routes, Navigate } from 'react-router-dom'
import Event from 'pages/event'
import SiteSearch from 'pages/search'
import {
	ROLE_ADMIN,
	ROLE_AFFILIATE,
	ROLE_DESTINATION_MANAGER,
	ROLE_HOTELIER,
	ROLE_PLANNER,
	ROLE_SUPPLIER,
	ROLE_SALES_OPS
} from 'lib/helpers/roles'
import Unauthorized from 'components/unauthorized'
import ProtectedRoute from './protectedRoute'
import PageNotFound from 'components/pageNotFound'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import lazyLoad from 'components/lazyComponent'

const HotelProfile = lazyLoad(async () => import('pages/hotel'))
const ChatView = lazyLoad(async () => import('pages/chatView'))
const MyProfile = lazyLoad(async () => import('pages/myProfile'))

const AffiliateOrganization = lazyLoad(
	async () => import('pages/affiliateorganization')
)
const OrganizationDetail = lazyLoad(async () => import('pages/organization'))
const CheckoutRedirect = lazyLoad(async () => import('components/checkout'))
const PaymentView = lazyLoad(async () => import('components/payment'))
const DestinationProfile = lazyLoad(async () => import('pages/destination'))

const AdminRoutes = lazyLoad(async () => import('./adminRoutes'))
const AffiliateRoutes = lazyLoad(async () => import('./affiliateRoutes'))
const DestinationRoutes = lazyLoad(async () => import('./destinationRoutes'))
const HotelierRoutes = lazyLoad(async () => import('./hotelierRoutes'))
const SalesOpsRoutes = lazyLoad(async () => import('./salesOpsRoutes'))
const SupplierRoute = lazyLoad(async () => import('./supplierRoutes'))
const PlannerRoutes = lazyLoad(async () => import('./plannerRoutes'))
const HotelDetailPage = lazyLoad(async () => import('pages/hotel/detail'))

const Navigation = () => {
	const { userProfile } = useUserProfileContext()
	const { role = '' } = userProfile ?? { role: '' }

	const getRoleBasedDashboard = (): string => {
		switch (role) {
			case ROLE_ADMIN: {
				return '/admin-tools/general/dashboard'
			}
			case ROLE_AFFILIATE: {
				return '/affiliate'
			}
			case ROLE_SUPPLIER: {
				return '/supplier'
			}
			case ROLE_DESTINATION_MANAGER: {
				return '/destinationmanager'
			}
			case ROLE_SALES_OPS: {
				return '/salesops'
			}
			case ROLE_HOTELIER: {
				return '/hotelier'
			}
			case ROLE_PLANNER: {
				return '/planner'
			}
			default: {
				return '/page-not-found'
			}
		}
	}
	return (
		<Routes>
			<Route path='/#/signup' element={<Navigate to='/signup' replace />} />
			<Route path='/signup' element={<Navigate to='/' replace />} />

			<Route
				path='/'
				element={<Navigate replace to={getRoleBasedDashboard()} />}
			/>
			{/* Admin Routes */}
			<Route element={<ProtectedRoute allowedRoles={[ROLE_ADMIN]} />}>
				<Route path='/admin-tools/*' element={<AdminRoutes />} />
			</Route>

			{/* Supplier Routes */}
			<Route
				element={<ProtectedRoute allowedRoles={[ROLE_SUPPLIER, ROLE_ADMIN]} />}
			>
				<Route path='/supplier/*' element={<SupplierRoute />} />
			</Route>

			{/* Destination Manager Routes */}
			<Route
				element={
					<ProtectedRoute
						allowedRoles={[ROLE_DESTINATION_MANAGER, ROLE_ADMIN]}
					/>
				}
			>
				<Route path='/destinationmanager/*' element={<DestinationRoutes />} />
			</Route>

			<Route
				element={<ProtectedRoute allowedRoles={[ROLE_SALES_OPS, ROLE_ADMIN]} />}
			>
				<Route path='/salesops/*' element={<SalesOpsRoutes />} />
			</Route>

			{/* Affiliate Routes */}
			{[ROLE_AFFILIATE, ROLE_ADMIN].includes(role ?? '') ? (
				<Route
					element={
						<ProtectedRoute allowedRoles={[ROLE_AFFILIATE, ROLE_ADMIN]} />
					}
				>
					<Route path='/affiliate/*' element={<AffiliateRoutes />} />
				</Route>
			) : null}

			{/* Hotelier Routes */}
			{[ROLE_HOTELIER, ROLE_ADMIN].includes(role ?? '') ? (
				<Route
					element={
						<ProtectedRoute allowedRoles={[ROLE_HOTELIER, ROLE_ADMIN]} />
					}
				>
					<Route path='/hotelier/*' element={<HotelierRoutes />} />
				</Route>
			) : null}
			{[ROLE_PLANNER, ROLE_ADMIN].includes(role ?? '') ? (
				<Route
					element={<ProtectedRoute allowedRoles={[ROLE_PLANNER, ROLE_ADMIN]} />}
				>
					<Route path='/planner/*' element={<PlannerRoutes />} />
				</Route>
			) : null}

			{/* Other Routes */}

			<Route path='/event' element={<Event />} />
			<Route
				path='/site-search/:view?/:eventId?/:viewId?'
				element={<SiteSearch />}
			/>

			<Route
				path='hotel-profiles/:chapterName/:venueId?/:infoType?/:viewId?'
				element={<HotelProfile />}
			/>
			{[ROLE_AFFILIATE, ROLE_ADMIN].includes(role ?? '') ? (
				<Route
					path='/industry-contacts/affiliate-organization/:organizationId/:chapterName?/:view?'
					element={<AffiliateOrganization />}
				/>
			) : null}
			{[ROLE_PLANNER, ROLE_ADMIN].includes(role ?? '') ? (
				<Route
					path='/planners/planner-organization/:organizationId/:chapterName?/:view?'
					element={<OrganizationDetail />}
				/>
			) : null}
			{[ROLE_DESTINATION_MANAGER, ROLE_ADMIN].includes(role ?? '') ? (
				<Route
					path='dmo-profiles/:chapterName/:destinationId?/:infoType?/:viewId?'
					element={<DestinationProfile />}
				/>
			) : null}

			<Route path='/my-profile/:activeTab?' element={<MyProfile />} />

			<Route path='/chat/:eventPlanId/:venueId?' element={<ChatView />} />
			<Route
				path='/checkout/:role/:priceId/:productId/:quantity/:amount'
				element={<CheckoutRedirect />}
			/>
			<Route path='hotel-detail/:venueId' element={<HotelDetailPage />} />

			<Route path='/payment/:paymentStatus' element={<PaymentView />} />
			{/* <Route path='*' element={<Navigate replace to='/page-not-found' />} /> */}
			<Route path='/page-not-found' element={<PageNotFound />} />
			<Route path='/unauthorized' element={<Unauthorized />} />
		</Routes>
	)
}

export default Navigation
