# PowerShell script to generate release notes based on commits since the last production deployment

#region Parameters
param (
    [string]$Organization,
    [string]$Project,
    [string]$RepositoryId,
    [string]$BuildId,
    [string]$ReleaseNotesFile,
    [string]$ReleaseNotesVariable,
    [string]$PAT,
    [int]$ReleaseDefinitionId,
    [int]$ReleaseDefinitionEnvironmentId
)


$B64Pat = "Basic " + [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(":$($PAT)"))
Write-Host $B64Pat
$Headers = @{
    Authorization =  $B64Pat
}

#endregion Parameters

function Get-BuildCommits { param ( [string]$Organization, [string]$Project, [string]$BuildId, [string]$PAT, [string]$RepositoryId )

try {
    $Uri = "https://dev.azure.com/$Organization/$Project/_apis/build/builds/$BuildId/changes?`$top=1&api-version=7.1"
    Write-Host "Requesting URI: $Uri"
    $Commits = Invoke-RestMethod -Uri $Uri -Method Get -Headers $Headers
    Write-Host "Commits: $Commits.count"
    if ($Commits.count -eq 0) {
        Write-Warning "No commits found for build '$BuildId'."
        return @()
    }

    return $Commits.value
}
catch {
    Write-Error "Error getting commits: $($_.Exception.Message)"
    return $null
}}

# Function to get the last successful production deployment
function Get-LastSuccessfulProductionDeployment {
    param (
        [string]$Organization,
        [string]$Project,
        [string]$PAT,
        [int]$ReleaseDefinitionId,
        [int]$ReleaseDefinitionEnvironmentId
    )

    try {
        $Uri = "https://vsrm.dev.azure.com/$Organization/$Project/_apis/Release/deployments?definitionId=$ReleaseDefinitionId&definitionEnvironmentId=$ReleaseDefinitionEnvironmentId&deploymentStatus=30&operationStatus=7960&`$top=1&api-version=7.1"
        Write-Host "Requesting URI: $Uri"
        $Deployments = Invoke-RestMethod -Uri $Uri -Method Get -Headers $Headers

        if ($null -eq $Deployments.value -or $Deployments.count -eq 0) {
            Write-Warning "No successful production deployments found for Release Definition ID '$ReleaseDefinitionId' and Environment ID '$ReleaseDefinitionEnvironmentId'."
            return $null
        }

        return $Deployments.value[0]
    }
    catch {
        Write-Error "Error getting last production deployment: $($_.Exception.Message)"
        return $null
    }
}

# Function to get commits since a specific build
function Get-CommitsSinceBuild {
    param (
        [string]$Organization,
        [string]$Project,
        [string]$RepositoryId,
        [string]$BuildId,
        [string]$PAT
    )

    try {
        $Uri = "https://dev.azure.com/$Organization/$Project/_apis/git/repositories/$RepositoryId/commits?searchCriteria.fromDate=$($LastProductionBuildDate)&api-version=7.1"
        Write-Host "Requesting URI: $Uri"
        $Commits = Invoke-RestMethod -Uri $Uri -Method Get -Headers $Headers

        if ($Commits.count -eq 0) {
            Write-Warning "No commits found since build '$BuildId'."
            return @()
        }

        return $Commits.value
    }
    catch {
        Write-Error "Error getting commits: $($_.Exception.Message)"
        return $null
    }
}

# Function to get work items associated with a commit
function Get-WorkItemsForCommit {
    param (
        [string]$Organization,
        [string]$Project,
        [string]$CommitId,
        [string]$PAT
    )

    try {
        $Uri = "https://dev.azure.com/$Organization/$Project/_apis/wit/workitems?ids=$($CommitId.workItems.id -join ",")&api-version=7.1"
        $WorkItems = Invoke-RestMethod -Uri $Uri -Method Get -Headers @{Authorization = "Basic $B64Pat"}

        if (!$WorkItems.value) {
            Write-Warning "No work items found associated with commit '$CommitId'."
            return @()
        }

        return $WorkItems.value
    }
    catch {
        Write-Error "Error getting work items: $($_.Exception.Message)"
        return $null
    }
}

# Function to get the build associated with a release
function Get-BuildFromRelease {
    param (
        [string]$Organization,
        [string]$Project,
        [string]$PAT,
        [string]$ReleaseId
    )

    try {
        $Uri = "https://vsrm.dev.azure.com/$Organization/$Project/_apis/Release/releases/$ReleaseId`?api-version=7.1"
        Write-Host "Requesting URI: $Uri"
        $Release = Invoke-RestMethod -Uri $Uri -Method Get -Headers $Headers

        if (!$Release) {
            Write-Warning "Could not find release with ID '$ReleaseId'."
            return $null
        }

        # Assuming the build is linked as an artifact in the release
        if (!$Release.artifacts -or $Release.artifacts.Count -eq 0) {
            Write-Warning "No artifacts found for release '$ReleaseId'."
            return $null
        }

        # Assuming the first artifact is the build
        $BuildId = $Release.artifacts[0].definitionReference.version.id
        return $BuildId
    }
    catch {
        Write-Error "Error getting build from release: $($_.Exception.Message)"
        return $null
    }
}

# Main script execution

# Get the last successful production deployment
$LastProductionDeployment = Get-LastSuccessfulProductionDeployment -Organization $Organization -Project $Project -PAT $PAT -ReleaseDefinitionId $ReleaseDefinitionId -ReleaseDefinitionEnvironmentId $ReleaseDefinitionEnvironmentId

if (!$LastProductionDeployment) {
    Write-Host "No last production deployment found.  Generating release notes for all commits in the current build."
    # Fallback to original behavior: get commits for the current build
    $Commits = Get-BuildCommits -Organization $Organization -Project $Project -BuildId $BuildId -PAT $PAT -RepositoryId $RepositoryId
} else {
    Write-Host "Last Production Deployment ID: $($LastProductionDeployment.id)"

    # Get the release ID from the deployment
    $LastReleaseId = $($LastProductionDeployment.release.id)
    Write-Host $LastProductionDeployment.release.id

    # Get the build ID associated with the release
    $LastProductionBuildId = Get-BuildFromRelease -Organization $Organization -Project $Project -PAT $PAT -ReleaseId $LastReleaseId

    if (!$LastProductionBuildId) {
        Write-Host "Could not determine the build associated with the last production release. Generating empty release notes."
        $Commits = @()
    } else {
        Write-Host "Last Production Build ID: $LastProductionBuildId"

        # Get the date of the last production build
        $BuildUri = "https://dev.azure.com/$Organization/$Project/_apis/build/builds/$LastProductionBuildId`?api-version=7.1"
        try {
            $LastProductionBuild = Invoke-RestMethod -Uri $BuildUri -Method Get -Headers $Headers
            $LastProductionBuildDate = $LastProductionBuild.startTime
            Write-Host "Last Production Build Date: $LastProductionBuildDate"
        } catch {
            Write-Error "Error getting last production build details: $($_.Exception.Message)"
            $LastProductionBuildDate = $null  # Handle error appropriately
        }

        # Get commits since the last production build
        if ($LastProductionBuildDate) {
            $Commits = Get-CommitsSinceBuild -Organization $Organization -Project $Project -RepositoryId $RepositoryId -BuildId $LastProductionBuildId -PAT $PAT
        } else {
            Write-Host "Could not determine last production build date.  Generating empty release notes."
            $Commits = @() # No commits
        }
    }
}

if ($Commits) {
    # Build the release notes content
    $ReleaseNotes = New-Object System.Text.StringBuilder

    [void]$ReleaseNotes.AppendLine("## Release Notes (Since Last Production Deployment)")
    [void]$ReleaseNotes.AppendLine("")

    # Iterate through commits and associated work items
    foreach ($Commit in $Commits) {
        [void]$ReleaseNotes.AppendLine("## $($Commit.author.name) on $($Commit.author.date)")
        [void]$ReleaseNotes.AppendLine("### Message: $($Commit.comment)")
        [void]$ReleaseNotes.AppendLine("")

        # Get work items associated with the commit
        #$WorkItems = Get-WorkItemsForCommit -Organization $Organization -Project $Commit.commitId -PAT $PAT
        $WorkItems = @()

        if ($WorkItems) {
            [void]$ReleaseNotes.AppendLine("#### Work Items:")
            foreach ($WorkItem in $WorkItems) {
                [void]$ReleaseNotes.AppendLine("- $($WorkItem.Fields.'System.Id'): $($WorkItem.Fields.'System.Title')")
            }
            [void]$ReleaseNotes.AppendLine("")
        } else {
            [void]$ReleaseNotes.AppendLine("No work items associated")
            [void]$ReleaseNotes.AppendLine("")
        }
    }

    # Write the release notes to the file
    $ReleaseNotes.ToString() | Out-File -FilePath $ReleaseNotesFile -Encoding UTF8

    # Set the pipeline variable
    Write-Host "##vso[task.setvariable variable=$ReleaseNotesVariable;]$($ReleaseNotes.ToString())"
    Write-Host "##vso[task.uploadsummary]$ReleaseNotesFile"

    Write-Host "Release notes written to: $ReleaseNotesFile"
    Write-Host "Release notes content set to variable: $($ReleaseNotesVariable)"

} else {
    Write-Host "No commits found since the last production deployment."
    "No commits found since the last production deployment." | Out-File -FilePath $ReleaseNotesFile -Encoding UTF8
    Write-Host "##vso[task.setvariable variable=$ReleaseNotesVariable;]No commits found since the last production deployment."
}
