/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable @typescript-eslint/no-unsafe-return */
import { clientNavigate, iconMap } from 'components/sidebar'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { ProposalRequestStatusMap } from 'lib/helpers/statusMaps'
import { useGetSummaryProposals } from 'lib/services/hotel.hub.service'
import { Link, useLocation } from 'react-router-dom'

const MarketAvailabilityNavItem = ({
	customClassName
}: {
	customClassName?: string
}) => {
	const { data: summaryProposals } = useGetSummaryProposals()
	const { userProfile } = useUserProfileContext()
	const location = useLocation()

	const newSummaryProposals =
		summaryProposals?.filter(
			proposal =>
				proposal.proposalRequest.status ===
					ProposalRequestStatusMap.Received?.key ||
				proposal.proposalRequest.status === ProposalRequestStatusMap.New?.key
		) ?? []

	return (
		<div className='nav-item notifications'>
			<Link
				to='/hotelier/proposal-summary'
				tabIndex={0}
				aria-label='Market Availability'
				key='Market Availability'
				onClick={() => {
					clientNavigate(userProfile?.id ?? '', location)
				}}
			>
				<div
					className={` ${customClassName ?? 'relative px-2 py-1 text-logo-light no-underline hover:rounded-lg hover:bg-gray-700 hover:text-white'}`}
				>
					<img src={iconMap['clipboard-check'].svg} alt='' />
					{newSummaryProposals.length > 0 ? (
						<div
							className={`absolute bottom-4 right-1 z-10 flex items-center justify-center rounded-full bg-red-700 text-xs !leading-4 text-white ${newSummaryProposals.length > 9 ? (newSummaryProposals.length > 99 ? 'left-5 h-6 w-6' : 'left-5 h-5 w-5') : '!bottom-5 -right-1 left-5 h-5 w-5'}`}
						>
							{newSummaryProposals.length >= 100
								? '99+'
								: newSummaryProposals.length}
						</div>
					) : null}
				</div>
			</Link>
		</div>
	)
}

export default MarketAvailabilityNavItem
