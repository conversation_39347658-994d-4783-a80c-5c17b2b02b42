import HSTabs from 'components/tab'
import HotelList from './hotelList'
import HotelSearch from './hotelSearch'
import { useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { useGetEventDetail } from 'lib/services/eventPlans.service'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import useSiteSearchStore from './store'
import PageLoader from 'components/pageLoader'
import HSButton from 'components/button'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faEdit } from '@fortawesome/pro-regular-svg-icons'

const HotelListTab = () => {
	const { eventId } = useParams()
	const [activeTabId, setActiveTabId] = useState(eventId ? 0 : 1)
	const { userProfile } = useUserProfileContext()
	const { initializeStore, selectedEventPlan, setObject } = useSiteSearchStore()

	const { refetch } = useGetEventDetail(eventId ?? '', !!eventId)

	useEffect(() => {
		if (userProfile) {
			if (eventId) {
				refetch()
					.then(response => {
						if (response.data) {
							setObject(response.data, true)
						}
					})
					.catch((error: unknown) => {
						console.error('Error fetching event plan:', error)
					})
			} else {
				initializeStore(userProfile)
			}
		}
	}, [eventId, userProfile, initializeStore, refetch, setObject])

	return (
		<div className='mt-4 flex flex-col gap-4'>
			{eventId && selectedEventPlan?.name ? (
				<div className=''>
					<div className='flex items-center justify-between rounded-lg bg-white px-6 py-2 shadow-md'>
						<div className='text-sm font-semibold leading-none text-gray-900'>
							{selectedEventPlan.name}
						</div>
						<HSButton color='light' size='xs'>
							<FontAwesomeIcon icon={faEdit} size='lg' />
						</HSButton>
					</div>
				</div>
			) : null}
			{selectedEventPlan ? (
				<div className='px-6 pb-6'>
					<HSTabs
						tabs={[
							{
								key: 0,
								title: 'Hotels Added to Search',
								children: (
									<div className='card'>
										<HotelList />
									</div>
								),
								isDisabled: !eventId
							},
							{
								key: 1,
								title: 'Add New Hotels',
								children: (
									<div className='card'>
										<HotelSearch
											enableBackButton
											eventPlan={selectedEventPlan}
										/>
									</div>
								)
							}
						]}
						enableRootCard={false}
						orientation='vertical'
						activeTabId={activeTabId}
						onActiveTabChange={tabId => setActiveTabId(tabId)}
					/>
				</div>
			) : (
				<PageLoader />
			)}
		</div>
	)
}
export default HotelListTab
