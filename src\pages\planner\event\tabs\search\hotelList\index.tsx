import HotelPreview from 'components/hotel/preview'
import AddHotelToSearch from 'components/hotelSearch/drawers/addToSearch'
import HSPagination from 'components/pagination'
import analytics from 'lib/analytics/segment/load'
import { appInsights } from 'lib/auth/appInsights'
import { getHopSkipConfig } from 'lib/auth/auth.config'
import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import type { Venue } from 'models/venue'
import { useMemo, useState } from 'react'

interface VerticalHotelListProperties {
	searchResults: Venue[]
	eventInfo: EventPlan | undefined
	setSelectedVenueId: (id: string) => void
	pageIndex: number
	setPageIndex: (page: number) => void
	pageSize: number
	disableFields: boolean | undefined
	searchResultsCount: number
	searchId: string | null
	handleAdd: (
		pr: Partial<ProposalRequest>[],
		callback: () => void,
		isAdmin: boolean
	) => Promise<void>
	setSelectedVenue: (venue: Venue | null) => void
	selectedVenue: Venue | null
}

const VerticalHotelList = (properties: VerticalHotelListProperties) => {
	const {
		searchResults,
		eventInfo,
		setSelectedVenueId,
		pageIndex,
		setPageIndex,
		pageSize,
		disableFields,
		searchResultsCount,
		searchId,
		handleAdd,
		setSelectedVenue,
		selectedVenue
	} = properties
	const [showSiteSearchDrawer, setShowSiteSearchDrawer] = useState(false)

	const renderList = useMemo(
		() =>
			searchResults.map(venue => (
				<div key={`hotel-preview-card-${venue.id}`}>
					<HotelPreview
						venue={venue}
						eventInfo={eventInfo}
						isSearch
						onClick={selectedHotel => {
							analytics.track('Search Result Hotel Clicked', {
								hotelId: selectedHotel.id,
								hotelName: selectedHotel.name,
								SearchId: searchId
							})
							appInsights.trackEvent(
								{ name: 'Search Result Hotel Clicked' },
								{
									SearchServiceName: getHopSkipConfig().search.serviceName,
									SearchId: searchId,
									ClickedDocId: selectedHotel.id
								}
							)
							setSelectedVenueId(selectedHotel.id ?? '')
						}}
						showAddToRFP
						disabledFields={disableFields}
						addRemoveProposalRequests={handleAdd}
					/>
				</div>
			)),
		[
			disableFields,
			eventInfo,
			handleAdd,
			searchId,
			searchResults,
			setSelectedVenueId
		]
	)

	return (
		<div className='flex h-full flex-col'>
			<div className='flex flex-1 flex-col gap-4 overflow-y-auto p-4'>
				{renderList}
			</div>
			{searchResultsCount > pageSize ? (
				<div className='flex flex-shrink-0 items-center justify-center rounded-b-lg border-t bg-white px-4 py-2'>
					<HSPagination
						currentPage={pageIndex}
						onPageChange={updatedPageIndex => {
							analytics.track('Search Results Paged', {
								pageIndex: updatedPageIndex,
								previousPageIndex: pageIndex,
								pageSize,
								searchResultsCount
							})
							setPageIndex(updatedPageIndex)
						}}
						totalPages={Math.floor(searchResultsCount / pageSize)}
					/>
				</div>
			) : null}
			{showSiteSearchDrawer && selectedVenue ? (
				<AddHotelToSearch
					onClose={() => {
						setShowSiteSearchDrawer(false)
						setSelectedVenue(null)
					}}
					venueToAdd={{ ...selectedVenue }}
				/>
			) : null}
		</div>
	)
}

export default VerticalHotelList
