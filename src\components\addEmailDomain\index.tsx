/* eslint-disable react/no-array-index-key */
import { faAt } from '@fortawesome/pro-light-svg-icons'
import HSButton from 'components/button'
import HSIcon from 'components/HSIcon'
import HSModal from 'components/modal'
import H<PERSON>extField from 'components/textField'
import { Modal } from 'flowbite-react'
import React from 'react'

interface IAddEmailDomainModal {
	show: boolean
	setShow: (show: boolean) => void
	onSubmit: (name: string, type: string, value: object) => void
}

const AddEmailDomainModal = (properties: IAddEmailDomainModal) => {
	const { setShow, show, onSubmit } = properties
	const [emailDomain, setEmailDomain] = React.useState<string>('')

	return (
		<div>
			<HSModal
				openModal={show}
				onClose={() => {
					setShow(false)
				}}
				header='Auto-add email domain'
				size='md'
			>
				<Modal.Body>
					<div className='flex flex-col gap-4'>
						<div className='text-gray-500'>
							Automatically add new members who sign up with the following email
							domain:
						</div>
						<div className='flex flex-col gap-2'>
							<div>Domain</div>
							<div className='flex items-center justify-between gap-4'>
								<HSTextField
									icon={HSIcon(faAt)}
									placeholder='Enter an email domain'
									value={emailDomain}
									onChange={event => {
										setEmailDomain(event.target.value)
									}}
								/>
							</div>
						</div>
					</div>
				</Modal.Body>
				<Modal.Footer>
					<div className='flex grow items-center gap-4'>
						<HSButton
							color='light'
							className='grow'
							onClick={() => setShow(false)}
						>
							Cancel
						</HSButton>
						<HSButton
							className='grow'
							onClick={() => {
								onSubmit('memberEmailDomain', 'string', emailDomain)
								setShow(false)
							}}
						>
							Create
						</HSButton>
					</div>
				</Modal.Footer>
			</HSModal>
		</div>
	)
}

export default AddEmailDomainModal
