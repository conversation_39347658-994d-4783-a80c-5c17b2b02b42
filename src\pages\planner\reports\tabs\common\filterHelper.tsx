/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faFilter } from '@fortawesome/pro-light-svg-icons'
import HSButton from 'components/button'
import HSBadge from 'components/badge'
import HSPopover from 'components/popover'
import HSToggleSwitch from 'components/toggleSwitch'
import HSDropdownButton from 'components/dropdown'
import HSRadioButton from 'components/radioButton'
import { requestTypeFilters } from '../../common'
import type { ISetFilters } from '../../helper'
import type { HotelChain } from 'models/chains'
import { useSearchParams } from 'react-router-dom'

export const chainQuery = 'chainId'
export const brandQuery = 'brandId'

const DEFAULT_CHAINS: HotelChain[] = []

const DEFAULT_FILTERS: ISetFilters = {
	includeCancelled: false,
	chainId: null,
	brandId: null,
	requestType: null,
	dateFilter: {
		type: {
			key: '',
			label: ''
		},
		startDate: '',
		endDate: ''
	},
	currency: {
		code: 'USD',
		symbol: '$',
		name: 'United States Dollar'
	}
}

interface FilterHelperProperties {
	filters: ISetFilters
	setFilters: (filters: ISetFilters) => void
	chains?: HotelChain[] | undefined
	filteredChainIds: (string | null)[]
}

const FilterPopover = ({
	filters = DEFAULT_FILTERS,
	setFilters,
	chains = DEFAULT_CHAINS,
	filteredChainIds = DEFAULT_CHAINS.map(chain => chain.id)
}: FilterHelperProperties): React.ReactElement => {
	const [currentQueryParameters, setSearchParams] = useSearchParams()
	const newQueryParameters: URLSearchParams = new URLSearchParams()

	const dropdownItems = [
		{ text: 'All Chains', value: '' },
		{ text: 'Independent', value: 'INDEPENDENT' },
		...chains
			.filter(chain => chain.id && filteredChainIds?.includes(chain.id))
			.sort((a, b) => ((a.name ?? '') > (b.name ?? '') ? 1 : -1))
			.map(chain => ({
				text: chain.name,
				value: chain.id
			}))
	]

	const dropdownItemsForBrand = [
		{ text: 'All Brands', value: '' },
		...(chains
			.find(c => c.id === filters.chainId)
			?.brands?.map(brand => ({
				text: brand.name,
				value: brand.id
			})) ?? [])
	]

	const radioButtonItems = [
		{ label: 'All Request Types', value: '' },
		...Object.keys(requestTypeFilters).map(k => ({
			label: requestTypeFilters[k]?.label ?? '',
			value: requestTypeFilters[k]?.key ?? ''
		}))
	]

	const filterCount = (() => {
		let count = 0
		if (filters.includeCancelled) count += 1
		if (filters.chainId) count += 1
		if (filters.brandId) count += 1
		return count
	})()

	const popOverContent = (
		<div className='flex max-h-96 w-80 flex-col gap-4 overflow-y-auto p-4'>
			<div className='flex flex-col gap-2 p-2'>
				<div className='flex items-center justify-between'>
					<div className='text-sm font-medium text-gray-900'>Filters</div>
					<div className='flex gap-2'>
						<HSButton size='sm' color='text'>
							Save View
						</HSButton>
						<HSButton
							size='sm'
							color='text'
							onClick={() => {
								setFilters({
									...filters,
									includeCancelled: false,
									chainId: null,
									brandId: null,
									requestType: null
								})
							}}
						>
							Clear All
						</HSButton>
					</div>
				</div>
			</div>

			<HSToggleSwitch
				label='Include Inactive/Closed RFPs'
				checked={filters.includeCancelled}
				onChange={() => {
					setFilters({
						...filters,
						includeCancelled: !filters.includeCancelled
					})
				}}
			/>

			<div className='border-b' />

			<div className='flex flex-col gap-2'>
				<div className='text-sm font-medium text-gray-900'>Chains</div>
				<div className='dropdown-select'>
					<HSDropdownButton
						label={
							filters.chainId === '' || filters.chainId === null
								? 'All Chains'
								: dropdownItems.find(chain => filters.chainId === chain.value)
										?.text
						}
						color='light'
						items={dropdownItems.map(item => ({
							id: item.value ?? '',
							item: item.text ?? '',
							clickFunction: () => {
								setFilters({
									...filters,
									chainId: item.value || null,
									brandId: null
								})
								if (item.value) {
									newQueryParameters.set(chainQuery, item.value || '')
								}
								setSearchParams(newQueryParameters)
							}
						}))}
						className='w-full truncate'
					/>
				</div>
			</div>

			<div className='border-b' />

			<div className='flex flex-col gap-2'>
				<div className='text-sm font-medium text-gray-900'>Brands</div>
				<div className='dropdown-select'>
					<HSDropdownButton
						label={
							filters.brandId === '' || !filters.brandId
								? 'All Brands'
								: dropdownItemsForBrand.find(
										brand => filters.brandId === brand.value
									)?.text
						}
						disabled={
							!filters.chainId ||
							!chains.find(c => c.id === filters.chainId)?.brands?.length
						}
						color='light'
						items={dropdownItemsForBrand.map(item => ({
							id: item.value ?? '',
							item: item.text ?? '',
							clickFunction: () => {
								setFilters({
									...filters,
									brandId: item.value || null
								})
								if (item.value) {
									if (filters.chainId) {
										newQueryParameters.set(chainQuery, filters.chainId)
									}
									newQueryParameters.set(brandQuery, item.value || '')
								}
								setSearchParams(newQueryParameters)
							}
						}))}
						className='w-full truncate'
					/>
				</div>
			</div>

			<div className='border-b' />

			<div className='flex flex-col gap-2'>
				<div className='text-sm font-medium text-gray-900'>Request types</div>
				{radioButtonItems.map(item => (
					<HSRadioButton
						key={item.value}
						value={item.value}
						label={item.label}
						name='requestType'
						selectedValue={filters.requestType ?? ''}
						onChange={event => {
							setFilters({
								...filters,
								requestType: event.target.value || null
							})
						}}
					/>
				))}
			</div>
		</div>
	)

	return (
		<HSPopover
			content={popOverContent}
			aria-labelledby='filter'
			placement='bottom-end'
			arrow={false}
		>
			<div className='relative inline-block'>
				<HSButton color='light'>
					<div className='flex items-center gap-2'>
						<FontAwesomeIcon icon={faFilter} />
						<div>Filter Page</div>
					</div>
				</HSButton>
				{filterCount > 0 ? (
					<HSBadge
						color='failure'
						title={`${filterCount} Filter/s Applied`}
						className='absolute right-0 top-0 z-10 -translate-y-1/2 translate-x-1/2 transform rounded-full bg-red-600'
					>
						<div className='text-md flex p-1 font-semibold text-white'>
							{filterCount}
						</div>
					</HSBadge>
				) : null}
			</div>
		</HSPopover>
	)
}

export default FilterPopover
