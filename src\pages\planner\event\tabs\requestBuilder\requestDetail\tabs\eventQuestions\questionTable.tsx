import HSTable from 'components/table'
import { requestGroups } from 'lib/helpers/requestGroups'
import type { IQuestion, IQuestionEvent } from 'models/questions'
import type { JSX } from 'react'

interface QuestionTableProperties {
	questions: IQuestionEvent[]
	actionTemplate: (item: IQuestion) => JSX.Element
}

const QuestionTable = (properties: QuestionTableProperties) => {
	const { questions, actionTemplate } = properties

	return (
		<HSTable
			defaultSort={{ field: 'requestGroupId', direction: 'asc' }}
			columns={[
				{
					field: 'requestGroupId',
					headerText: 'Question Category',
					render: item => (
						<div>
							{requestGroups[item.requestGroupId ?? '']?.name ??
								'No Grouping Specified'}
						</div>
					),
					sortable: true,
					width: 200
				},
				{
					field: 'text',
					headerText: 'Question',
					sortable: true,
					clipMode: 'ellipsis',
					render: item => <div className='text-wrap'>{item.text}</div>
				},
				{
					field: 'requestGroupId',
					headerText: 'Actions',
					headerAlign: 'center',
					render: actionTemplate,
					width: 100,
					freeze: 'right'
				}
			]}
			rows={questions.map(item => item.question)}
		/>
	)
}

export default QuestionTable
