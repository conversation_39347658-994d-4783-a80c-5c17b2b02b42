import { render, screen, fireEvent } from '@testing-library/react'
// eslint-disable-next-line import/no-extraneous-dependencies
import '@testing-library/jest-dom'
import { describe, it, expect } from 'vitest'
import HSButton from '.'
import type { ButtonProps } from 'flowbite-react'

// Helper function to assert common button properties
const expectButtonToBeInTheDocumentWithClass = (name: string) => {
	const button = screen.getByRole('button', { name: /test button/i })
	expect(button).toBeInTheDocument()
	expect(button).toHaveClass(name)
}

describe('HSButton', () => {
	// Centralized button rendering function
	const renderButton = (properties: ButtonProps) =>
		render(<HSButton {...properties}>Test Button</HSButton>)

	// this is no longer valid
	// it('renders the button with default properties', () => {
	// 	renderButton({})
	// 	expectButtonToBeInTheDocumentWithClass(
	// 		'group relative flex items-stretch justify-center p-0.5 text-center font-medium transition-[color,background-color,border-color,text-decoration-color,fill,stroke,box-shadow] focus:z-10 focus:outline-none border-transparent bg-primary-700 text-white focus:ring-0 enabled:hover:bg-primary-800 dark:bg-primary-600 dark:focus:ring-primary-800 dark:enabled:hover:bg-primary-700 disabled-bg-gray-400 rounded-lg'
	// 	)
	// })

	it('applies custom themes', () => {
		renderButton({ theme: { color: { primary: 'custom-primary-theme' } } })
		expectButtonToBeInTheDocumentWithClass('custom-primary-theme')
	})

	it('handles different button colors', () => {
		renderButton({ color: 'light' })
		expectButtonToBeInTheDocumentWithClass(
			'group relative flex items-stretch justify-center p-0.5 text-center font-medium transition-[color,background-color,border-color,text-decoration-color,fill,stroke,box-shadow] focus:z-10 focus:outline-none border border-gray-300 bg-white text-gray-900 focus:ring-0 focus:ring-none enabled:hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-600 dark:text-white dark:focus:ring-none dark:enabled:hover:border-gray-700 dark:enabled:hover:bg-gray-700 rounded-lg'
		)
	})

	it('applies correct classes for outline variant with primary color', () => {
		renderButton({
			color: 'primary',
			outline: true,
			theme: { outline: { color: { primary: 'custom-primary-theme' } } }
		})
		expectButtonToBeInTheDocumentWithClass('custom-primary-theme')
	})

	it('renders the button in disabled state', () => {
		renderButton({ disabled: true })
		const button = screen.getByRole('button', { name: /test button/i })
		expect(button).toBeDisabled()
		expect(button).toHaveClass(
			' group relative flex items-stretch justify-center p-0.5 text-center font-medium transition-[color,background-color,border-color,text-decoration-color,fill,stroke,box-shadow] focus:z-10 focus:outline-none cursor-not-allowed opacity-50 border-transparent bg-primary-700 text-white focus:ring-0 enabled:hover:bg-primary-800 dark:bg-primary-600 focus:ring-cyan-300 dark:focus:ring-cyan-800 dark:enabled:hover:bg-primary-700 !disabled:bg-primary-disabled !disabled:text-white rounded-lg'
		)
	})

	it('handles button click', () => {
		const handleClick = vi.fn()
		renderButton({ onClick: handleClick })
		const button = screen.getByRole('button', { name: /Test Button/ }) // Adjusted to match visible text content
		fireEvent.click(button)
		expect(handleClick).toHaveBeenCalled()
	})

	it('has appropriate accessibility attributes', () => {
		renderButton({ 'aria-label': 'Accessible Button' })
		const button = screen.getByRole('button', { name: 'Accessible Button' })
		expect(button).toHaveAttribute('aria-label', 'Accessible Button')
	})
})
