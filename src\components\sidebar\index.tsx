/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
import type { IconDefinition } from '@fortawesome/pro-light-svg-icons'
import {
	faAnalytics,
	faBuilding,
	faFileContract,
	faGlobe,
	faGripHorizontal,
	faLayerPlus,
	faMegaphone,
	faSearch,
	faToolbox,
	faBell,
	faGraduationCap,
	faQuestionCircle,
	faCircle,
	faMessageSmile,
	faClockRotateLeft
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import type { MenuItemModel } from '@syncfusion/ej2-react-navigations'
import type { Location } from 'react-router-dom'
import {
	adminMenuItems,
	affiliateMenuItems,
	commonMenuItems,
	dmoMenuItems,
	hotelierMenuItems,
	plannerMenuItems,
	salesOpsMenuItem,
	supplierMenuItems
} from 'components/layout/navHelper'
import { useEffect, useState } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import navLogoSvg from '../../assets/images/hopskip_logo.svg'
import UserAvatar from 'components/avatar'
import HSTooltip from 'components/tooltip'
import rfpLogo from 'assets/images/rfpLogo.svg'
import locationDollar from 'assets/images/locationDollar.svg'
import NotificationsNavItem from 'components/notificationsNavItem'
import { getHopSkipConfig } from 'lib/auth/auth.config'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import {
	faFileMagnifyingGlass,
	faUserSecret
} from '@fortawesome/pro-regular-svg-icons'
import Impersonate from 'components/impersonate'
import { ROLE_ADMIN } from 'lib/helpers/roles'
import MarketAvailabilityNavItem from 'components/marketAvailabiltyNavItem'
import HSDrawer from 'components/drawer'

interface IconStyle {
	transform: string
}

type IconMap = Record<
	string,
	{
		icon: IconDefinition
		style?: IconStyle
		useSvg?: boolean
		svg?: string
	}
>

export const iconMap: IconMap = {
	bell: { icon: faBell },
	'new-rfp': { icon: faLayerPlus },
	search: { icon: faSearch },
	board: { icon: faGripHorizontal },
	history: { icon: faClockRotateLeft },
	docs: { icon: faFileContract },
	toolbox: { icon: faToolbox },
	reports: { icon: faAnalytics },
	promo: { icon: faMegaphone, style: { transform: 'rotate(336deg)' } },
	building: { icon: faBuilding },
	destination: { icon: faGlobe },
	graduation: { icon: faGraduationCap },
	question: { icon: faQuestionCircle },
	feedback: { icon: faMessageSmile },
	'rfp-svg': { icon: faLayerPlus, useSvg: true, svg: rfpLogo },
	impersonate: { icon: faUserSecret },
	'clipboard-check': {
		icon: faFileMagnifyingGlass,
		useSvg: true,
		svg: locationDollar
	}
}

export const clientNavigate = (
	userProfileId: string,
	location: Location<unknown>
) => {
	if (getHopSkipConfig().api.environment === 'Prod') {
		// eslint-disable-next-line @typescript-eslint/no-unsafe-call
		globalThis.analytics.page(location)
	}
	// eslint-disable-next-line @typescript-eslint/no-unsafe-call
	globalThis.Intercom('update')
}

const Sidebar = ({ isChatPopup }: { isChatPopup: boolean }) => {
	const { userProfile } = useUserProfileContext()
	const { pathname } = useLocation()
	const [selectedItem, setSelectedItem] = useState<MenuItemModel | undefined>()
	const [selectedSubItem, setSelectedSubItem] = useState<
		MenuItemModel | undefined
	>()
	const [isOpen, setIsOpen] = useState(false)
	const [isSubDrawerOpen, setIsSubDrawerOpen] = useState(false)
	const location = useLocation()

	const sidebarRoleMapper: Record<string, MenuItemModel[] | null> = {
		Admin: [...adminMenuItems(userProfile?.associatedVenues ?? [])],
		Hotelier: [...hotelierMenuItems(userProfile?.associatedVenues ?? [])],
		Planner: plannerMenuItems(),
		Affiliate: [...affiliateMenuItems()],
		Supplier: [...supplierMenuItems()],
		DestinationManager: dmoMenuItems,
		SalesOps: salesOpsMenuItem
	}

	const toolbarClicked = (item: MenuItemModel) => {
		if (selectedItem?.text === item.text) {
			setIsOpen(previous => !previous)
		} else {
			setSelectedItem(item)
			setIsOpen(true)
			setIsSubDrawerOpen(false)
		}
	}

	const subItemToolbarClicked = (item: MenuItemModel) => {
		if (selectedItem?.text === item.text) {
			setIsSubDrawerOpen(previous => !previous)
		} else {
			setSelectedSubItem(item)
			setIsSubDrawerOpen(true)
		}
	}

	const handleClose = () => {
		setIsOpen(false)
		setIsSubDrawerOpen(false)
	}

	useEffect(() => {
		if (pathname) {
			const [none, main, sub] = pathname.split('/')
			const currentSelectedItem = sidebarRoleMapper[
				userProfile?.role ?? ''
			]?.find(item => item.url === `/${main}`)
			if (currentSelectedItem) {
				setSelectedItem(currentSelectedItem)
				const currentSelectedSubItem = currentSelectedItem.items?.find(
					item => item.url === `/${main}/${sub}`
				)
				setSelectedSubItem(currentSelectedSubItem)
			}
		}
		return () => {
			setSelectedItem(undefined)
			setSelectedSubItem(undefined)
		}
	}, [])

	const renderSideBar = (item: MenuItemModel) => {
		if (item.text === 'Notifications') {
			return <NotificationsNavItem key={item.text} />
		}

		if (item.text === 'Impersonate') {
			return <Impersonate key={item.text} />
		}

		if (item.text === 'Market Availability') {
			return <MarketAvailabilityNavItem key={item.text} />
		}
		return item.items?.length ? (
			<div
				role='button'
				tabIndex={0}
				aria-label={item.text}
				className={`px-2 py-1 no-underline hover:rounded-lg hover:bg-gray-700 hover:text-white ${pathname.includes(item.url ?? '') || selectedItem?.url?.includes(item.url ?? '') ? 'rounded-lg border border-white bg-gray-900 text-white' : 'text-logo-light'}`}
				onClick={() => {
					toolbarClicked(item)
				}}
				onKeyDown={event_ => {
					if (event_.key === 'Enter' || event_.key === ' ') toolbarClicked(item)
				}}
				key={item.text}
			>
				{iconMap[item.iconCss as keyof IconMap].useSvg ? (
					<img src={iconMap[item.iconCss as keyof IconMap].svg} alt='' />
				) : (
					<FontAwesomeIcon
						className='h-[24px] w-[24px]'
						icon={iconMap[item.iconCss as keyof IconMap].icon}
						style={iconMap[item.iconCss as keyof IconMap].style}
					/>
				)}
			</div>
		) : (
			<Link
				to={item.url ?? ''}
				tabIndex={0}
				aria-label={item.text}
				key={item.text}
				onClick={() => {
					clientNavigate(userProfile?.id ?? '', location)
				}}
			>
				<div
					className={`px-2 py-1 no-underline hover:rounded-lg hover:bg-gray-700 hover:text-white ${pathname.includes(item.url ?? '') || selectedItem?.url?.includes(item.url ?? '') ? 'rounded-lg border border-white bg-gray-900 text-white' : 'text-logo-light'}`}
				>
					{iconMap[item.iconCss as keyof IconMap].useSvg ? (
						<img src={iconMap[item.iconCss as keyof IconMap].svg} alt='' />
					) : (
						<FontAwesomeIcon
							className='h-[24px] w-[24px]'
							icon={iconMap[item.iconCss as keyof IconMap].icon}
							style={iconMap[item.iconCss as keyof IconMap].style}
						/>
					)}
				</div>
			</Link>
		)
	}

	if (isChatPopup) {
		return null
	}

	return (
		<>
			<div className='flex h-screen w-16 flex-col justify-between bg-gray-800'>
				<div className='flex flex-1 flex-col'>
					<div className='mb-4 flex justify-center py-4'>
						<a href='/' rel='noopener noreferrer' className='w-[20px]'>
							<img alt='HopSkip Logo' src={navLogoSvg} />
						</a>
					</div>
					<ul className='flex flex-col items-center gap-2'>
						{sidebarRoleMapper[userProfile?.role ?? '']?.map(item => (
							<div key={item.text} className='text-xl font-medium'>
								<HSTooltip
									content={item.text}
									placement='right'
									tabIndex={0}
									arrow={false}
									// cssClass='tooltip'
								>
									{renderSideBar(item)}
								</HSTooltip>
							</div>
						))}
					</ul>
					{(userProfile?.role ?? '') ? (
						<>
							<div className='mt-4 border-b border-gray-700 px-2' />
							<div className='flex flex-1'>
								<ul className='flex flex-1 flex-col items-center justify-end gap-2'>
									{[ROLE_ADMIN].includes(userProfile?.role ?? '')
										? commonMenuItems.map(item => renderSideBar(item))
										: commonMenuItems
												.filter(item => item.iconCss === 'question')
												.map(item => renderSideBar(item))}
								</ul>
							</div>
							<div className='mt-4 border-b border-gray-700 px-2' />
							{[ROLE_ADMIN].includes(userProfile?.role ?? '') ? (
								<div className='flex items-center justify-center'>
									<UserAvatar />
								</div>
							) : null}
						</>
					) : null}
				</div>
			</div>

			<HSDrawer
				open={isOpen}
				onClose={handleClose}
				className={`bg-gray-700 ${isOpen ? 'left-16' : 'left-0'}`}
				theme={{
					root: {
						backdrop:
							'fixed inset-0 z-50 bg-gray-900/50 dark:bg-gray-900/80 left-16'
					}
				}}
				id='drawer'
			>
				<div className='flex h-full flex-col justify-between'>
					<div>
						<div className='p-5 text-lg font-semibold text-white'>
							{selectedItem?.text}
						</div>
						{selectedItem?.items?.map(item =>
							item.items && item.items.length > 0 ? (
								<div
									role='button'
									tabIndex={0}
									onClick={() => subItemToolbarClicked(item)}
									onKeyDown={event_ => {
										if (event_.key === 'Enter' || event_.key === ' ') {
											toolbarClicked(item)
										}
									}}
									key={item.text}
									className={`m-[2px] mx-3 flex items-center gap-2 px-2 py-1 no-underline hover:rounded-lg hover:bg-gray-600 hover:text-white hover:no-underline ${selectedSubItem?.url?.includes(item.url ?? '') ? 'rounded-lg border border-white bg-gray-800 text-white' : 'text-logo-light'}`}
								>
									<div>
										<FontAwesomeIcon
											icon={faCircle}
											className='text-logo-light hover:text-inherit'
										/>
									</div>
									<div className='p-2 text-base font-semibold text-white'>
										{item.text}
									</div>
								</div>
							) : (
								<Link
									to={item.url ?? ''}
									key={item.text}
									className={`m-[2px] mx-3 flex items-center gap-2 px-2 py-1 no-underline hover:rounded-lg hover:bg-gray-600 hover:text-white hover:no-underline ${pathname.includes(item.url ?? '') || selectedItem.url?.includes(item.url ?? '') ? 'rounded-lg border border-white bg-gray-800 text-white' : 'text-logo-light'}`}
									onClick={handleClose}
								>
									<div>
										<FontAwesomeIcon
											icon={faCircle}
											className='text-logo-light hover:text-inherit'
										/>
									</div>
									<div className='p-2 text-base font-semibold text-white'>
										{item.text}
									</div>
								</Link>
							)
						)}
					</div>
					<div className='flex flex-col justify-center px-3 pb-4'>
						<div className='text-base text-white'>
							{userProfile?.firstName} {userProfile?.lastName}
						</div>
						<div className='text-xs text-gray-400'>{userProfile?.email}</div>
					</div>
				</div>
			</HSDrawer>
			<HSDrawer
				open={isSubDrawerOpen}
				onClose={() => {
					setIsSubDrawerOpen(false)
					setIsOpen(false)
				}}
				className={`bg-gray-600 ${isSubDrawerOpen ? 'left-96' : 'left-0'}`}
				id='subDrawer'
				theme={{
					root: {
						backdrop:
							'fixed inset-0 z-50 bg-transparent dark:bg-transparent left-16'
					}
				}}
			>
				<div className='flex h-full flex-col justify-between'>
					<div>
						<div className='p-5 text-lg font-semibold text-white'>
							{selectedSubItem?.text}
						</div>
						{selectedSubItem?.items?.map(item => (
							<Link
								to={item.url ?? ''}
								key={item.text}
								className={`m-[2px] mx-3 flex items-center gap-2 px-2 py-1 no-underline hover:rounded-lg hover:bg-gray-500 hover:text-white hover:no-underline ${pathname.includes(item.url ?? '') || selectedSubItem.url?.includes(item.url ?? '') ? 'rounded-lg border border-white bg-gray-700 text-white' : 'text-logo-light'}`}
								onClick={handleClose}
							>
								<div>
									<FontAwesomeIcon
										icon={faCircle}
										className='text-logo-light hover:text-inherit'
									/>
								</div>
								<div className='p-2 text-base font-semibold text-white'>
									{item.text}
								</div>
							</Link>
						))}
					</div>
				</div>
			</HSDrawer>
		</>
	)
}

export default Sidebar
