/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import {
	faChevronDown,
	faClock,
	faColumns3,
	faDownload,
	faSackDollar,
	faInfoCircle,
	faPenToSquare
} from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	PresetDirective,
	PresetsDirective
} from '@syncfusion/ej2-react-calendars'
import {
	dateTemplate,
	rfpNameTemplate,
	currencyTemplate
} from '../openPipeline/templates'
import HSButton from 'components/button'
import HSDonutChart from 'components/charts/donutChart'
import HSLegend from 'components/charts/legends'
import HSCurrencyPicker from 'components/currencySelector'
import { But<PERSON> } from 'flowbite-react'
import {
	calculatePeriod,
	formatCurrency,
	periodTypes,
	type ICurrency
} from 'lib/helpers'
import { ProposalRequestStatusMap } from 'lib/helpers/statusMaps'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { useEffect, useState } from 'react'
import { chainColors } from '../../common'
import { useGetChains } from 'lib/services/chains.service'
import { requestGroups } from 'lib/helpers/requestGroups'
import dateFilterStore from '../common/dateFilterStore'
import HSTable from 'components/table'
import CustomizeColumnsModal from 'components/planner/customizeColumnsModal'
import { columnOptions } from 'components/planner/customizeColumnsModal/columnConfig'
import FilterPopover from '../common/filterHelper'
import type { ISetFilters } from '../../helper'
import CostSavingsGrid from './costSavingsGrid'
import { packageWorkbook } from 'lib/services/report.service'
import { toast } from 'react-toastify'
import rawDataFieldsForPlanner from '../columns/columnDefinition'
import Loader from 'components/loader'
import MetricCard from 'components/metric/card'
import HSDateRangePicker from 'components/dateRangePicker'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'

// Header template components
const RfpNameHeaderTemplate = () => (
	<div className='flex items-center gap-2'>
		RFP Name/Code
		<TooltipComponent content='Name of the RFP associated with the cost savings'>
			<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
		</TooltipComponent>
	</div>
)

const OwnerHeaderTemplate = () => (
	<div className='flex items-center gap-2'>
		Owner
		<TooltipComponent content='The RFP owner noted in the RFP'>
			<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
		</TooltipComponent>
	</div>
)

const SentHeaderTemplate = () => (
	<div className='flex items-center gap-2'>
		RFP Sent
		<TooltipComponent content='The date the RFP was sent to hotels'>
			<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
		</TooltipComponent>
	</div>
)

const ContractedHeaderTemplate = () => (
	<div className='flex items-center gap-2'>
		Contracted
		<TooltipComponent content='Date when the status of the RFP changed to contracted'>
			<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
		</TooltipComponent>
	</div>
)

const ProposedHeaderTemplate = () => (
	<div className='flex items-center gap-2'>
		Proposed
		<TooltipComponent content='The initial proposed cost before negotiations or concessions'>
			<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
		</TooltipComponent>
	</div>
)

const NegotiatedHeaderTemplate = () => (
	<div className='flex items-center gap-2'>
		Negotiated
		<TooltipComponent content='Savings achieved through negotiation from proposed to contracted rates'>
			<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
		</TooltipComponent>
	</div>
)

const ConcessionsHeaderTemplate = () => (
	<div className='flex items-center gap-2'>
		Concessions
		<TooltipComponent content='Approved concessions with a monetary value added to the contract'>
			<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
		</TooltipComponent>
	</div>
)

const SavingsHeaderTemplate = () => (
	<div className='flex items-center gap-2'>
		Savings
		<TooltipComponent content='Final contracted rates after negotiations'>
			<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
		</TooltipComponent>
	</div>
)

const ContractValueHeaderTemplate = () => (
	<div className='flex items-center gap-2'>
		Contract Value
		<TooltipComponent content='Total savings realized from negotiated rates and concessions'>
			<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
		</TooltipComponent>
	</div>
)

interface CostSavingsProperties {
	eventPlans: EventPlan[]
	currency: ICurrency
	setFilters: React.Dispatch<React.SetStateAction<ISetFilters>>
	filteredChainIds: string[]
	filters: ISetFilters
	isFeatureLocked: boolean
}
interface Savings {
	savingsType: string
	chainId: string
	proposedValue: number
	contractedValue: number
	savingsValue: number
	concessionType?: string
}

interface IMetrics {
	totalSavings: number
	totalSavingsByType: { name: string; value: number; fill: string }[]
	totalCostSavingsByChain: { name: string; value: number; fill: string }[]
	concessionSavingsByType: { name: string; value: number; fill: string }[]
	averageSavings: number
	negotiatedSavingsByChain: { name: string; value: number; fill: string }[]
	eventPlans: EventPlan[]
}

const CostSavings = (properties: CostSavingsProperties) => {
	const {
		eventPlans,
		currency,
		setFilters,
		filters,
		filteredChainIds,
		isFeatureLocked
	} = properties
	const { data: chains } = useGetChains()
	const [showColumnsModal, setShowColumnsModal] = useState(false)
	const [visibleColumns, setVisibleColumns] = useState<string[]>(columnOptions)
	const { dateFilter, setDateFilter } = dateFilterStore()
	const [metrics, setMetrics] = useState<IMetrics>({
		totalSavings: 0,
		averageSavings: 0,
		totalSavingsByType: [],
		totalCostSavingsByChain: [],
		concessionSavingsByType: [],
		negotiatedSavingsByChain: [],
		eventPlans: []
	})
	const [selectedView, setSelectedView] = useState('negotiations')
	const [isDownloading, setIsDownloading] = useState(false)
	const savingsKeys = {
		rooms: 'rooms',
		meeting: 'meeting',
		concession: 'concession'
	}

	const onClickExport = () => {
		setIsDownloading(true)
		toast.info(
			<div className='flex gap-2'>
				<FontAwesomeIcon
					icon={faClock}
					className='rounded-full bg-yellow-100 p-1 text-yellow-700'
					size='lg'
				/>
				Downloading...
			</div>
		)

		const exportConfiguration = {
			workbookId: 'clientexport',
			packageData: {
				workbookName: 'Cost-Savings',
				sheets: [
					{
						name: 'Cost Savings',
						title: 'Cost Savings',
						rows: metrics.eventPlans
							.sort((a, b) => ((a.name ?? '') > (b.name ?? '') ? 1 : -1))
							.map(item => ({
								values: Object.fromEntries(
									rawDataFieldsForPlanner['cost-savings']
										.filter(column => column.header !== '')
										.map(field => [
											field.header,
											{
												value:
													typeof field.exportFormat === 'function'
														? field.exportFormat(item)
														: item[field.name as keyof EventPlan],
												formatString: field.exportFormatString ?? ''
											}
										])
								)
							}))
					}
				]
			}
		}
		packageWorkbook(exportConfiguration)
			.catch((error: unknown) => console.log(error))
			.finally(() => {
				setIsDownloading(false)
			})
	}

	useEffect(() => {
		let allSavings: Savings[] = []
		const eventPlansWithSavings = eventPlans.map(eventPlan => {
			const proposalRequests =
				eventPlan.proposalRequestStatuses[
					ProposalRequestStatusMap?.ContractSigned?.key ?? ''
				] ?? []
			const proposalRequestsWithSavings = proposalRequests.map(
				proposalRequest => {
					const nullRoomsSavings =
						proposalRequest.rateSetContracted.roomCostPlusPlus === null
					const nullMeetingSavings =
						proposalRequest.rateSetContracted.foodAndBeveragePlusPlus ===
							null &&
						proposalRequest.rateSetContracted.roomRentalPlusPlus === null &&
						proposalRequest.rateSetContracted.serviceChargePlusPlus === null
					const savings = [
						{
							savingsType: savingsKeys.rooms,
							chainId: proposalRequest.chainId ?? 'INDEPENDENT',
							proposedValue: proposalRequest.rateSetProposed.roomCostPlusPlus,
							contractedValue: nullRoomsSavings
								? null
								: proposalRequest.rateSetContracted.roomCostPlusPlus,
							savingsValue: nullRoomsSavings
								? null
								: (proposalRequest.rateSetProposed.roomCostPlusPlus ?? 0) -
									(proposalRequest.rateSetContracted?.roomCostPlusPlus ?? 0)
						},
						{
							savingsType: savingsKeys.meeting,
							chainId: proposalRequest.chainId ?? 'INDEPENDENT',
							proposedValue:
								(proposalRequest.rateSetProposed.foodAndBeveragePlusPlus ?? 0) +
								(proposalRequest.rateSetProposed.roomRentalPlusPlus ?? 0) +
								+Number(proposalRequest.rateSetProposed.serviceChargePlusPlus),
							contractedValue: nullMeetingSavings
								? null
								: Number(
										proposalRequest.rateSetContracted.foodAndBeveragePlusPlus
									) +
									Number(proposalRequest.rateSetContracted.roomRentalPlusPlus) +
									Number(
										proposalRequest.rateSetContracted.serviceChargePlusPlus
									),
							savingsValue: nullMeetingSavings
								? null
								: Number(
										proposalRequest.rateSetProposed.foodAndBeveragePlusPlus
									) +
									Number(proposalRequest.rateSetProposed.roomRentalPlusPlus) +
									Number(
										proposalRequest.rateSetProposed.serviceChargePlusPlus
									) -
									(Number(
										proposalRequest.rateSetContracted.foodAndBeveragePlusPlus
									) +
										Number(
											proposalRequest.rateSetContracted.roomRentalPlusPlus
										) +
										Number(
											proposalRequest.rateSetContracted.serviceChargePlusPlus
										))
						},
						...proposalRequest.concessionValues.map(concessionValue => ({
							savingsType: savingsKeys.concession,
							chainId: proposalRequest.chainId ?? 'INDEPENDENT',
							...concessionValue
						}))
					]
					allSavings = [
						...allSavings,
						...savings.map(s => ({ ...s, id: proposalRequest.id }))
					]
					return {
						...proposalRequest,
						savings
					}
				}
			)
			const savings = proposalRequestsWithSavings.reduce((a: Savings[], c) => {
				// eslint-disable-next-line unicorn/no-array-for-each
				c.savings.forEach(saving => {
					const item = a.find((event: Savings) => {
						if (
							saving.savingsType === savingsKeys.concession &&
							'concessionType' in saving
						) {
							return (
								event.savingsType === saving.savingsType &&
								event.concessionType === saving.concessionType
							)
						}
						return saving.savingsType === event.savingsType
					})
					if (item) {
						if ('proposedValue' in saving) {
							item.proposedValue += saving.proposedValue ?? 0
						}
						item.contractedValue += saving.contractedValue ?? 0
						item.savingsValue += saving.savingsValue ?? 0
					} else {
						a.push(saving)
					}
				})
				return a
			}, [])
			const values = savings.reduce(
				(a, c) => {
					if (c.savingsType === savingsKeys.concession) {
						a.concessionsValue += c.savingsValue
					} else {
						if (c.contractedValue !== null) a.hasContractedValue = true
						a.proposedValue += c.proposedValue
						a.contractedValue += c.contractedValue ?? 0
					}
					a.savingsValue =
						a.proposedValue - a.contractedValue + a.concessionsValue
					return a
				},
				{
					hasContractedValue: false,
					proposedValue: 0,
					contractedValue: 0,
					concessionsValue: 0,
					savingsValue: 0
				}
			)
			return {
				...eventPlan,
				savings,
				proposalRequestsWithSavings,
				...values
			}
		})
		const totalSavings = eventPlansWithSavings.reduce(
			(s, sc) => s + sc.savings.reduce((a, c) => a + (c.savingsValue ?? 0), 0),
			0
		)
		setMetrics({
			totalSavings,
			averageSavings: totalSavings / eventPlansWithSavings.length,
			totalSavingsByType: [
				{
					name: 'Negotiations',
					value: eventPlansWithSavings.reduce(
						(a, c) =>
							a +
							c.savings
								.filter(s => s.savingsType !== savingsKeys.concession)
								.reduce((s, sc) => s + (sc.savingsValue ?? 0), 0),
						0
					),
					fill: '#027587'
				},
				{
					name: 'Concessions',
					value: eventPlansWithSavings.reduce(
						(a, c) =>
							a +
							c.savings
								.filter(s => s.savingsType === savingsKeys.concession)
								.reduce((s, sc) => s + (sc.savingsValue ?? 0), 0),
						0
					),
					fill: '#95D9D0'
				}
			] as { name: string; value: number; fill: string }[],
			totalCostSavingsByChain: allSavings
				.reduce((a: { chainId: string; value: number }[], c) => {
					const item = a.find(event => event.chainId === c.chainId)
					if (item) {
						item.value += c.savingsValue ?? 0
					} else {
						a.push({
							chainId: c.chainId,
							value: c.savingsValue
						})
					}
					return a
				}, [])
				.map((savings, index: number) => {
					const chain = chains?.find(c => c.id === savings.chainId)
					return {
						name: chain?.name ?? 'Independent',
						value: savings.value,
						fill: chainColors[index]
					}
				}),
			concessionSavingsByType: eventPlansWithSavings
				.flatMap(eventPlan =>
					eventPlan.proposalRequestsWithSavings.flatMap(proposalRequest =>
						proposalRequest.savings.filter(
							s => s.savingsType === savingsKeys.concession
						)
					)
				)
				.reduce((a: { concessionType: string; value: number }[], c) => {
					const item = a.find(
						event => event.concessionType === c.concessionType
					)
					if (item) {
						item.value += c.savingsValue ?? 0
					} else {
						a.push({
							concessionType: c.concessionType,
							value: c.savingsValue
						})
					}
					return a
				}, [])
				.map((savings, index) => ({
					name: requestGroups[savings.concessionType]?.name || 'Other',
					value: savings.value,
					fill: chainColors[index]
				})),
			negotiatedSavingsByChain: allSavings
				.filter(s => s.savingsType !== savingsKeys.concession)
				.reduce((a: { chainId: string; value: number }[], c) => {
					const item = a.find(event => event.chainId === c.chainId)
					if (item) {
						item.value += c.savingsValue ?? 0
					} else {
						a.push({
							chainId: c.chainId,
							value: c.savingsValue ?? 0
						})
					}
					return a
				}, [])
				.map((savings: { chainId: string; value: number }, index) => {
					const chain = chains?.find(c => c.id === savings.chainId)
					return {
						name: chain?.name ?? 'Independent',
						value: savings.value,
						fill: chainColors[index]
					}
				}),
			eventPlans: eventPlansWithSavings
		})
	}, [
		chains,
		eventPlans,
		savingsKeys.concession,
		savingsKeys.meeting,
		savingsKeys.rooms
	])

	const transformedSavingsData = metrics.totalSavingsByType.map(
		(s: { name: string; value: number; fill: string }) => ({
			...s,
			label: s.name,
			formattedValue: formatCurrency(s.value, currency),
			color: s.name === 'Negotiations' ? '#AFECEF' : '#FCD9BD'
		})
	)

	const totalValue = transformedSavingsData.reduce(
		(sum, item) => sum + item.value,
		0
	)

	const onDateChangeFilter = (dates: Date[] | null) => {
		if (dates) {
			setFilters(previous => ({
				...previous,
				dateFilter: {
					...previous.dateFilter,
					startDate: new Date(dates[0]).toISOString().split('.')[0],
					endDate: new Date(dates[1]).toISOString().split('.')[0]
				}
			}))

			setDateFilter({
				startDate: new Date(dates[0]).toISOString().split('.')[0],
				endDate: new Date(dates[1]).toISOString().split('.')[0]
			})
		}
	}

	const formatContractedValue = (item: EventPlan) => {
		if (item.hasContractedValue) {
			return formatCurrency(item.contractedValue, currency)
		}
		return (
			<HSButton
				size='sm'
				color='light'
				onClick={() => {
					globalThis.location.href = `/planner/event/${item.id}/contract/cost-savings`
				}}
			>
				<div className='flex items-center gap-2 text-primary-600'>
					<FontAwesomeIcon icon={faPenToSquare} />
					<span>Edit</span>
				</div>
			</HSButton>
		)
	}

	return (
		<>
			<div
				className='flex flex-col gap-2'
				style={{ maxHeight: 'calc(100vh - 8rem)' }}
			>
				<div className='border-b px-6 py-4'>
					<div className='text-xl font-semibold text-gray-900'>
						Cost Savings
					</div>
					<div className='flex flex-row justify-between gap-2'>
						<div className='max-w-[600px] text-sm font-normal text-gray-500'>
							Quantify your negotiation wins and identify strategic
							opportunities to further reduce expenses across your bookings
						</div>
						<div className='flex items-center gap-4'>
							<div className='flex items-center gap-2'>
								<HSCurrencyPicker
									value={currency}
									onChange={value => {
										setFilters(previous => ({
											...previous,
											currency: value
										}))
									}}
									color='light'
									icon={faChevronDown}
								/>
							</div>
							<FilterPopover
								filters={filters}
								setFilters={setFilters}
								chains={chains}
								filteredChainIds={filteredChainIds}
							/>
							<div className='w-80'>
								<HSDateRangePicker
									placeholder='Select Date Range'
									onChange={event => {
										if (event.value) {
											onDateChangeFilter(event.value)
										}
									}}
									value={[
										new Date(dateFilter.startDate),
										new Date(dateFilter.endDate)
									]}
									format='MMM dd, yyyy'
								>
									<PresetsDirective>
										{Object.keys(periodTypes).map(period => {
											const {
												type: { label, key },
												startDate,
												endDate
											} = calculatePeriod(period)
											return (
												<PresetDirective
													key={key}
													label={label}
													start={new Date(startDate)}
													end={new Date(endDate)}
												/>
											)
										})}
									</PresetsDirective>
								</HSDateRangePicker>
							</div>
						</div>
					</div>
				</div>
				<div className='px-4 py-6'>
					<div className='flex flex-row gap-6'>
						<div className='w-1/4 rounded-2xl border border-gray-200 bg-white p-8 shadow-sm'>
							<div className='space-y-12'>
								<div>
									<div className='mb-6 text-sm font-bold text-gray-500'>
										Total savings
									</div>
									<MetricCard isLocked={isFeatureLocked} lockMessage=''>
										<div className='flex items-center gap-4'>
											<div className='flex h-6 w-6 items-center justify-center rounded-lg bg-green-100'>
												<FontAwesomeIcon
													icon={faSackDollar}
													className='text-green-600'
													size='sm'
												/>
											</div>
											<span className='text-2xl font-semibold text-gray-900'>
												{formatCurrency(metrics.totalSavings, currency)}
											</span>
										</div>
									</MetricCard>
								</div>

								<div className='border-t pt-8'>
									<div className='mb-6 text-sm font-bold text-gray-500'>
										Average savings
									</div>
									<MetricCard isLocked={isFeatureLocked} lockMessage=''>
										<div className='flex items-center gap-4'>
											<div className='flex h-6 w-6 items-center justify-center rounded-lg bg-gray-100'>
												<FontAwesomeIcon
													icon={faSackDollar}
													className='text-gray-600'
													size='sm'
												/>
											</div>
											<div className='flex items-center gap-2'>
												<span className='text-2xl font-semibold text-gray-900'>
													{formatCurrency(metrics.averageSavings, currency)}
												</span>
												<span className='text-sm text-gray-500'>
													per booking
												</span>
											</div>
										</div>
									</MetricCard>
								</div>
							</div>
						</div>

						<div className='card flex h-80 flex-col'>
							<div className='flex w-full flex-row items-center justify-between border-b px-4 py-4'>
								<div className='text-base font-medium text-gray-700'>
									Savings Details
								</div>
								<div className='flex flex-row items-center gap-4'>
									<div className='text-sm font-medium text-gray-900'>
										Savings Type
									</div>
									<div>
										<Button.Group className='w-fit'>
											<HSButton
												color='gray'
												className='flex-1 rounded-r-none'
												onClick={() => setSelectedView('concessions')}
											>
												Concessions
											</HSButton>
											<HSButton
												color='gray'
												className='rounded-l-none'
												onClick={() => setSelectedView('negotiations')}
											>
												Negotiations
											</HSButton>
										</Button.Group>
									</div>
								</div>
							</div>
							<div className='flex flex-row justify-between gap-4'>
								<MetricCard isLocked={isFeatureLocked} lockMessage=''>
									<div className='ml-6 flex flex-col gap-2 p-6'>
										<div className='flex items-center gap-6 p-2'>
											{selectedView === 'concessions' ? (
												<HSDonutChart
													data={transformedSavingsData.map(item => ({
														name: item.name,
														value: item.value,
														color: item.fill
													}))}
													label={
														totalValue > 0
															? Math.round(
																	(transformedSavingsData[0].value /
																		totalValue) *
																		100
																)
															: 0
													}
												/>
											) : (
												<HSDonutChart
													data={metrics.totalCostSavingsByChain
														.sort((c, n) => n.value - c.value)
														.map(item => ({
															name: item.name,
															value: item.value,
															color: item.fill
														}))}
													showTooltip
												/>
											)}
											<div className='flex flex-col gap-2 p-4'>
												{selectedView === 'concessions' ? (
													<HSLegend
														data={transformedSavingsData.map(item => ({
															color: item.color,
															label: `${item.label} (${item.formattedValue})`
														}))}
													/>
												) : (
													<HSLegend
														data={metrics.totalCostSavingsByChain
															.sort((c, n) => n.value - c.value)
															.map(item => ({
																color: item.fill,
																label: `${item.name} (${formatCurrency(item.value, currency)})`
															}))}
													/>
												)}
											</div>
										</div>
									</div>
								</MetricCard>
								<div className='disabled-rounded-border w-1/2 border-l'>
									<CostSavingsGrid
										data={
											selectedView === 'concessions'
												? metrics.concessionSavingsByType
														.sort(
															(a: { value: number }, b: { value: number }) =>
																b.value - a.value
														)
														.slice(0, 10)
														.map(
															(savingsItem: {
																name: string
																value: number
															}) => ({
																label: savingsItem.name,
																formattedValue: formatCurrency(
																	savingsItem.value,
																	currency
																),
																value: savingsItem.value
															})
														)
												: metrics.negotiatedSavingsByChain
														.sort(
															(a: { value: number }, b: { value: number }) =>
																b.value - a.value
														)
														.slice(0, 10)
														.map(
															(savingsItem: {
																name: string
																value: number
															}) => ({
																label: savingsItem.name,
																formattedValue: formatCurrency(
																	savingsItem.value,
																	currency
																),
																value: savingsItem.value
															})
														)
										}
										currency={currency}
										isFeatureLocked={isFeatureLocked}
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className='px-4 py-6'>
					<div className='flex items-center justify-between'>
						<div className='text-sm font-medium text-gray-900'>
							{eventPlans.length} RFP{}
							{eventPlans.length > 1 ? 's' : ''}
						</div>
						<div className='flex items-center gap-4'>
							<HSButton
								color='light'
								onClick={onClickExport}
								disabled={!eventPlans?.length}
							>
								{isDownloading ? (
									<Loader size='sm' />
								) : (
									<FontAwesomeIcon icon={faDownload} />
								)}
							</HSButton>
							<HSButton color='light' onClick={() => setShowColumnsModal(true)}>
								<FontAwesomeIcon icon={faColumns3} />
							</HSButton>
						</div>
					</div>
				</div>
				<div className='px-4 pb-6'>
					<HSTable
						rows={metrics.eventPlans.map((eventPlan: EventPlan) => ({
							...eventPlan,
							negotiatedValue:
								Number(eventPlan.proposedValue) -
								Number(eventPlan.contractedValue)
						}))}
						allowPaging
						columns={[
							{
								field: 'name',
								headerText: 'RFP Name/Code',
								headerTemplate: RfpNameHeaderTemplate,
								render: rfpNameTemplate,
								clipMode: 'ellipsis',
								width: 250,
								sortable: true,
								freeze: 'left',
								visible: visibleColumns.includes('RFP Name/Code')
							},
							{
								field: 'ownerName',
								headerText: 'Owner',
								headerTemplate: OwnerHeaderTemplate,
								clipMode: 'ellipsis',
								width: 200,
								sortable: true,
								visible: visibleColumns.includes('Assigned')
							},
							{
								field: 'submitted',
								headerText: 'RFP Sent',
								headerTemplate: SentHeaderTemplate,
								render: (item: EventPlan) => dateTemplate(item.submitted),
								clipMode: 'ellipsis',
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Sent')
							},
							{
								field: 'firstContractSigned',
								headerText: 'Contract',
								headerTemplate: ContractedHeaderTemplate,
								render: (item: EventPlan) =>
									dateTemplate(item.firstContractSigned),
								clipMode: 'ellipsis',
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Contract Signed')
							},
							{
								field: 'proposedValue',
								headerText: 'Proposed',
								headerTemplate: ProposedHeaderTemplate,
								render: (item: EventPlan) =>
									currencyTemplate(item.proposedValue, currency),
								clipMode: 'ellipsis',
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Proposed')
							},
							{
								field: 'negotiatedValue',
								headerText: 'Negotiated',
								headerTemplate: NegotiatedHeaderTemplate,
								clipMode: 'ellipsis',
								render: (item: EventPlan) =>
									formatCurrency(
										item.hasContractedValue ? item.negotiatedValue : 0,
										currency
									),
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Negotiated')
							},
							{
								field: 'concessionsValue',
								headerText: 'Concessions',
								headerTemplate: ConcessionsHeaderTemplate,
								clipMode: 'ellipsis',
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Concessions')
							},
							{
								field: 'savingsValue',
								headerText: 'Savings',
								headerTemplate: SavingsHeaderTemplate,
								clipMode: 'ellipsis',
								render: (item: EventPlan) => (
									<div className='font-medium text-green-600'>
										{formatCurrency(
											item.hasContractedValue
												? item.savingsValue
												: item.concessionsValue,
											currency
										)}
									</div>
								),
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Savings')
							},
							{
								field: 'contractedValue',
								headerText: 'Contract Value',
								headerTemplate: ContractValueHeaderTemplate,
								clipMode: 'ellipsis',
								width: 150,
								sortable: true,
								render: formatContractedValue,
								visible: visibleColumns.includes('Contracted'),
								freeze: 'right'
							}
						]}
					/>
				</div>
			</div>
			<CustomizeColumnsModal
				show={showColumnsModal}
				onClose={() => setShowColumnsModal(false)}
				context='costSavings'
				selectedColumns={visibleColumns}
				onColumnsChange={setVisibleColumns}
			/>
		</>
	)
}

export default CostSavings
