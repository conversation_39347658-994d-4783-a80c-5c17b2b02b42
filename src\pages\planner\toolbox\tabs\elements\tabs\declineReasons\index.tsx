import Loader from 'components/loader'
import { useLoadDeclinedReasons } from 'lib/services/organizations.service'
import { useEffect, useMemo, useState } from 'react'
import { useParams } from 'react-router-dom'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { declinedReasons } from 'lib/helpers/declinedReasons'
import HSTextField from 'components/textField'
import HSIcon from 'components/HSIcon'
import { faPlus, faSearch } from '@fortawesome/pro-light-svg-icons'
import HSButton from 'components/button'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSToggleSwitch from 'components/toggleSwitch'
import HSTable from 'components/table'
import AddNewDeclineReason from 'pages/organization/declinereasons/addNewDeclineReason'
import plannerOrganizationStore from 'lib/store/plannerOrganizationStore'

interface DeclinedReasonItem {
	text: string | null
	disabled: boolean | null
	usage: number
}

const PlannerDeclineReasons = ({ isLoading }: { isLoading: boolean }) => {
	const { organizationId } = useParams()
	const { data, isFetching } = useLoadDeclinedReasons(
		organizationId ?? '',
		!!organizationId
	)
	const { userProfile } = useUserProfileContext()
	const { setOrganization, organization } = plannerOrganizationStore()

	const [globalSearch, setGlobalSearch] = useState('')
	const [mergedReasons, setMergedReasons] = useState<DeclinedReasonItem[]>([])
	const [declineReasonContext, setDeclineReasonContext] = useState<{
		declineReasonToEdit: {
			text: string
			isEnabled: boolean
		}
		showAddModal: boolean
	}>()

	const mappedDeclinedReasons = useMemo(() => {
		if (organization?.declinedReasons && data) {
			const customReasonStrings = new Set(
				organization.declinedReasons.map(r => r.text)
			)

			const result: DeclinedReasonItem[] = [
				...declinedReasons.Planner.filter(
					r => !customReasonStrings.has(r.reason ?? '')
				).map(r => ({
					text: r.reason,
					disabled: false,
					usage: 0 // Default value for usage
				})),
				...organization.declinedReasons.map(r => ({
					text: r.text,
					disabled: r.disabled,
					usage: 0
				}))
			]

			return result.map(r => ({
				...r,
				usage: data[r.text ?? ''] ?? 0
			}))
		}
		return []
	}, [data, organization?.declinedReasons])

	useEffect(() => {
		let result = mappedDeclinedReasons
		if (globalSearch !== '') {
			result = result.filter(r =>
				r.text?.toLowerCase().includes(globalSearch.toLowerCase())
			)
		}
		setMergedReasons(result)
	}, [globalSearch, mappedDeclinedReasons])

	const formatUsage = (item: DeclinedReasonItem) => {
		if (data && item.text) {
			const usageCount = data[item.text]
			return (
				<div className='text-sm font-normal text-gray-600'>{`${usageCount || 0} proposal${usageCount === 1 ? '' : 's'}`}</div>
			)
		}
		return ''
	}

	const onToggle = (item: DeclinedReasonItem, enabled: boolean) => {
		const existingReasons = organization?.declinedReasons
			? organization.declinedReasons.filter(r => r.text !== item.text)
			: []
		const declinedReasonsValue = [
			...existingReasons,
			{
				text: item.text,
				disabled: !enabled,
				created: new Date(),
				createdBy: userProfile?.id
			}
		]
		setOrganization({
			type: 'setProperty',
			name: 'declinedReasons',
			value: {
				value: declinedReasonsValue
			}
		})
	}

	const formatSwitch = (item: DeclinedReasonItem) => (
		<div className='flex items-center justify-center'>
			<HSToggleSwitch
				id='checked'
				checked={!item.disabled}
				onChange={(checked: boolean) => {
					onToggle(item, checked)
				}}
			/>
		</div>
	)

	return (
		<>
			<div>
				{isFetching || isLoading ? (
					<Loader />
				) : (
					<div className='flex flex-col gap-2'>
						<div className='flex items-center justify-between pb-2'>
							<div className='text-sm font-medium text-gray-900'>
								{mergedReasons.length} Decline Reason
								{mergedReasons.length > 1 ? 's' : ''}
							</div>
							<div className='flex items-center gap-4'>
								<div className='w-80'>
									<HSTextField
										icon={HSIcon(faSearch)}
										placeholder='Search '
										showClearButton
										value={globalSearch}
										onChange={event => setGlobalSearch(event.target.value)}
									/>
								</div>
								<HSButton
									onClick={() => {
										setDeclineReasonContext({
											declineReasonToEdit: { text: '', isEnabled: true },
											showAddModal: true
										})
									}}
								>
									<div className='flex items-center gap-2'>
										<FontAwesomeIcon icon={faPlus} />
										Add New
									</div>
								</HSButton>
							</div>
						</div>
						<HSTable
							rows={mergedReasons}
							defaultSort={{
								field: 'text',
								direction: 'asc'
							}}
							columns={[
								{
									field: 'text',
									headerText: 'Reason',
									sortable: true
								},
								{
									field: 'usage',
									headerText: 'Usage',
									render: formatUsage,
									sortable: true,
									width: 150
								},
								{
									field: 'disabled',
									headerText: 'Enable/Disable',
									render: formatSwitch,
									headerAlign: 'center',
									width: 150
								}
							]}
						/>
					</div>
				)}
			</div>
			{declineReasonContext?.showAddModal && organization ? (
				<AddNewDeclineReason
					open={declineReasonContext.showAddModal}
					onClose={() => {
						setDeclineReasonContext({
							declineReasonToEdit: { text: '', isEnabled: true },
							showAddModal: false
						})
					}}
					setOrganization={setOrganization}
					organization={organization}
					declineReasonToEdit={declineReasonContext.declineReasonToEdit}
				/>
			) : null}
		</>
	)
}

export default PlannerDeclineReasons
