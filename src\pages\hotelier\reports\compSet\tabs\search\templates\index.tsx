import { faTrophy } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { getOrdinalSuffix } from 'lib/helpers'
import { engagementTypes } from 'lib/helpers/contentEngagement'
import type { IEngagementMetric } from 'lib/services/contentEngagement.service'
import type { Venue } from 'models/venue'

export interface IRowTemplate {
	id: string | null
	name: string | null
	engagementMetrics: Record<string, IEngagementMetric> | undefined
	rank?: number | null
}

export const rankTemplate = (
	row: IRowTemplate & { index: string },
	hotel: Venue | undefined
) => (
	<div
		className={`${hotel?.id === row.id ? 'font-bold' : ''} flex items-center gap-2 text-sm text-gray-600`}
	>
		{getOrdinalSuffix(Number(row.rank))}
		{hotel?.id === row.id && row.rank === 1 ? (
			<FontAwesomeIcon className='text-primary-500' icon={faTrophy} />
		) : null}
	</div>
)

export const hotelTemplate = (row: IRowTemplate, hotel: Venue | undefined) => (
	<div
		className={`${hotel?.id === row.id ? 'font-bold' : ''} text-sm text-gray-600`}
	>
		{row.id === hotel?.id ? 'My Hotel' : row.name}
	</div>
)

export const totalTemplate = (
	row: IRowTemplate & { index: string },
	hotel: Venue | undefined
) => (
	<div
		className={`${hotel?.id === row.id ? 'font-bold' : ''} text-sm text-gray-600`}
	>
		{Number(row.engagementMetrics?.[engagementTypes.impression]?.total) || 0}
	</div>
)

export const addedFromSearchTemplate = (
	row: IRowTemplate & { index: string },
	hotel: Venue | undefined
) => (
	<div
		className={`${hotel?.id === row.id ? 'font-bold' : ''} text-sm text-gray-600`}
	>
		{Number(row.engagementMetrics?.[engagementTypes.conversion]?.total) || 0}
	</div>
)
