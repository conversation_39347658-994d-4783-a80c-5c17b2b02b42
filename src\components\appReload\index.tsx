import HSButton from 'components/button'
import HSModal from 'components/modal'
import { Modal } from 'flowbite-react'
import React from 'react'

const AppReload = () => {
	const [show, setShow] = React.useState(false)

	globalThis.addEventListener('vite:preloadError', () => {
		setShow(true)
	})

	return (
		<HSModal
			size='md'
			openModal={show}
			onClose={() => setShow(false)}
			header='Application Update Available'
			hideCloseButton
		>
			<Modal.Body>
				<p className='text-lg font-semibold'>A new version is available.</p>
				<p className='text-sm'>
					Please reload the page to update to the latest version.
				</p>
			</Modal.Body>
			<Modal.Footer>
				<div className='flex grow items-center justify-center'>
					<HSButton
						onClick={() => {
							globalThis.location.reload()
							setShow(false)
						}}
						color='cyan'
						className='grow'
					>
						Reload Now
					</HSButton>
				</div>
			</Modal.Footer>
		</HSModal>
	)
}

export default AppReload
