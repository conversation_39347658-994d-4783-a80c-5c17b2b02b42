/* eslint-disable react/no-array-index-key */
import type { IMeetingRoom } from 'models/venue'
import { roomLayoutOptions } from 'lib/helpers/roomLayouts'
import { formatNumber } from 'lib/helpers'

export const renderDimensions = (item: IMeetingRoom) => (
	<div className='text-sm font-normal text-gray-600'>
		{item.length}&apos; x {item.width}&apos;
	</div>
)

export const renderMaxCapacity = (item: IMeetingRoom) => {
	const maxCapacity = item.layouts?.reduce(
		(a, c) => (a.capacity > c.capacity ? a : c),
		{ layoutStyle: '', capacity: 0 }
	)
	const maxCapacityLayoutName =
		roomLayoutOptions[maxCapacity?.layoutStyle ?? '']?.name || null
	return maxCapacityLayoutName && maxCapacity?.capacity ? (
		<div className='text-sm font-normal text-gray-600'>
			{formatNumber(maxCapacity.capacity)} {maxCapacityLayoutName}
		</div>
	) : (
		'-'
	)
}

export const renderRoomSizeHeader = () => (
	<div className='text-xs font-semibold text-gray-500'>
		Room Size (FT<sup>2</sup>)
	</div>
)

export const renderCeilingHeightHeader = () => (
	<div className='text-xs font-semibold text-gray-500'>Ceiling Height (ft)</div>
)

export const renderCapacity = (item: IMeetingRoom, layoutKey: string) => {
	const capacity = item.layouts?.find(
		l => l.layoutStyle === layoutKey
	)?.capacity
	return (
		<div className='text-sm font-normal text-gray-600'>
			<div
				className='layout-cell'
				title={
					capacity
						? ''
						: 'Capacity details for this room layout are not available'
				}
			>
				{capacity ? (
					<div className='flex items-center gap-1'>
						<span className='value'>{formatNumber(capacity)}</span>
						<span className='units'>
							{capacity === 1 ? 'Person' : 'People'}
						</span>
					</div>
				) : (
					<span>-</span>
				)}
			</div>
		</div>
	)
}

export const renderTheatre = (
	item: IMeetingRoom,
	layouts: {
		count: number
		items: Record<string, string>
	}
) => (
	<div className='text-sm font-normal text-gray-600'>
		{Object.keys(layouts.items).map((k, ki) => {
			const capacity =
				item.layouts?.find(l => l.layoutStyle === k)?.capacity ?? null
			return (
				<div
					key={ki}
					className='layout-cell'
					title={
						capacity
							? ''
							: 'Capacity details for this room layout are not available'
					}
				>
					{capacity ? (
						<>
							<span className='value'>{formatNumber(capacity)}</span>
							<span className='units'>&nbsp;ppl</span>
						</>
					) : (
						<span>-</span>
					)}
				</div>
			)
		})}
	</div>
)
