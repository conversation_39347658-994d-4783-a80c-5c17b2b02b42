/* eslint-disable react/no-array-index-key */
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import type { ProposalRequest } from 'models/proposalResponseMonitor'
import { useState } from 'react'
import { faCircleExclamation } from '@fortawesome/pro-regular-svg-icons'
import HSCheckbox from 'components/checkbox'
import HSTextArea from 'components/textarea'

interface RemoveProposalRequestModalProperties {
	proposalRequests: ProposalRequest[]
	onClose: () => void
	onConfirm: () => void
}

const removeReasons = [
	'The event location has changed',
	'The event requirements have changed and the hotel no longer fits our needs',
	'The event budget has changed and the hotel no longer fits within our budget',
	'Response time',
	'Other'
]

const RemoveProposalRequestDrawer = (
	properties: RemoveProposalRequestModalProperties
) => {
	const { onClose, onConfirm, proposalRequests } = properties
	const [reason, setReason] = useState('')
	const [removedComment, setRemovedComment] = useState('')
	const [internalComment, setInternalComment] = useState('')

	return (
		<HSDrawer open onClose={onClose}>
			<Drawer.Header title='Remove Hotel' titleIcon={() => null} />
			<Drawer.Items className='flex flex-col gap-6'>
				<div className='bg-gray-200 p-4'>
					<div className='flex items-center justify-between gap-4'>
						<div className='text-sm font-medium text-gray-900'>
							Hotels to Remove
						</div>
						<div className='text-sm font-normal text-gray-600'>
							{proposalRequests.map(pr => pr.venueName).join(',')}
						</div>
					</div>
				</div>
				<div className='flex flex-col'>
					<div className='flex items-center gap-2'>
						<div className='text-sm font-medium text-gray-900'>
							Please select a reason why you are removing this hotel
						</div>
						{removedComment === '' ? (
							<FontAwesomeIcon
								icon={faCircleExclamation}
								className='text-red-600'
							/>
						) : null}
					</div>
					<div className='flex flex-col gap-1'>
						{removeReasons.map((r, index) => (
							<div className='flex items-center gap-2' key={index}>
								<HSCheckbox
									checked={r === reason}
									onClick={() => {
										if (r === reason) {
											setReason('')
										} else {
											setReason(r)
										}
									}}
								/>
								<div className='text-sm font-normal text-gray-700'>{r}</div>
							</div>
						))}
					</div>
				</div>
				<div className='flex flex-col'>
					<div className='text-sm font-medium text-gray-900'>
						Comments, visible to Hotel (optional)
					</div>
					<HSTextArea
						color='light'
						value={removedComment}
						onChange={event => setRemovedComment(event.target.value)}
						rows={7}
						placeholder='Write comments here...'
					/>
				</div>
				<div className='flex flex-col'>
					<div className='text-sm font-medium text-gray-900'>
						Internal comment (optional)
					</div>
					<HSTextArea
						color='light'
						value={internalComment}
						onChange={event => setInternalComment(event.target.value)}
						rows={7}
						placeholder='Write comments here...'
					/>
				</div>
			</Drawer.Items>
			<div className='flex grow items-center justify-between gap-4'>
				<HSButton color='light' onClick={onClose} className='grow'>
					Cancel
				</HSButton>
				<HSButton className='grow' onClick={onConfirm}>
					Remove Hotel{proposalRequests.length > 1 ? 's' : ''}
				</HSButton>
			</div>
		</HSDrawer>
	)
}

export default RemoveProposalRequestDrawer
