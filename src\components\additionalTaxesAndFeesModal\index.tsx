/* eslint-disable react/boolean-prop-naming */
import HSButton from 'components/button'
import HSCheckbox from 'components/checkbox'
import H<PERSON>rawer from 'components/drawer'
import HSRadioButton from 'components/radioButton'
import <PERSON><PERSON>ext<PERSON>ield from 'components/textField'
import HSText<PERSON>rea from 'components/textarea'
import { Drawer } from 'flowbite-react'
import { decimalMask, type ICurrency } from 'lib/helpers'
import {
	additionalTaxTypes,
	defaultAdditionalTax
} from 'lib/helpers/additionalTaxesAndFees'
import { useAddAdditionalTaxesAndFees } from 'lib/services/hotels.service'
import { startCase } from 'es-toolkit'
import type { IAdditionalTaxesAndFees } from 'models/venue'
import { useEffect, useState } from 'react'

interface IAdditionalTaxesAndFeesModal {
	show: boolean
	onHide: () => void
	onSave: (item: IAdditionalTaxesAndFees) => void
	venueId: string | null
	additionalTax: IAdditionalTaxesAndFees
	isProfile: boolean
	currency: ICurrency | undefined
}

const AdditionalTaxesAndFeesModal = (
	properties: IAdditionalTaxesAndFeesModal
) => {
	const { additionalTax, currency, isProfile, onHide, onSave, show, venueId } =
		properties

	const { mutateAsync: addAdditionalTaxesAndFees } =
		useAddAdditionalTaxesAndFees()

	const [formInvalid, setFormInvalid] = useState(true)
	const [additionalTaxLocal, setAdditionalTaxLocal] =
		useState<IAdditionalTaxesAndFees>({
			...defaultAdditionalTax
		})
	const [saveToProfile, setSaveToProfile] = useState(true)

	useEffect(() => {
		setFormInvalid(
			!additionalTaxLocal.name ||
				!additionalTaxLocal.label ||
				!additionalTaxLocal.description ||
				!additionalTaxLocal.type ||
				additionalTaxLocal.rate === null ||
				Number.isNaN(additionalTaxLocal.rate)
		)
	}, [additionalTaxLocal])

	useEffect(() => {
		setAdditionalTaxLocal({ ...additionalTax })
	}, [additionalTax])

	const onChange = (name: string, value: number | string | null) => {
		setAdditionalTaxLocal(s => ({
			...s,
			[name]: value
		}))
	}

	const onClickSave = async () => {
		if (!isProfile && saveToProfile) {
			await addAdditionalTaxesAndFees({
				hotelId: venueId ?? '',
				additionalTax: additionalTaxLocal
			})
		}
		onSave({ ...additionalTaxLocal })
	}

	if (!show) {
		return null
	}

	return (
		<div>
			<HSDrawer
				data-testid='additionalTaxesAndFeesModal'
				onClose={onHide}
				open
				position='right'
				style={{ width: '400px' }}
			>
				<Drawer.Header title='Add New Tax/Fee' titleIcon={() => null} />
				<Drawer.Items
					className='overflow-auto'
					style={{ height: 'calc(100% - 6rem)' }}
				>
					<div className='flex flex-col gap-6'>
						<div className='flex flex-col gap-4'>
							<HSTextField
								data-testid='name'
								label='Name'
								value={additionalTaxLocal.name ?? ''}
								onChange={event => onChange('name', event.target.value)}
								isInvalid={!additionalTaxLocal.name}
							/>
							<HSTextField
								data-testid='label'
								label='Label'
								value={additionalTaxLocal.label ?? ''}
								onChange={event => onChange('label', event.target.value)}
								isInvalid={!additionalTaxLocal.label}
							/>
							<HSTextArea
								data-testid='description'
								label='Description'
								value={additionalTaxLocal.description ?? ''}
								rows={7}
								color='light'
								onChange={event => onChange('description', event.target.value)}
								isInvalid={!additionalTaxLocal.description}
							/>
						</div>
						<div className='border-b border-gray-200' />
						<div className='flex flex-col gap-4'>
							<div className='flex flex-col gap-2'>
								<div>Type</div>
								<HSRadioButton
									label='Percentage of Room Rate'
									name='type'
									value={additionalTaxTypes.percent}
									selectedValue={additionalTaxLocal.type ?? ''}
									onChange={event => onChange('type', event.target.value)}
								/>
								<HSRadioButton
									label='Flat Rate per Room'
									name='type'
									value={additionalTaxTypes.flat}
									selectedValue={additionalTaxLocal.type ?? ''}
									onChange={event => onChange('type', event.target.value)}
								/>
							</div>
							<div>
								<HSTextField
									data-testid='rate'
									label={startCase(`${additionalTaxLocal.type} Rate`)}
									groupPlacement='left'
									value={additionalTaxLocal.rate?.toString()}
									onChange={event => onChange('rate', event.target.value)}
									groupItem={
										<span className='font-semibold'>
											{additionalTaxLocal.type === additionalTaxTypes.percent
												? '%'
												: currency?.symbol}
										</span>
									}
									color='light'
									mask={decimalMask as (RegExp | string)[]}
									isInvalid={
										!additionalTaxLocal.rate ||
										Number.isNaN(additionalTaxLocal.rate)
									}
								/>
							</div>
							<div>
								{isProfile ? null : (
									<div className='flex flex-col gap-1'>
										<HSCheckbox
											label='Save to my hotel profile'
											checked={saveToProfile}
											onChange={event => setSaveToProfile(event.target.checked)}
										/>
									</div>
								)}
							</div>
						</div>
					</div>
				</Drawer.Items>
				<div className='flex gap-4'>
					<HSButton className='flex-1' color='light' onClick={() => onHide()}>
						Cancel
					</HSButton>
					<HSButton
						className='flex-1'
						onClick={async () => {
							await onClickSave()
						}}
						disabled={formInvalid}
					>
						Add Tax/Fee
					</HSButton>
				</div>
			</HSDrawer>
		</div>
	)
}

export default AdditionalTaxesAndFeesModal
