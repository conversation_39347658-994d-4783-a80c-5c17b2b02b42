import { faCircleExclamation } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import {
	formatTypeOption,
	formatCurrency,
	UnitedStatesDollar,
	formatNumber
} from 'lib/helpers'
import { eventTypeOptions } from 'lib/helpers/eventTypes'
import industryTypeOptions from 'lib/helpers/industryTypes'
import useQuoteRequestStore from 'lib/store/quoteRequestStore'
import { useEffect, useState } from 'react'
import AlternateDateDrawer from '../../roomRate/alternateDateDrawer'
import { format, parseISO } from 'date-fns'
import { useCurrencyContext } from 'lib/contexts/currency.context'

const EventSummaryDetail = () => {
	const { quoteRequest } = useQuoteRequestStore()

	const [openAlternativeDateDrawer, setOpenAlternativeDateDrawer] =
		useState(false)

	const [currency, setCurrency] = useState(UnitedStatesDollar)
	const { currencies } = useCurrencyContext()

	useEffect(() => {
		const prCurrency =
			currencies[quoteRequest.proposalRequest?.currencyCode ?? '']
		setCurrency(prCurrency)
	}, [quoteRequest.proposalRequest?.currencyCode, currencies])

	return (
		<div className='flex gap-4'>
			<div className='w-1/3'>
				<div className='card h-full p-4'>
					<div className='flex flex-col gap-1'>
						<div className='text-sm font-bold text-gray-700'>Event Profile</div>
						<div className='flex items-center gap-2'>
							<div className='font-normal text-gray-400'>Event Type:</div>
							<div className='font-normal text-gray-600'>
								{formatTypeOption(
									eventTypeOptions,
									quoteRequest.eventPlan?.type ?? ''
								)}
							</div>
						</div>
						<div className='flex items-center gap-2'>
							<div className='font-normal text-gray-400'>Industry:</div>
							<div className='font-normal text-gray-600'>
								{formatTypeOption(
									industryTypeOptions,
									quoteRequest.eventPlan?.industryType ?? ''
								)}
							</div>
						</div>
						<div className='flex items-center gap-2'>
							<div className='font-normal text-gray-400'>F&B Budget:</div>
							<div className='font-normal text-gray-600'>
								{quoteRequest.eventPlan?.meetingSpaceBudget
									? formatCurrency(
											quoteRequest.eventPlan.meetingSpaceBudget,
											currency,
											'0'
										)
									: 'Not Provided'}
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className='w-1/3'>
				<div className='card h-full p-4'>
					<div className='flex flex-col gap-4'>
						<div className='flex flex-col gap-2.5'>
							<div className='text-sm font-bold text-gray-500'>
								Largest Room Capacity
							</div>
							<div className='flex'>
								<div className='w-1/2'>
									<div className='flex items-center gap-2'>
										<div className='text-2xl font-semibold text-gray-900'>
											{formatNumber(
												quoteRequest.eventPlan?.meetingSpaceRequests?.reduce(
													(a, c) => Math.max(a, c.capacity ?? 0),
													0
												)
											)}
										</div>
										<div className='text-xs font-medium text-gray-500'>
											People
										</div>
									</div>
								</div>
								<div className='w-1/2'>
									<div className='flex items-center gap-2'>
										<div className='text-2xl font-semibold text-gray-900'>
											{formatNumber(
												quoteRequest.eventPlan?.meetingSpaceRequests?.reduce(
													(a, c) => Math.max(a, c.areaTotal ?? 0),
													0
												)
											)}
										</div>
										<div className='text-xs font-medium text-gray-500'>
											ft<sup>2</sup>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div className='border-b' />
						<div className='flex flex-col gap-2.5'>
							<div className='text-sm font-bold text-gray-500'>
								Total Room Capacity
							</div>
							<div className='flex'>
								<div className='w-1/2'>
									<div className='flex items-center gap-2'>
										<div className='text-2xl font-semibold text-gray-900'>
											{formatNumber(
												quoteRequest.eventPlan?.meetingSpaceRequests?.reduce(
													(a: number, c) => {
														const sum = a + (c.capacity ?? 0)
														return sum
													},
													0
												)
											)}
										</div>
										<div className='text-xs font-medium text-gray-500'>
											People
										</div>
									</div>
								</div>
								<div className='w-1/2'>
									<div className='flex items-center gap-2'>
										<div className='text-2xl font-semibold text-gray-900'>
											{formatNumber(
												quoteRequest.eventPlan?.meetingSpaceRequests?.reduce(
													(a: number, c) => {
														const sum = a + (c.areaTotal ?? 0)
														return sum
													},
													0
												)
											)}
										</div>
										<div className='text-xs font-medium text-gray-500'>
											ft<sup>2</sup>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className='w-1/3'>
				<div className='card h-full'>
					<div className='flex h-full flex-col justify-between'>
						<div>
							<div className='px-4 pb-2.5 pt-4'>
								<div className='flex flex-col gap-1'>
									<div className='text-sm font-bold text-gray-700'>
										Preferred Event Start
									</div>
									<div className='flex items-center gap-2'>
										<div className='text-gray-500'>
											{quoteRequest.eventPlan?.startDate
												? format(
														parseISO(quoteRequest.eventPlan.startDate),
														'MMM d, yyyy'
													)
												: ''}
										</div>
										<div className='text-xs font-medium text-gray-400'>
											{quoteRequest.eventPlan?.startDate
												? format(
														parseISO(quoteRequest.eventPlan.startDate),
														'EEEE'
													)
												: ''}
										</div>
									</div>
								</div>
							</div>
							<div className='border-b' />
							<div className='px-4 py-2.5'>
								<div className='flex flex-col gap-1'>
									<div className='text-sm font-bold text-gray-700'>
										Preferred Event End
									</div>
									<div className='flex items-center gap-2'>
										<div className='text-gray-500'>
											{quoteRequest.eventPlan?.endDate
												? format(
														parseISO(quoteRequest.eventPlan.endDate),
														'MMM d, yyyy'
													)
												: ''}
										</div>
										<div className='text-xs font-medium text-gray-400'>
											{quoteRequest.eventPlan?.endDate
												? format(
														parseISO(quoteRequest.eventPlan.endDate),
														'EEEE'
													)
												: ''}
										</div>
									</div>
								</div>
							</div>
						</div>
						{quoteRequest.eventPlan?.alternateDates?.some(
							ad => ad.startDate
						) ? (
							<div className='rounded-b-lg bg-primary-100 px-4 py-2'>
								<div className='flex items-center justify-between'>
									<div className='flex gap-1.5'>
										<FontAwesomeIcon
											icon={faCircleExclamation}
											className='text-primary-600'
										/>
										<div className='text-xs font-medium text-primary-600'>
											{
												quoteRequest.eventPlan.alternateDates.filter(
													ad => ad.startDate
												).length
											}{' '}
											Alternate Dates
										</div>
									</div>
									<div>
										<HSButton
											color='text'
											onClick={() => setOpenAlternativeDateDrawer(true)}
										>
											<span className='font-semibold'>View</span>
										</HSButton>
									</div>
								</div>
							</div>
						) : null}
						{openAlternativeDateDrawer ? (
							<AlternateDateDrawer
								onClose={() => setOpenAlternativeDateDrawer(false)}
							/>
						) : null}
					</div>
				</div>
			</div>
		</div>
	)
}

export default EventSummaryDetail
