import { APIProvider } from '@vis.gl/react-google-maps'
import Loader from 'components/loader'
import analytics from 'lib/analytics/segment/load'
import { getHopSkipConfig } from 'lib/auth/auth.config'
import {
	isUserProfileInitialized,
	useUserProfileContext
} from 'lib/contexts/userProfile.context'
import ChatProvider from 'lib/providers/chat.provider'
import CurrencyProvider from 'lib/providers/currency.provider'
import { FeatureProvider } from 'lib/providers/feature.provider'
import type { ReactElement } from 'react'

import { useEffect } from 'react'
import posthog from 'posthog-js'
import { getUnixTime, parseISO } from 'date-fns'
import lazyLoad from 'components/lazyComponent'

const Layout = lazyLoad(async () => import('components/layout'))

const PageLoader = () => (
	<div className='flex h-screen items-center justify-center'>
		<Loader />
	</div>
)

const hopskipConfig = getHopSkipConfig()

const App = (): ReactElement => {
	const { userProfile } = useUserProfileContext()

	useEffect(() => {
		if (userProfile) {
			const r = { data: userProfile }
			analytics.identify(r.data.id ?? '', {
				name: `${r.data.firstName} ${r.data.lastName}`,
				email: r.data.email,
				role: r.data.role
			})

			posthog.identify(r.data.id ?? '', {
				name: `${r.data.firstName} ${r.data.lastName}`,
				email: r.data.email,
				role: r.data.role,
				organizationId: r.data.organizationId,
				organizationName: r.data.organizationName,
				user_license: r.data.licenseInfo?.tier,
				user_license_type: r.data.licenseInfo?.type,
				user_license_expiration_at: r.data.licenseInfo?.expiryDate
					? getUnixTime(parseISO(r.data.licenseInfo.expiryDate))
					: null
			})
			// eslint-disable-next-line @typescript-eslint/no-unsafe-call
			globalThis.Intercom('boot', {
				app_id: 'tbzm7jeb',
				name: `${r.data.firstName} ${r.data.lastName}`,
				email: r.data.email,
				created_at: getUnixTime(parseISO(r.data.firstLogin ?? '')),
				user_hash: r.data.user_hash,
				company: r.data.organizationId
					? {
							id: r.data.organizationId,
							name: r.data.organizationName
						}
					: {},
				user_role: r.data.role,
				user_license: r.data.licenseInfo?.tier,
				user_license_type: r.data.licenseInfo?.type,
				user_license_expiration_at: r.data.licenseInfo?.expiryDate
					? getUnixTime(parseISO(r.data.licenseInfo.expiryDate))
					: null,
				associated_hotels:
					r.data.associatedVenues && r.data.associatedVenues.length > 0
						? r.data.associatedVenues.map(v => v.name).join(',')
						: null,
				default_property_id:
					r.data.associatedVenues && r.data.associatedVenues.length > 0
						? r.data.associatedVenues[0].id
						: null,
				custom_launcher_selector: '#custom-messenger-launcher'
			})
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	if (isUserProfileInitialized(userProfile)) {
		return (
			<FeatureProvider>
				<ChatProvider userProfileId={userProfile?.id ?? ''}>
					<CurrencyProvider>
						<APIProvider
							apiKey={hopskipConfig.googleMaps.key}
							libraries={['places', 'drawing', 'marker']}
						>
							<Layout />
						</APIProvider>
					</CurrencyProvider>
				</ChatProvider>
			</FeatureProvider>
		)
	}
	return <PageLoader />
}

export default App
