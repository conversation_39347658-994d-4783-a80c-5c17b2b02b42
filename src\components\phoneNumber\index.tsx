import type { ParsedCountry, PhoneInputProps } from 'react-international-phone'
import { PhoneInput } from 'react-international-phone'
import 'react-international-phone/style.css'
import './index.css'
import { useMemo, useCallback, useState, memo } from 'react'
import isValidPhoneNumber from 'lib/utils/phoneNumber'

interface PhoneNumberProperties extends PhoneInputProps {
	isRequired?: boolean
	size?: 'sm' | 'md' | 'lg' // Add size prop
	label?: string
}

// Move style objects outside component to prevent recreation on each render
const baseStyles: Record<string, React.CSSProperties> = {
	sm: {
		height: '32px',
		padding: '4px 8px',
		lineHeight: '1.5'
	},
	md: {
		height: '40px',
		padding: '8px 12px',
		lineHeight: '1.5'
	},
	lg: {
		height: '48px',
		padding: '12px 16px',
		lineHeight: '1.5'
	}
}

const dropDownBaseStyles: Record<string, React.CSSProperties> = {
	sm: {
		borderRadius: '0.25rem 0 0 0.25rem'
	},
	md: {
		borderRadius: '0.375rem 0 0 0.375rem'
	},
	lg: {
		borderRadius: '0.5rem 0 0 0.5rem'
	}
}

const inputBaseStyle: Record<string, React.CSSProperties> = {
	sm: {
		borderRadius: '0 0.25rem 0.25rem 0'
	},
	md: {
		borderRadius: '0 0.375rem 0.375rem 0'
	},
	lg: {
		borderRadius: '0 0.5rem 0.5rem 0'
	}
}

const HSPhoneNumber = memo((properties: PhoneNumberProperties) => {
	const { onChange, isRequired, label, size = 'md', value } = properties

	// Fix the validation logic - properly initialize isValid state
	const [isValid, setIsValid] = useState(() => {
		if (value && typeof value === 'string') {
			return isValidPhoneNumber(value) || !isRequired
		}
		return !isRequired
	})

	// Memoize the change handler
	const handleChange = useCallback(
		(
			phone: string,
			meta: {
				country: ParsedCountry
				inputValue: string
			}
		) => {
			if (onChange && value !== meta.inputValue) {
				try {
					const isPhoneNumberValid = isValidPhoneNumber(phone)
					setIsValid(isPhoneNumberValid || !isRequired)
					onChange(phone, meta)
				} catch {
					setIsValid(!isRequired)
					// Still call onChange so the input value is updated
					onChange(phone, meta)
				}
			}
		},
		[onChange, value, isRequired]
	)

	// Memoize style objects to prevent recreation on each render
	const baseStyleForSize = useMemo(() => baseStyles[size], [size])

	const dropdownStyle = useMemo(() => {
		const finalStyle: React.CSSProperties = {
			...dropDownBaseStyles[size],
			border: '1px solid #d1d5db',
			transition: 'border-color 0.2s ease-in-out'
		}

		if (isRequired && !isValid) {
			return { ...finalStyle, border: '1px solid red' }
		}
		return finalStyle
	}, [size, isRequired, isValid])

	const inputStyle = useMemo(() => {
		const finalStyle: React.CSSProperties = {
			...inputBaseStyle[size],
			border: '1px solid #d1d5db',
			transition: 'border-color 0.2s ease-in-out'
		}

		if (isRequired && !isValid) {
			return { ...finalStyle, border: '1px solid red' }
		}
		return finalStyle
	}, [size, isRequired, isValid])

	// Combine styles with memoization
	const finalInputStyle = useMemo(
		() => ({ ...inputStyle, ...baseStyleForSize }),
		[inputStyle, baseStyleForSize]
	)

	const finalDropdownStyle = useMemo(
		() => ({ ...dropdownStyle, ...baseStyleForSize }),
		[dropdownStyle, baseStyleForSize]
	)

	return (
		<div className='flex flex-col gap-2'>
			{label ? (
				<div className='text-sm font-medium text-gray-900'>
					{label}
					{isRequired ? <span className='ml-1 text-red-500'>*</span> : null}
				</div>
			) : null}
			<PhoneInput
				{...properties}
				onChange={handleChange}
				inputStyle={finalInputStyle}
				inputClassName='w-full'
				countrySelectorStyleProps={{
					buttonStyle: finalDropdownStyle
				}}
				forceDialCode
				defaultCountry={navigator.language.slice(-2).toLowerCase()}
			/>
			{isRequired && !isValid ? (
				<div className='mt-1 text-xs text-red-500'>
					Please enter a valid phone number
				</div>
			) : null}
		</div>
	)
})

export default HSPhoneNumber
