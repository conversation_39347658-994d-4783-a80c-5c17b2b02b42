/* eslint-disable no-plusplus */
import { differenceInDays, formatISO } from 'date-fns'
import { v4 as uuidv4 } from 'uuid'
import { calculateSortIndex } from './planner/meetingSpaceRequests'
import type { IEventHistory } from 'models/proposalResponseMonitor'

export const getDefaultEventHistory = (
	sd: string,
	ed: string
): IEventHistory => ({
	startDate: sd,
	endDate: ed,
	numberOfAttendees: 0,
	numberOfRooms: 0,
	propertyName: '',
	comments: '',
	fbSpend: 0,
	roomRate: null
})

export const getDefaultRoomBlockRequest = (
	name: string,
	sd: Date,
	ed: Date
) => {
	const a = []
	for (let d = 0; d < differenceInDays(ed, sd); d++) {
		a.push({
			dayNumber: d,
			roomsRequested: 0
		})
	}
	return {
		id: uuidv4(),
		name,
		peakSize: 0,
		roomTypeRequests: [
			{
				roomType: 0,
				budget: 0,
				roomNights: [...a]
			}
		],
		created: formatISO(new Date())
	}
}

export const getDefaultMeetingSpaceRequest = (
	dayNumber: number,
	index: number
) => ({
	id: uuidv4(),
	name: '',
	layoutStyle: '',
	dayNumber,
	sortIndex: calculateSortIndex(dayNumber, index),
	startTime: 9,
	endTime: 17,
	avRequired: false,
	fbRequired: false,
	physicalDistancingRequired: false,
	excludeFromTotals: false,
	diagramRequested: false,
	hold24Hours: false
})
