/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable import/prefer-default-export */

import type { IconDefinition } from '@fortawesome/pro-light-svg-icons'
import { faChartBar, faPencilAlt } from '@fortawesome/pro-light-svg-icons'
import type { ProposalRequest } from 'models/proposalResponseMonitor'
import { create } from 'zustand'

// Define RateSet types
export interface RoomRate {
	dayNumber: number
	offered: number
	rate: number
	[key: string]: number | string | undefined
}

export interface RoomTypeRate {
	roomType: string
	averageRate: number | null
	totalRooms: number
	attritionRooms: number
	totalCost: number
	roomRates: RoomRate[]
	editingAverageRate?: boolean
	[key: string]: number | string | boolean | undefined | null | RoomRate[]
}

export interface ISavingData {
	proposedRooms: number
	proposedMeeting: number
	proposedTotal: number
	contractedRooms: number
	contractedMeeting: number
	contractedTotal: number
	percentage: number
	value: number
	rooms: number
	meeting: number
	concessions: number
	items: {
		name: string
		percentage: number
		value: number
		fill: string
	}[]
}

export interface RateSet {
	id?: string
	eventPlanId?: string
	venueId?: string
	venueName?: string
	rateSetType?: string
	attritionRate: number
	applyAttrition: boolean
	roomTypeRates: RoomTypeRate[]
	totalRooms: number
	attritionRooms: number
	totalRoomCost: number
	perRoomFees: number
	rebateAmount: number
	taxesFeesAssessments: number
	foodAndBeverage: number
	foodAndBeverageTaxRate: number
	roomRental: number
	roomRentalTaxRate: number
	serviceChargeRate: number
	serviceChargeTaxRate: number
	roomCostPlusPlus: number
	totalRebateAmount: number
	foodAndBeveragePlusPlus: number
	roomRentalPlusPlus: number
	serviceCharge: number
	serviceChargePlusPlus: number
	isDirty?: boolean
	loading?: boolean
	[key: string]: string | number | boolean | undefined | RoomTypeRate[]
}

// Extended ProposalRequest type with rateSets property
export interface ExtendedProposalRequest extends ProposalRequest {
	rateSets: {
		[key: string]: RateSet
		targeted: RateSet
		proposed: RateSet
		contracted: RateSet
	}
}

// Rate set keys constant
export const rateSetKeys = {
	proposed: 'proposed',
	targeted: 'targeted',
	contracted: 'contracted'
}

export type RateSetKey = 'proposed' | 'contracted' | 'targeted'

interface IViewMode {
	view: IViewModeData
	edit: IViewModeData
}

interface IViewModeData {
	key: keyof typeof viewModes
	title: string
	toggleCta: {
		icon: IconDefinition
		label: string
	}
}

export const viewModes: IViewMode = {
	view: {
		key: 'view',
		title: 'Cost Savings Dashboard',
		toggleCta: {
			icon: faPencilAlt,
			label: 'Edit Rates'
		}
	},
	edit: {
		key: 'edit',
		title: 'Edit Negotiated Rates',
		toggleCta: {
			icon: faChartBar,
			label: 'View Dashboard'
		}
	}
}

interface ICostSavingsAction {
	onToggleViewMode: () => void
	setSaving: (saving: boolean) => void
	setSelectedProposals: (proposals: ExtendedProposalRequest[]) => void
	setStaticProperty: (
		venueId: string,
		value: unknown,
		rateSet: RateSetKey
	) => void
	setProperty: (
		name: string,
		value: unknown,
		venueId: string,
		applyAttrition: boolean,
		type?: RateSetKey
	) => void
	setRoomRate: (
		venueId: string,
		rateSet: RateSetKey,
		roomType: string,
		value: Partial<RoomTypeRate>
	) => void
	setDailyRoomRate: (
		venueId: string,
		rateSet: RateSetKey,
		roomType: string,
		dayNumber: number,
		value: Partial<RoomRate>
	) => void
	initSet: (venueId: string, rateSet: RateSetKey, value: RateSet) => void
}

interface ICostSavingsState {
	selectedProposals: ExtendedProposalRequest[]
	saving: boolean
	viewMode: IViewModeData
}

const recalculateRateSet = (rateSet: RateSet) => {
	const roomTypeRates = rateSet.roomTypeRates.map(
		(roomTypeRate: RoomTypeRate) => {
			let totalCost = 0
			let totalOffered = 0

			for (const rate of roomTypeRate.roomRates) {
				totalCost += Number(rate.rate || 0) * Number(rate.offered || 0)
				totalOffered += Number(rate.offered || 0)
			}

			const attritionRooms =
				totalOffered *
				(Number(rateSet.applyAttrition ? rateSet.attritionRate : 100) / 100)

			const averageRate = roomTypeRate.editingAverageRate
				? Number(roomTypeRate.averageRate)
				: (totalOffered > 0
					? totalCost / totalOffered
					: 0)

			return {
				...roomTypeRate,
				totalRooms: totalOffered,
				averageRate,
				attritionRooms,
				totalCost: averageRate * attritionRooms
			}
		}
	)

	const total = { rooms: 0, roomCost: 0, attritionRooms: 0 }
	for (const rate of roomTypeRates) {
		total.rooms += Number(rate.totalRooms)
		total.roomCost += Number(rate.totalCost)
		total.attritionRooms += Number(rate.attritionRooms)
	}

	const serviceCharge =
		(Number(rateSet.foodAndBeverage) + Number(rateSet.roomRental)) *
		(Number(rateSet.serviceChargeRate || 0) / 100)

	return {
		...rateSet,
		isDirty: true,
		roomTypeRates,
		attritionRooms: total.attritionRooms,
		totalRooms: total.rooms,
		totalRoomCost: total.roomCost,
		roomCostPlusPlus:
			total.roomCost * (1 + Number(rateSet.taxesFeesAssessments || 0) / 100) +
			total.rooms * Number(rateSet.perRoomFees || 0),
		totalRebateAmount: Number(total.rooms) * Number(rateSet.rebateAmount || 0),
		foodAndBeveragePlusPlus:
			Number(rateSet.foodAndBeverage || 0) *
			(1 + Number(rateSet.foodAndBeverageTaxRate || 0) / 100),
		roomRentalPlusPlus:
			Number(rateSet.roomRental) *
			(1 + Number(rateSet.roomRentalTaxRate || 0) / 100),
		serviceCharge,
		serviceChargePlusPlus:
			serviceCharge * (1 + Number(rateSet.serviceChargeTaxRate || 0) / 100)
	}
}

export const costSavingsStore = create<ICostSavingsState & ICostSavingsAction>(
	(set, get) => ({
		selectedProposals: [],
		saving: false,

		viewMode: viewModes.view,
		onToggleViewMode: () => {
			const currentMode = get().viewMode.key
			const newMode = currentMode === 'view' ? 'edit' : 'view'
			set({
				viewMode: viewModes[newMode as keyof typeof viewModes]
			})
		},

		setSaving: saving => set({ saving }),

		setSelectedProposals: proposals => set({ selectedProposals: proposals }),

		setStaticProperty: (venueId, value, rateSet) => {
			if (!['proposed', 'contracted', 'targeted'].includes(rateSet)) return
			const updated: ExtendedProposalRequest[] = get().selectedProposals.map(
				p =>
					p.venueId === venueId
						? {
								...p,
								rateSets: {
									targeted: p.rateSets.targeted,
									proposed: p.rateSets.proposed,
									contracted: p.rateSets.contracted,
									[rateSet]: {
										...p.rateSets[rateSet],
										...(typeof value === 'object' && value !== null
											? (value as Partial<RateSet>)
											: {})
									}
								}
							}
						: p
			)
			set({ selectedProposals: updated })
		},

		setProperty: (name, value, venueId, applyAttrition, type = 'proposed') => {
			if (!['proposed', 'contracted', 'targeted'].includes(type)) return
			const updated = get().selectedProposals.map(p => {
				if (p.venueId !== venueId) return p
				if (applyAttrition) {
					const valueAsObject =
						typeof value === 'object' && value !== null
							? value
							: { [name]: value }
					return {
						...p,
						rateSets: {
							targeted: recalculateRateSet({
								...p.rateSets.targeted,
								...valueAsObject
							}),
							proposed: recalculateRateSet({
								...p.rateSets.proposed,
								...valueAsObject
							}),
							contracted: recalculateRateSet({
								...p.rateSets.contracted,
								...valueAsObject
							})
						}
					}
				}
				return {
					...p,
					rateSets: {
						...p.rateSets,
						[type]: recalculateRateSet({
							...p.rateSets[type],
							...(typeof value === 'object' && value !== null
								? value
								: { [name]: value })
						})
					}
				}
			})
			set({ selectedProposals: updated })
		},

		setRoomRate: (venueId, rateSet, roomType, value) => {
			if (!['proposed', 'contracted', 'targeted'].includes(rateSet)) return
			const updated = get().selectedProposals.map(p => {
				if (p.venueId !== venueId) return p
				const oldRTR = p.rateSets[rateSet].roomTypeRates
				const updatedRTR = oldRTR.map(rtr => {
					if (rtr.roomType === roomType) {
						const recalculated = recalculateRateSet({
							...p.rateSets[rateSet],
							roomTypeRates: oldRTR.map(r =>
								r.roomType === roomType
									? { ...r, ...value, editingAverageRate: true }
									: r
							)
						})
						return (
							recalculated.roomTypeRates.find(r => r.roomType === roomType) ||
							rtr
						)
					}
					return rtr
				})
				const { targeted, proposed, contracted } = p.rateSets
				const updatedRateSets: ExtendedProposalRequest['rateSets'] = {
					targeted,
					proposed,
					contracted
				}
				updatedRateSets[rateSet] = recalculateRateSet({
					...p.rateSets[rateSet],
					roomTypeRates: updatedRTR
				})
				return {
					...p,
					rateSets: updatedRateSets
				}
			})
			set({ selectedProposals: updated })
		},

		setDailyRoomRate: (venueId, rateSet, roomType, dayNumber, value) => {
			if (!['proposed', 'contracted', 'targeted'].includes(rateSet)) return
			const updated = get().selectedProposals.map(p => {
				if (p.venueId !== venueId) return p
				const oldRTR = p.rateSets[rateSet].roomTypeRates
				const rtr = oldRTR.find(r => r.roomType === roomType)
				if (!rtr) return p

				const updatedRates = rtr.roomRates.map(rr =>
					rr.dayNumber === dayNumber ? { ...rr, ...value } : rr
				)
				const updatedRTR = oldRTR.map(r =>
					r.roomType === roomType
						? {
								...r,
								editingAverageRate: false,
								roomRates: updatedRates
							}
						: r
				)
				const { targeted, proposed, contracted } = p.rateSets
				const updatedRateSets: ExtendedProposalRequest['rateSets'] = {
					targeted,
					proposed,
					contracted
				}
				updatedRateSets[rateSet] = recalculateRateSet({
					...p.rateSets[rateSet],
					roomTypeRates: updatedRTR
				})
				return {
					...p,
					rateSets: updatedRateSets
				}
			})
			set({ selectedProposals: updated })
		},

		initSet: (venueId, rateSet, value) => {
			if (!['proposed', 'contracted', 'targeted'].includes(rateSet)) return
			const updated = get().selectedProposals.map(p =>
				p.venueId === venueId
					? {
							...p,
							rateSets: {
								...p.rateSets,
								[rateSet]: { ...value }
							}
						}
					: p
			)
			set({ selectedProposals: updated })
		}
	})
)
