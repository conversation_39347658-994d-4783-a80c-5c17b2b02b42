/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
// import { faInfoCircle } from '@fortawesome/pro-light-svg-icons'
// import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
// import HSButton from 'components/button'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import { format, parseISO } from 'date-fns'
import {
	formatCurrency,
	formatNumber,
	formatTypeOption,
	UnitedStatesDollar
} from 'lib/helpers'
import { groupTypeOptions } from 'lib/helpers/groupTypes'
import useQuoteRequestStore from 'lib/store/quoteRequestStore'
import AlternateDateDrawer from './alternateDateDrawer'
import { memo, useEffect, useState } from 'react'
import {
	faCheck,
	faCircleExclamation,
	faExclamation,
	faPlus
} from '@fortawesome/pro-regular-svg-icons'
import type { IProposalDates, IRoomRate } from 'models/proposalResponseMonitor'
import ProposeNewDatesModal from './proposeNewDatesModal'
import { sortProposalDates } from 'lib/common/proposalValues'
import { sleepingRoomTypeOptions } from 'lib/helpers/sleepingRoomType'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import HorizontalScroller from 'components/scroller'
import RoomRateEditor from './rateEditor'
import HSTooltip from 'components/tooltip'

const RoomRate = ({ isSummary = false }: { isSummary: boolean }) => {
	const { quoteRequest, setSuggestedProposalDate } = useQuoteRequestStore()
	const proposalRequest = isSummary
		? quoteRequest.summaryProposalRequest
		: quoteRequest.proposalRequest

	const [openAlternativeDateDrawer, setOpenAlternativeDateDrawer] =
		useState(false)
	const [selectedProposalDate, setSelectedProposalDate] =
		useState<IProposalDates>()
	const [proposalDateComplete, setProposalDateComplete] =
		useState<IProposalDates>()
	const [openProposeNewDate, setOpenProposeNewDate] = useState(false)
	const [currency, setCurrency] = useState(UnitedStatesDollar)
	const { currencies } = useCurrencyContext()

	const [selectedProposalDateKey, setSelectedProposalDateKey] = useState(
		proposalRequest?.currentBid?.proposalDates?.sort(sortProposalDates).at(0)
			?.key
	)

	useEffect(() => {
		if (proposalRequest?.currencyCode) {
			setCurrency(
				currencies[proposalRequest.currencyCode] ?? UnitedStatesDollar
			)
		}
	}, [currencies, proposalRequest?.currencyCode])

	useEffect(() => {
		if (proposalRequest?.currentBid?.proposalDates) {
			if (selectedProposalDateKey) {
				setSelectedProposalDate(
					proposalRequest.currentBid.proposalDates.find(
						pd => pd.key === selectedProposalDateKey
					)
				)
			} else if (
				proposalRequest.currentBid.proposalDates.some(pd => pd.preferred)
			) {
				setSelectedProposalDate(
					proposalRequest.currentBid.proposalDates.find(pd => pd.preferred)
				)
			} else {
				setSelectedProposalDate(proposalRequest.currentBid.proposalDates.at(0))
			}
		}

		const updatedProposalDates =
			proposalRequest?.currentBid?.proposalDates?.reduce(
				(a, c) => {
					if (a[c.key as keyof IProposalDates] === undefined) {
						return { ...a, key: a.key ?? '', [c.key ?? '']: false }
					}
					return a
				},
				{ ...proposalDateComplete }
			)
		setProposalDateComplete(updatedProposalDates)
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [proposalRequest?.currentBid?.proposalDates])

	useEffect(() => {
		const a: Record<string, boolean> = {}
		const expectedRates =
			quoteRequest.eventPlan?.roomBlockRequests?.reduce(
				(a1, rbr) =>
					a1 +
					rbr.roomTypeRequests.reduce(
						(a2, rtr) =>
							a2 + rtr.roomNights.filter(rn => rn.roomsRequested > 0).length,
						0
					),
				0
			) ?? 0

		const proposalDates = proposalRequest?.currentBid?.proposalDates ?? []

		for (const proposalDate of proposalDates) {
			const roomRates = proposalRequest?.currentBid?.roomRates?.filter(
				rr =>
					rr.key?.endsWith(proposalDate.key ?? '') &&
					rr.rate !== null &&
					rr.rate > 0
			)
			let complete = true

			if (proposalDate.isOfferedDifferentThanRequested) {
				const roomRateAccumulator =
					roomRates?.reduce(
						(accumulator, rr: IRoomRate) => {
							if (!Number.isNaN(rr.roomType)) {
								if (accumulator[rr.roomType] < 0) accumulator[rr.roomType] = 0
								accumulator[rr.roomType] += rr.offered || 0
							}
							return accumulator
						},
						Array.from({ length: sleepingRoomTypeOptions.length }).fill(-1)
					) || []

				const hasOfferedRoomsWithoutRates =
					proposalRequest?.currentBid?.roomRates
						?.filter(rr => rr.key?.endsWith(proposalDate.key ?? ''))
						.some(rr => {
							if (!proposalDate.isVaryingRate) {
								const nightRoomRates =
									proposalRequest.currentBid?.roomRates?.filter(
										rrn =>
											rrn.key?.endsWith(proposalDate.key ?? '') &&
											rrn.roomType === rr.roomType &&
											rrn.roomBlockIndex === rr.roomBlockIndex
									)
								const isRateOfferedForNight = nightRoomRates?.some(
									rrn => rrn.rate !== null && rrn.rate > 0
								)

								const isRoomOfferedForNight = nightRoomRates?.some(
									rrn => rrn.offered && rrn.roomType !== null
								)
								if (isRateOfferedForNight) {
									return !isRoomOfferedForNight
								}

								return isRoomOfferedForNight
							}

							let result = false
							if (
								((rr.offered ?? 0) > 0 && (rr.rate ?? 0) === 0) ||
								((rr.rate ?? 0) > 0 && (rr.offered ?? 0) === 0)
							) {
								result = true
							}
							return result
						})

				const isEmpty = roomRateAccumulator.every(value => value === -1)
				const roomRateEntries = roomRateAccumulator.entries()

				if (isEmpty || hasOfferedRoomsWithoutRates) {
					complete = false
				} else {
					for (const [, rto] of roomRateEntries) {
						if (rto === 0) {
							complete = false
						}
					}
				}
			} else if (
				(roomRates?.filter(rr => !!rr.offered && !!rr.rate).length ?? 0) <
				expectedRates
			) {
				complete = false
			}
			a[proposalDate.key ?? ''] = complete
		}

		setProposalDateComplete(a)
	}, [quoteRequest.eventPlan?.roomBlockRequests, proposalRequest?.currentBid])

	const updateSelectedProposalDate = (proposalDate: IProposalDates) => {
		setSelectedProposalDate(proposalDate)
		setSelectedProposalDateKey(proposalDate.key)
	}

	if (!quoteRequest.eventPlan?.roomBlocksRequired) {
		return (
			<div className='flex h-64 w-full items-center justify-center'>
				<div className='text-sm font-normal text-primary-disabled'>
					Room Rate Has Not Been Requested
				</div>
			</div>
		)
	}

	return (
		<div className='flex flex-col gap-6'>
			<div className='flex gap-4'>
				<div className='card flex flex-1 flex-col gap-1 p-4'>
					<div className='text-sm font-bold text-gray-700'>Group Profile</div>
					<div className='flex items-center gap-2'>
						<div className='text-gray-400'>Group Type:</div>
						<div className='text-gray-600'>
							{formatTypeOption(
								groupTypeOptions,
								quoteRequest.eventPlan.groupType ?? null
							)}
						</div>
					</div>
					<div className='flex items-center gap-2'>
						<div className='text-gray-400'>Room Budget:</div>
						<div className='text-gray-600'>
							{formatCurrency(
								quoteRequest.eventPlan.roomNightBudget ?? null,
								currency
							)}
						</div>
					</div>
					<div className='flex items-center gap-2'>
						<div className='text-gray-400'>Commissionable Rates:</div>
						<div className='text-gray-600'>
							{proposalRequest?.isBookingFee || proposalRequest?.commissionable
								? 'Yes'
								: 'No'}
						</div>
					</div>
					<div className='flex items-center gap-2'>
						<div className='text-gray-400'>Rebate Requested:</div>
						<div className='text-gray-600'>
							{proposalRequest?.currentBid?.rebateRequestAmount
								? `Yes: ${formatCurrency(proposalRequest.currentBid.rebateRequestAmount, currency)}/room night`
								: 'No'}
						</div>
					</div>
				</div>
				<div className='card flex flex-1 flex-col gap-4 p-4'>
					<div className='flex flex-col gap-1'>
						<div className='text-sm font-bold text-gray-500'>
							Peak Room Nights
						</div>
						<div className='flex items-baseline gap-2'>
							<div className='text-2xl font-semibold text-gray-900'>
								{formatNumber(quoteRequest.eventPlan.peakRooms)}
							</div>
							<div className='text-xs font-medium text-gray-500'>Rooms</div>
						</div>
					</div>
					<div className='border-b border-gray-200' />

					<div className='flex flex-col gap-1'>
						<div className='text-sm font-bold text-gray-500'>
							Total Room Nights
						</div>
						<div className='flex items-baseline gap-2'>
							<div className='text-2xl font-semibold text-gray-900'>
								{formatNumber(quoteRequest.eventPlan.totalRoomsRequested)}
							</div>
							<div className='text-xs font-medium text-gray-500'>Rooms</div>
						</div>
					</div>
				</div>

				<div className='card flex flex-1 flex-col gap-[10px]'>
					<div className='p-4 pb-0'>
						<div className='text-sm font-bold text-gray-700'>
							Preferred Event Start
						</div>
						<div className='flex items-center gap-2'>
							<div className='text-gray-500'>
								{quoteRequest.eventPlan.startDate
									? format(
											parseISO(quoteRequest.eventPlan.startDate),
											'MMM d, yyyy'
										)
									: ''}
							</div>
							<div className='text-xs font-medium text-gray-400'>
								{quoteRequest.eventPlan.startDate
									? format(parseISO(quoteRequest.eventPlan.startDate), 'EEEE')
									: ''}
							</div>
						</div>
					</div>
					<div className='border-b border-gray-200' />
					<div className='p-4 pb-0'>
						<div className='text-sm font-bold text-gray-700'>
							Preferred Event End
						</div>
						<div className='flex items-center gap-2'>
							<div className='text-gray-500'>
								{quoteRequest.eventPlan.endDate
									? format(
											parseISO(quoteRequest.eventPlan.endDate),
											'MMM d, yyyy'
										)
									: ''}
							</div>
							<div className='text-xs font-medium text-gray-400'>
								{quoteRequest.eventPlan.endDate
									? format(parseISO(quoteRequest.eventPlan.endDate), 'EEEE')
									: ''}
							</div>
						</div>
					</div>
					{quoteRequest.eventPlan.alternateDates?.some(ad => ad.startDate) ? (
						<div className='rounded-b-lg bg-primary-100 px-4 py-2'>
							<div className='flex items-center justify-between'>
								<div className='flex gap-1.5'>
									<FontAwesomeIcon
										icon={faCircleExclamation}
										className='text-primary-600'
									/>
									<div className='text-xs font-medium text-primary-600'>
										{
											quoteRequest.eventPlan.alternateDates.filter(
												ad => ad.startDate
											).length
										}{' '}
										Alternate Dates
									</div>
								</div>
								<div>
									<HSButton
										color='text'
										onClick={() => setOpenAlternativeDateDrawer(true)}
									>
										<span className='font-semibold'>View</span>
									</HSButton>
								</div>
							</div>
						</div>
					) : null}
					{openAlternativeDateDrawer ? (
						<AlternateDateDrawer
							onClose={() => setOpenAlternativeDateDrawer(false)}
						/>
					) : null}
				</div>
			</div>
			<div className='flex items-center justify-between'>
				<div className='w-full'>
					<div className='text-lg font-semibold text-gray-900'>
						Enter Room Rates
					</div>
					<div className='text-xs font-normal text-gray-500'>
						Select each date below to provide rates for that date range.Decline
						any dates that you&apos;re not able to accommodate.You may propose
						up to 3 other date ranges using the button below.
					</div>
				</div>
			</div>
			<div className='card'>
				<div className='flex items-center border-b shadow-sm'>
					<HorizontalScroller
						style={{
							maxWidth: 'calc(100vw - 40rem)'
						}}
						className='rounded-tl-lg'
						scrollAmount={400}
					>
						{proposalRequest?.currentBid?.proposalDates
							?.sort(sortProposalDates)
							.map(proposalDate => {
								const iconStyle = proposalDate.declineToBid
									? {
											icon: faCheck,
											style: 'text-green-800',
											bgColor: 'bg-green-100'
										}
									: proposalDateComplete?.[
												proposalDate.key as keyof IProposalDates
										  ]
										? {
												icon: faCheck,
												style: 'text-green-800',
												bgColor: 'bg-green-100'
											}
										: {
												icon: faExclamation,
												style: 'text-red-800',
												bgColor: 'bg-red-100'
											}

								return (
									<div
										className={`flex min-w-60 justify-between gap-4 border-r p-4 ${selectedProposalDateKey === proposalDate.key ? 'bg-primary-50' : ''}`}
										key={`${proposalDate.key}.${proposalDate.startDate}`}
										onClick={() => {
											setSelectedProposalDate(proposalDate)
											setSelectedProposalDateKey(proposalDate.key)
										}}
										tabIndex={0}
										role='button'
										onKeyDown={event => {
											if (event.key === 'Enter') {
												setSelectedProposalDate(proposalDate)
												setSelectedProposalDateKey(proposalDate.key)
											}
										}}
									>
										<div className='flex flex-col'>
											<div
												className={`text-xs font-medium ${
													proposalDate.declineToBid
														? 'text-gray-500 line-through'
														: 'text-gray-900'
												}`}
											>
												{proposalDate.startDate
													? `${format(parseISO(proposalDate.startDate), 'PP')} - ${
															proposalDate.endDate
																? format(parseISO(proposalDate.endDate), 'PP')
																: ''
														}`
													: 'Selection'}
											</div>
											<div className='text-xs font-medium text-gray-500'>
												{proposalDate.preferred
													? 'Preferred'
													: proposalDate.alternate
														? 'Alternate'
														: 'Proposed'}
												{proposalDate.declineToBid ? (
													<>
														<span className='text-gray-500'>,</span>
														<span className='text-red-500'> Declined</span>
													</>
												) : null}
											</div>
										</div>
										<div
											className={`flex h-5 w-5 items-center justify-center rounded-full ${iconStyle.bgColor}`}
										>
											<TooltipComponent
												content={
													iconStyle.icon === faExclamation
														? 'Rate/Room is Required'
														: iconStyle.icon === faCheck
															? 'All Good'
															: 'Declined'
												}
												position='TopCenter'
											>
												<FontAwesomeIcon
													icon={iconStyle.icon}
													className={iconStyle.style}
													style={{
														display: 'flex',
														alignItems: 'center',
														justifyContent: 'center',
														width: '10px',
														height: '10px'
													}}
												/>
											</TooltipComponent>
										</div>
									</div>
								)
							})}
						{isSummary || proposalRequest?.status === 'Active' ? null : (
							<div className='flex flex-1 grow items-center px-4 py-2'>
								<HSTooltip
									content="Planner didn't allow to propose new dates"
									className={
										quoteRequest.eventPlan.allowDateSuggestions ? 'hidden' : ''
									}
								>
									<HSButton
										color='text'
										outline
										onClick={() => {
											setSuggestedProposalDate({ suggested: true })
											setOpenProposeNewDate(true)
										}}
										size='sm'
										disabled={!quoteRequest.eventPlan.allowDateSuggestions}
										className='flex-1 grow'
									>
										<div className='flex items-center gap-2'>
											<FontAwesomeIcon icon={faPlus} />
											<div>Propose New Dates</div>
										</div>
									</HSButton>
								</HSTooltip>
							</div>
						)}
					</HorizontalScroller>
				</div>
				{selectedProposalDate ? (
					<RoomRateEditor
						roomBlockRequests={quoteRequest.eventPlan.roomBlockRequests}
						currentBid={proposalRequest?.currentBid}
						proposalDate={selectedProposalDate}
						isFormReadOnly={proposalRequest?.status !== 'Received'}
						setProposalDateComplete={setProposalDateComplete}
						currency={currency}
						updateSelectedProposalDate={updateSelectedProposalDate}
						isSummary={isSummary}
						proposalStatus={proposalRequest?.status}
						isRoomBlockValid={
							!!proposalDateComplete?.[
								selectedProposalDate.key as keyof IProposalDates
							]
						}
					/>
				) : null}
			</div>
			{openProposeNewDate ? (
				<ProposeNewDatesModal
					isSummary={isSummary}
					onClose={() => {
						setOpenProposeNewDate(false)
					}}
					setSelectedProposalDate={updateSelectedProposalDate}
				/>
			) : null}
		</div>
	)
}

export default memo(RoomRate)
