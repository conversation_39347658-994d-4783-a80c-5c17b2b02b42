/* eslint-disable react/no-array-index-key */
import EventTable from '.'

interface EventTableListProperties {
	availableDates: Date[]
}

const EventTableList = (properties: EventTableListProperties) => {
	const { availableDates } = properties

	return availableDates.map((date, dateIndex) => (
		<EventTable
			key={`event-space-${dateIndex}`}
			dateIndex={dateIndex}
			date={date}
			availableDates={availableDates}
		/>
	))
}

export default EventTableList
