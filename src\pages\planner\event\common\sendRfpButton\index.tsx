/* eslint-disable react/no-array-index-key */
import HSButton from 'components/button'
import HSModal from 'components/modal'
import { Modal } from 'flowbite-react'
import { useState } from 'react'

export type ValidationRuleType = 'responsesDueDate' | 'selectionDate'

interface SendRfpButtonProperties {
	submitValidationErrors: Record<ValidationRuleType, string | null>
	validationRules: Record<
		ValidationRuleType,
		{ label: string; min: Date | null; max: Date | null }
	>
}

const SendRfpButton = (properties: SendRfpButtonProperties) => {
	const { submitValidationErrors, validationRules } = properties

	const [showSubmitValidationErrorsModal, setShowSubmitValidationErrorsModal] =
		useState(false)
	return (
		<>
			{submitValidationErrors.responsesDueDate ||
			submitValidationErrors.selectionDate ? (
				<HSButton
					onClick={() => {
						setShowSubmitValidationErrorsModal(true)
					}}
				>
					Send Request
				</HSButton>
			) : null}
			{showSubmitValidationErrorsModal ? (
				<HSModal
					header='Send RFP'
					openModal={showSubmitValidationErrorsModal}
					onClose={() => {
						setShowSubmitValidationErrorsModal(false)
					}}
					size='md'
				>
					<Modal.Body>
						{(Object.keys(submitValidationErrors) as ValidationRuleType[])
							.filter(k => submitValidationErrors[k] !== null)
							.map((k, index) => (
								<p key={index} className='flex items-center gap-2'>
									<div className='text-sm font-normal text-gray-900'>
										{validationRules[k].label}
									</div>
									<div className='text-xs font-medium text-gray-500'>
										{submitValidationErrors[k] === 'required'
											? 'is required'
											: submitValidationErrors[k]}
									</div>
								</p>
							))}
					</Modal.Body>
					<Modal.Footer>
						<div className='flex grow items-center justify-end'>
							<HSButton
								color='light'
								onClick={() => setShowSubmitValidationErrorsModal(false)}
								className='w-32'
							>
								Ok
							</HSButton>
						</div>
					</Modal.Footer>
				</HSModal>
			) : null}
		</>
	)
}

export default SendRfpButton
