import HSBadge from 'components/badge'
import HSButton from 'components/button'
import HSModal from 'components/modal'
import type { EventPlanStatusKey } from 'lib/helpers/statusMaps'
import { EventPlanStatusMap } from 'lib/helpers/statusMaps'
import type { EventPlan } from 'models/proposalResponseMonitor'

interface IEventPlanDeleteModalProperties {
	showDeleteModal: boolean
	onCancelDelete: () => void
	onDeleteEventPlan: () => void
	eventPlan: EventPlan
}

const EventPlanDeleteModal = (properties: IEventPlanDeleteModalProperties) => {
	const { showDeleteModal, onCancelDelete, onDeleteEventPlan, eventPlan } =
		properties

	const renderRFPCard = () => (
		<div className='card flex flex-col gap-2 p-4'>
			<div className='flex items-center justify-between gap-2'>
				<div className='whitespace-nowrap text-sm font-medium text-gray-900'>
					RFP Name
				</div>
				<div className='text-sm font-normal text-gray-600'>
					{eventPlan.name}
				</div>
			</div>
			<div className='flex items-center justify-between gap-2'>
				<div className='text-sm font-medium text-gray-900'>Status</div>
				<div>
					<HSBadge
						color={
							EventPlanStatusMap[eventPlan.status as EventPlanStatusKey]
								?.color ?? 'gray'
						}
						className='w-fit p-1 text-center'
					>
						{EventPlanStatusMap[eventPlan.status as EventPlanStatusKey]
							?.label ?? eventPlan.status}
					</HSBadge>
				</div>
			</div>
		</div>
	)
	return (
		<HSModal
			openModal={showDeleteModal}
			onClose={() => onCancelDelete()}
			header='Delete RFP'
			size='lg'
		>
			<div className='flex flex-col gap-4 p-5'>
				<div className='text-base font-normal text-gray-500'>
					Are you sure you want to delete this RFP?
				</div>
				{renderRFPCard()}
				<div className='flex justify-center gap-4'>
					<HSButton
						color='light'
						className='w-full'
						onClick={() => onCancelDelete()}
					>
						Cancel
					</HSButton>
					<HSButton
						className='w-full'
						color='danger'
						onClick={() => onDeleteEventPlan()}
					>
						Delete
					</HSButton>
				</div>
			</div>
		</HSModal>
	)
}

export default EventPlanDeleteModal
