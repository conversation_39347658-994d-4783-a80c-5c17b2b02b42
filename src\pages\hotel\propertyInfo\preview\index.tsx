/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import HotelPreview from 'components/hotel/preview'
import useHotelStore from 'lib/store/hotelStore'
import { useNavigate } from 'react-router-dom'

const HotelPreviewPage = () => {
	const { venue: hotel } = useHotelStore()
	const navigate = useNavigate()

	return (
		<>
			<div className='border-b px-6 py-4'>
				<div className='text-xl font-semibold'>Search Result Preview</div>
			</div>
			<div
				className='overflow-y-auto p-6'
				style={{ maxHeight: 'calc(100vh - 13rem)' }}
			>
				<HotelPreview
					venue={hotel}
					onClick={() => navigate(`/hotel-detail/${hotel.id}`)}
				/>
			</div>
		</>
	)
}

export default HotelPreviewPage
