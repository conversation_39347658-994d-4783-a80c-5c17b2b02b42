/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable unicorn/no-array-for-each */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable @typescript-eslint/max-params */
import {
	faCalendarLines,
	faExclamationCircle,
	faInfoCircle
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSBadge from 'components/badge'
import HSButton from 'components/button'
import HSTooltip from 'components/tooltip'
import {
	MarketAvailabilityStatusMap,
	ProposalRequestStatusMap
} from 'lib/helpers/statusMaps'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type {
	IProposalDates,
	ProposalRequest,
	Shortlist
} from 'models/proposalResponseMonitor'
import { useState, useEffect, useMemo } from 'react'
import { useParams, useNavigate, Link } from 'react-router-dom'
import PlannerProposalDates from './proposalDates'
import HSTable from 'components/table'
import {
	faCheck,
	faClipboardListCheck,
	faEdit,
	faEye,
	faPlus
} from '@fortawesome/pro-regular-svg-icons'
import type { IConcessionRequest } from 'models/eventPlans'
import { calculateProposalValue } from 'lib/helpers/proposalValues'
import { additionalTaxTypes } from 'lib/helpers/additionalTaxesAndFees'
import { format, parseISO, set } from 'date-fns'
import { formatCurrency, type ICurrency } from 'lib/helpers'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import { DefaultCurrency } from 'lib/providers/currency.provider'
import HSDropdownButton from 'components/dropdown'
import PlannerNewShortlist from './newShortlist'
import { findSupplierContactOrTeammate } from 'lib/helpers/supplierContacts'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import concessionRequestStore from 'lib/store/plannerEvent/concessionRequestStore'

interface IConcessionEventResponse extends IConcessionRequest {
	responses: {
		venueId: string
		score: number
		concessionValuePlanned: number
		concessionValueProposed: number
	}[]
}

interface ProposalDashboardData {
	hotelName: string
	proposedDates: string
	rate: string
	tfaTrf: string
	fnb: string
	roomCost: string
	service: string
	total: string
	concessions: string
	proposal: ProposalRequest
	declinedToBid?: boolean
	roomRental: string
}

const viewModes = {
	all: 'all',
	byDate: 'byDate'
}

export const filterProposalRequests = (proposalRequest: ProposalRequest) =>
	[
		ProposalRequestStatusMap.Active?.key,
		ProposalRequestStatusMap.Reviewed?.key,
		ProposalRequestStatusMap.ClosedLost?.key,
		ProposalRequestStatusMap.ClosedWon?.key,
		ProposalRequestStatusMap.ContractSigned?.key
	].includes(proposalRequest.status ?? '') || !!proposalRequest.submitted

const PlannerProposalDashboard = () => {
	const { eventId } = useParams()
	const navigate = useNavigate()
	const { eventInfo: eventPlan, setProperty } = eventInfoStore()
	const { currencies } = useCurrencyContext()
	const { concessionRequests: plannerConcessionRequests } =
		concessionRequestStore()

	const [items, setItems] = useState<ProposalRequest[]>([])
	const [totalSummaryProposalRequests, setTotalSummaryProposalRequests] =
		useState(0)
	const [proposals, setProposals] = useState<ProposalRequest[]>([])
	const [proposalDates, setProposalDates] = useState<IProposalDates[]>([])
	const [currency, setCurrency] = useState<ICurrency | undefined>(
		DefaultCurrency.USD
	)
	const [viewMode, setViewMode] = useState(viewModes.all)
	const [proposalDateFilter, setProposalDateFilter] = useState<string | null>(
		null
	)
	const [dateFilterText, setDateFilterText] = useState('Show All')
	const [showShortlistDrawer, setShowShortlistDrawer] = useState(false)
	const [manageShortlist, setManageShortlist] = useState(false)
	const [selectedShortlist, setSelectedShortlist] = useState<Shortlist>({
		hotels: {},
		name: 'All Hotels',
		id: '',
		owner: {
			id: '',
			firstName: '',
			lastName: ''
		},
		createdBy: '',
		created: new Date().toISOString()
	})

	const mapProposalValues = (
		proposalRequest: ProposalRequest,
		applyAttritionFilter: boolean,
		meetingSpaceBudget: number | null,
		roomNightBudget: number | null,
		concessionRequests: IConcessionEventResponse[] | null
	) => {
		const attritionRate = applyAttritionFilter ? 80 : 100
		const roomTaxRate = proposalRequest.currentBid?.taxesFeesAssessments ?? 0
		const concessionValuePlanned = (concessionRequests ?? [])
			.flatMap(cr =>
				cr.responses.filter(r => r.venueId === proposalRequest.venueId)
			)
			.reduce(
				(a, c) =>
					a +
						(c.score > 0
							? c.concessionValuePlanned
							: c.concessionValueProposed) || 0,
				0
			)

		return {
			...proposalRequest,
			proposalValue: calculateProposalValue(proposalRequest.proposalValues),
			proposalDates: proposalRequest.currentBid?.proposalDates?.map(
				proposalDate => {
					const totalRoomsOffered =
						proposalDate.proposalValues?.totalRoomsOffered ?? 0
					const averageRoomRate =
						proposalDate.proposalValues?.averageRoomRate ?? 0
					const roomCost =
						totalRoomsOffered * (attritionRate / 100) * averageRoomRate
					let roomTax = Number(roomCost) * (Number(roomTaxRate) / 100)
					let roomFees =
						totalRoomsOffered *
						(attritionRate / 100) *
						(proposalRequest.currentBid?.perRoomFees ?? 0)
					if (
						(proposalRequest.currentBid?.additionalTaxesAndFees ?? []).length >
						0
					) {
						proposalRequest.currentBid?.additionalTaxesAndFees.forEach(
							additionalTax => {
								if (additionalTax.type === additionalTaxTypes.percent) {
									roomTax += roomCost * ((additionalTax.rate ?? 0) / 100)
								} else if (additionalTax.type === additionalTaxTypes.flat) {
									roomFees +=
										totalRoomsOffered *
										(attritionRate / 100) *
										(additionalTax.rate ?? 0)
								}
							}
						)
					}
					const totalRoomCost = roomCost + roomTax + roomFees

					return {
						...proposalDate,
						declineToBid: proposalDate.declineToBid ?? true,
						roomRate: proposalDate.declineToBid ? null : averageRoomRate,
						roomNights: totalRoomsOffered,
						isAverageWeighted:
							!!proposalDate.proposalValues?.isAverageWeighted ||
							!!proposalRequest.currentBid?.isAverageWeighted,
						attritionRate: proposalRequest.currentBid?.attritionRate,
						roomTax,
						roomFees,
						totalRoomCost,
						foodAndBeverage: proposalDate.proposalValues?.foodAndBeverage,
						foodAndBeverageTax: proposalDate.proposalValues?.foodAndBeverageTax,
						roomRental: proposalDate.proposalValues?.roomRental,
						roomRentalTax: proposalDate.proposalValues?.roomRentalTax,
						serviceCharge: proposalDate.proposalValues?.serviceCharge,
						serviceChargeTax: proposalDate.proposalValues?.serviceChargeTax,
						estTotalCost: proposalDate.proposalValues
							? calculateProposalValue(proposalDate.proposalValues)
							: null,
						fbBudget:
							(meetingSpaceBudget || 0) >=
							(proposalDate.proposalValues?.foodAndBeverage ?? 0),
						meetsBudget:
							(roomNightBudget || 0) >=
							(proposalRequest.currentBid?.averageRate || 0),
						concessionValuePlanned
					}
				}
			)
		}
	}

	const getCurrency = useMemo(
		() => currencies[eventPlan?.currencyCode ?? ''],
		[currencies, eventPlan?.currencyCode]
	)
	useEffect(() => {
		if (eventPlan?.proposalRequests) {
			setProposals(
				eventPlan.proposalRequests
					.filter(pr => filterProposalRequests(pr))
					.map(pr =>
						mapProposalValues(
							pr,
							false,
							eventPlan.meetingSpaceBudget,
							eventPlan.roomNightBudget,
							plannerConcessionRequests
						)
					) as ProposalRequest[]
			)

			const a: IProposalDates[] = []
			if (eventPlan.startDate) {
				a.push({
					key: format(parseISO(eventPlan.startDate), 'yyyy.MM.dd'),
					startDate: parseISO(eventPlan.startDate).toISOString(),
					endDate: eventPlan.endDate
						? parseISO(eventPlan.endDate).toISOString()
						: null,
					count: 0,
					totalValue: 0,
					averageValue: 0
				})
			}
			if (eventPlan.alternateDates?.some(ad => ad.startDate)) {
				a.push(
					...eventPlan.alternateDates
						.filter(ad => ad.startDate)
						.map(ad => ({
							key: format(parseISO(ad.startDate ?? ''), 'yyyy.MM.dd'),
							startDate: parseISO(ad.startDate ?? '').toISOString(),
							endDate: ad.endDate ? parseISO(ad.endDate).toISOString() : null,
							count: 0,
							totalValue: 0,
							averageValue: 0,
							declineToBid: false,
							isVaryingRate: false,
							isOfferedDifferentThanRequested: false,
							preferred: false,
							alternate: false,
							suggested: false,
							contracted: false,
							value: 0,
							proposalValues: null
						}))
				)
			}
			const proposed = eventPlan.proposalRequests
				.filter(pr => filterProposalRequests(pr))
				.flatMap(pr =>
					pr.currentBid?.proposalDates?.filter(pd => pd.declineToBid !== true)
				)
				.reduce(
					(accumulator: IProposalDates[], c) => {
						const pd = accumulator.find(d => d.key === c?.key)
						const totalValue = c?.proposalValues
							? calculateProposalValue(c.proposalValues)
							: null
						if (pd) {
							pd.count += 1
							pd.totalValue = (pd.totalValue ?? 0) + (totalValue ?? 0)
							pd.averageValue = (totalValue ?? 0) / pd.count
						} else {
							accumulator.push({
								key: c?.key ?? '',
								preferred: c?.preferred ?? false,
								alternate: c?.alternate ?? false,
								suggested: c?.suggested ?? false,
								startDate: parseISO(c?.startDate ?? '').toISOString(),
								endDate: parseISO(c?.endDate ?? '').toISOString(),
								count: 1,
								totalValue,
								averageValue: totalValue
							})
						}
						return accumulator
					},
					[...a]
				)
			setProposalDates(proposed)
			setCurrency(getCurrency)

			setProposalDateFilter(s => s ?? a[0]?.key)
		}
	}, [
		eventPlan?.alternateDates,
		eventPlan?.endDate,
		eventPlan?.meetingSpaceBudget,
		eventPlan?.proposalRequests,
		eventPlan?.roomNightBudget,
		eventPlan?.startDate,
		getCurrency,
		plannerConcessionRequests
	])

	useEffect(() => {
		const summaryProposalRequests = eventPlan?.summaryProposalRequests ?? []
		const proposalRequests = eventPlan?.proposalRequests ?? []

		setTotalSummaryProposalRequests(summaryProposalRequests.length)
		setItems(
			summaryProposalRequests.filter(spr => {
				if (proposalRequests.some(pr => pr.venueId === spr.venueId)) {
					return false
				}
				return (
					spr.status === MarketAvailabilityStatusMap.Active?.key ||
					spr.status === MarketAvailabilityStatusMap.Reviewed?.key
				)
			})
		)
	}, [eventPlan?.summaryProposalRequests, eventPlan?.proposalRequests])

	const filteredProposals = proposals
		.map(proposal => {
			if (viewMode === viewModes.all) {
				return proposal
			}

			const matchingProposalDates = (proposal.proposalDates ?? []).filter(
				proposalDate => proposalDate.key === proposalDateFilter
			)

			if (matchingProposalDates.length > 0) {
				return {
					...proposal,
					proposalDates: matchingProposalDates
				}
			}

			return null
		})
		.filter(proposal => proposal !== null)

	const dateTemplate = (item: ProposalDashboardData) => (
		<div className='flex flex-col'>
			<div>{item.proposedDates}</div>
			<div>
				{item.declinedToBid === true ? (
					<div className='text-xs font-medium text-red-600'>Dates Declined</div>
				) : null}
			</div>
		</div>
	)

	const nameTemplate = (item: ProposalDashboardData) => {
		const suggestedBy = findSupplierContactOrTeammate(
			eventPlan?.supplierContacts ?? null,
			item.proposal.createdBy
		)
		const infoDateLabel =
			item.proposal.status === 'ClosedWon'
				? 'Selected:'
				: item.proposal.status === 'ClosedLost'
					? 'Declined:'
					: 'Received:'
		const infoDate =
			item.proposal.status === 'ClosedWon'
				? item.proposal.won
				: item.proposal.status === 'ClosedLost'
					? item.proposal.lost
					: item.proposal.submitted

		return (
			<div className='flex flex-col'>
				<Link to='/'>
					<div className='text-sm font-medium text-primary-700 underline'>
						{item.hotelName}
					</div>
				</Link>
				<div className='text-xs font-medium text-gray-400'>
					{(suggestedBy?.isHopSkip === true ? (
						<div className=''>Hopskip Comparable Hotel</div>
					) : null) ||
						(suggestedBy && (
							<div className=''>
								Suggested by{' '}
								{`${suggestedBy.firstName} ${suggestedBy.lastName}`}
							</div>
						))}
				</div>
				<div className='text-xs font-medium text-gray-400'>{`${infoDateLabel} ${infoDate?.toString() ? format(parseISO(infoDate?.toString()), 'eee MMM d, yyyy') : ''}`}</div>
			</div>
		)
	}

	// eslint-disable-next-line react-hooks/exhaustive-deps
	const filterProposals = (proposal: ProposalRequest) =>
		selectedShortlist.id
			? Object.keys(selectedShortlist.hotels).includes(proposal.venueId)
			: true

	const data = useMemo(
		() =>
			filteredProposals
				.filter(pr => filterProposals(pr))
				.sort((c: ProposalRequest, n: ProposalRequest) =>
					(c.venueName ?? '') > (n.venueName ?? '') ? 1 : -1
				)
				.flatMap(proposal =>
					(proposal.proposalDates ?? [])
						.sort((c, n) => ((c.key ?? '') > (n.key ?? '') ? 1 : -1))
						.map(
							proposalDate =>
								({
									proposal,
									hotelName: proposal.venueName,
									proposedDates: `${format(parseISO(proposalDate.startDate ?? ''), 'MMM d, yyyy')}${proposalDate.endDate ? ` - ${format(parseISO(proposalDate.endDate), 'MMM d, yyyy')}` : ''}`,
									rate: proposalDate.declineToBid
										? '-'
										: formatCurrency(proposalDate.roomRate ?? 0, currency),
									tfaTrf: proposalDate.declineToBid
										? '-'
										: formatCurrency(
												(proposalDate.roomTax ?? 0) +
													(proposalDate.roomFees ?? 0),
												currency
											),
									fnb: proposalDate.declineToBid
										? '-'
										: formatCurrency(
												proposalDate.foodAndBeverage ?? 0,
												currency
											),
									roomRental: proposalDate.declineToBid
										? '-'
										: formatCurrency(proposalDate?.roomRental ?? 0, currency),
									roomCost: eventPlan?.meetingSpaceRequired
										? proposalDate.declineToBid
											? ''
											: formatCurrency(
													proposalDate.totalRoomCost ?? 0,
													currency
												)
										: '-',
									service: proposalDate.declineToBid
										? '-'
										: formatCurrency(proposalDate.serviceCharge ?? 0, currency),
									total: formatCurrency(
										proposalDate.estTotalCost ?? 0,
										currency
									),
									concessions: formatCurrency(
										proposalDate.concessionValuePlanned ?? 0,
										currency
									),
									declinedToBid: proposalDate.declineToBid
								}) as ProposalDashboardData
						)
				),
		[
			currency,
			eventPlan?.meetingSpaceRequired,
			filterProposals,
			filteredProposals
		]
	)

	const tfaTemplate = () => (
		<div className='flex items-center gap-2'>
			<div>TFA/TFR</div>
			<TooltipComponent content='Total Taxes, Fees, and Assessments'>
				<FontAwesomeIcon icon={faInfoCircle} className='text-gray-400' />
			</TooltipComponent>
		</div>
	)

	const totalTemplate = () => (
		<div className='flex items-center gap-2'>
			<div>Total</div>
			<TooltipComponent
				content='Total estimated cost including taxes on F&B, rental, and service charges.'
				title='Total Including Taxes'
			>
				<FontAwesomeIcon icon={faInfoCircle} className='text-gray-400' />
			</TooltipComponent>
		</div>
	)

	const onClickDeleteShortlist = (shortlist: Shortlist) => {
		if (selectedShortlist.id === shortlist.id) {
			setSelectedShortlist({
				hotels: {},
				name: 'All Hotels',
				id: '',
				owner: {
					id: '',
					firstName: '',
					lastName: ''
				},
				createdBy: '',
				created: new Date().toISOString()
			})
		}
		const updatedShortlists = Object.values(eventPlan?.shortlists ?? {}).filter(
			s => s.id !== shortlist.id
		)
		setProperty('shortlists', updatedShortlists)

		if (updatedShortlists.length === 0) {
			setShowShortlistDrawer(false)
			setManageShortlist(false)
		}
	}

	return (
		<div className='flex flex-col'>
			<div className='border-b'>
				<div className='flex justify-between gap-2'>
					<div className='flex flex-col p-4'>
						<div className='text-xl font-semibold text-gray-900'>
							Proposals Dashboard
						</div>
						<div className='text-sm font-normal text-gray-500'>
							Compare costs and create shortlists. If multiple room types were
							requested, room rates are displayed as averages.
						</div>
					</div>
					<div className='flex items-center gap-2 border-l p-4'>
						<div className='flex flex-col gap-1'>
							<div className='flex items-center gap-2'>
								<div className='text-gary-700 text-sm font-normal'>
									Comparable Hotels
								</div>
								<div>
									<HSTooltip content="Explore hotels with availability in the same locations you're requesting proposals for">
										<FontAwesomeIcon icon={faExclamationCircle} />
									</HSTooltip>
								</div>
							</div>
							<div className='flex items-center gap-2'>
								<div className='flex items-center gap-1 border-r pr-2'>
									<HSBadge className='w-fit text-center'>
										{items.length}
									</HSBadge>
									<div className='text-xxs font-normal text-gray-400'>
										New Hotels
									</div>
								</div>
								<div className='flex items-center gap-1'>
									<div className='text-xxs font-normal text-gray-600'>
										{totalSummaryProposalRequests}
									</div>
									<div className='text-xxs font-normal text-gray-400'>
										Total Hotels
									</div>
								</div>
							</div>
						</div>
						<HSButton
							size='sm'
							onClick={() => {
								navigate(
									`/planner/event/${eventId}/review-proposals/comparable-hotels`
								)
							}}
						>
							View Comparable Hotels
						</HSButton>
					</div>
				</div>
			</div>
			<div className='flex flex-col gap-4 p-6'>
				<PlannerProposalDates
					proposals={proposals}
					currency={currency}
					proposalDates={proposalDates}
				/>
				<div className='flex items-center justify-between p-4'>
					<div className='text-sm font-medium text-gray-900'>
						{data.length} Hotels
					</div>
					<div className='flex items-center gap-4'>
						<HSDropdownButton
							outline
							label={
								<div className='flex items-center gap-1'>
									<div className='text-xs font-normal text-gray-900'>
										<FontAwesomeIcon
											icon={faCalendarLines}
											className='h-3 w-3'
										/>
									</div>
									<div className='w-fit text-sm font-medium text-gray-900'>
										Dates: {dateFilterText}
									</div>
								</div>
							}
							color='gray'
							items={[
								{
									cssName:
										dateFilterText === 'Show All'
											? 'bg-primary-50  rounded-none py-2'
											: '',
									id: 'show-all',
									clickFunction: () => {
										setViewMode(viewModes.all)
										setProposalDateFilter('Show All')
										setDateFilterText('Show All')
									},
									item: (
										<div className='flex justify-between gap-2'>
											<div>Show All</div>
											<div>
												{dateFilterText === 'Show All' ? (
													<FontAwesomeIcon
														icon={faCheck}
														className='text-primary-600'
														size='xl'
													/>
												) : null}
											</div>
										</div>
									)
								},
								...proposalDates.map((pd, index) => ({
									id: `date-${index}`,
									clickFunction: () => {
										setViewMode(viewModes.byDate)
										setProposalDateFilter(pd.key)
										setDateFilterText(
											`${format(pd.startDate ?? '', 'MMM d, yyyy')}${
												pd.endDate
													? ` - ${format(pd.endDate, 'MMM d, yyyy')}`
													: ''
											}`
										)
									},
									item: (
										<div className='flex justify-between gap-2'>
											<div>
												{`${format(pd.startDate ?? '', 'MMM d, yyyy')}${
													pd.endDate
														? ` - ${format(pd.endDate, 'MMM d, yyyy')}`
														: ''
												}`}
											</div>
											<div>
												{dateFilterText ===
												`${format(pd.startDate ?? '', 'MMM d, yyyy')}${
													pd.endDate
														? ` - ${format(pd.endDate, 'MMM d, yyyy')}`
														: ''
												}` ? (
													<FontAwesomeIcon
														icon={faCheck}
														className='text-primary-600'
														size='xl'
													/>
												) : null}
											</div>
										</div>
									),
									cssName:
										dateFilterText ===
										`${format(pd.startDate ?? '', 'MMM d, yyyy')}${
											pd.endDate
												? ` - ${format(pd.endDate, 'MMM d, yyyy')}`
												: ''
										}`
											? 'bg-primary-50  rounded-none py-2'
											: ''
								}))
							]}
							value={proposalDateFilter ?? 'Show All'}
							className='dropdown w-fit'
							showDropdownIcon={false}
						/>
						<HSDropdownButton
							outline
							label={
								<div className='flex items-center gap-1'>
									<FontAwesomeIcon
										icon={faClipboardListCheck}
										className='h-3 w-3'
									/>
									<span className='w-fit truncate'>
										Shortlist: {selectedShortlist?.name}
									</span>
								</div>
							}
							color='gray'
							items={[
								{
									cssName:
										selectedShortlist?.name === 'All Hotels'
											? 'bg-primary-50  rounded-none py-2'
											: '',
									id: 'all-hotels',
									clickFunction: () => {
										setSelectedShortlist({
											hotels: {},
											name: 'All Hotels',
											id: '',
											owner: {
												id: '',
												firstName: '',
												lastName: ''
											},
											createdBy: '',
											created: new Date().toISOString()
										})
									},
									item: (
										<div className='flex justify-between gap-2'>
											<div>All Hotels</div>
											{selectedShortlist?.name === 'All Hotels' ? (
												<FontAwesomeIcon
													icon={faCheck}
													className='text-primary-600'
													size='xl'
												/>
											) : null}
										</div>
									)
								},
								...Object.values(eventPlan?.shortlists || {}).map(
									(shortlist, index) => ({
										id: `shortlist-${index}`,
										clickFunction: () => {
											setSelectedShortlist(shortlist)
										},
										item: (
											<div className='flex justify-between gap-2'>
												<div>{shortlist.name}</div>
												{selectedShortlist?.name === shortlist.name ? (
													<FontAwesomeIcon
														icon={faCheck}
														className='text-primary-600'
														size='xl'
													/>
												) : null}
											</div>
										),
										cssName:
											selectedShortlist?.name === shortlist.name
												? 'bg-primary-50  rounded-none py-2'
												: ''
									})
								),

								{
									id: 'create-new-shortlist',
									clickFunction: () => {
										setShowShortlistDrawer(true)
									},
									item: (
										<span className='flex items-center gap-2 text-primary-700'>
											<FontAwesomeIcon icon={faPlus} />
											Create New
										</span>
									)
								},
								{
									id: 'manage-shortlist',
									clickFunction: () => {
										setManageShortlist(true)
										setShowShortlistDrawer(true)
									},
									item: (
										<span className='flex items-center gap-2 text-gray-600'>
											<FontAwesomeIcon icon={faEdit} />
											Manage Shortlists
										</span>
									)
								}
							]}
							showDropdownIcon={false}
						/>
					</div>
				</div>

				<HSTable
					rows={data}
					rowDataBound={[
						{
							condition: row => row.declinedToBid === true,
							className: '!bg-red-50'
						}
					]}
					columns={[
						{
							field: 'hotelName',
							headerText: 'Hotel Name',
							freeze: 'left',
							render: item => nameTemplate(item)
						},
						{
							field: 'proposedDates',
							headerText: 'Proposed Dates',
							sortable: true,
							render: item => dateTemplate(item)
						},
						{
							field: 'rate',
							headerText: 'Rate',
							sortable: true,
							visible: eventPlan?.roomBlocksRequired ?? false
						},
						{
							field: 'tfaTrf',
							headerText: 'TFA/TFR',
							sortable: true,
							visible: eventPlan?.roomBlocksRequired ?? false,
							headerTemplate: tfaTemplate
						},
						{
							field: 'roomCost',
							headerText: 'Room Cost',
							sortable: true,
							visible: !(
								eventPlan?.roomBlocksRequired === false ||
								!!(
									eventPlan?.roomBlocksRequired &&
									!eventPlan?.meetingSpaceRequired
								)
							)
						},
						{
							field: 'fnb',
							headerText: 'F&B',
							sortable: true,
							visible: eventPlan?.meetingSpaceRequired ?? false
						},
						{
							field: 'roomRental',
							headerText: 'Rental',
							sortable: true,
							visible: eventPlan?.meetingSpaceRequired ?? false
						},
						{
							field: 'service',
							headerText: 'Service',
							sortable: true,
							visible: eventPlan?.meetingSpaceRequired ?? false
						},
						{
							field: 'concessions',
							headerText: 'Concessions',
							sortable: true
						},
						{
							field: 'total',
							headerText: 'Total',
							sortable: true,
							headerTemplate: totalTemplate
						},
						{
							field: 'name',
							headerText: 'Open',
							render: () => (
								<HSButton color='light' onClick={() => {}} size='sm'>
									<FontAwesomeIcon icon={faEye} />
								</HSButton>
							),
							freeze: 'right'
						}
					]}
				/>
			</div>
			{showShortlistDrawer ? (
				<PlannerNewShortlist
					showShortlistDrawer={showShortlistDrawer}
					onClose={() => {
						setShowShortlistDrawer(false)
						setManageShortlist(false)
					}}
					manageShortlist={manageShortlist}
					onClickDeleteShortlist={onClickDeleteShortlist}
				/>
			) : null}
		</div>
	)
}

export default PlannerProposalDashboard
