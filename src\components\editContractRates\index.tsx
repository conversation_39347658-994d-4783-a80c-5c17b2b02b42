import type { IContractClauses } from 'models/organizations'
import HSDrawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import HSTable from 'components/table'
import HSButton from 'components/button'

interface IContractClauseModal {
	onClose: () => void
	isEdit: boolean
	isProfile: boolean
	editContractClause?: IContractClauses | undefined
	userProfileId: string
	contractClauses: IContractClauses[] | undefined
	onAddUpdate: (value: IContractClauses) => void
	showAddFromToolbox?: boolean
}

interface RoomTypeData {
	rate: number
	proposed: number
	contracted: number
	savings: number
	savingsincludingtax: number
}

const ContractClauseModal = (properties: IContractClauseModal) => {
	const { onClose } = properties

	const roomTypeData: RoomTypeData[] = [
		{
			rate: 174,
			proposed: 200,
			contracted: 104,
			savings: 200,
			savingsincludingtax: 506
		},
		{
			rate: 158,
			proposed: 0,
			contracted: 14,
			savings: 132,
			savingsincludingtax: 0
		}
	]

	return (
		<div className='control-pane'>
			<div id='targetElement'>
				<HSDrawer
					id='add-edit-contract-clause-drawer'
					position='right'
					onClose={onClose}
					open
					style={{ width: '20vw' }}
				>
					<Drawer.Header
						title='Edit Contracted Room Rates'
						titleIcon={() => null}
					/>

					<Drawer.Items style={{ height: 'calc(100vh - 10rem)' }}>
						<div className='flex flex-col gap-8'>
							<div className='gray-200 flex flex-row rounded-lg border p-4'>
								<div className='text-sm font-medium text-gray-900'>
									Hotel Name
								</div>
							</div>

							<div className='gap-2'>
								<HSTable
									allowPaging={false}
									rows={roomTypeData}
									columns={[
										{
											field: 'runofhouse',
											headerText: 'Run of house',
											width: 120
										}
									]}
								/>
							</div>
						</div>
					</Drawer.Items>
					<Drawer.Items>
						<div className='flex items-center gap-2'>
							<HSButton color='light' onClick={onClose} className='grow'>
								Cancel
							</HSButton>
							<HSButton className='grow'>Save</HSButton>
						</div>
					</Drawer.Items>
				</HSDrawer>
			</div>
		</div>
	)
}

export default ContractClauseModal
