/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable unicorn/no-array-reduce */
import type { ICurrentBid, RoomBlock } from 'models/proposalResponseMonitor'

export const renderRequestedRooms = (
	requested: number,
	offered: number,
	allowPartialProposal: boolean,
	isOfferedDifferentThanRequested: boolean
) => {
	const isValid =
		!allowPartialProposal && isOfferedDifferentThanRequested
			? (requested > 0 && offered > 0 && requested <= offered) ||
				requested === 0
			: true
	return (
		<div className='flex items-center gap-1'>
			<span
				className={`text-sm font-normal ${isValid ? 'text-gray-600' : 'text-red-600'}`}
			>
				{requested}
			</span>
			<div
				className={`w-20 text-xs font-medium ${isValid ? 'text-gray-400' : 'text-red-600'}`}
			>
				Requested
			</div>
		</div>
	)
}

export const isRoomsOfferedPerNightEnough = (
	rbr: RoomBlock,
	currentBid: ICurrentBid,
	allowPartialProposal: boolean,
	isRoomBlockValid: boolean,
	rbrIndex: number,
	proposalDateKey: string
) => {
	// For each dayNumber in all roomNights, sum requested and offered
	const roomsMap: {
		dayNumber: number
		roomsRequested: number
		roomsOffered: number
	}[] = []
	// Collect all unique dayNumbers from all roomTypeRequests
	const dayNumbers = [
		...new Set(
			rbr.roomTypeRequests.flatMap(
				rtr => rtr.roomNights?.map(rn => rn.dayNumber) ?? []
			)
		)
	].sort((a, b) => a - b)

	for (const dayNumber of dayNumbers) {
		let roomsRequested = 0
		let roomsOffered = 0
		for (const rtr of rbr.roomTypeRequests) {
			const roomNight = rtr.roomNights?.find(n => n.dayNumber === dayNumber)
			roomsRequested += roomNight?.roomsRequested ?? 0
			// Find all matching roomRates for this block, type, and day
			const offered = currentBid.roomRates?.find(
				rr => rr.roomType === rtr.roomType && rr.dayNumber === dayNumber
			)
			roomsOffered += offered?.offered ?? 0
		}
		roomsMap.push({ dayNumber, roomsRequested, roomsOffered })
	}

	const roomsRequestedTotal =
		rbr.roomTypeRequests.reduce((accumulator, current) => {
			const total =
				accumulator +
				(current.roomNights?.reduce((a, c) => {
					const sum = a + (c.roomsRequested ?? 0)
					return sum
				}, 0) ?? 0)
			return total
		}, 0) || 0

	const roomsOfferedTotal =
		currentBid.roomRates
			?.filter(
				rr =>
					rr.roomBlockIndex === rbrIndex && rr.key?.endsWith(proposalDateKey)
			)
			.reduce((accumulator, current) => {
				const total = accumulator + (current.offered ?? 0)
				return total
			}, 0) || 0

	const isValid = allowPartialProposal
		? true
		: roomsRequestedTotal > 0 &&
			roomsOfferedTotal > 0 &&
			roomsOfferedTotal >= roomsRequestedTotal

	return isValid ? null : (
		<div className='flex flex-col border-b bg-orange-50 p-2'>
			<div className='text-sm font-medium text-orange-800'>
				Room Count Offered Below Requested
			</div>
			<span className='text-xs font-normal text-orange-800'>
				<span>
					You&apos;ve offered {roomsOfferedTotal} total rooms, but the planner
					requested {roomsRequestedTotal}.
				</span>{' '}
				{isRoomBlockValid ? (
					<span>
						While you can still submit your proposal, offering the full room
						count requested would strengthen your proposal
					</span>
				) : null}
			</span>
		</div>
	)
}
