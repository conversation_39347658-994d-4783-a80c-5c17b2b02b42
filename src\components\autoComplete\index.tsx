/* eslint-disable unicorn/no-keyword-prefix */
import type { ChangeEvent } from 'react'
import type React from 'react'
import { useState } from 'react'
import { TextInput } from 'flowbite-react'

interface AutoCompleteProperties {
	suggestions: string[]
	placeholder?: string
	onChange: (value: string) => void
	value: string
	onSelect: (suggestion: string) => void
}

const AutoComplete: React.FC<AutoCompleteProperties> = ({
	suggestions,
	placeholder,
	onChange,
	value,
	onSelect
}) => {
	const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([])
	const [showSuggestions, setShowSuggestions] = useState<boolean>(false)

	const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
		const newValue = event.target.value
		onChange(newValue)

		const filtered = suggestions.filter(suggestion =>
			suggestion.toLowerCase().includes(newValue.toLowerCase())
		)
		setFilteredSuggestions(filtered)
		setShowSuggestions(true)
	}

	const handleSuggestionClick = (suggestion: string) => {
		onChange(suggestion)
		onSelect(suggestion)
		setShowSuggestions(false)
	}

	return (
		<div className='flex'>
			<TextInput
				type='text'
				value={value}
				onChange={handleChange}
				placeholder={placeholder}
				className='grow border-none'
			/>
			{showSuggestions && filteredSuggestions.length > 0 ? (
				<ul className='r absolute z-10 mt-1 max-h-60 w-full overflow-auto bg-white '>
					{filteredSuggestions.map((suggestion, index) => (
						<div
							// eslint-disable-next-line react/no-array-index-key
							key={index}
							className='cursor-pointer px-4 py-2 hover:bg-gray-100'
							onClick={() => handleSuggestionClick(suggestion)}
							onKeyDown={event => {
								if (event.key === 'Enter') {
									handleSuggestionClick(suggestion)
								}
							}}
							role='button'
							tabIndex={-1}
						>
							{suggestion}
						</div>
					))}
				</ul>
			) : null}
		</div>
	)
}

export default AutoComplete
