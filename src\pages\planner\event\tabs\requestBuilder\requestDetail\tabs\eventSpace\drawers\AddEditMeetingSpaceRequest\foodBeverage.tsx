/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable no-constant-condition */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable react/no-array-index-key */
import { faInfoCircle, faTrashXmark } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSSelect from 'components/select'
import HSTextArea from 'components/textarea'
import HSTextField from 'components/textField'
import HSTooltip from 'components/tooltip'
import { useGetFoodAndBeverageTypes } from 'lib/services/foodAndBeverage.service'
import meetingSpaceRequestStore from './store'
import type { IFoodAndBeverageRequest } from 'models/proposalResponseMonitor'
import { v4 as uuidv4 } from 'uuid'

const FoodBeverage = () => {
	const { data: foodAndBeverageTypes } = useGetFoodAndBeverageTypes()
	const { meetingSpaceRequest, foodAndBeverages, setFoodAndBeverages } =
		meetingSpaceRequestStore()

	const onAddNew = () => {
		if (foodAndBeverages && meetingSpaceRequest) {
			const newFbr: IFoodAndBeverageRequest = {
				type: '',
				notes: '',
				id: uuidv4(),
				quantityUnit: null,
				sortIndex: foodAndBeverages.filter(
					fbr => fbr.meetingSpaceRequestId === meetingSpaceRequest.id
				).length,
				meetingSpaceRequestId: meetingSpaceRequest.id,
				quantity: meetingSpaceRequest.capacity ?? 0,
				dayNumber: meetingSpaceRequest.dayNumber,
				startTime: meetingSpaceRequest.startTime,
				endTime: meetingSpaceRequest.endTime
			}
			setFoodAndBeverages([...foodAndBeverages, newFbr])
		}
	}

	const onDelete = (fbr: IFoodAndBeverageRequest) => {
		if (foodAndBeverages && meetingSpaceRequest) {
			const updatedValue = foodAndBeverages.filter(
				f =>
					f.id !== fbr.id && f.meetingSpaceRequestId === meetingSpaceRequest.id
			)
			setFoodAndBeverages(updatedValue)
		}
	}

	return meetingSpaceRequest && foodAndBeverages ? (
		<div className='flex flex-col gap-2'>
			<div className='text-sm font-medium text-gray-900'>
				Food and Beverage (optional)
			</div>
			<div className='card'>
				{foodAndBeverages.map((fbr, fbrIndex) => (
					<div className='flex flex-col gap-2.5 border-b p-4' key={fbrIndex}>
						<div className='flex items-end gap-4'>
							<div className='flex grow flex-col gap-2'>
								<div className='flex items-center gap-2'>
									<div className='text-sm font-normal text-gray-700'>
										Meal type
									</div>
									<HSTooltip content='Select the type of meal service required for this meeting space'>
										<FontAwesomeIcon
											icon={faInfoCircle}
											className='text-gray-500'
										/>
									</HSTooltip>
								</div>
								<HSSelect
									value={fbr.type ?? ''}
									name='foodAndBeverageTypeId'
									onChange={event => {
										const updatedFbr = {
											...fbr,
											type: event.target.value
										}

										const updatedValue = [
											...foodAndBeverages.splice(0, fbrIndex),
											updatedFbr,
											...foodAndBeverages.splice(fbrIndex + 1)
										]
										setFoodAndBeverages(updatedValue)
									}}
								>
									<option value=''>Select...</option>
									{foodAndBeverageTypes
										?.sort((c, n) => c.sortIndex - n.sortIndex)
										.map((fbt, index) => (
											<option key={index} value={fbt.id}>
												{fbt.name}
											</option>
										))}
								</HSSelect>
							</div>
							<div className='flex flex-col gap-2'>
								<div className='flex items-center gap-2'>
									<div className='text-sm font-normal text-gray-700'>
										Quantity
									</div>
									<HSTooltip content='Specify the number of attendees requiring food and beverage service'>
										<FontAwesomeIcon
											icon={faInfoCircle}
											className='text-gray-500'
										/>
									</HSTooltip>
								</div>
								<div className='flex w-40 flex-col gap-1'>
									<HSTextField
										value={fbr.quantity ?? ''}
										name='amount'
										min={2}
										placeholder='Amount'
										groupPlacement='left'
										groupItem={
											<div
												className={`text-xs font-medium text-gray-600 ${true ? 'text-gray-600' : 'text-white'}`}
											>
												people
											</div>
										}
										onChange={event => {
											const updatedFbr = {
												...fbr,
												quantity: Number(event.target.value)
											}

											const updatedValue = [
												...foodAndBeverages.splice(0, fbrIndex),
												updatedFbr,
												...foodAndBeverages.splice(fbrIndex + 1)
											]
											setFoodAndBeverages(updatedValue)
										}}
									/>
								</div>
							</div>
							<HSButton color='light' onClick={() => onDelete(fbr)}>
								<FontAwesomeIcon
									icon={faTrashXmark}
									className='text-red-600'
									size='lg'
								/>
							</HSButton>
						</div>
						<HSTextArea
							placeholder='Add any special dietary requirements, service timing preferences, or other F&B-related instructions for the hotel'
							rows={3}
							value={fbr.notes ?? ''}
							onChange={event => {
								const updatedFbr = {
									...fbr,
									notes: event.target.value
								}

								const updatedValue = [
									...foodAndBeverages.splice(0, fbrIndex),
									updatedFbr,
									...foodAndBeverages.splice(fbrIndex + 1)
								]
								setFoodAndBeverages(updatedValue)
							}}
						/>
					</div>
				))}
				<div className='p-2'>
					<HSButton
						color='text'
						onClick={() => onAddNew()}
						disabled={foodAndBeverages
							.filter(
								fbr => fbr.meetingSpaceRequestId === meetingSpaceRequest.id
							)
							.some(
								fbr => !fbr.type || !fbr.quantity || Number.isNaN(fbr.quantity)
							)}
					>
						+ Add New
					</HSButton>
				</div>
			</div>
		</div>
	) : null
}

export default FoodBeverage
