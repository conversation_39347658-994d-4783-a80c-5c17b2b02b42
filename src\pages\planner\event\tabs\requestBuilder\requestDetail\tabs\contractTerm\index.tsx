/* eslint-disable react/no-array-index-key */
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSCheckbox from 'components/checkbox'
import LocationSelector from 'components/locationSelector'
import PageLoader from 'components/pageLoader'
import HSSelect from 'components/select'
import HSTextField from 'components/textField'
import HSToggleSwitch from 'components/toggleSwitch'
import HSTooltip from 'components/tooltip'
import { Button } from 'flowbite-react'
import { paymentMethodTypeOptions } from 'lib/common/paymentMethodTypeOptions'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { formatCurrency, UnitedStatesDollar } from 'lib/helpers'
import { useGetOrganizationById } from 'lib/services/organizations.service'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { ILocation } from 'models/location'
import type {
	ContractSigner,
	PaymentMethods,
	ReservationMethods,
	RfpOwner
} from 'models/proposalResponseMonitor'
import { useEffect, useState } from 'react'

const ContractTerm = () => {
	const { eventInfo: event, setProperty, disableFields } = eventInfoStore()
	const { currencies } = useCurrencyContext()
	const { userProfile } = useUserProfileContext()
	const { data: organization } = useGetOrganizationById(
		event?.organizationId ?? '',
		!!event?.organizationId
	)

	const [contractSignerLocation, setContractSignerLocation] = useState({})
	const [rfpOwnerLocation, setRfpOwnerLocation] = useState({})

	const [useAddressLookupSigner, setUseAddressLookupSigner] = useState(true)
	const [useAddressLookupOwner, setUseAddressLookupOwner] = useState(true)
	const [applyPaymentToAll, setApplyPaymentToAll] = useState(false)

	const currency = event?.currencyCode
		? currencies[event.currencyCode]
		: UnitedStatesDollar

	useEffect(() => {
		if (event) {
			setContractSignerLocation({
				name: event.contractSigner?.address
			})
			setRfpOwnerLocation({
				name: event.rfpOwner?.address
			})
		}
	}, [event, event?.contractSigner?.address])

	const onChange = (
		name: string,
		value:
			| RfpOwner
			| ReservationMethods
			| PaymentMethods
			| ContractSigner
			| boolean
			| null
			| number
			| string
	) => {
		setProperty(name, value)
	}

	const onChangeAddress = (
		location: ILocation,
		name: 'contractSigner' | 'rfpOwner'
	) => {
		if (location.addressComponents && event) {
			const { streetNumber, route, city, state, zip, country } =
				location.addressComponents
			const v: ContractSigner = {}
			if (route) {
				v.address = `${streetNumber ? `${streetNumber} ` : ''}${route}`
			}
			if (city) {
				v.city = city
			}
			if (state) {
				v.state = state
			}
			if (zip) {
				v.postalCode = zip
			}
			if (country) {
				v.country = country
			}

			setProperty(name, {
				...event[name],
				...v
			})
		}
	}

	const selectClassName = 'bg-gray-200 font-bold'

	return event ? (
		<div className='flex flex-col gap-6 px-6 pb-6 pt-4'>
			<div className='text-lg font-semibold text-gray-900'>Contract Terms</div>

			<div className='card flex flex-col gap-6 p-4'>
				<div className='flex gap-4 border-b pb-4'>
					{event.roomBlocksRequired ? (
						<div className='flex w-1/3 flex-col gap-2 border-r'>
							<div className='flex items-center gap-2'>
								<div className='text-sm font-medium text-gray-700'>
									Reservation method(s)
								</div>
								{!event.reservationMethods?.bookingLink &&
								!event.reservationMethods?.callIn &&
								!event.reservationMethods?.masterList ? (
									<FontAwesomeIcon
										icon={faInfoCircle}
										className='text-orange-600'
									/>
								) : null}
							</div>
							<div className='flex flex-wrap items-center gap-6'>
								<div className='flex items-center gap-2'>
									<HSCheckbox
										checked={event.reservationMethods?.bookingLink}
										onChange={event_ => {
											if (event.reservationMethods)
												onChange('reservationMethods', {
													...event.reservationMethods,
													bookingLink: event_.target.checked
												})
										}}
									/>
									<div className='flex items-center gap-1'>
										<div className='text-sm font-normal text-gray-700'>
											Booking Link
										</div>
										<HSTooltip content='I would like to receive a booking link to provide my guests so they can book their reservations online'>
											<FontAwesomeIcon
												icon={faInfoCircle}
												className='text-gray-700'
											/>
										</HSTooltip>
									</div>
								</div>
								<div className='flex items-center gap-2'>
									<HSCheckbox
										checked={event.reservationMethods?.callIn}
										onChange={event_ => {
											if (event.reservationMethods)
												onChange('reservationMethods', {
													...event.reservationMethods,
													callIn: event_.target.checked
												})
										}}
									/>
									<div className='flex items-center gap-1'>
										<div className='text-sm font-normal text-gray-700'>
											Call In
										</div>
										<HSTooltip content='My guests will individually call the hotel and will provide their reservation information'>
											<FontAwesomeIcon
												icon={faInfoCircle}
												className='text-gray-500'
											/>
										</HSTooltip>
									</div>
								</div>
								<div className='flex items-center gap-2'>
									<HSCheckbox
										checked={event.reservationMethods?.masterList}
										onChange={event_ => {
											if (event.reservationMethods)
												onChange('reservationMethods', {
													...event.reservationMethods,
													masterList: event_.target.checked
												})
										}}
									/>
									<div className='flex items-center gap-1'>
										<div className='text-sm font-normal text-gray-700'>
											Master List
										</div>
										<HSTooltip content='I will provide the hotel a list of the names of the individuals staying in the guestrooms'>
											<FontAwesomeIcon
												icon={faInfoCircle}
												className='text-gray-500'
											/>
										</HSTooltip>
									</div>
								</div>
							</div>
						</div>
					) : null}

					<div
						className={`flex w-1/3 flex-col gap-2 ${event.roomBlocksRequired ? 'border-r' : ''}`}
					>
						<div className='text-sm font-medium text-gray-700'>
							Allow hotels to
						</div>
						<div className='flex flex-col gap-2'>
							<div className='flex items-center gap-2'>
								<HSCheckbox
									checked={event.doNotCollectCutOffDate || false}
									name='doNotCollectCutOffDate'
									onChange={event_ =>
										onChange('doNotCollectCutOffDate', event_.target.checked)
									}
								/>
								<div className='flex items-center gap-1'>
									<div className='text-sm font-normal text-gray-700'>
										Do not allow hotels to propose a Cut Off Date
									</div>
									<HSTooltip content='Specify if hotels can suggest a deadline for the room block release'>
										<FontAwesomeIcon
											icon={faInfoCircle}
											className='text-gray-500'
										/>
									</HSTooltip>
								</div>
							</div>
							{event.roomBlocksRequired ? (
								<>
									<div className='flex items-center gap-2'>
										<HSCheckbox
											checked={event.doNotCollectAttritionRate || false}
											onChange={event_ =>
												onChange(
													'doNotCollectAttritionRate',
													event_.target.checked
												)
											}
										/>
										<div className='flex items-center gap-1'>
											<div className='text-sm font-normal text-gray-700'>
												Do not allow hotels to propose an Attrition Rate
											</div>
											<HSTooltip content='Specify if hotels can suggest the minimum room commitment percentage'>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-500'
												/>
											</HSTooltip>
										</div>
									</div>
									{event.canRequestAttritionRate ? (
										<div className='flex flex-col gap-2'>
											<div className='flex items-center gap-2'>
												<HSCheckbox
													checked={event.requestAttritionRate === null}
													onChange={event_ =>
														onChange(
															'requestAttritionRate',
															event_.target.checked
																? null
																: (event.requestAttritionRateDefault ?? 80)
														)
													}
													disabled={event.doNotCollectAttritionRate || false}
												/>
												<div className='flex items-center gap-1'>
													<div className='text-sm font-normal text-gray-700'>
														Attrition will not be accepted
													</div>
													<HSTooltip content='Attrition will not be accepted'>
														<FontAwesomeIcon
															icon={faInfoCircle}
															className='text-gray-500'
														/>
													</HSTooltip>
												</div>
											</div>

											<div className='flex items-center gap-4'>
												<div className='flex w-40'>
													<HSButton
														className='rounded-r-none border-r disabled:border-gray-400'
														color='gray'
														disabled={event.doNotCollectAttritionRate || false}
													>
														<div className='text-sm font-semibold text-gray-900'>
															%
														</div>
													</HSButton>
													<HSTextField
														name='rebateRequestAmount'
														value={
															event.requestAttritionRate ??
															event.requestAttritionRateDefault ??
															80
														}
														onChange={event_ =>
															onChange(
																'requestAttritionRate',
																event_.target.value
																	.replace('%', '')
																	.replaceAll(',', '')
															)
														}
														readOnly={
															disableFields ||
															event.requestAttritionRate === null
														}
														isInvalid={
															!disableFields &&
															Number.isNaN(event.requestAttritionRate)
														}
														disabled={event.doNotCollectAttritionRate || false}
														className='max-w-40'
														groupPlacement='right'
														placeholder='Rate'
													/>
												</div>
												<div className='text-sm font-normal text-gray-400'>
													per room night
												</div>
											</div>
										</div>
									) : null}
								</>
							) : null}
						</div>
					</div>

					<div className='flex w-1/3 flex-col gap-2'>
						{userProfile?.canRequestRebate && event.roomBlocksRequired ? (
							<div className='flex flex-col gap-2'>
								<div className='flex items-center gap-2'>
									<HSToggleSwitch
										checked={event.requestRebate ?? false}
										onChange={checked => onChange('requestRebate', checked)}
									/>

									<div className='flex items-center gap-1'>
										<div className='text-sm font-normal text-gray-700'>
											Request Rebate
										</div>
										<HSTooltip content='Set a per-room rebate amount to be returned after the event'>
											<FontAwesomeIcon
												icon={faInfoCircle}
												className='text-gray-500'
											/>
										</HSTooltip>
									</div>
								</div>
								{event.requestRebate ? (
									<div className='flex items-center gap-4'>
										<div className='flex w-32'>
											<HSButton
												className='rounded-r-none border-r disabled:border-gray-400'
												color='gray'
											>
												<div className='text-sm font-medium text-gray-900'>
													{currency.symbol}
												</div>
											</HSButton>
											<HSTextField
												name='rebateRequestAmount'
												value={event.rebateRequestAmount || ''}
												disabled={!event.requestRebate}
												isInvalid={!event.rebateRequestAmount}
												className='max-w-32'
												groupPlacement='right'
												placeholder='Rate'
												type='number'
												onChange={event_ => {
													onChange('rebateRequestAmount', event_.target.value)
												}}
											/>
										</div>
										{event.rebateRequestAmount ? (
											<div className='flex items-center gap-2'>
												<div className='text-sm font-normal text-gray-400'>
													Total Rebate:
												</div>
												<div className='text-sm font-normal text-gray-600'>
													{formatCurrency(
														event.rebateRequestAmount *
															(event.totalRoomsRequested ?? 0),
														currency
													)}
												</div>
											</div>
										) : null}
									</div>
								) : null}
								{organization?.commissionEligible ? (
									<div className='flex items-center gap-2'>
										<HSToggleSwitch
											name='commissionable'
											checked={event.commissionable || false}
											onChange={checked => onChange('commissionable', checked)}
											disabled={disableFields}
										/>

										<div className='flex items-center gap-1'>
											<div className='text-sm font-normal text-gray-700'>
												Request Commission
											</div>
											<HSTooltip content='Request commissionable rates'>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-500'
												/>
											</HSTooltip>
										</div>
									</div>
								) : null}
							</div>
						) : null}
					</div>
				</div>
				<div className='flex flex-col gap-2'>
					<div className='flex items-center gap-2'>
						<div className='text-sm font-medium text-gray-900'>
							Payment methods
						</div>
						<HSTooltip content='Select how meeting space charges will be handled'>
							<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
						</HSTooltip>
					</div>
					<div className='flex gap-6'>
						<div className='w-1/4'>
							<div className='flex flex-col gap-2'>
								<div>
									<div className='text-sm font-medium text-gray-700'>
										Meeting Space
									</div>
									<HSSelect
										name='meetingSpace'
										value={event.paymentMethods?.meetingSpace || ''}
										onChange={event_ => {
											if (event.paymentMethods) {
												if (applyPaymentToAll) {
													onChange('paymentMethods', {
														...event.paymentMethods,
														foodAndBeverage: event_.target.value,
														audioVisual: event_.target.value,
														meetingSpace: event_.target.value
													})
												} else {
													onChange('paymentMethods', {
														...event.paymentMethods,
														meetingSpace: event_.target.value
													})
												}
											}
										}}
										isInvalid={!event.paymentMethods?.meetingSpace}
									>
										<option value=''>Select...</option>
										{Object.keys(paymentMethodTypeOptions).map((pmt, index) => (
											<option key={`ep-ms-pm-${index}`} value={pmt}>
												{paymentMethodTypeOptions[pmt]}
											</option>
										))}
									</HSSelect>
								</div>
								<div className='flex items-center gap-2'>
									<HSCheckbox
										checked={applyPaymentToAll}
										onChange={event_ => {
											setApplyPaymentToAll(event_.target.checked)
											if (
												event_.target.checked &&
												event.paymentMethods?.meetingSpace
											) {
												onChange('paymentMethods', {
													...event.paymentMethods,
													foodAndBeverage: event.paymentMethods.meetingSpace,
													audioVisual: event.paymentMethods.meetingSpace
												})
											}
										}}
									/>
									<div className='text-xs font-normal leading-none text-gray-700'>
										Apply to all payment options
									</div>
								</div>
							</div>
						</div>
						<div className='w-1/4'>
							<div className='text-sm font-medium text-gray-700'>
								Food and Beverage
							</div>
							<HSSelect
								value={event.paymentMethods?.foodAndBeverage || ''}
								name='foodAndBeverage'
								onChange={event_ => {
									if (event.paymentMethods) {
										onChange('paymentMethods', {
											...event.paymentMethods,
											foodAndBeverage: event_.target.value
										})
									}
								}}
								disabled={applyPaymentToAll}
								isInvalid={!event.paymentMethods?.foodAndBeverage}
							>
								<option value=''>Select...</option>
								{Object.keys(paymentMethodTypeOptions).map((pmt, index) => (
									<option key={`ep-ms-pm-${index}`} value={pmt}>
										{paymentMethodTypeOptions[pmt]}
									</option>
								))}
							</HSSelect>
						</div>
						<div className='w-1/4'>
							<div className='text-sm font-medium text-gray-700'>
								Audio Visual
							</div>
							<HSSelect
								value={event.paymentMethods?.audioVisual || ''}
								name='foodAndBeverage'
								onChange={event_ => {
									if (event.paymentMethods) {
										onChange('paymentMethods', {
											...event.paymentMethods,
											audioVisual: event_.target.value
										})
									}
								}}
								disabled={applyPaymentToAll}
								isInvalid={!event.paymentMethods?.audioVisual}
							>
								<option value=''>Select...</option>
								{Object.keys(paymentMethodTypeOptions).map((pmt, index) => (
									<option key={`ep-ms-pm-${index}`} value={pmt}>
										{paymentMethodTypeOptions[pmt]}
									</option>
								))}
							</HSSelect>
						</div>
						<div className='w-1/4'>
							<div className='flex flex-col'>
								<div className='flex items-center gap-2'>
									<div className='text-sm font-medium text-gray-700'>
										Set up Master with credit card
									</div>
									<HSTooltip content='Indicate if a credit card will be provided for the master account'>
										<FontAwesomeIcon
											icon={faInfoCircle}
											className='text-gray-500'
										/>
									</HSTooltip>
								</div>
								<div className='flex items-center'>
									<Button.Group>
										<Button
											color={
												event.paymentMethods?.setupMaster === true
													? 'gray'
													: 'light'
											}
											className={
												event.paymentMethods?.setupMaster === true
													? selectClassName
													: ''
											}
											size='sm'
											onClick={() => {
												if (event.paymentMethods) {
													onChange('paymentMethods', {
														...event.paymentMethods,
														setupMaster: true
													})
												}
											}}
										>
											Yes
										</Button>
										<Button
											color={
												event.paymentMethods?.setupMaster === false
													? 'gray'
													: 'light'
											}
											className={
												event.paymentMethods?.setupMaster === false
													? selectClassName
													: ''
											}
											size='sm'
											onClick={() => {
												if (event.paymentMethods) {
													onChange('paymentMethods', {
														...event.paymentMethods,
														setupMaster: false
													})
												}
											}}
										>
											No
										</Button>
										<Button
											color={
												event.paymentMethods?.setupMaster === null
													? 'gray'
													: 'light'
											}
											className={
												event.paymentMethods?.setupMaster === null
													? selectClassName
													: ''
											}
											size='sm'
											onClick={() => {
												if (event.paymentMethods) {
													onChange('paymentMethods', {
														...event.paymentMethods,
														setupMaster: null
													})
												}
											}}
										>
											TBD
										</Button>
									</Button.Group>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div className='flex gap-6'>
				<div className='w-1/2'>
					<div className='card flex h-full flex-col gap-4 p-4 shadow-lg'>
						<div className='flex flex-col gap-2'>
							<div className='flex items-center gap-2'>
								<div className='text-base font-medium text-gray-700'>
									Contract Signatory
								</div>
								<HSTooltip content='Indicate if a credit card will be provided for the master account'>
									<FontAwesomeIcon
										icon={faInfoCircle}
										className='text-gray-500'
									/>
								</HSTooltip>
							</div>
							<div className='text-sm font-normal text-gray-600'>
								Name, Title, and Address are hidden from the hotels until a
								proposal is selected
							</div>
						</div>
						<div className='flex flex-col gap-4'>
							<div className='flex items-center justify-between gap-4'>
								<HSTextField
									label='Name'
									placeholder='Enter Name'
									name='name'
									value={event.contractSigner?.name || ''}
									isInvalid={!event.contractSigner?.name}
									onChange={event_ => {
										if (event.contractSigner)
											onChange('contractSigner', {
												...event.contractSigner,
												name: event_.target.value
											})
									}}
								/>
								<HSTextField
									label='Title'
									placeholder='Enter Title'
									name='title'
									value={event.contractSigner?.title || ''}
									isInvalid={!event.contractSigner?.title}
									onChange={event_ => {
										if (event.contractSigner)
											onChange('contractSigner', {
												...event.contractSigner,
												title: event_.target.value
											})
									}}
								/>
							</div>
							<div className='flex flex-col gap-2'>
								<div className='flex items-center justify-between gap-4'>
									<div className='text-sm font-medium text-gray-700'>
										Address
									</div>
									<HSButton
										color='text'
										size='sm'
										onClick={() =>
											setUseAddressLookupSigner(!useAddressLookupSigner)
										}
									>
										{useAddressLookupSigner
											? 'Enter manually'
											: 'Lookup address'}
									</HSButton>
								</div>
								{useAddressLookupSigner ? (
									<LocationSelector
										customPlaceholder='Lookup an address...'
										value={contractSignerLocation as ILocation}
										onChange={l => onChangeAddress(l, 'contractSigner')}
										isInvalid={!event.contractSigner?.address}
									/>
								) : (
									<HSTextField
										placeholder='Lookup your address'
										name='address'
										value={event.contractSigner?.address || ''}
										isInvalid={!event.contractSigner?.address}
										onChange={event_ => {
											if (event.contractSigner)
												onChange('contractSigner', {
													...event.contractSigner,
													address: event_.target.value
												})
										}}
									/>
								)}
							</div>
							<div className='flex items-center justify-between gap-4'>
								<HSTextField
									label='City'
									placeholder='Enter City'
									name='city'
									value={event.contractSigner?.city || ''}
									isInvalid={!event.contractSigner?.city}
									onChange={event_ => {
										if (event.contractSigner)
											onChange('contractSigner', {
												...event.contractSigner,
												city: event_.target.value
											})
									}}
								/>
								<HSTextField
									label='State'
									placeholder='Enter State'
									name='state'
									value={event.contractSigner?.state || ''}
									isInvalid={!event.contractSigner?.state}
									onChange={event_ => {
										if (event.contractSigner)
											onChange('contractSigner', {
												...event.contractSigner,
												state: event_.target.value
											})
									}}
								/>
							</div>
							<div className='flex items-center justify-between gap-4'>
								<HSTextField
									label='Postal Code'
									placeholder='Enter Code'
									name='postalCode'
									value={event.contractSigner?.postalCode || ''}
									isInvalid={!event.contractSigner?.postalCode}
									onChange={event_ => {
										if (event.contractSigner)
											onChange('contractSigner', {
												...event.contractSigner,
												postalCode: event_.target.value
											})
									}}
								/>
								<HSTextField
									label='Country'
									placeholder='Enter Country'
									name='country'
									value={event.contractSigner?.country || ''}
									isInvalid={!event.contractSigner?.country}
									onChange={event_ => {
										if (event.contractSigner)
											onChange('contractSigner', {
												...event.contractSigner,
												country: event_.target.value
											})
									}}
								/>
							</div>
						</div>
					</div>
				</div>

				<div className='w-1/2'>
					<div className='card flex flex-col gap-4 p-4'>
						<div className='flex flex-col gap-2'>
							<div className='flex items-center gap-2'>
								<div className='text-base font-medium text-gray-700'>
									Planner Point of Contact
								</div>
								<HSTooltip content='Indicate if a credit card will be provided for the master account'>
									<FontAwesomeIcon
										icon={faInfoCircle}
										className='text-gray-500'
									/>
								</HSTooltip>
							</div>
							<div className='text-sm font-normal text-gray-600'>
								Hotels use your state and country to assign your RFP to the
								right people
							</div>
						</div>
						<div className='flex flex-col gap-4'>
							<div className='flex items-center justify-between gap-4'>
								<HSTextField
									label='Name'
									name='name'
									placeholder='Enter Name'
									value={event.rfpOwner?.name || ''}
									isInvalid={!event.rfpOwner?.name}
									onChange={event_ => {
										if (event.rfpOwner)
											onChange('rfpOwner', {
												...event.rfpOwner,
												name: event_.target.value
											})
									}}
								/>
								<HSTextField
									label='Title'
									placeholder='Enter Title'
									value={event.rfpOwner?.title || ''}
									onChange={event_ => {
										if (event.rfpOwner)
											onChange('rfpOwner', {
												...event.rfpOwner,
												title: event_.target.value
											})
									}}
								/>
							</div>
							<div className='flex flex-col gap-2'>
								<div className='flex items-center justify-between gap-4'>
									<div className='text-sm font-medium text-gray-700'>
										Address
									</div>
									<HSButton
										color='text'
										size='sm'
										onClick={() =>
											setUseAddressLookupOwner(!useAddressLookupOwner)
										}
									>
										{useAddressLookupOwner
											? 'Enter manually'
											: 'Lookup address'}
									</HSButton>
								</div>
								{useAddressLookupOwner ? (
									<LocationSelector
										customPlaceholder='Lookup an address...'
										value={rfpOwnerLocation as ILocation}
										onChange={l => onChangeAddress(l, 'rfpOwner')}
									/>
								) : (
									<HSTextField placeholder='Lookup your address' />
								)}
							</div>
							<div className='flex items-center justify-between gap-4'>
								<HSTextField
									label='City'
									placeholder='Enter City'
									value={event.rfpOwner?.city || ''}
									onChange={event_ => {
										if (event.rfpOwner)
											onChange('rfpOwner', {
												...event.rfpOwner,
												city: event_.target.value
											})
									}}
								/>
								<HSTextField
									label='State'
									name='state'
									placeholder='Enter State'
									value={event.rfpOwner?.state || ''}
									onChange={event_ => {
										if (event.rfpOwner)
											onChange('rfpOwner', {
												...event.rfpOwner,
												state: event_.target.value
											})
									}}
								/>
							</div>
							<div className='flex items-center justify-between gap-4'>
								<HSTextField
									name='postalCode'
									label='Postal Code'
									placeholder='Enter Code'
									value={event.rfpOwner?.postalCode || ''}
									onChange={event_ => {
										if (event.rfpOwner)
											onChange('rfpOwner', {
												...event.rfpOwner,
												postalCode: event_.target.value
											})
									}}
								/>
								<HSTextField
									name='country'
									label='Country'
									placeholder='Enter Country'
									value={event.rfpOwner?.country || ''}
									isInvalid={!event.rfpOwner?.country}
									onChange={event_ => {
										if (event.rfpOwner)
											onChange('rfpOwner', {
												...event.rfpOwner,
												country: event_.target.value
											})
									}}
								/>
							</div>
							<HSToggleSwitch
								label='Include my company overview in the RFP'
								checked={event.includeOrganizationDescription || false}
								onChange={checked =>
									onChange('includeOrganizationDescription', checked)
								}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	) : (
		<PageLoader />
	)
}

export default ContractTerm
