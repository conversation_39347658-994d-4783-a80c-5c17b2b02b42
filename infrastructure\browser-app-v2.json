﻿{
	"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#",
	"contentVersion": "1.0.0.0",
	"parameters": {
		"solutionPrefix": {
			"type": "string"
		},
		"environmentSuffix": {
			"type": "string"
		},
		"storageType": {
			"type": "string",
			"defaultValue": "Standard_LRS",
			"allowedValues": [
				"Standard_LRS",
				"Standard_ZRS",
				"Standard_GRS",
				"Standard_RAGRS",
				"Premium_LRS"
			]
		},
		"blobContainerName": {
			"type": "string",
			"defaultValue": "$web"
		}
	},
	"variables": {
		"environmentSuffixLower": "[toLower(parameters('environmentSuffix'))]",
		"storageAccountName": "[concat('web', replace(variables('environmentSuffixLower'), '-', ''))]",
		"storageAccountid": "[concat(resourceGroup().id,'/providers/','Microsoft.Storage/storageAccounts/', variables('storageAccountName'))]"
	},
	"resources": [
		{
			"name": "[variables('storageAccountName')]",
			"type": "Microsoft.Storage/storageAccounts",
			"location": "[resourceGroup().location]",
			"apiVersion": "2023-01-01",
			"kind": "StorageV2",
			"sku": {
				"name": "[parameters('storageType')]"
			},
			"tags": {
				"displayName": "storage"
			},
			"properties": {
				"defaultToOAuthAuthentication": false,
				"allowCrossTenantReplication": true,
				"minimumTlsVersion": "TLS1_2",
				"allowBlobPublicAccess": true,
				"allowSharedKeyAccess": true,
				"networkAcls": {
					"bypass": "AzureServices",
					"virtualNetworkRules": [],
					"ipRules": [],
					"defaultAction": "Allow"
				},
				"supportsHttpsTrafficOnly": true,
				"encryption": {
					"services": {
						"file": {
							"keyType": "Account",
							"enabled": true
						},
						"blob": {
							"keyType": "Account",
							"enabled": true
						}
					},
					"keySource": "Microsoft.Storage"
				},
				"accessTier": "Hot"
			},
			"resources": [
				{
					"name": "default",
					"type": "blobServices",
					"apiVersion": "2023-01-01",
					"dependsOn": ["[variables('storageAccountName')]"],
					"properties": {
						"changeFeed": {
							"enabled": false
						},
						"restorePolicy": {
							"enabled": false
						},
						"containerDeleteRetentionPolicy": {
							"enabled": true,
							"days": 7
						},
						"cors": {
							"corsRules": [
								{
									"allowedOrigins": ["*"],
									"allowedMethods": ["GET", "HEAD", "OPTIONS"],
									"maxAgeInSeconds": 200,
									"exposedHeaders": ["*"],
									"allowedHeaders": ["*"]
								}
							]
						},
						"deleteRetentionPolicy": {
							"allowPermanentDelete": false,
							"enabled": true,
							"days": 7
						},
						"isVersioningEnabled": false
					},
					"resources": [
						{
							"name": "[parameters('blobContainerName')]",
							"type": "containers",
							"apiVersion": "2023-01-01",
							"dependsOn": ["default"],
							"properties": {
								"immutableStorageWithVersioning": {
									"enabled": false
								},
								"defaultEncryptionScope": "$account-encryption-key",
								"denyEncryptionScopeOverride": false,
								"publicAccess": "None"
							}
						},
						{
							"name": "static-website",
							"type": "staticWebsite",
							"apiVersion": "2023-01-01",
							"dependsOn": ["default"],
							"properties": {
								"indexDocument": "index.html",
								"errorDocument404Path": "index.html"
							}
						}
					]
				}
			]
		}
	],
	"outputs": {
		"blobStorageContainerPath": {
			"type": "string",
			"value": "[concat(replace(reference(concat('Microsoft.Storage/storageAccounts/', variables('storageAccountName')), '2018-07-01').primaryEndpoints.blob, 'https://', ''), parameters('blobContainerName'))]"
		},
		"blobStorageAccountName": {
			"type": "string",
			"value": "[variables('storageAccountName')]"
		},
		"blobStorageAccountKey": {
			"type": "string",
			"value": "[listKeys(variables('storageAccountid'),'2015-05-01-preview').key1]"
		},
		"blobStorageContainerName": {
			"type": "string",
			"value": "[parameters('blobContainerName')]"
		}
	}
}
