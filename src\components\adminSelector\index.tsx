import type { ChangeEventArgs } from '@syncfusion/ej2-react-dropdowns'
import { DropDownListComponent } from '@syncfusion/ej2-react-dropdowns'
import type { IUserProfile } from 'models/userProfiles'

interface IAdminSelectorProperties {
	adminsList: IUserProfile[] | undefined
	value: string
	onChange: (selected: string) => void
}

const AdminSelector = (properties: IAdminSelectorProperties) => {
	const { adminsList, value, onChange } = properties

	const valueTemplate = (user: IUserProfile) => (
		<div className='flex h-full w-full items-center'>
			{user.firstName && user.lastName
				? `${user.firstName} ${user.lastName}`
				: 'None'}
		</div>
	)

	return (
		<DropDownListComponent
			dataSource={adminsList ?? []}
			fields={{ text: 'firstName', value: 'id' }}
			itemTemplate={(user: IUserProfile) =>
				`${user.firstName} ${user.lastName}`
			}
			valueTemplate={(user: IUserProfile) => valueTemplate(user)}
			placeholder='Select...'
			value={value}
			popupHeight='200px'
			// eslint-disable-next-line react/jsx-handler-names
			change={(event: ChangeEventArgs) => onChange(event.value as string)}
			cssClass='e-dropdownlist-css'
		/>
	)
}

export default AdminSelector
