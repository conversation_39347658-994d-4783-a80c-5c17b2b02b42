/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/max-params */
import { requestGroups } from 'lib/helpers/requestGroups'
import type { IConcessionRequest } from 'models/eventPlans'
import type { QuoteRequest } from 'models/hotelier'
import type {
	IContractClauses,
	IContractClauseResponse
} from 'models/organizations'
import type {
	IConcessionEventResponse,
	ProposalRequest
} from 'models/proposalResponseMonitor'
import type { IQuestion, IQuestionResponse } from 'models/questions'
import { format, parseISO } from 'date-fns'
import useQuoteRequestStore from './quoteRequestStore'
import useQuestionStore from './questionsStore'
import useConcessionRequestStore from './concessionRequests'
import useContractClauseStore from './contractClauses'
import { useEffect, useState } from 'react'
import { sleepingRoomTypeOptions } from 'lib/helpers/sleepingRoomType'
import { isEmail } from 'lib/helpers'
import isValidPhoneNumber from 'lib/utils/phoneNumber'

export interface ICompletionStatus {
	key: string
	pendingItems: string[]
}

export const completionItems = (
	isSummary: boolean,
	quoteRequest: QuoteRequest
) =>
	[
		{
			title: 'Room Rate',
			key: 'roomRate',
			visible: quoteRequest.eventPlan?.roomBlocksRequired ?? false
		},
		{
			title: 'Event Space',
			key: 'event-space',
			visible: quoteRequest.eventPlan?.meetingSpaceRequired ?? false
		},
		{
			title: 'F&B',
			key: 'fandb',
			visible:
				(quoteRequest.eventPlan?.foodAndBeverageRequests?.length ?? 0) > 0 &&
				!isSummary
		},
		{
			title: 'Questions',
			key: 'questions',
			visible: !isSummary
		},
		{
			title: 'Concessions',
			key: 'concessions',
			visible: !isSummary
		},
		{
			title: 'Contract Clauses',
			key: 'contract-clauses',
			visible: !isSummary
		},
		{
			title: 'Taxes & Fees',
			key: 'taxes-and-fees',
			visible: true
		},
		{
			title: 'General',
			key: 'general',
			visible: true
		}
	].filter(c => c.visible)

export const getCompletionStatus = (
	quoteRequest: QuoteRequest,
	proposalRequest: ProposalRequest,
	concessionRequests: {
		concessionRequest: IConcessionRequest
		response: IConcessionEventResponse
	}[],
	questions: {
		question: IQuestion
		response: IQuestionResponse | null
	}[],
	questionErrors: string[],
	concessionRequestErrors: string[],
	contractClauses: {
		contractClause: IContractClauses
		response: IContractClauseResponse
	}[],
	contractClauseErrors: string[],
	proposalDateComplete: Record<string, boolean>,
	isSummary: boolean
): ICompletionStatus[] => {
	const completionStatus: ICompletionStatus[] = []
	const { eventPlan, summaryProposalRequest } = quoteRequest

	if (
		Object.entries(proposalDateComplete)
			.filter(([key]) => {
				const currentProposalDate =
					proposalRequest.currentBid?.proposalDates?.find(pd => pd.key === key)
				if (currentProposalDate?.declineToBid) {
					return false
				}
				return true
			})
			.some(([, value]) => !value)
	) {
		const pendingItems = Object.entries(proposalDateComplete)
			.filter(
				item =>
					!proposalRequest.currentBid?.proposalDates?.find(
						pd => pd.key === item[0]
					)?.declineToBid
			)
			.filter(([, value]) => !value)
			.map(
				([key]) => `Missing Rates for ${key ? format(new Date(key), 'PP') : ''}`
			)
		completionStatus.push({ key: 'roomRate', pendingItems })
	}
	if (!isSummary) {
		if (
			concessionRequestErrors ||
			concessionRequests.some(c => c.response.id === null)
		) {
			const uniqueRequestGroups = [
				...new Set(
					concessionRequests
						.filter(item => {
							const hasApprovedResponse =
								(item.response?.responses?.[0]?.score ?? 0) > 0
							const hasConcessionValue =
								item.response?.concessionValueProposed !== null
							const hasResponseText =
								(item.response?.response?.trim().length ?? 0) > 0
							const hasNegativeScore =
								item.response?.responses?.[0]?.score === -1

							return (
								!(hasApprovedResponse && hasConcessionValue) &&
								!hasResponseText &&
								!hasNegativeScore
							)
						})
						.map(c => c.concessionRequest.requestGroupId)
				)
			]
			const pendingItems = uniqueRequestGroups.map(
				rg =>
					`No answer for ${requestGroups[rg ?? '']?.name ?? 'No Grouping Specified'}`
			)
			completionStatus.push({ key: 'concessions', pendingItems })
		}

		if (
			questions.some(q => q.response?.id === null) ||
			questionErrors.length > 0
		) {
			const uniqueRequestGroups = [
				...new Set(
					questions
						.filter(
							q =>
								q.response === null ||
								(q.question.responseType === 'text' &&
									(q.response.response === null ||
										q.response.response.length === 0)) ||
								(q.question.responseType === 'number' &&
									(q.response.response === null ||
										q.response.response.length === 0 ||
										Number.isNaN(q.response.response))) ||
								(['singleChoice', 'multipleChoice'].includes(
									q.question.responseType ?? ''
								) &&
									(q.response.responses === null ||
										q.response.responses.length === 0 ||
										(!q.response.comment &&
											q.response.responses.some(c => c.requireComment))))
						)
						.map(c => c.question.requestGroupId)
				)
			]

			const pendingItems = uniqueRequestGroups.map(
				rg => `No answer for ${requestGroups[rg ?? '']?.name}`
			)
			completionStatus.push({ key: 'questions', pendingItems })
		}

		if (
			contractClauses.some(
				cc => cc.response.id === null || contractClauseErrors.length > 0
			)
		) {
			const pendingItems = contractClauses
				.filter(
					cc =>
						!cc.response.acknowledged &&
						(!cc.response.comment || cc.response.length < '<p></p>'.length)
				)
				.map(cc => `No answer for ${cc.contractClause.title}`)
			completionStatus.push({ key: 'contract-clauses', pendingItems })
		}
	}

	const pendingGeneralItems = []

	if (!isSummary) {
		if (!proposalRequest?.currentBid?.bidExpirationDate) {
			pendingGeneralItems.push('Missing Expiration Date')
		}
		if (!proposalRequest?.currentBid?.cutOffDate) {
			pendingGeneralItems.push('Missing Cut Off Date')
		}
	}

	if (
		!isEmail(proposalRequest?.proposalContact?.email ?? '') ||
		!proposalRequest?.proposalContact?.phone ||
		!isValidPhoneNumber(proposalRequest.proposalContact.phone ?? '') ||
		!proposalRequest.proposalContact.name
	) {
		pendingGeneralItems.push('Missing Contact detail')
	}
	if (
		summaryProposalRequest === null &&
		['New', 'Received'].includes(proposalRequest.status ?? '')
	) {
		if (proposalRequest.currentBid?.bidExpirationDate) {
			if (parseISO(proposalRequest.currentBid.bidExpirationDate) < new Date()) {
				pendingGeneralItems.push(
					'Please provide a Proposal Expiration date in the future'
				)
			}
		} else {
			pendingGeneralItems.push('Please provide a Proposal Expiration date')
		}
	}
	completionStatus.push({ key: 'general', pendingItems: pendingGeneralItems })

	const pendingFandBItems = []

	if (!isSummary && (eventPlan?.foodAndBeverageRequests?.length ?? 0) > 0) {
		if (
			proposalRequest?.currentBid?.offerDetailedFoodAndBeverage === null &&
			summaryProposalRequest === null
		) {
			pendingFandBItems.push('Missing Detailed rates')
		} else if (
			proposalRequest?.currentBid?.offerDetailedFoodAndBeverage === null &&
			summaryProposalRequest !== null
		) {
			pendingFandBItems.push('Missing Detailed rates')
		}
	}

	if (
		(eventPlan?.foodAndBeverageRequests?.length ?? 0) > 0 &&
		proposalRequest?.currentBid?.offerDetailedFoodAndBeverage &&
		proposalRequest.currentBid.foodAndBeverageItems?.some(
			fbi =>
				(!fbi.quantity && fbi.quantity !== 0) ||
				Number.isNaN(fbi.quantity) ||
				fbi.rate === undefined ||
				fbi.rate === null ||
				fbi.rate === '' ||
				(fbi.quantity > 0 && (fbi.rate === '' || null || undefined)) ||
				(Number(fbi.rate) > 0 && !(fbi.quantity > 0)) ||
				Number.isNaN(fbi.rate)
		)
	) {
		pendingFandBItems.push('Each F&B quote requires a quantity and rate.')
	}
	completionStatus.push({ key: 'fandb', pendingItems: pendingFandBItems })

	const meetingSpaceRequests = eventPlan?.meetingSpaceRequests ?? []
	const meetingSpaceAssignment =
		proposalRequest?.currentBid?.meetingSpaceAssignments ?? []

	if (!isSummary) {
		const pendingAssignment = meetingSpaceRequests
			.map(msr => {
				const assignment = meetingSpaceAssignment.find(
					msa => msa.meetingSpaceRequestId === msr.id
				)
				if (!assignment) {
					return `Missing Assignment for Day ${(msr.dayNumber ?? 0) + 1}`
				}
				return null
			})
			.filter(Boolean) as string[]

		completionStatus.push({
			key: 'event-space',
			pendingItems: pendingAssignment
		})
	} else if (
		isSummary &&
		proposalRequest.currentBid?.meetingSpaceAvailable === null
	) {
		completionStatus.push({
			key: 'event-space',
			pendingItems: ['Meeting Space Availability is not specified']
		})
	}

	const pendingTaxesAndFees = []
	const dateKeys = new Set(
		(proposalRequest?.currentBid?.proposalDates ?? [])
			.filter(pd => !pd.declineToBid)
			.map(pd => pd.key)
	)
	const meetingRates = proposalRequest?.currentBid?.meetingRates?.filter(mr =>
		dateKeys.has(mr.key)
	)
	if (eventPlan?.meetingSpaceRequired) {
		if (proposalRequest?.currentBid?.isMeetingRateDependentOnDate) {
			if (
				summaryProposalRequest === null &&
				meetingRates?.some(
					mr =>
						Number.isNaN(mr.fbMinimum) ||
						mr.fbMinimum === null ||
						mr.fbMinimum === '' ||
						mr.fbMinimum === undefined
				)
			) {
				pendingTaxesAndFees.push('F&B Minimum is required for each date range')
			}

			if (
				summaryProposalRequest === null &&
				meetingRates?.some(
					mr =>
						Number.isNaN(mr.roomRental) ||
						mr.roomRental === null ||
						mr.roomRental === undefined
				)
			) {
				pendingTaxesAndFees.push('Room Rental is required for each date range')
			}

			if (
				summaryProposalRequest === null &&
				meetingRates?.some(
					mr =>
						Number.isNaN(mr.serviceChargeRate) ||
						mr.serviceChargeRate === null ||
						mr.serviceChargeRate === ''
				)
			) {
				pendingTaxesAndFees.push(
					'Service Charge Rate is required for each date range'
				)
			}
		}
		if (proposalRequest?.currentBid?.isMeetingRateDependentOnDate === false) {
			if (
				Number.isNaN(proposalRequest?.currentBid?.fbMinimum) ||
				proposalRequest?.currentBid?.fbMinimum === null ||
				proposalRequest?.currentBid?.fbMinimum === '' ||
				proposalRequest?.currentBid?.fbMinimum === undefined
			) {
				pendingTaxesAndFees.push('Missing F&B Minimum')
			}

			if (
				summaryProposalRequest === null &&
				(Number.isNaN(proposalRequest?.currentBid?.roomRental) ||
					proposalRequest?.currentBid?.roomRental === null ||
					proposalRequest?.currentBid?.roomRental === undefined ||
					proposalRequest?.currentBid?.roomRental === '')
			) {
				pendingTaxesAndFees.push('Room Rental is required')
			}
		}

		if (
			(Number.isNaN(proposalRequest?.currentBid?.serviceChargeRate) ||
				proposalRequest?.currentBid?.serviceChargeRate === null ||
				proposalRequest?.currentBid?.serviceChargeRate === '') &&
			!isSummary
		) {
			pendingTaxesAndFees.push('Missing Service Charge Rate ')
		}
	}

	if (
		proposalRequest.currentBid?.attritionRate === null ||
		proposalRequest.currentBid?.attritionRate === undefined ||
		proposalRequest.currentBid.attritionRate === ''
	) {
		pendingTaxesAndFees.push('Attrition Rate is required  ')
	}

	if (
		proposalRequest.currentBid?.taxesFeesAssessments === null ||
		proposalRequest.currentBid?.taxesFeesAssessments === undefined ||
		proposalRequest.currentBid?.taxesFeesAssessments === ''
	) {
		pendingTaxesAndFees.push('Taxes, Fees field is required (may be zero)')
	}

	if (
		proposalRequest.commissionable &&
		(proposalRequest.currentBid?.commissionPercent === null ||
			proposalRequest.currentBid?.commissionPercent === undefined ||
			proposalRequest.currentBid.commissionPercent === '')
	) {
		pendingTaxesAndFees.push('Commission Rate is required (may be zero)')
	}

	completionStatus.push({
		key: 'taxes-and-fees',
		pendingItems: pendingTaxesAndFees
	})

	return completionStatus
}

export const useProposalCompletionStore = () => {
	const { quoteRequest, isSummary } = useQuoteRequestStore()
	const { questions, errors: questionErrors } = useQuestionStore()
	const { concessionRequests, errors: concessionRequestErrors } =
		useConcessionRequestStore()
	const { contractClauses, errors: contractClauseErrors } =
		useContractClauseStore()
	const [completionStatus, setCompletionStatus] = useState<ICompletionStatus[]>(
		[]
	)

	const proposalRequest = isSummary
		? quoteRequest.summaryProposalRequest
		: quoteRequest.proposalRequest

	const [proposalDateComplete, setProposalDateComplete] = useState<
		Record<string, boolean>
	>({})

	useEffect(() => {
		const updatedProposalDates =
			proposalRequest?.currentBid?.proposalDates?.reduce(
				(a, c) => ({ ...a, key: a.key, [c.key ?? '']: false }),
				{ ...proposalDateComplete }
			)
		if (updatedProposalDates) {
			setProposalDateComplete(updatedProposalDates)
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [proposalRequest?.currentBid?.proposalDates])

	useEffect(() => {
		const a: Record<string, boolean> = {}
		const expectedRates =
			quoteRequest.eventPlan?.roomBlockRequests?.reduce(
				(a1, rbr) =>
					a1 +
					rbr.roomTypeRequests.reduce(
						(a2, rtr) =>
							a2 + rtr.roomNights.filter(rn => rn.roomsRequested > 0).length,
						0
					),
				0
			) ?? 0

		const proposalDates = proposalRequest?.currentBid?.proposalDates ?? []

		for (const proposalDate of proposalDates) {
			const roomRates = proposalRequest?.currentBid?.roomRates?.filter(
				rr =>
					rr.key?.endsWith(proposalDate.key ?? '') &&
					rr.rate !== null &&
					rr.rate > 0
			)
			let complete = true
			if (proposalDate.isOfferedDifferentThanRequested) {
				const roomTypeOfferings = roomRates?.reduce(
					(accumulator, rr) => {
						const updatedA = [...accumulator]
						const roomType = rr.roomType ?? 0
						if (updatedA[roomType] < 0) updatedA[roomType] = 0
						updatedA[roomType] += rr.offered || 0
						return updatedA
					},
					Array.from<number>({ length: sleepingRoomTypeOptions.length }).fill(
						-1
					)
				)

				for (const rto of roomTypeOfferings ?? []) {
					if (rto === 0) {
						complete = false
					}
				}
			} else if ((roomRates?.length ?? 0) < expectedRates) {
				complete = false
			}
			a[proposalDate.key ?? ''] = complete
		}

		setProposalDateComplete(a)
	}, [
		quoteRequest.eventPlan?.roomBlockRequests,
		proposalRequest?.currentBid?.proposalDates,
		proposalRequest?.currentBid?.roomRates
	])

	useEffect(() => {
		if (proposalRequest) {
			const result = getCompletionStatus(
				quoteRequest,
				proposalRequest,
				concessionRequests,
				questions,
				questionErrors,
				concessionRequestErrors,
				contractClauses,
				contractClauseErrors,
				proposalDateComplete,
				isSummary
			)
			setCompletionStatus(result)
		}
	}, [
		concessionRequestErrors,
		concessionRequests,
		contractClauseErrors,
		contractClauses,
		isSummary,
		proposalDateComplete,
		proposalRequest,
		questionErrors,
		questions,
		quoteRequest
	])

	return { completionStatus }
}
