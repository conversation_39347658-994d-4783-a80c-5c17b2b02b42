/* eslint-disable react-hooks/rules-of-hooks */
import { useState } from 'react'
import H<PERSON>rawer from 'components/drawer'
import HSButton from 'components/button'

interface ICustomizeReportModal {
	onClose: () => void
	show: boolean
	onSave: (selectedSections: string[]) => void
}

const reportSections = [
	{ id: 'summary', label: 'Summary' },
	{ id: 'profile', label: 'Event Overview' },
	{ id: 'programinfo', label: 'Program Information' },
	{ id: 'rooms', label: 'Room Blocks' },
	{ id: 'meeting', label: 'Meeting Agenda' },
	{ id: 'fandb', label: 'Food & Beverage' },
	{ id: 'questions', label: 'Questions' },
	{ id: 'concessionRequests', label: 'Concession Requests' },
	{ id: 'contract', label: 'Contract Information' },
	{ id: 'clauses', label: 'Requested Contract Clauses' },
	{ id: 'attachments', label: 'Attached Documents' },
	{ id: 'notes', label: 'Notes (Blank Page)' }
]

const customizeReportPdfModal = (properties: ICustomizeReportModal) => {
	const { onClose, show, onSave } = properties
	const [selectedSections, setSelectedSections] = useState<string[]>(
		reportSections.map(section => section.id)
	)

	const toggleSection = (sectionId: string) => {
		setSelectedSections(previous =>
			previous.includes(sectionId)
				? previous.filter(id => id !== sectionId)
				: [...previous, sectionId]
		)
	}

	const handleSave = () => {
		onSave(selectedSections)
		onClose()
	}

	return (
		<HSDrawer
			position='right'
			onClose={onClose}
			open={show}
			style={{ width: '500px' }}
			className='p-2'
		>
			<div className='flex h-full flex-col rounded-lg bg-white'>
				<div className='p-4'>
					<div className='flex items-center justify-between'>
						<span className='text-xl font-semibold'>Customize Report</span>
					</div>
					<div className='mt-4 rounded-lg border border-gray-200 p-4'>
						<div className='flex items-center gap-1'>
							<div className='text-sm font-medium text-gray-900'>RFP</div>
							<div className='rounded-full bg-red-100 px-2 py-0.5 text-xs font-medium text-red-600'>
								PDF
							</div>
						</div>
						<div className='mt-2 text-sm text-gray-600'>
							Your RFP in a PDF format
						</div>
					</div>
				</div>

				<div className='flex-1 p-4'>
					<div className='mb-4 text-sm font-medium'>
						Customize information to include
					</div>
					<div className='space-y-3'>
						{reportSections.map(section => (
							<label
								key={section.id}
								className='flex items-center gap-2 text-sm'
								htmlFor={section.id}
							>
								<input
									type='checkbox'
									id={section.id}
									checked={selectedSections.includes(section.id)}
									onChange={() => toggleSection(section.id)}
									className='h-4 w-4 rounded border-gray-300 text-teal-600'
								/>
								{section.label}
							</label>
						))}
					</div>
				</div>

				<div className='flex gap-2 p-4'>
					<HSButton color='light' onClick={onClose} className='flex-1'>
						Cancel
					</HSButton>
					<HSButton className='flex-1' onClick={handleSave}>
						Save
					</HSButton>
				</div>
			</div>
		</HSDrawer>
	)
}

export default customizeReportPdfModal
