import type { BarProps } from 'recharts'
import { <PERSON><PERSON>hart, Bar, XAxis, <PERSON><PERSON><PERSON>s, ResponsiveContainer } from 'recharts'

const CustomBar = (properties: BarProps) => {
	const { x, y, width, height, fill } = properties

	// Adjust the radius here
	const radius = 16

	return (
		<g>
			<rect
				x={x}
				y={y}
				width={width}
				height={height}
				fill={fill}
				rx={radius} // Round the corners
				ry={radius} // Round the corners
			/>
		</g>
	)
}

const HSBarChart = () => {
	const data = [
		{
			name: 'Page A',
			uv: 4000,
			pv: 2400,
			amt: 2400
		},
		{
			name: 'Page B',
			uv: 3000,
			pv: 1398,
			amt: 2210
		}
	]
	return (
		<ResponsiveContainer height={150} width={400}>
			<BarChart data={data} layout='vertical'>
				<XAxis type='number' hide />
				<YAxis type='category' hide />
				<Bar
					dataKey='uv'
					background
					label
					fill='#EDEBFE'
					radius={[16, 16, 16, 16]}
					barSize={32}
					shape={CustomBar}
				/>
			</BarChart>
		</ResponsiveContainer>
	)
}

export default HSBarChart
