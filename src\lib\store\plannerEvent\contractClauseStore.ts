import type { ContractClauseEvent } from 'models/organizations'
import { create } from 'zustand'

interface ContractClauseState {
	contractClauses: ContractClauseEvent[]
	saving: boolean
}

interface ContractClauseAction {
	setObject: (contractClause: ContractClauseEvent[]) => void
	setSaving: (saving: boolean) => void
	addToArray: (contractClause: ContractClauseEvent) => void
	removeFromArray: (contractClause: ContractClauseEvent) => void
	replaceInArray: (contractClause: ContractClauseEvent) => void
}

const contractClauseStore = create<ContractClauseState & ContractClauseAction>(
	set => ({
		contractClauses: [],
		saving: false,
		setSaving: (saving: boolean) => set({ saving }),
		setObject: (contractClauses: ContractClauseEvent[]) => {
			set({ contractClauses })
		},
		addToArray: value => {
			set(state => {
				const contractClauses = [...state.contractClauses, value]
				return { contractClauses }
			})
		},
		removeFromArray: value => {
			set(state => {
				const contractClauses = state.contractClauses.filter(
					(item: ContractClauseEvent) =>
						item.contractClause.id !== value.contractClause.id
				)
				return { contractClauses }
			})
		},
		replaceInArray: value => {
			set(state => {
				const contractClauses = state.contractClauses.map(
					(item: ContractClauseEvent) =>
						item.contractClause.id === value.contractClause.id ? value : item
				)
				return { contractClauses }
			})
		}
	})
)

export default contractClauseStore
