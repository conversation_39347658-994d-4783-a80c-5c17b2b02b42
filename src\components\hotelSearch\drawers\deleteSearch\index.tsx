import HSButton from 'components/button'
import HSModal from 'components/modal'
import { Modal } from 'flowbite-react'
import { removeEventPlan } from 'lib/services/event.service'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { toast } from 'react-toastify'

interface DeleteHotelSearchModalProperties {
	onClose: () => void
	siteSearch: EventPlan
	onConfirmDelete: () => void
}

const DeleteHotelSearchModal = (
	properties: DeleteHotelSearchModalProperties
) => {
	const { onClose, siteSearch, onConfirmDelete } = properties

	const handleDelete = () => {
		if (siteSearch.id) {
			removeEventPlan(siteSearch.id)
				.then(() => {
					onConfirmDelete()
					toast.success('List deleted successfully')
				})
				.catch((error: unknown) => {
					console.error(error)
					toast.error('Failed to delete the list')
				})
				.finally(() => {
					onClose()
				})
		}
	}
	return (
		<HSModal openModal onClose={onClose} size='lg' header='Delete List'>
			<Modal.Body>
				<div className='font-normal text-gray-500'>
					<p>Are you sure you want to delete the selected list?</p>
					<p>This action cannot be undone.</p>
				</div>
			</Modal.Body>
			<Modal.Footer>
				<div className='flex grow items-center justify-between gap-4'>
					<HSButton color='light' onClick={onClose} className='grow'>
						Cancel
					</HSButton>
					<HSButton color='failure' className='grow' onClick={handleDelete}>
						Delete
					</HSButton>
				</div>
			</Modal.Footer>
		</HSModal>
	)
}

export default DeleteHotelSearchModal
