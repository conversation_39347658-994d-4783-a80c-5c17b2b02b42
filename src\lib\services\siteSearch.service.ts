/* eslint-disable @typescript-eslint/max-params */
import { useMutation, useQuery } from '@tanstack/react-query'
import type { AxiosResponse } from 'axios'
import api from 'lib/interceptor/axios.interceptor'
import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import type { ODataVenueResponse } from 'models/venue'

export const getSiteSearch = async () => {
	const response = await api.get(`/sourcing/api/events/siteSearches`)
	return response.data as EventPlan[]
}

export const addSiteSearch = async (data: EventPlan) => {
	const response = await api.post(`/sourcing/api/events/siteSearches`, data)
	return response.data as EventPlan
}

export const addProposalRequestToSiteSearch = async (
	eventPlanId: string,
	data: Partial<ProposalRequest>[]
) => {
	const response = await api.post(
		`/sourcing/api/events/plans/${eventPlanId}/proposalRequests`,
		data
	)
	return response.data as EventPlan
}

export const useGetSiteSearch = () =>
	useQuery({
		queryKey: ['siteSearch'],
		queryFn: getSiteSearch
	})

export const useAddSiteSearch = () =>
	useMutation({
		mutationKey: ['addSiteSearch'],
		mutationFn: async (eventPlan: EventPlan) => addSiteSearch(eventPlan)
	})

export const useAddProposalRequest = () =>
	useMutation({
		mutationKey: ['addProposalRequest'],
		mutationFn: async ({
			eventPlanId,
			data
		}: {
			eventPlanId: string
			data: Partial<ProposalRequest>[]
		}) => addProposalRequestToSiteSearch(eventPlanId, data)
	})

export const getSiteSearchResults = async (
	searchQuery: string,
	filterQuery: string,
	pagingQuery: string,
	sortQuery: string
) => {
	const response = await api.get(
		`/search/indexes/venue-index/docs?api-version=2020-06-30${searchQuery}${filterQuery}${pagingQuery}&$count=true${sortQuery}`,
		{
			headers: {
				'x-ms-azs-return-searchid': 'true',
				'Access-Control-Expose-Headers': 'x-ms-azs-searchid'
			}
		}
	)
	return response as AxiosResponse<ODataVenueResponse | undefined>
}

export const useGetSiteSearchResults = (
	searchQuery: string,
	filterQuery: string,
	pagingQuery: string,
	sortQuery: string,
	enabled = false
) =>
	useQuery({
		queryKey: [
			'siteSearchResults',
			searchQuery,
			filterQuery,
			pagingQuery,
			sortQuery
		],
		queryFn: async () =>
			getSiteSearchResults(searchQuery, filterQuery, pagingQuery, sortQuery),
		enabled
	})

export const deleteVenueFromSiteSearch = async (
	eventPlanId: string,
	venueId: string
) => {
	const response = await api.delete(
		`/sourcing/api/events/plans/${eventPlanId}/${venueId}`
	)
	return response.data as EventPlan
}
