/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import api from 'lib/interceptor/axios.interceptor'
import { useState } from 'react'
import type { OrganizationODataResponse, ODataResponse } from './model'
import { getSuggestionValue } from './helper'
import HSDropdownButton from 'components/dropdown'
import HSTextField from 'components/textField'
import HotelSelectorList from 'components/hotelSelector/hotelSelectorList'

const OrganizationSelector = ({
	selectedOrg,
	setSelectedOrg
}: {
	selectedOrg: OrganizationODataResponse | undefined
	setSelectedOrg: (value: OrganizationODataResponse | undefined) => void
}) => {
	const { userProfile } = useUserProfileContext()

	const [value, setValue] = useState(selectedOrg?.name ?? '')
	const [organizationList, setOrganizationList] = useState<
		OrganizationODataResponse[]
	>([])
	const [fuzzyDistance, setFuzzyDistance] = useState(0)

	const searchHotels = async (searchText: string) => {
		const searchString =
			fuzzyDistance > 0
				? `${searchText.split(' ').join(`~${fuzzyDistance} `)}~${fuzzyDistance}`
				: searchText

		const response = await api.post<ODataResponse>(
			'/search/indexes/organization-index/docs/search?api-version=2020-06-30',
			{
				search: searchString,
				select: 'id,name,city,state,isDeleted',
				count: true,
				skip: 0,
				top: 25,
				searchMode: 'any',
				searchFields: 'name,id,city,state',
				queryType: 'full',
				orderby: 'search.score() desc,name',
				filter: userProfile?.isAdmin ? null : 'isDeleted ne true'
			}
		)

		const data = response.data.value.map(v => ({
			...v,
			city: v.city ? v.city.trim() : null,
			state: v.state ? v.state.trim() : null
		})) as []
		setOrganizationList(data)
	}

	const onChange = async (searchString: string) => {
		setValue(searchString)
		await searchHotels(searchString)
	}

	const itemTemplate = (data: OrganizationODataResponse) => (
		<span className='flex items-center gap-1'>
			<div className='text-sm font-normal text-gray-700'>
				{getSuggestionValue(data)}
			</div>
			<div className='text-xs font-medium text-red-600'>
				{data.isDeleted ? <span>EXCLUDED</span> : null}
			</div>
		</span>
	)

	const dropdownItems = [
		'Exact match',
		'1 letter off',
		'2 letter off',
		'3 letter off'
	].map((a, i) => ({
		key: i,
		item: a,
		id: a,
		clickFunction: () => {
			setFuzzyDistance(i)
		}
	}))

	return (
		<div>
			<div className='relative flex flex-col gap-1'>
				<div className='flex flex-row items-center'>
					{userProfile?.isAdmin ? (
						<HSDropdownButton
							color='light'
							label={dropdownItems[fuzzyDistance].item}
							items={dropdownItems}
							dismissOnClick
							className='dropdown w-1/2 !rounded-r-none bg-gray-300'
						/>
					) : null}
					<HSTextField
						type='text'
						className='flex-1'
						groupPlacement={userProfile?.isAdmin ? 'right' : undefined}
						placeholder='Enter organization name...'
						onChange={async (event: { target: { value: string } }) => {
							await onChange(event.target.value)
						}}
						required
						value={value}
					/>
				</div>
				<div className='relative bottom-12'>
					{value === '' && selectedOrg === undefined ? null : (
						<HotelSelectorList
							venueList={organizationList}
							venueSelected={value_ =>
								setSelectedOrg(value_ as unknown as OrganizationODataResponse)
							}
							onChange={onChange}
							itemTemplate={value_ =>
								itemTemplate(value_ as unknown as OrganizationODataResponse)
							}
							popoverPosition='top'
							asPopover
						/>
					)}
				</div>
			</div>
		</div>
	)
}

export default OrganizationSelector
