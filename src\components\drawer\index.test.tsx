import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import HSDrawer from './index'
import { Drawer } from 'flowbite-react'

describe('HSDrawer', () => {
	const setShowModalMock = vi.fn()

	it('should render the drawer with the correct title and content', () => {
		render(
			<HSDrawer
				title='Drawer title'
				open
				onClose={() => {
					setShowModalMock(false)
				}}
				position='right'
			>
				<Drawer.Header title='Drawer title' />
				<div>Drawer content</div>
			</HSDrawer>
		)

		expect(screen.getByText('Drawer title')).toBeInTheDocument()
		expect(screen.getByText('Drawer content')).toBeInTheDocument()
	})

	it('should call onClose when close button is clicked', () => {
		render(
			<HSDrawer
				title='Drawer title'
				open
				onClose={() => {
					setShowModalMock(false)
				}}
				position='right'
			>
				<Drawer.Header title='Drawer title' />
				<div>Drawer content</div>
			</HSDrawer>
		)

		const closeButton = screen.getByRole('button')
		fireEvent.click(closeButton)
		expect(setShowModalMock).toHaveBeenCalled()
	})
})
