/* eslint-disable no-param-reassign */
/* eslint-disable unicorn/no-array-reduce */
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import NegotiatedSavingsDetails from 'components/negotiatedSavingsDetails'
import HSTable from 'components/table'
import { savingsKeys } from 'lib/common/common'
import type { ICurrency } from 'lib/helpers'
import { formatCurrency } from 'lib/helpers'
import concessionRequestStore from 'lib/store/plannerEvent/concessionRequestStore'
import type { ISavingData } from 'lib/store/plannerEvent/costSavingsStore'
import { costSavingsStore } from 'lib/store/plannerEvent/costSavingsStore'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'

const emptyTemplate = (eventPlanId: string) => (
	<div className='flex items-center justify-center p-4'>
		<div className='flex max-w-lg flex-col gap-2 text-left'>
			<div className='text-base font-medium text-gray-700'>
				Award your RFP to view your savings
			</div>
			<div className='text-sm font-normal text-gray-500'>
				<div>To begin contracting:</div>
				<ol className='list-decimal space-y-1 pl-6 text-left'>
					<li>
						<Link
							className='text-sm font-semibold underline'
							to={`/planner/event/${eventPlanId}/review-proposals/proposal-tracker`}
						>
							Go to the Proposal Tracker
						</Link>
					</li>
					<li>Click into the proposal details of your chosen venue</li>
					<li>Select &quot;Begin Contracting&quot; to award the booking</li>
				</ol>
			</div>
			<div className='text-sm font-normal text-gray-500'>
				This will notify your selected hotel that they&apos;ve won your business
				and give you the option to inform other properties that you won&apos;t
				be moving forward with them
			</div>
		</div>
	</div>
)

interface CostSavingDashboardProperties {
	currency: ICurrency
}

const CostSavingDashboard = (properties: CostSavingDashboardProperties) => {
	const { currency } = properties
	const { eventInfo: event } = eventInfoStore()
	const { viewMode, onToggleViewMode, selectedProposals } = costSavingsStore()
	const { concessionRequests } = concessionRequestStore()
	const [showNegotiatedDetails, setShowNegotiatedDetails] = useState(false)
	const [savingsData, setSavingsData] = useState<ISavingData>({
		proposedRooms: 0,
		proposedMeeting: 0,
		proposedTotal: 0,
		contractedRooms: 0,
		contractedMeeting: 0,
		contractedTotal: 0,
		percentage: 0,
		value: 0,
		rooms: 0,
		meeting: 0,
		concessions: 0,
		items: []
	})
	const [savingsDetails, setSavingsDetails] = useState<
		{
			id: string
			title: string | null
			venueId: string | null
			venueName: string | null
			type: string
			approved: boolean
			savingsKey: string
			savings: number
		}[]
	>([])

	console.log(savingsDetails)

	useEffect(() => {
		const venues = selectedProposals.reduce(
			(a: Record<string, string | null>, c) => {
				a[c.venueId] = c.venueName
				return a
			},
			{}
		)
		const totalProposedComponents = selectedProposals.reduce(
			(a, c) => {
				const {
					roomCostPlusPlus,
					totalRebateAmount,
					foodAndBeveragePlusPlus,
					roomRentalPlusPlus,
					serviceChargePlusPlus
				} = c.rateSets.proposed
				return {
					roomCostPlusPlus: a.roomCostPlusPlus + Number(roomCostPlusPlus),
					totalRebateAmount: a.totalRebateAmount + Number(totalRebateAmount),
					foodAndBeveragePlusPlus:
						a.foodAndBeveragePlusPlus + Number(foodAndBeveragePlusPlus),
					roomRentalPlusPlus: a.roomRentalPlusPlus + Number(roomRentalPlusPlus),
					serviceChargePlusPlus:
						a.serviceChargePlusPlus + Number(serviceChargePlusPlus)
				}
			},
			{
				roomCostPlusPlus: 0,
				totalRebateAmount: 0,
				foodAndBeveragePlusPlus: 0,
				roomRentalPlusPlus: 0,
				serviceChargePlusPlus: 0
			}
		)
		const details = [
			...selectedProposals.flatMap(proposalRequest => {
				const { venueId, venueName, rateSets } = proposalRequest
				const {
					roomCostPlusPlus,
					totalRebateAmount,
					foodAndBeveragePlusPlus,
					roomRentalPlusPlus,
					serviceChargePlusPlus
				} = rateSets.contracted
				const a = []
				if (event?.roomBlocksRequired) {
					a.push({
						id: `${venueId}-${savingsKeys.rooms}`,
						title: 'Contracted Rooms Cost',
						venueId,
						venueName,
						type: 'Negotiated',
						approved: true,
						savingsKey: savingsKeys.rooms,
						contracted: Number(roomCostPlusPlus) - Number(totalRebateAmount),
						savings:
							Number(totalProposedComponents.roomCostPlusPlus) -
							Number(totalProposedComponents.totalRebateAmount) -
							(Number(roomCostPlusPlus) - Number(totalRebateAmount))
					})
				}
				if (event?.meetingSpaceRequired) {
					a.push({
						id: `${venueId}-${savingsKeys.fandb}`,
						title: 'Contracted Meeting Cost',
						venueId,
						venueName,
						type: 'Negotiated',
						approved: true,
						savingsKey: savingsKeys.meeting,
						contracted:
							Number(foodAndBeveragePlusPlus) +
							Number(roomRentalPlusPlus) +
							Number(serviceChargePlusPlus),
						savings:
							Number(totalProposedComponents.foodAndBeveragePlusPlus) +
							Number(totalProposedComponents.roomRentalPlusPlus) +
							Number(totalProposedComponents.serviceChargePlusPlus) -
							(Number(foodAndBeveragePlusPlus) +
								Number(roomRentalPlusPlus) +
								Number(serviceChargePlusPlus))
					})
				}
				return a
			}),
			...concessionRequests.flatMap(cr =>
				cr.responses
					.filter(r => Object.keys(venues).includes(r.venueId ?? ''))
					.map(response => ({
						id: `${response.id}-${savingsKeys.concession}`,
						title: cr.concessionRequest.text,
						venueId: response.venueId,
						venueName: venues[response.venueId ?? ''],
						type: 'Concession',
						approved:
							(response.responses?.length ?? 0) > 0 &&
							(response.responses?.at(0)?.score ?? 0) > 0,
						savingsKey: savingsKeys.concession,
						savings:
							response.concessionValuePlanned === null
								? Number(response.concessionValueProposed)
								: Number(response.concessionValuePlanned) || 0
					}))
			)
		]
		const totalProposed =
			totalProposedComponents.roomCostPlusPlus +
			-1 * totalProposedComponents.totalRebateAmount +
			totalProposedComponents.foodAndBeveragePlusPlus +
			totalProposedComponents.roomRentalPlusPlus +
			totalProposedComponents.serviceChargePlusPlus

		const totalSavings = details.reduce(
			(a, c) => {
				const savings = c.approved ? c.savings : 0
				a.total += savings
				a[c.type] += savings
				a[c.savingsKey] += savings
				return a
			},
			{
				total: 0,
				[savingsKeys.rooms]: 0,
				[savingsKeys.meeting]: 0,
				[savingsKeys.concession]: 0,
				Negotiated: 0,
				Concession: 0
			}
		)

		const totalContracted = details.reduce(
			(a, c) => {
				a.total += c.contracted || 0
				a[c.savingsKey] += c.contracted || 0
				return a
			},
			{
				total: 0,
				[savingsKeys.rooms]: 0,
				[savingsKeys.meeting]: 0,
				[savingsKeys.concession]: 0
			}
		)

		setSavingsData({
			proposedRooms:
				totalProposedComponents.roomCostPlusPlus -
				totalProposedComponents.totalRebateAmount,
			proposedMeeting:
				totalProposedComponents.foodAndBeveragePlusPlus +
				totalProposedComponents.roomRentalPlusPlus +
				totalProposedComponents.serviceChargePlusPlus,
			proposedTotal: totalProposed,
			contractedRooms: totalContracted[savingsKeys.rooms],
			contractedMeeting: totalContracted[savingsKeys.meeting],
			contractedTotal: totalContracted.total,
			percentage: (totalSavings.total / totalProposed) * 100,
			value: totalSavings.total,
			rooms: totalSavings[savingsKeys.rooms],
			meeting: totalSavings[savingsKeys.meeting],
			concessions: totalSavings[savingsKeys.concession],
			items: [
				{
					name: 'Negotiations',
					percentage: (totalSavings.Negotiated / totalProposed) * 100,
					value: totalSavings.Negotiated,
					fill: '#027587'
				},
				{
					name: 'Concessions',
					percentage: (totalSavings.Concession / totalProposed) * 100,
					value: totalSavings.Concession,
					fill: '#95D9D0'
				}
			]
		})
		setSavingsDetails(details)
	}, [
		event?.roomBlocksRequired,
		event?.meetingSpaceRequired,
		selectedProposals,
		concessionRequests
	])

	return event ? (
		<div className='flex flex-col'>
			<div className='flex items-center justify-between border-b p-4'>
				<div className='flex-1'>
					<div className='gap-2'>
						<div className='text-xl font-semibold'>Cost Savings Dashboard</div>
						<span className='text-sm text-gray-600'>
							Measure and track your cost savings after beginning the
							contracting process with one or more hotels. Any quantifiable
							approved concessions and any updates that you&apos;ve made to your{' '}
							<Link
								className='text-sm font-semibold underline'
								to={`/planner/event/${event.id}/review-proposals/proposal-tracker`}
							>
								contracted rates
							</Link>
							{' will be reflected in this dashboard.'}
						</span>
					</div>
				</div>
				<div className='ml-4 flex items-center'>
					<HSButton
						color='light'
						size='sm'
						className='w-grow'
						onClick={() => onToggleViewMode()}
					>
						<div className='flex items-center gap-2 text-sm font-medium'>
							<FontAwesomeIcon icon={viewMode.toggleCta.icon} />
							{viewMode.toggleCta.label}
						</div>
					</HSButton>
				</div>
			</div>
			<div className='flex flex-col gap-4 p-4'>
				<div className='grid grid-cols-3 gap-4'>
					<div className='rounded-lg border shadow-sm'>
						<div className='flex items-center justify-start gap-2 border-b p-4'>
							<div className='text-2xl font-semibold'>
								{formatCurrency(savingsData.value, currency)}
							</div>
							<div className='text-xs font-medium text-gray-500'>
								Total Cost Savings
							</div>
						</div>
						<div className='p-4 text-base font-medium text-gray-700'>
							Cost Savings Breakdown
							<div className='mt-2 text-sm text-gray-500'>
								{savingsData.value > 0 ? (
									<>
										{Boolean(event.roomBlocksRequired) &&
											typeof savingsData.rooms === 'number' &&
											!Number.isNaN(savingsData.rooms) && (
												<div>{`Rooms: $${savingsData.rooms.toFixed(2)}`}</div>
											)}
										{Boolean(event.meetingSpaceRequired) &&
											typeof savingsData.meeting === 'number' &&
											!Number.isNaN(savingsData.meeting) && (
												<div>{`Meeting: $${savingsData.meeting.toFixed(2)}`}</div>
											)}
										<div>{`Concessions: $${savingsData.concessions.toFixed(2)}`}</div>
									</>
								) : (
									<div className='text-center text-gray-400'>No Savings</div>
								)}
							</div>
						</div>
					</div>
					<div className='rounded-lg border p-4 shadow-sm'>
						<div className='text-base font-medium text-gray-700'>
							Savings Summary
						</div>
						{/* You can add a chart here using savingsData if needed */}
						<div className='mt-2 text-sm text-gray-500'>
							Total: ${savingsData.value.toFixed(2)} (
							{savingsData.percentage.toFixed(1)}%)
						</div>
					</div>
					<div className='rounded-lg border p-4 shadow-sm'>
						<div className='text-base font-medium text-gray-700'>
							Proposed vs Contracted
						</div>
						<div className='mt-2 text-sm text-gray-500'>
							Proposed: ${savingsData.proposedTotal.toFixed(2)}
							<br />
							Contracted: ${savingsData.contractedTotal.toFixed(2)}
						</div>
					</div>
				</div>
				<HSTable
					allowPaging={false}
					rows={[]}
					noDataTemplate={emptyTemplate(event.id ?? '')}
					columns={[
						{
							field: 'title',
							headerText: 'Savings',
							width: 250
						},
						{
							field: 'type',
							headerText: 'Type',
							width: 120
						},
						{
							field: 'approved',
							headerText: 'Status',
							width: 120
						},
						{
							field: 'savings',
							headerText: 'Amount',
							width: 120
						},
						{ field: 'savingsKey', headerText: 'Details', width: 120 }
					]}
				/>
			</div>
			{showNegotiatedDetails ? (
				<NegotiatedSavingsDetails
					onClose={() => setShowNegotiatedDetails(false)}
					isEdit={false}
					isProfile={false}
					userProfileId={event.id ?? ''}
					contractClauses={[]}
					onAddUpdate={() => {}}
				/>
			) : null}
		</div>
	) : null
}

export default CostSavingDashboard
