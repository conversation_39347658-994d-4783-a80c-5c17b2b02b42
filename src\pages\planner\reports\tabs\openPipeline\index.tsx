/* eslint-disable react/no-array-index-key */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable unicorn/no-array-reduce */
import {
	faColumns3,
	faDownload,
	faInfoCircle,
	faChevronDown,
	faClock
} from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import { formatCurrency, formatNumber, type ICurrency } from 'lib/helpers'
import { ProposalRequestStatusMap } from 'lib/helpers/statusMaps'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { useState, useEffect } from 'react'
import { useGetChains } from 'lib/services/chains.service'
import HSTooltip from 'components/tooltip'
import { chainColors } from '../../common'
import HSCurrencyPicker from 'components/currencySelector'
import OpenRequests from './openRequests'
import { chainCountFilters } from './helpers'
import RFPsOverdue from './rfpsOverDue'
import FilterPopover from '../common/filterHelper'
import {
	currencyTemplate,
	dateTemplate,
	rfpNameTemplate,
	statusTemplate
} from './templates'
import type { ISetFilters } from '../../helper'
import CustomizeColumnsModal from 'components/planner/customizeColumnsModal'
import { columnOptions } from 'components/planner/customizeColumnsModal/columnConfig'
import HSTable from 'components/table'
import HSProgress from 'components/progressBar'
import { packageWorkbook } from 'lib/services/report.service'
import rawDataFieldsForPlanner from '../columns/columnDefinition'
import { toast } from 'react-toastify'
import Loader from 'components/loader'
import PageLoader from 'components/pageLoader'
import { Link } from 'react-router-dom'
import HSBadge from 'components/badge'

interface OpenPipelineProperties {
	eventPlans: EventPlan[]
	currency: ICurrency
	setFilters: React.Dispatch<React.SetStateAction<ISetFilters>>
	filteredChainIds: string[]
	filters: ISetFilters
	isFeatureLocked: boolean
	isLoading: boolean
	hotelsListFilterFunction: (hotel: { status: string }) => boolean
}

export interface IHotel {
	venueId: string
	status: string
	venueName: string
	venueLocation: string
	chainId: string
	brandId: string
}

const OpenPipeline = (properties: OpenPipelineProperties) => {
	const {
		eventPlans,
		currency,
		setFilters,
		filters,
		filteredChainIds,
		isFeatureLocked,
		isLoading,
		hotelsListFilterFunction
	} = properties
	const { data: chains } = useGetChains()
	const [showColumnsModal, setShowColumnsModal] = useState(false)
	const [isDownloading, setIsDownloading] = useState(false)
	const [visibleColumns, setVisibleColumns] = useState<string[]>(columnOptions)
	const [metrics, setMetrics] = useState<{
		overdueForSelection: EventPlan[]
		overdueForResponses: EventPlan[]
		requestsByChain: {
			key: string
			name: string
			rfpIds: string[]
			rfpCount: number
			hotelCount: number
			fill: string
		}[]
	}>({
		overdueForSelection: [],
		overdueForResponses: [],
		requestsByChain: []
	})

	useEffect(() => {
		const proposalRequests = eventPlans.reduce(
			(a, c) => [
				...a,
				...Object.keys(c.proposalRequestStatuses)
					.filter(k => k !== ProposalRequestStatusMap.Pending?.key)
					.flatMap(k => c.proposalRequestStatuses[k])
			],
			[]
		)
		setMetrics({
			...eventPlans.reduce(
				(
					a: {
						overdueForSelection: EventPlan[]
						overdueForResponses: EventPlan[]
					},
					c
				) => {
					if (c.overdueForSelection) {
						a.overdueForSelection.push(c)
					}
					if (c.overdueForResponses) {
						a.overdueForResponses.push(c)
					}
					return a
				},
				{
					overdueForSelection: [],
					overdueForResponses: []
				}
			),
			requestsByChain: proposalRequests.reduce((a, c) => {
				const chainId = c.chainId || 'INDEPENDENT'
				const chainItem = a.find(
					(item: { key: string }) => item.key === chainId
				)
				if (chainItem?.key) {
					const rfpCounted = chainItem.rfpIds.includes(c.eventPlanId)
					return [
						...a.filter((item: { key: string }) => item.key !== chainItem.key),
						{
							...chainItem,
							rfpIds: rfpCounted
								? chainItem.rfpIds
								: [...chainItem.rfpIds, c.eventPlanId],
							[chainCountFilters.rfpCount.key]: rfpCounted
								? chainItem.rfpCount
								: chainItem.rfpCount + 1,
							[chainCountFilters.hotelCount.key]: chainItem.hotelCount + 1
						}
					]
				}
				return [
					...a,
					{
						key: chainId,
						name:
							chains?.find(chain => chain.id === c.chainId)?.name ??
							'Independent',
						rfpIds: [c.eventPlanId],
						[chainCountFilters.rfpCount.key]: 1,
						[chainCountFilters.hotelCount.key]: 1,
						fill: chainColors[a.length % chainColors.length]
					}
				]
			}, [])
		})
	}, [chains, eventPlans])

	const onClickExport = () => {
		setIsDownloading(true)
		toast.info(
			<div className='flex gap-2'>
				<FontAwesomeIcon
					icon={faClock}
					className='rounded-full bg-yellow-100 p-1 text-yellow-700'
					size='lg'
				/>
				Downloading...
			</div>
		)

		const exportConfiguration = {
			workbookId: 'clientexport',
			packageData: {
				workbookName: 'Open-Pipeline',
				sheets: [
					{
						name: 'open pipeline',
						title: 'Open Pipeline',
						rows: eventPlans
							.sort((a, b) => ((a.name ?? '') > (b.name ?? '') ? 1 : -1))
							.map(item => ({
								values: Object.fromEntries(
									rawDataFieldsForPlanner['open-pipeline'](
										hotelsListFilterFunction
									)
										.filter(column => column.header !== '')
										.map(field => [
											field.header,
											{
												value:
													typeof field.exportFormat === 'function'
														? field.exportFormat(item)
														: item[field.name as keyof EventPlan],
												formatString: field.exportFormatString ?? ''
											}
										])
								)
							}))
					}
				]
			}
		}
		packageWorkbook(exportConfiguration)
			.catch((error: unknown) => console.log(error))
			.finally(() => {
				setIsDownloading(false)
			})
	}

	const requestedTemplate = (item: number | null) => (
		<div className='text-gray-600'>
			{item}{' '}
			<span className='text-gray-400'>
				hotel{item !== null && item > 1 ? 's' : ''}
			</span>
		</div>
	)

	const filterHotels = (hotel: IHotel) =>
		typeof hotelsListFilterFunction === 'function'
			? hotelsListFilterFunction(hotel)
			: true
	const formatHotelsList = (
		item: EventPlan & {
			hotels?: IHotel[]
		}
	) => {
		const filteredHotels = (item.hotels || []).filter(hotel =>
			filterHotels(hotel)
		)
		const renderTooltip = () => (
			<div className='flex flex-col gap-1'>
				{filteredHotels.slice(1).map((hotel, index) => (
					<div key={index} className='text-sm font-medium text-gray-400'>
						<div className='flex flex-col'>{hotel.venueName}</div>
						<div className='text-sm font-medium text-gray-400'>
							{hotel.venueLocation}
						</div>
					</div>
				))}
			</div>
		)
		return (
			<div className='flex gap-1'>
				{filteredHotels.length > 0 && (
					<div>
						<Link to={`/planner/event/${item.id}`}>
							<div className='text-gray-600'>{filteredHotels[0].venueName}</div>
							<div className='text-sm text-gray-400'>
								{filteredHotels[0].venueLocation}
							</div>
						</Link>
					</div>
				)}
				{filteredHotels.length > 1 && (
					<div className='flex gap-2 text-sm'>
						<HSTooltip content={renderTooltip()}>
							<HSBadge color='gray'>{`+${filteredHotels.length - 1}`}</HSBadge>
						</HSTooltip>
					</div>
				)}
			</div>
		)
	}

	if (isLoading) {
		return <PageLoader />
	}

	return (
		<>
			<div
				className='flex flex-col overflow-auto'
				style={{ maxHeight: 'calc(100vh - 8rem)' }}
			>
				<div className='border-b px-4 py-6'>
					<div className='flex items-center justify-between'>
						<div className='flex flex-col'>
							<div className='text-xl font-semibold text-gray-900'>
								Open Pipeline Dashboard
							</div>
							<div className='text-sm font-normal text-gray-500'>
								Monitor your active RFP pipeline in real-time to prioritize
								high-value opportunities, address bottlenecks, and accelerate
								time-to-close
							</div>
						</div>
						<div className='flex items-center gap-4'>
							<div className='flex items-center gap-2'>
								<HSCurrencyPicker
									value={currency}
									onChange={value => {
										setFilters(previous => ({
											...previous,
											currency: value
										}))
									}}
									color='light'
									icon={faChevronDown}
								/>
							</div>
							<FilterPopover
								filters={filters}
								setFilters={setFilters}
								chains={chains}
								filteredChainIds={filteredChainIds}
							/>
						</div>
					</div>
				</div>
				<div className='px-4 py-6'>
					<div className='flex flex-col gap-6'>
						<div className='flex gap-6'>
							<div className='w-3/4'>
								<div className='card h-full p-4'>
									<div className='flex flex-col gap-3'>
										<div className='flex gap-4'>
											<div className='w-1/3'>
												<div className='flex flex-col gap-2.5'>
													<div className='flex items-center gap-2'>
														<div className='text-sm font-bold text-gray-500'>
															Active RFPs
														</div>
														<HSTooltip content='The total number of active RFPs that are currently open and awaiting responses'>
															<FontAwesomeIcon
																icon={faInfoCircle}
																className='text-gray-500'
															/>
														</HSTooltip>
													</div>
													<div className='text-2xl font-semibold leading-none text-gray-900'>
														{formatNumber(eventPlans.length)}
													</div>
												</div>
											</div>
											<div className='border-l' />
											<div className='w-1/3'>
												<div className='flex flex-col gap-2.5'>
													<div className='flex items-center gap-2'>
														<div className='text-sm font-bold text-gray-500'>
															Overdue for RFPs
														</div>
														<HSTooltip content='The total number of RFPs where the proposal selection date has passed but the booking has not yet been awarded'>
															<FontAwesomeIcon
																icon={faInfoCircle}
																className='text-gray-500'
															/>
														</HSTooltip>
													</div>
													<div className='flex items-center gap-4'>
														<div className='flex items-center gap-2'>
															<div className='text-2xl font-semibold leading-none text-gray-900'>
																{formatNumber(
																	metrics.overdueForSelection.length
																)}{' '}
															</div>
															<div className='text-xs font-medium text-gray-500'>
																for proposals
															</div>
														</div>
														<div className='flex items-center gap-2'>
															<div className='text-2xl font-semibold leading-none text-gray-900'>
																{formatNumber(
																	metrics.overdueForResponses.length
																)}
															</div>
															<div className='text-xs font-medium text-gray-500'>
																for selection
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div className='w-1/4'>
								<div className='card h-full p-4'>
									<div className='flex flex-col gap-2.5'>
										<div className='flex flex-col gap-2.5'>
											<div className='flex items-center gap-2'>
												<div className='text-sm font-bold text-gray-500'>
													Total Opportunity Value
												</div>
												<HSTooltip content='The number of RFPs where the response due date has passed but responses have not yet been received'>
													<FontAwesomeIcon
														icon={faInfoCircle}
														className='text-gray-500'
													/>
												</HSTooltip>
											</div>

											<div className='text-2xl font-semibold leading-none text-gray-900'>
												{formatCurrency(
													eventPlans.reduce((a, c) => a + (c.rfpValue ?? 0), 0),
													currency
												)}
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div className='flex gap-6'>
							<div className='w-1/5'>
								<div className='card h-full'>
									<OpenRequests
										metrics={metrics}
										isFeatureLocked={isFeatureLocked}
									/>
								</div>
							</div>
							<div className='w-4/5'>
								<div className='card h-full'>
									<RFPsOverdue
										metrics={metrics}
										isFeatureLocked={isFeatureLocked}
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className='px-4 py-6'>
					<div className='flex items-center justify-between'>
						<div className='text-sm font-medium text-gray-900'>
							{eventPlans.length} RFP{}
							{eventPlans.length > 1 ? 's' : ''}
						</div>
						<div className='flex items-center gap-4'>
							<HSButton
								color='light'
								onClick={onClickExport}
								disabled={eventPlans.length === 0 || isFeatureLocked}
							>
								{isDownloading ? (
									<Loader size='sm' />
								) : (
									<FontAwesomeIcon icon={faDownload} />
								)}
							</HSButton>
							<HSButton color='light' onClick={() => setShowColumnsModal(true)}>
								<FontAwesomeIcon icon={faColumns3} />
							</HSButton>
						</div>
					</div>
				</div>
				<div className='px-4 pb-6'>
					<HSTable
						isLocked={isFeatureLocked}
						allowPaging
						rows={isFeatureLocked ? [] : eventPlans}
						defaultSort={{
							field: 'name',
							direction: 'asc'
						}}
						columns={[
							{
								field: 'name',
								headerText: 'RFP Name/Code',
								render: rfpNameTemplate,
								width: 250,
								sortable: true,
								clipMode: 'ellipsis',
								freeze: 'left',
								visible: visibleColumns.includes('RFP Name/Code')
							},
							{
								field: 'status',
								headerText: 'Status',
								render: statusTemplate,
								width: 200,
								sortable: true,
								clipMode: 'ellipsis',
								visible: visibleColumns.includes('Status')
							},
							{
								field: 'ownerName',
								headerText: 'Owner',
								width: 300,
								sortable: true,
								visible: visibleColumns.includes('Assigned')
							},
							{
								field: 'rfpValue',
								headerText: 'RFP Value',
								render: (item: EventPlan) =>
									currencyTemplate(item.rfpValue, currency),
								sortable: true,
								clipMode: 'ellipsis',
								width: 150,
								visible: visibleColumns.includes('RFP Value')
							},
							{
								field: 'submitted',
								headerText: 'Sent',
								width: 150,
								render: (item: EventPlan) => dateTemplate(item.submitted),
								sortable: true,
								clipMode: 'ellipsis',
								visible: visibleColumns.includes('Sent')
							},

							{
								field: 'responsesDueDate',
								headerText: 'Response Due',
								render: (item: EventPlan) =>
									dateTemplate(item.responsesDueDate),
								width: 150,
								sortable: true,
								clipMode: 'ellipsis',
								visible: visibleColumns.includes('Responses due')
							},
							{
								field: 'responseRate',
								headerText: 'Response Rate',
								width: 250,
								sortable: true,
								clipMode: 'ellipsis',
								visible: visibleColumns.includes('Response rate'),
								render: (item: EventPlan) => (
									<div className='flex flex-col gap-1'>
										<div className='flex items-center justify-between'>
											<div className='text-sm font-medium text-gray-600'>
												{(item.requestsResponded && item.requestsTotal
													? (item.requestsResponded / item.requestsTotal) * 100
													: 0
												).toFixed(0)}
												%
											</div>
											<div className='text-xs font-normal text-gray-500'>
												{`${formatNumber(item.requestsResponded)}/${formatNumber(item.requestsTotal)} hotels`}
											</div>
										</div>
										<HSProgress
											progress={
												item.requestsResponded && item.requestsTotal
													? (item.requestsResponded / item.requestsTotal) * 100
													: 0
											}
											size='sm'
											color='primary'
										/>
									</div>
								)
							},
							{
								field: 'selectionDate',
								headerText: 'Decision Due',
								render: (item: EventPlan) => dateTemplate(item.selectionDate),
								width: 150,
								sortable: true,
								clipMode: 'ellipsis',
								visible: visibleColumns.includes('Decision due')
							},
							{
								field: 'averageRoomRate',
								headerText: 'AVG Room Rate',
								render: (item: EventPlan) =>
									currencyTemplate(item.averageRoomRate, currency),
								width: 150,
								sortable: true,
								visible: visibleColumns.includes('Average Room Rate')
							},
							{
								field: 'requestsTotal',
								headerText: 'Requested',
								width: 150,
								sortable: true,
								render: (item: EventPlan) =>
									requestedTemplate(item.requestsTotal),
								visible: visibleColumns.includes('Requested')
							},
							{
								field: 'requestsAwaiting',
								headerText: 'Awaiting',
								width: 150,
								sortable: true,
								render: (item: EventPlan) =>
									requestedTemplate(item.requestsAwaiting),
								visible: visibleColumns.includes('Awaiting')
							},
							{
								field: 'requestsResponded',
								headerText: 'Responses',
								width: 150,
								sortable: true,
								render: (item: EventPlan) =>
									requestedTemplate(item.requestsResponded),
								visible: visibleColumns.includes('Responses')
							},
							{
								field: 'hotelsList',
								headerText: 'Hotels Requested',
								width: 300,
								render: formatHotelsList,
								visible: visibleColumns.includes('Hotels Requested'),
								sortable: true,
								clipMode: 'ellipsis'
							}
						]}
					/>
				</div>
			</div>
			<CustomizeColumnsModal
				show={showColumnsModal}
				onClose={() => setShowColumnsModal(false)}
				context='openPipeline'
				selectedColumns={visibleColumns}
				onColumnsChange={setVisibleColumns}
			/>
		</>
	)
}

export default OpenPipeline
