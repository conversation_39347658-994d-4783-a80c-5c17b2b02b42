import { faPlus, faTrashXmark } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	ColumnDirective,
	ColumnsDirective,
	Inject,
	Sort
} from '@syncfusion/ej2-react-grids'
import HSButton from 'components/button'
import DataGrid from 'components/dataGrid'
import { useGetChains } from 'lib/services/chains.service'
import type { HotelChain } from 'models/chains'
import { useEffect, useState } from 'react'
import AddNewChainDrawer from './addNewChain'
import useUserProfileStore from 'lib/store/userProfileStore'
import Loader from 'components/loader'

interface IChainMatchEditor {
	criteria: Record<string, string[]>
	onChange?: ({
		key,
		label,
		value
	}: {
		key: string
		label: string
		value: string[]
	}) => void
}

const criteriaKey = 'chainId'

const ChainMatchEditor = (properties: IChainMatchEditor) => {
	const { onChange } = properties

	const { userProfile } = useUserProfileStore()
	const { data: chainsData, isFetching: isLoadingChains } = useGetChains()

	const [chains, setChains] = useState<HotelChain[] | undefined>([])
	const [showAddChainDrawer, setShowAddChainDrawer] = useState(false)
	const [selectedItems, setSelectedItems] = useState<string[]>(
		userProfile.venueMatchCriteria.chainId ?? []
	)

	//
	const onChainUpdate = (
		selectedChains: {
			label: string
			value: string
		}[]
	) => {
		if (typeof onChange === 'function')
			onChange({
				key: criteriaKey,
				label: 'Chain',
				value: selectedChains.map(chain => chain.value)
			})
	}

	useEffect(() => {
		setChains(chainsData?.filter((d: HotelChain) => d.name))
	}, [chainsData])

	const onRemoveChain = (chainId: string) => {
		const updatedChains =
			userProfile.venueMatchCriteria.chainId?.filter(c => c !== chainId) ?? []
		onChange?.({
			key: criteriaKey,
			label: 'Chain',
			value: updatedChains
		})
		setSelectedItems(updatedChains)
	}

	const removeChainTemplate = (item: HotelChain) => (
		<div className='flex items-center justify-center'>
			<HSButton
				color='light'
				size='sm'
				onClick={() => onRemoveChain(item.id ?? '')}
			>
				<FontAwesomeIcon icon={faTrashXmark} className='text-red-500' />
			</HSButton>
		</div>
	)

	const nameTemplate = (item: HotelChain) => (
		<div className='text-sm font-normal text-gray-600'>{item.name}</div>
	)
	const gridData = chainsData
		?.map((chain: HotelChain) => ({
			name: chain.name,
			id: chain.id
		}))
		.filter(d => userProfile.venueMatchCriteria.chainId?.includes(d.id ?? ''))

	if (typeof onChange === 'function') {
		return (
			<div className='flex flex-col gap-2'>
				<div className='flex items-center justify-between'>
					<div className='text-sm font-medium text-gray-900'>
						{gridData?.length} Chain{gridData && gridData.length > 1 ? 's' : ''}
					</div>
					<HSButton onClick={() => setShowAddChainDrawer(true)}>
						<div className='flex items-center gap-2'>
							<FontAwesomeIcon icon={faPlus} color='light' />
							<div>Add New</div>
						</div>
					</HSButton>
				</div>
				<div>
					{isLoadingChains ? (
						<Loader />
					) : (
						<DataGrid dataSource={gridData ?? []} allowPaging={false}>
							<ColumnsDirective>
								<ColumnDirective
									field='name'
									headerText='Name'
									template={nameTemplate}
									autoFit
								/>
								<ColumnDirective
									field='id'
									headerText='Remove'
									template={removeChainTemplate}
									textAlign='Center'
									width={150}
								/>
							</ColumnsDirective>
							<Inject services={[Sort]} />
						</DataGrid>
					)}
				</div>
				<AddNewChainDrawer
					onClose={() => setShowAddChainDrawer(false)}
					show={showAddChainDrawer}
					onUpdate={onChainUpdate}
					selectedItems={selectedItems}
					setSelectedItems={setSelectedItems}
				/>
			</div>
		)
	}

	return (
		<div className='chain-match-label'>
			{userProfile.venueMatchCriteria.chainId
				?.map(c => chains?.find(chain => chain.id === c)?.name)
				.join(', ')}
		</div>
	)
}

export default ChainMatchEditor
