/* eslint-disable react/no-array-index-key */
/* eslint-disable no-plusplus */
import { faExclamationTriangle } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import { updateSortIndex } from 'components/event/planner/meetingSpaceRequests'
import HSModal from 'components/modal'
import HSRadioButton from 'components/radioButton'
import HSSelect from 'components/select'
import { addDays, differenceInDays, parseISO, startOfWeek } from 'date-fns'
import { Modal } from 'flowbite-react'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { useEffect, useState } from 'react'

interface IShiftItemModalProperties {
	setShowShiftItemsContext: React.Dispatch<
		React.SetStateAction<{
			fromDay: number
			delta: number
			showModal: boolean
		}>
	>
	showShiftItemsContext: {
		fromDay: number
		delta: number
		showModal: boolean
	}
}

const ShiftItemModal = (properties: IShiftItemModalProperties) => {
	const { setShowShiftItemsContext, showShiftItemsContext } = properties
	const { eventInfo: event, setProperty } = eventInfoStore()
	const [availableDates, setAvailableDates] = useState<Date[]>([])

	const onClose = () => {
		setShowShiftItemsContext({
			fromDay: -1,
			delta: 0,
			showModal: false
		})
	}

	useEffect(() => {
		const a = []
		if (event) {
			if (event.itemType === 'eventPlan') {
				// total days
				const sd = parseISO(event.startDate)
				const ed = parseISO(event.endDate ?? '')
				for (let d = 0; d <= differenceInDays(ed, sd); d++) {
					a.push(addDays(sd, d))
				}
			} else if (event.itemType === 'eventPlanTemplate') {
				const sd = addDays(startOfWeek(new Date()), event.startDayOfWeek ?? 0)
				for (let d = 0; d <= (event.totalDays ?? 0) - 1; d++) {
					a.push(addDays(sd, d))
				}
			}
			setAvailableDates(a)
		}
	}, [
		event?.startDate,
		event?.endDate,
		event?.startDayOfWeek,
		event?.totalDays,
		event?.itemType,
		event
	])

	const onConfirmShiftItems = () => {
		if (event) {
			const maxDayNumber = availableDates.length - 1

			setProperty(
				'meetingSpaceRequests',
				event.meetingSpaceRequests?.map(msr => {
					if (
						msr.dayNumber === showShiftItemsContext.fromDay ||
						showShiftItemsContext.fromDay < 0
					) {
						const dayNumber = Math.max(
							0,
							Math.min(
								(msr.dayNumber ?? 0) + showShiftItemsContext.delta,
								maxDayNumber
							)
						)
						console.log({ from: msr.dayNumber, to: dayNumber })
						return {
							...msr,
							dayNumber,
							sortIndex: updateSortIndex(
								msr.dayNumber ?? 0,
								msr.sortIndex ?? 0,
								dayNumber
							)
						}
					}
					return msr
				})
			)

			setShowShiftItemsContext({
				fromDay: -1,
				delta: 0,
				showModal: false
			})
		}
	}

	return (
		<HSModal header='Shift Items' openModal onClose={onClose} size='md'>
			{event ? (
				<Modal.Body>
					<div className='flex flex-col gap-4'>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-medium text-gray-900'>
								Shift Items from
							</div>
							<HSSelect>
								<option value={-1}>
									All Days ({event.meetingSpaceRequests?.length} item
									{event.meetingSpaceRequests?.length === 1 ? '' : 's'})
								</option>
								{availableDates.map((d, index) => (
									<option key={index} value={index}>
										Day {index + 1} (
										{
											event.meetingSpaceRequests?.filter(
												msr => msr.dayNumber === index
											).length
										}{' '}
										item
										{event.meetingSpaceRequests?.filter(
											msr => msr.dayNumber === index
										).length === 1
											? ''
											: 's'}
										)
									</option>
								))}
							</HSSelect>
						</div>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-medium text-gray-900'>
								Shift Items by
							</div>
							<div className='flex flex-col gap-2'>
								{[
									{ label: 'No Change', value: 0 },
									{ label: '2 Days Earlier', value: -2 },
									{ label: '1 Day Earlier', value: -1 },
									{ label: '1 Day Later', value: 1 },
									{ label: '2 Days Later', value: 2 }
								].map((option, index) => (
									<HSRadioButton
										key={index}
										value={option.value.toString()}
										label={option.label}
										name='shift-item-by'
										selectedValue={showShiftItemsContext.delta.toString()}
										onChange={() => {
											setShowShiftItemsContext(s => ({
												...s,
												delta: Number(option.value)
											}))
										}}
									/>
								))}
							</div>
						</div>

						<div className='flex items-start gap-2'>
							<FontAwesomeIcon
								icon={faExclamationTriangle}
								className='text-red-600'
							/>
							<div className='text-sm font-normal text-orange-600'>
								Agenda items will not shift before the first day or after the
								last day of your event. You may need to add days to the event.
							</div>
						</div>
					</div>
				</Modal.Body>
			) : null}
			<Modal.Footer className='flex items-center justify-between gap-4'>
				<HSButton color='light' onClick={onClose} className='grow'>
					Cancel
				</HSButton>
				<HSButton onClick={onConfirmShiftItems} className='grow'>
					Shift Item
				</HSButton>
			</Modal.Footer>
		</HSModal>
	)
}

export default ShiftItemModal
