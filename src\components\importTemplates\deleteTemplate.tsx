import HSModal from 'components/modal'
import { Modal } from 'flowbite-react'
import type { EventPlan } from 'models/proposalResponseMonitor'
import HSButton from 'components/button'
import { deleteEventPlan } from 'lib/services/planner.service'
import { toast } from 'react-toastify'

interface IDeleteTemplateProperties {
	eventPlan: EventPlan
	onClose: () => void
}

const DeleteTemplate = (properties: IDeleteTemplateProperties) => {
	const { eventPlan, onClose } = properties

	const handleDelete = () => {
		deleteEventPlan(eventPlan.id ?? '')
			.then(() => {
				toast.success('Template deleted successfully')
				onClose()
			})
			.catch(() => {
				toast.error('Failed to delete template')
			})
	}
	return (
		<HSModal openModal onClose={onClose} header='Delete Template' size='md'>
			<Modal.Body>
				<div className='flex flex-col'>
					<div className='text-base font-normal text-gray-500'>
						Are you sure you want to delete <b>{eventPlan.name}</b> Template?
					</div>
					<div className='text-base font-normal text-gray-500'>
						This action cannot be undone.
					</div>
				</div>
			</Modal.Body>
			<Modal.Footer>
				<HSButton color='light' className='grow'>
					Cancel
				</HSButton>
				<HSButton color='failure' className='grow' onClick={handleDelete}>
					Delete
				</HSButton>
			</Modal.Footer>
		</HSModal>
	)
}

export default DeleteTemplate
