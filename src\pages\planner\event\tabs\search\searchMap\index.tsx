/* eslint-disable react/no-array-index-key */
import MapControl from './mapControl/index'
import { useSearchParametersStore } from '../store'
import HotelMarker from 'components/googleMapV2/components/hotelMarker'
import type { Venue } from 'models/venue'
import GoogleMapV2, { defaultCenter } from 'components/googleMapV2'
import useMapStore from '../store/mapStore'
import { useGetEventDetail } from 'lib/services/eventPlans.service'
import LocationMapMarker from 'components/googleMapV2/components/locationMapMarker'
import { faPlaneUp } from '@fortawesome/pro-light-svg-icons'
import { useMemo, useState } from 'react'
import { InfoWindow, useAdvancedMarkerRef } from '@vis.gl/react-google-maps'
import HotelPreview from 'components/hotel/preview'

interface SearchMapProperties {
	searchResults: Venue[] | null
	eventId?: string
}
const SearchMap = (properties: SearchMapProperties) => {
	const { searchResults, eventId } = properties
	const { location } = useSearchParametersStore()
	const { showTraffic, showTransit } = useMapStore()
	const { data: eventPlan } = useGetEventDetail(eventId ?? '', !!eventId)
	const [selectedVenue, setSelectedVenue] = useState<Venue | undefined>()

	const [markerReference, marker] = useAdvancedMarkerRef()

	const getDefaultCenter = useMemo(() => {
		if (location.latitude) {
			return {
				lat: location.latitude,
				lng: location.longitude ?? 0
			}
		}
		return defaultCenter
	}, [location.latitude, location.longitude])

	return (
		<div className='flex grow'>
			<div className='w-1/4'>
				<MapControl />
			</div>
			<div className='w-3/4'>
				<GoogleMapV2
					defaultCenter={getDefaultCenter}
					showTraffic={showTraffic}
					showTransit={showTransit}
					defaultZoom={15}
					markerComponent={[
						...(searchResults
							? searchResults.map(venue => (
									<div key={venue.id}>
										<HotelMarker
											position={{
												lat: Number(venue.latitude),
												lng: Number(venue.longitude)
											}}
											onClick={() => {
												setSelectedVenue(venue)
											}}
											ref={markerReference}
										/>
										{selectedVenue?.id === venue.id ? (
											<InfoWindow
												anchor={marker}
												onCloseClick={() => {
													console.log('close')
												}}
												className='info-window w-full'
												style={{
													padding: 0
												}}
												maxWidth={1000}
												minWidth={800}
												zIndex={1000}
											>
												<HotelPreview venue={venue} />
											</InfoWindow>
										) : null}
									</div>
								))
							: []),
						...(eventPlan?.siteSearch?.additionalLocations
							? eventPlan.siteSearch.additionalLocations.map((ml, mlIndex) => (
									<LocationMapMarker
										position={{
											lat: Number(ml.latitude),
											lng: Number(ml.longitude)
										}}
										onClick={() => {}}
										icon={faPlaneUp}
										key={mlIndex}
									/>
								))
							: [])
					]}
				/>
			</div>
		</div>
	)
}

export default SearchMap
