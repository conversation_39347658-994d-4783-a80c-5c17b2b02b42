import type { BadgeProps, CustomFlowbiteTheme } from 'flowbite-react'
import { Badge } from 'flowbite-react'
import { memo } from 'react'

const customTheme: CustomFlowbiteTheme['badge'] = {
	root: {
		color: {
			light:
				'bg-gray-100 text-gray-600 group-hover:bg-gray-200 dark:bg-gray-200 dark:text-gray-700 dark:group-hover:bg-gray-300',
			orange:
				'bg-orange-100 text-orange-800 group-hover:bg-orange-200 dark:bg-orange-200 dark:text-orange-900 dark:group-hover:bg-orange-300',
			yellow:
				'bg-yellow-100 text-yellow-800 group-hover:bg-yellow-200 dark:bg-yellow-200 dark:text-yellow-900 dark:group-hover:bg-yellow-300',
			green:
				'bg-green-100 text-green-800 group-hover:bg-green-200 dark:bg-green-200 dark:text-green-900 dark:group-hover:bg-green-300',
			purple:
				'bg-purple-100 text-purple-800 group-hover:bg-purple-200 dark:bg-purple-200 dark:text-purple-900 dark:group-hover:bg-purple-300',
			blue: 'bg-blue-100 text-blue-800 group-hover:bg-blue-200 dark:bg-blue-200 dark:text-blue-900 dark:group-hover:bg-blue-300',
			primary:
				'bg-primary-100 text-primary-800 group-hover:bg-primary-200 dark:bg-primary-200 dark:text-primary-900 dark:group-hover:bg-primary-300',
			red: 'bg-red-100 text-red-800 group-hover:bg-red-200 dark:bg-red-200 dark:text-red-900 dark:group-hover:bg-red-300',
			white: 'bg-white text-gray-700'
		}
	}
}

const HSBadge = memo((properties: BadgeProps) => {
	const { children, ...rest } = properties

	return (
		<Badge theme={customTheme} {...rest}>
			{children}
		</Badge>
	)
})

export default HSBadge
