import type { ProposalRequest } from 'models/proposalResponseMonitor'
import { create } from 'zustand'

type ProposalAction =
	| { action: 'setStaticProperty', venueId: string, set: string, value: any }
	| { action: 'setProperty', venueId: string, set: string, value: any }
	| { action: 'setRoomRate', venueId: string, set: string, roomType: string, value: any }
	| { action: 'setDailyRoomRate', venueId: string, set: string, roomType: string, dayNumber: number, value: any }
	| { action: 'initSet', venueId: string, set: string, value: any }
	| { action: 'init', value: ProposalRequest[] }

interface ProposalStoreState {
	selectedProposals: ProposalRequest[]
}

interface ProposalStoreActions {
	dispatch: (action: ProposalAction) => void
}

const recalculateRateSet = (rateSet: any) => {
	// ...implement or import as needed...
	return rateSet
}

const autosave = true // or false, as needed

const updateRateSet = (proposalRequest: ProposalRequest, set: string) => {
	// ...implement or import as needed...
}

function proposalReducer(state: ProposalRequest[], { action, venueId, set, roomType, dayNumber, value }: any): ProposalRequest[] {
	let a: ProposalRequest[] = []
	const applyAttrition = value && typeof value === 'object' && Object.keys(value).includes('applyAttrition')
	const proposalRequest = venueId ? state.find(s => s.venueId === venueId) : undefined

	switch (action) {
		case 'setStaticProperty':
			a = [
				...state.filter(s => s.venueId !== venueId),
				{
					...proposalRequest,
					rateSets: {
						...proposalRequest.rateSets,
						[set]: {
							...proposalRequest.rateSets[set],
							...value
						}
					}
				}
			]
			break
		case 'setProperty':
			if (applyAttrition) {
				a = [
					...state.filter(s => s.venueId !== venueId),
					{
						...proposalRequest,
						rateSets: {
							targeted: recalculateRateSet({
								...proposalRequest.rateSets.targeted,
								...value
							}),
							proposed: recalculateRateSet({
								...proposalRequest.rateSets.proposed,
								...value
							}),
							contracted: recalculateRateSet({
								...proposalRequest.rateSets.contracted,
								...value
							})
						}
					}
				]
			} else {
				a = [
					...state.filter(s => s.venueId !== venueId),
					{
						...proposalRequest,
						rateSets: {
							...proposalRequest.rateSets,
							[set]: recalculateRateSet({
								...proposalRequest.rateSets[set],
								...value
							})
						}
					}
				]
			}
			break
		case 'setRoomRate':
			a = [
				...state.filter(s => s.venueId !== venueId),
				{
					...proposalRequest,
					rateSets: {
						...proposalRequest.rateSets,
						[set]: recalculateRateSet({
							...proposalRequest.rateSets[set],
							roomTypeRates: [
								...proposalRequest.rateSets[set].roomTypeRates.filter(rtr => rtr.roomType !== roomType),
								{
									...proposalRequest.rateSets[set].roomTypeRates.find(rtr => rtr.roomType === roomType),
									editingAverageRate: true,
									...value
								}
							]
						})
					}
				}
			]
			break
		case 'setDailyRoomRate':
			const roomTypeRate = proposalRequest.rateSets[set].roomTypeRates.find(rtr => rtr.roomType === roomType)
			const roomRate = roomTypeRate.roomRates.find(rr => rr.dayNumber === dayNumber) || {
				dayNumber: dayNumber,
				offered: 0,
				rate: 0
			}
			a = [
				...state.filter(s => s.venueId !== venueId),
				{
					...proposalRequest,
					rateSets: {
						...proposalRequest.rateSets,
						[set]: recalculateRateSet({
							...proposalRequest.rateSets[set],
							roomTypeRates: [
								...proposalRequest.rateSets[set].roomTypeRates.filter(rtr => rtr.roomType !== roomType),
								{
									...roomTypeRate,
									editingAverageRate: false,
									roomRates: [
										...roomTypeRate.roomRates.filter(rr => rr.dayNumber !== dayNumber),
										{
											...roomRate,
											...value
										}
									]
								}
							]
						})
					}
				}
			]
			break
		case 'initSet':
			a = [
				...state.filter(s => s.venueId !== venueId),
				{
					...proposalRequest,
					rateSets: {
						...proposalRequest.rateSets,
						[set]: { ...value }
					}
				}
			]
			break
		case 'init':
		default:
			a = [...value]
			break
	}

	if (autosave && !applyAttrition && ['init','initSet','setStaticProperty'].includes(action) === false) {
		if (venueId && set) updateRateSet(a.find(s => s.venueId === venueId), set)
	}

	return a
}

const useSelectedProposalStore = create<ProposalStoreState & ProposalStoreActions>((set, get) => ({
	selectedProposals: [],
	dispatch: (action: ProposalAction) => {
		set(state => ({
			selectedProposals: proposalReducer(state.selectedProposals, action)
		}))
	}
}))

export default useSelectedProposalStore
