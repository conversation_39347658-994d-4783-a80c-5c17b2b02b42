/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/no-confusing-void-expression */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable unicorn/no-array-reduce */
import HSTabs from 'components/tab'
import type { ISetFilters } from './helper'
import { mapEventPlan, reportViews } from './helper'
import {
	useNavigate,
	useParams,
	useSearchParams,
	useLocation
} from 'react-router-dom'
import { useGetChains } from 'lib/services/chains.service'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { useEffect, useMemo, useState } from 'react'
import { calculatePeriod, periodTypes, UnitedStatesDollar } from 'lib/helpers'
import { useGetPlannerOrganization } from 'lib/services/planner.service'
import { EventPlanStatusMap } from 'lib/helpers/statusMaps'
import Loader from 'components/loader'
import type { EventPlan } from 'models/proposalResponseMonitor'
import headerStore from 'components/header/headerStore'
import { useFeatureContext } from 'lib/providers/feature.provider'
import { chainQuery, brandQuery } from './tabs/common/filterHelper'

const leftComponent = () => (
	<div className='text-sm font-semibold leading-none text-gray-900'>
		Reports & Analytics
	</div>
)

const PlannerReport = () => {
	const { viewId } = useParams()
	const location = useLocation()
	const currentPath = location.pathname.split('/').pop() || 'pipeline'
	const { userProfile } = useUserProfileContext()
	const { getFeatureByKey } = useFeatureContext()
	const analyticsFeature = getFeatureByKey('ANALYTICS')
	const [selectedOrganization, setSelectedOrganization] = useState({
		id: userProfile?.organizationId ?? '',
		name: userProfile?.organizationName ?? ''
	})
	const [activeTab, setActiveTab] = useState(() => {
		const initialTab = reportViews(
			[],
			UnitedStatesDollar,
			() => {},
			[],
			{
				chainId: null,
				brandId: null,
				requestType: null,
				includeCancelled: false,
				dateFilter: calculatePeriod(periodTypes.quarterToDate.key),
				currency: UnitedStatesDollar
			},
			false,
			false,
			() => false
		).findIndex(tab => tab.path === currentPath)
		return initialTab === -1 ? 0 : initialTab
	})
	const [currentQueryParameters, setSearchParameters] = useSearchParams()

	const [loading, setLoading] = useState(false)
	const [eventPlans, setEventPlans] = useState<EventPlan[]>([])
	const [filters, setFilters] = useState<ISetFilters>({
		chainId: currentQueryParameters.get(chainQuery),
		brandId: currentQueryParameters.get(brandQuery),
		requestType: null,
		includeCancelled: false,
		dateFilter: calculatePeriod(periodTypes.quarterToDate.key),
		currency: UnitedStatesDollar
	})
	const [filteredChainIds, setFilteredChainIds] = useState<string[]>([])
	const navigate = useNavigate()

	const hotelsListFilterFunction = (hotel: { status: string }): boolean =>
		reportView.hotelStatuses?.includes(hotel.status) ?? false

	const reportTabs = useMemo(
		() =>
			reportViews(
				eventPlans,
				filters.currency,
				setFilters,
				filteredChainIds,
				filters,
				!analyticsFeature,
				loading,
				hotelsListFilterFunction
			),
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[analyticsFeature, eventPlans, filteredChainIds, filters, loading]
	)

	const [reportView, setReportView] = useState(reportTabs[0])
	const [dateFilterQuery, setDateFilterQuery] = useState('')
	const [dateFilterKeyQuery, setDateFilterKeyQuery] = useState('')
	const [statusFilterQuery, setStatusFilterQuery] = useState('')
	const [currencyCodeQuery, setCurrencyCodeQuery] = useState('')
	const { setHide, setLeftComponent, reset } = headerStore()

	const { data: plannerOrgEvents } = useGetPlannerOrganization(
		selectedOrganization.id,
		dateFilterQuery,
		dateFilterKeyQuery,
		statusFilterQuery,
		currencyCodeQuery,
		!!selectedOrganization.id
	)

	useEffect(() => {
		setDateFilterQuery(
			!!reportView.overrideDateFilter?.startDate &&
				!!reportView.overrideDateFilter.endDate
				? `startDate=${reportView.overrideDateFilter.startDate.split('T')[0]}&endDate=${reportView.overrideDateFilter.endDate.split('T')[0]}`
				: `startDate=${filters.dateFilter.startDate.split('T')[0]}&endDate=${filters.dateFilter.endDate.split('T')[0]}`
		)
		setDateFilterKeyQuery(`&dateFilterKey=${reportView.dateFilterKey}`)
		setStatusFilterQuery(
			`&statuses=${(filters.includeCancelled ? [...reportView.statuses, EventPlanStatusMap.Cancelled?.key] : [...reportView.statuses]).join(',')}`
		)
		setCurrencyCodeQuery(`&currencyCode=${filters.currency.code}`)
	}, [
		filters.currency.code,
		filters.dateFilter.endDate,
		filters.dateFilter.startDate,
		filters.includeCancelled,
		reportView.dateFilterKey,
		reportView.overrideDateFilter?.endDate,
		reportView.overrideDateFilter?.startDate,
		reportView.statuses
	])

	const mappedEventPlans = useMemo(
		() =>
			plannerOrgEvents
				? plannerOrgEvents.map(element => mapEventPlan(element))
				: [],
		[plannerOrgEvents]
	)

	useEffect(() => {
		if (selectedOrganization.id) {
			setLoading(true)

			if (plannerOrgEvents) {
				const filteredEventPlans = mappedEventPlans.filter(
					eventPlan =>
						(!filters.chainId ||
							eventPlan.chainIds.find(
								c =>
									(c === null && filters.chainId === 'INDEPENDENT') ||
									c === filters.chainId
							)) &&
						(!filters.brandId ||
							eventPlan.brandIds.find(
								b => b === `${filters.chainId}.${filters.brandId}`
							)) &&
						(!filters.requestType ||
							eventPlan.requestType === filters.requestType)
				)
				setEventPlans(filteredEventPlans)
				setFilteredChainIds(
					mappedEventPlans.reduce((a: string[], c) => {
						// add chainIds to filteredChainIds if they are not already there
						for (const chainId of c.chainIds) {
							if (!a.includes(chainId)) a.push(chainId)
						}
						return a
					}, [])
				)
			} else {
				setEventPlans([])
			}
			setLoading(false)
		}
	}, [
		filters.brandId,
		filters.chainId,
		filters.requestType,
		mappedEventPlans,
		plannerOrgEvents,
		selectedOrganization.id
	])

	useEffect(() => {
		setHide(false)
		setLeftComponent(leftComponent())

		return () => {
			reset()
		}
	}, [reset, setHide, setLeftComponent])

	useEffect(() => {
		const currentTab = reportTabs.find(v => v.path === viewId)
		if (currentTab) {
			setReportView(currentTab)
			setActiveTab(currentTab.key)
		}
	}, [reportTabs, viewId])

	return (
		<div>
			<div className='p-6'>
				<HSTabs
					tabs={reportTabs}
					activeTabId={activeTab}
					orientation='vertical'
					onActiveTabChange={tab => {
						setActiveTab(tab)
						navigate(`/planner/reports/${reportTabs[tab].path}`)
					}}
				/>
			</div>
		</div>
	)
}

export default PlannerReport
