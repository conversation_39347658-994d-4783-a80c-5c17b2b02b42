import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import Error from 'components/formComponents/error'
import { getDefaultEventPlan } from 'components/profile/tabs/overviewPlanner/helper'
import HSSelect from 'components/select'
import HSTextArea from 'components/textarea'
import <PERSON><PERSON><PERSON>t<PERSON>ield from 'components/textField'
import { <PERSON><PERSON>, Drawer } from 'flowbite-react'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import {
	useAddProposalRequest,
	useAddSiteSearch,
	useGetSiteSearch
} from 'lib/services/siteSearch.service'
import type { EventPlan } from 'models/proposalResponseMonitor'
import type { IUserProfile } from 'models/userProfiles'
import type { Venue } from 'models/venue'
import useSiteSearchStore from 'pages/search/tabs/store'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'

interface IAddHotelToSearchProperties {
	callback?: () => void
	onClose: () => void
	venueToAdd: Venue
}

const AddHotelToSearch = (properties: IAddHotelToSearchProperties) => {
	const { callback, onClose, venueToAdd } = properties
	const { userProfile } = useUserProfileContext()
	const { selectedEventPlan, setObject: setSelectedEventPlan } =
		useSiteSearchStore()

	const [selection, setSelection] = useState<'new' | 'existing'>('new')
	const [searchName, setSearchName] = useState<string>('')
	const [description, setDescription] = useState<string>('')
	const [siteSearches, setSiteSearches] = useState<EventPlan[]>()
	const [existingSearch, setExistingSearch] = useState<EventPlan>()

	const { refetch: reloadSiteSearch } = useGetSiteSearch()
	const { mutateAsync: addSiteSearch } = useAddSiteSearch()
	const { mutateAsync: updateProposalRequests } = useAddProposalRequest()

	const navigate = useNavigate()

	const handleClose = () => {
		setSearchName('')
		setDescription('')
		setSelection('new')
		setExistingSearch(undefined)
		setSiteSearches([])
		callback?.()
		onClose()
	}

	const handleConfirm = () => {
		if (selection === 'new') {
			addSiteSearch({
				...getDefaultEventPlan(
					userProfile as IUserProfile,
					'eventPlan.siteSearch'
				),
				name: searchName.trim(),
				description
			})
				.then(response => {
					toast.success('Site search created successfully')
					updateProposalRequests({
						eventPlanId: response.id ?? '',
						data: [
							{
								createdBy: userProfile?.id,
								createdByOrganizationId: userProfile?.organizationId,
								createdByOrganizationName: userProfile?.organizationName,
								destinations: venueToAdd.destinations,
								geolocation: venueToAdd.geolocation,
								propertySellers: venueToAdd.propertySellers,
								selected: false,
								status: 'Pending',
								venueId: venueToAdd.id ?? '',
								venueLocation: venueToAdd.address,
								venueName: venueToAdd.name
							}
						]
					})
						.then(prResponse => {
							setSelectedEventPlan(
								{ ...selectedEventPlan, ...prResponse },
								true
							)
							toast.success('Hotel added to site search successfully')
						})
						.catch((error: unknown) => {
							toast.error('Error adding hotels to site search')
							console.error('Error adding hotels to site search:', error)
						})
						.finally(() => {
							callback?.()
							navigate(`/site-search/search/${response.id}`)
						})
				})
				.catch((error: unknown) => {
					toast.error('Error creating site search')
					console.error('Error creating site search:', error)
				})
				.finally(() => {
					handleClose()
				})
		} else if (existingSearch) {
			updateProposalRequests({
				eventPlanId: existingSearch.id ?? '',
				data: [
					{
						createdBy: userProfile?.id,
						createdByOrganizationId: userProfile?.organizationId,
						createdByOrganizationName: userProfile?.organizationName,
						destinations: venueToAdd.destinations,
						geolocation: venueToAdd.geolocation,
						propertySellers: venueToAdd.propertySellers,
						selected: false,
						status: 'Pending',
						venueId: venueToAdd.id ?? '',
						venueLocation: venueToAdd.address,
						venueName: venueToAdd.name
					}
				]
			})
				.then(prResponse => {
					setSelectedEventPlan({ ...existingSearch, ...prResponse }, true)
					toast.success('Hotel added to site search successfully')
				})
				.catch((error: unknown) => {
					toast.error('Error adding hotels to site search')
					console.error('Error adding hotels to site search:', error)
				})
				.finally(() => {
					setExistingSearch(undefined)
					navigate(`/site-search/search/${existingSearch.id}`)
				})
		}
		handleClose()
		callback?.()
	}

	useEffect(() => {
		reloadSiteSearch()
			.then(response => {
				if (response.data) {
					setSiteSearches(response.data)
				}
			})
			.catch((error: unknown) => {
				console.error('Error fetching site search:', error)
			})
	}, [reloadSiteSearch])

	const isVenueAlreadyAdded = existingSearch?.proposalRequests?.some(
		pr => pr.venueId === venueToAdd.id
	)

	return (
		<HSDrawer
			open
			onClose={handleClose}
			position='right'
			style={{ width: '500px', height: '100vh' }}
			titleStyle='text-lg font-semibold text-gray-900'
		>
			<div className='flex h-full flex-col'>
				<Drawer.Header
					title='Select or Create a Search'
					titleIcon={() => null}
				/>
				<div className='flex flex-1 flex-col justify-between gap-4'>
					<Drawer.Items className='flex flex-1 flex-col gap-8'>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-medium text-gray-900'>
								Select where would you like to add this Hotel
							</div>
							<Button.Group className='w-full'>
								<HSButton
									color='gray'
									className={`${selection === 'new' ? '!bg-gray-100 font-semibold' : 'bg-white font-medium text-gray-600'} flex-1 rounded-r-none`}
									onClick={() => setSelection('new')}
								>
									New Search
								</HSButton>
								<HSButton
									color='gray'
									className={`${selection === 'existing' ? '!bg-gray-100 font-semibold' : 'bg-white font-medium text-gray-600'} flex-1 rounded-l-none`}
									onClick={() => setSelection('existing')}
								>
									Existing Search
								</HSButton>
							</Button.Group>
						</div>
						{selection === 'new' ? (
							<div>
								<HSTextField
									value={searchName}
									onChange={event => setSearchName(event.target.value)}
									label='Search Name'
									placeholder='Name'
									isInvalid={!searchName}
								/>
							</div>
						) : (
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-medium text-gray-900'>
									Select Existing Search
								</div>
								<HSSelect
									value={existingSearch?.id ?? ''}
									onChange={event =>
										setExistingSearch(
											siteSearches?.find(s => s.id === event.target.value)
										)
									}
									isInvalid={isVenueAlreadyAdded}
									color={isVenueAlreadyAdded ? 'failure' : 'gray'}
								>
									<option value=''>Select Search</option>
									{siteSearches?.map(s => (
										<option key={s.id} value={s.id ?? ''}>
											{`${s.name} (${s.proposalRequests?.length} hotel${s.proposalRequests?.length === 1 ? '' : 's'})`}
										</option>
									))}
								</HSSelect>
								{isVenueAlreadyAdded ? (
									<Error>Hotel already added to list</Error>
								) : null}
							</div>
						)}
						<div>
							<HSTextArea
								value={description}
								onChange={event => setDescription(event.target.value)}
								label='Description'
								placeholder='Write description here ...'
								className='bg-white text-gray-900'
								rows={4}
							/>
						</div>
					</Drawer.Items>
					<div className='flex items-center justify-between gap-4'>
						<HSButton color='light' onClick={handleClose} className='grow'>
							Cancel
						</HSButton>
						<HSButton
							className='grow'
							onClick={handleConfirm}
							disabled={
								selection === 'new'
									? !searchName
									: !existingSearch?.id || isVenueAlreadyAdded
							}
						>
							{selection === 'new' ? 'Create New Search' : 'Add to Search'}
						</HSButton>
					</div>
				</div>
			</div>
		</HSDrawer>
	)
}

export default AddHotelToSearch
