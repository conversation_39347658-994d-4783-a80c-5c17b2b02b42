/* eslint-disable react/no-array-index-key */
/* eslint-disable unicorn/no-negated-condition */
/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable unicorn/no-array-for-each */
/* eslint-disable unicorn/no-array-reduce */
import {
	faChevronDown,
	faClock,
	faColumns3,
	faDownload,
	faInfoCircle
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	PresetDirective,
	PresetsDirective
} from '@syncfusion/ej2-react-calendars'
import HSButton from 'components/button'
import HSDonutChart from 'components/charts/donut<PERSON>hart'
import HSCurrencyPicker from 'components/currencySelector'
import HSTooltip from 'components/tooltip'
import HSProgress from 'components/progressBar'
import HSLegend from 'components/charts/legends'
import {
	calculatePeriod,
	formatCurrency,
	formatNumber,
	periodTypes,
	type ICurrency,
	formatCurrencyToShortened
} from 'lib/helpers'
import type {
	EventPlan,
	IProposalByChain
} from 'models/proposalResponseMonitor'
import RfpResponseGrid from './rfpResponseGrid'
import { useEffect, useMemo, useState } from 'react'
import { calculateProposalValue } from 'lib/helpers/proposalValues'
import { useGetChains } from 'lib/services/chains.service'
import { chainColors } from '../../common'
import {
	currencyTemplate,
	dateTemplate,
	rfpNameTemplate,
	statusTemplate
} from '../openPipeline/templates'
import dateFilterStore from '../common/dateFilterStore'
import type { ISetFilters } from '../../helper'
import HSTable from 'components/table'
import CustomizeColumnsModal from 'components/planner/customizeColumnsModal'
import { columnOptions } from 'components/planner/customizeColumnsModal/columnConfig'
import { packageWorkbook } from 'lib/services/report.service'
import Loader from 'components/loader'
import FilterPopover from '../common/filterHelper'
import { toast } from 'react-toastify'
import rawDataFieldsForPlanner from '../columns/columnDefinition'
import MetricCard from 'components/metric/card'
import HSDateRangePicker from 'components/dateRangePicker'
import type { IHotel } from '../openPipeline'
import { Link } from 'react-router-dom'
import HSBadge from 'components/badge'

interface BidHistoryProperties {
	eventPlans: EventPlan[]
	currency: ICurrency
	setFilters: React.Dispatch<React.SetStateAction<ISetFilters>>
	filteredChainIds: string[]
	filters: ISetFilters
	isFeatureLocked: boolean
	hotelsListFilterFunction: (hotel: { status: string }) => boolean
}
interface IVenueLocationItem {
	venueLocation: string
	count: number
	value: number
}
interface IChainItem {
	chainId: string
	name: string
	count: number
	value: number
	fill: string
}
const BidHistory = (properties: BidHistoryProperties) => {
	const {
		eventPlans,
		currency,
		setFilters,
		filteredChainIds,
		filters,
		isFeatureLocked,
		hotelsListFilterFunction
	} = properties
	const { dateFilter, setDateFilter } = dateFilterStore()
	const [metrics, setMetrics] = useState({
		requestsResponded: 0,
		requestsAwaiting: 0,
		requestsProposed: 0,
		requestsTotal: 0,
		rfpValue: 0,
		roomsValue: 0,
		roomNights: 0,
		foodAndBeverageValue: 0,
		responseRate: 0,
		fastestResponseTimeTicks: null,
		totalResponseTimeTicks: 0,
		bidRate: 0,
		averageRoomRate: 0,
		averageFoodAndBeverage: 0,
		averageResponseTimeTicks: 0,
		averageConcessionValue: 0,
		proposalsByChain: [],
		eventPlansByVenueLocation: [],
		eventPlansByChain: []
	})
	const { data: chains } = useGetChains()
	const [chart, setChart] = useState<{
		total: number
		data: {
			color: string
			value: number
			label: string
			name: string
		}[]
	}>({ total: 0, data: [] })
	const [showColumnsModal, setShowColumnsModal] = useState(false)
	const [visibleColumns, setVisibleColumns] = useState<string[]>(columnOptions)
	const [isDownloading, setIsDownloading] = useState(false)

	const onClickExport = () => {
		setIsDownloading(true)
		toast.info(
			<div className='flex gap-2'>
				<FontAwesomeIcon
					icon={faClock}
					className='rounded-full bg-yellow-100 p-1 text-yellow-700'
					size='lg'
				/>
				Downloading...
			</div>
		)

		const exportConfiguration = {
			workbookId: 'clientexport',
			packageData: {
				workbookName: 'Bid-History',
				sheets: [
					{
						name: 'a',
						title: 'title',
						rows: eventPlans
							.sort((a, b) => ((a.name ?? '') > (b.name ?? '') ? 1 : -1))
							.map(item => ({
								values: Object.fromEntries(
									rawDataFieldsForPlanner['open-pipeline'](
										hotelsListFilterFunction
									)
										.filter(column => column.header !== '')
										.map(field => [
											field.header,
											{
												value:
													typeof field.exportFormat === 'function'
														? field.exportFormat(item)
														: item[field.name as keyof EventPlan],
												formatString: field.exportFormatString ?? ''
											}
										])
								)
							}))
					}
				]
			}
		}
		packageWorkbook(exportConfiguration)
			.catch((error: unknown) => console.log(error))
			.finally(() => {
				setIsDownloading(false)
			})
	}

	useEffect(() => {
		const updatedData = metrics.eventPlansByChain
			.sort(
				(
					c: {
						color: string
						value: number
						label: string
						name: string
					},
					n: {
						color: string
						value: number
						label: string
						name: string
					}
				) => (c.name > n.name ? 1 : -1)
			)
			.map((chain: { fill: string; value: number | null; name: string }) => ({
				color: chain.fill,
				value: chain.value ?? 0,
				label: `${chain.name} (${formatCurrency(chain.value)})`,
				name: chain.name
			}))

		setChart({
			total: updatedData.reduce(
				(accumulator, current) => accumulator + (current.value ?? 0),
				0
			),
			data: updatedData
		})
	}, [metrics.eventPlansByChain])

	useEffect(() => {
		const totals = eventPlans.reduce(
			(a, c) => ({
				...a,
				requestsResponded:
					(a.requestsResponded ?? 0) + (c.requestsResponded ?? 0),
				requestsAwaiting: (a.requestsAwaiting ?? 0) + (c.requestsAwaiting ?? 0),
				requestsProposed: (a.requestsProposed ?? 0) + (c.requestsProposed ?? 0),
				requestsTotal: (a.requestsTotal ?? 0) + (c.requestsTotal ?? 0),
				rfpValue: (a.rfpValue ?? 0) + (c.rfpValue ?? 0),
				roomsValue: (a.roomsValue ?? 0) + (c.roomsValue ?? 0),
				roomNights: (a.roomNights ?? 0) + (c.roomNights ?? 0),
				foodAndBeverageValue:
					(a.foodAndBeverageValue ?? 0) + (c.foodAndBeverageValue ?? 0),
				fastestResponseTimeTicks:
					!a.fastestResponseTimeTicks ||
					(!!c.fastestResponseTimeTicks &&
						c.fastestResponseTimeTicks < a.fastestResponseTimeTicks)
						? c.fastestResponseTimeTicks
						: a.fastestResponseTimeTicks,
				totalResponseTimeTicks:
					(a.totalResponseTimeTicks ?? 0) + (c.totalResponseTimeTicks ?? 0),
				proposalsByChain: (() => {
					const updatedProposals = [...a.proposalsByChain]
					Object.keys(c.proposalRequestStatuses).forEach(statusKey => {
						c.proposalRequestStatuses[statusKey]?.forEach(proposalRequest => {
							const chainId = proposalRequest.chainId ?? 'INDEPENDENT'

							const chainItem = updatedProposals.find(
								pbc => pbc.chainId === chainId
							)

							if (chainItem) {
								chainItem.count += 1
								chainItem.value += calculateProposalValue(
									proposalRequest.proposalValues
								)
								chainItem.totalRate +=
									proposalRequest.proposalValues?.averageRoomRate ?? 0
							} else {
								updatedProposals.push({
									chainId,
									name:
										chainId === 'INDEPENDENT'
											? 'Independent'
											: (chains?.find(ch => ch.id === chainId)?.name ??
												chainId),
									count: 1,
									value: calculateProposalValue(proposalRequest.proposalValues),
									totalRate:
										proposalRequest.proposalValues?.averageRoomRate ?? 0
								})
							}
						})
					})
					return updatedProposals
				})()
			}),
			{
				requestsResponded: 0,
				requestsAwaiting: 0,
				requestsProposed: 0,
				requestsTotal: 0,
				rfpValue: 0,
				roomsValue: 0,
				roomNights: 0,
				foodAndBeverageValue: 0,
				responseRate: 0,
				fastestResponseTimeTicks: null,
				totalResponseTimeTicks: 0,
				proposalsByChain: [] as IProposalByChain[]
			}
		)

		setMetrics({
			...totals,
			proposalsByChain: totals.proposalsByChain.map(pbc => ({
				...pbc,
				averageRoomRate: pbc.totalRate / pbc.count
			})),
			responseRate:
				(totals.requestsResponded ?? 0) / (totals.requestsTotal ?? 1),
			bidRate: (totals.requestsProposed ?? 0) / (totals.requestsTotal ?? 1),
			averageRoomRate: (totals.roomsValue ?? 0) / (totals.roomNights ?? 1),
			averageFoodAndBeverage:
				(totals.foodAndBeverageValue ?? 0) / (totals.requestsResponded ?? 1),
			averageResponseTimeTicks:
				(totals.totalResponseTimeTicks ?? 0) / (totals.requestsResponded ?? 1),
			eventPlansByVenueLocation: eventPlans.reduce<IVenueLocationItem[]>(
				(a, c) => {
					c.venueLocations.forEach(venueLocation => {
						const venueLocationItem = a.find(
							d => d.venueLocation === venueLocation
						)
						if (venueLocationItem) {
							venueLocationItem.count += 1
							venueLocationItem.value += c.rfpValue ?? 0
						} else {
							a.push({
								venueLocation,
								count: 1,
								value: c.rfpValue ?? 0
							})
						}
					})
					return a
				},
				[]
			),
			eventPlansByChain: eventPlans.reduce<IChainItem[]>((a, c) => {
				c.chainIds.forEach((cid: string) => {
					// eslint-disable-next-line unicorn/prefer-default-parameters
					const chainId = cid || 'INDEPENDENT'
					const chainItem = a.find(
						d => d.chainId === (chainId || 'INDEPENDENT')
					)
					if (chainItem?.chainId) {
						chainItem.count += 1
						chainItem.value += c.rfpValue ?? 0
					} else {
						a.push({
							chainId,
							name:
								chainId === 'INDEPENDENT'
									? 'Independent'
									: (chains?.find(ch => ch.id === chainId)?.name ?? chainId),
							count: 1,
							value: c.rfpValue ?? 0,
							fill: chainColors[a.length % chainColors.length]
						})
					}
				})
				return a
			}, [])
		})
	}, [chains, eventPlans])

	const filterHotels = (hotel: IHotel) =>
		typeof hotelsListFilterFunction === 'function'
			? hotelsListFilterFunction(hotel)
			: true
	const formatHotelsList = (
		item: EventPlan & {
			hotels?: IHotel[]
		}
	) => {
		const filteredHotels = (item.hotels || []).filter(hotel =>
			filterHotels(hotel)
		)
		const renderTooltip = () => (
			<div className='flex flex-col gap-1'>
				{filteredHotels.slice(1).map((hotel, index) => (
					<div key={index} className='text-sm font-medium text-gray-400'>
						<div className='flex flex-col'>{hotel.venueName}</div>
						<div className='text-sm font-medium text-gray-400'>
							{hotel.venueLocation}
						</div>
					</div>
				))}
			</div>
		)
		return (
			<div className='flex gap-1'>
				{filteredHotels.length > 0 && (
					<div>
						<Link to={`/planner/event/${item.id}`}>
							<div className='text-gray-600'>{filteredHotels[0].venueName}</div>
							<div className='text-sm text-gray-400'>
								{filteredHotels[0].venueLocation}
							</div>
						</Link>
					</div>
				)}
				{filteredHotels.length > 1 && (
					<div className='flex gap-2 text-sm'>
						<HSTooltip content={renderTooltip()}>
							<HSBadge color='gray'>{`+${filteredHotels.length - 1}`}</HSBadge>
						</HSTooltip>
					</div>
				)}
			</div>
		)
	}

	const chainTooltipContent = ({
		active,
		payload
	}: {
		active: unknown
		payload:
			| { dataKey: string; value: number | null; name: string }[]
			| undefined
	}) => {
		if (active && payload?.length) {
			return (
				<div
					className='p-1'
					style={{
						border: '1px solid #95D9D0',
						backgroundColor: '#fff'
					}}
				>
					<span>{payload[0].name}</span> received{' '}
					{formatCurrency(payload[0].value, currency)} in opportunity value
				</div>
			)
		}
		return null
	}

	const renderDateRangePicker = useMemo(
		() => (
			<HSDateRangePicker
				placeholder='Select Date Range'
				onChange={({ value }: { value: Date[] | null }) => {
					const dates = value
					if (dates) {
						setFilters(previous => ({
							...previous,
							dateFilter: {
								...previous.dateFilter, // Keep the previous filters type
								startDate: new Date(dates[0]).toISOString().split('.')[0],
								endDate: new Date(dates[1]).toISOString().split('.')[0]
							}
						}))

						setDateFilter({
							startDate: new Date(dates[0]).toISOString().split('.')[0],
							endDate: new Date(dates[1]).toISOString().split('.')[0]
						})
					}
				}}
				value={[new Date(dateFilter.startDate), new Date(dateFilter.endDate)]}
				format='MMM dd, yyyy'
			>
				<PresetsDirective>
					{Object.keys(periodTypes).map(period => {
						const {
							type: { label, key },
							startDate,
							endDate
						} = calculatePeriod(period)
						return (
							<PresetDirective
								key={key}
								label={label}
								start={new Date(startDate)}
								end={new Date(endDate)}
							/>
						)
					})}
				</PresetsDirective>
			</HSDateRangePicker>
		),
		[dateFilter.endDate, dateFilter.startDate, setDateFilter, setFilters]
	)

	return (
		<>
			<div
				className='flex flex-col gap-2'
				style={{ maxHeight: 'calc(100vh - 8rem)' }}
			>
				<div className='border-b px-6 py-4'>
					<div className='flex items-center justify-between'>
						<div className='flex flex-col'>
							<div className='text-xl font-semibold text-gray-900'>
								Bid History
							</div>

							<div className='text-sm font-normal text-gray-500'>
								Turn your historical proposal data into strategic insights that
								strengthen your negotiation position and increase win rates.
							</div>
						</div>
						<div className='flex items-center gap-4'>
							<div className='flex items-center gap-2'>
								<HSCurrencyPicker
									value={currency}
									onChange={value => {
										setFilters(previous => ({
											...previous,
											currency: value
										}))
									}}
									color='light'
									icon={faChevronDown}
								/>
							</div>
							<FilterPopover
								filters={filters}
								setFilters={setFilters}
								chains={chains}
								filteredChainIds={filteredChainIds}
							/>
							<div className='w-80'>{renderDateRangePicker}</div>
						</div>
					</div>
				</div>
				<div className='flex flex-col gap-6 px-4 py-6'>
					<div className='card flex h-full flex-col gap-6 p-4'>
						<div className='flex flex-col gap-3'>
							<div className='flex items-center gap-2'>
								<div className='text-base font-medium text-gray-700'>
									Sourcing Summary
								</div>
								<HSTooltip content='Aggregated metrics across all RFPs sent'>
									<FontAwesomeIcon
										icon={faInfoCircle}
										className='text-gray-700'
									/>
								</HSTooltip>
							</div>
							<div className='flex gap-4'>
								<div className='w-1/3'>
									<div className='flex flex-col gap-2.5'>
										<div className='flex items-center gap-2'>
											<div className='text-sm font-bold text-gray-700'>
												Sent
											</div>
											<HSTooltip content='The total number of active RFPs that are currently open and awaiting responses'>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-700'
												/>
											</HSTooltip>
										</div>
										<div className='text-2xl font-semibold leading-none text-gray-900'>
											<div className='flex items-center gap-1'>
												<span>{eventPlans.length}</span>
												<span className='text-sm text-gray-500'>RFPs</span>
											</div>
										</div>
									</div>
								</div>
								<div className='border-l' />
								<div className='w-1/3'>
									<div className='flex flex-col gap-2.5'>
										<div className='flex items-center gap-2'>
											<div className='text-sm font-bold text-gray-700'>
												Requested
											</div>
											<HSTooltip content='The total number of hotels requested across all RFPs sent.'>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-700'
												/>
											</HSTooltip>
										</div>
										<div className='text-2xl font-semibold leading-none text-gray-900'>
											<div className='flex items-center gap-1'>
												<span>{formatNumber(metrics.requestsTotal)}</span>
												<span className='text-sm text-gray-500'>hotels</span>
											</div>
										</div>
									</div>
								</div>
								<div className='border-l' />
								<div className='w-1/3'>
									<div className='flex flex-col gap-2.5'>
										<div className='flex items-center gap-2'>
											<div className='text-sm font-bold text-gray-700'>
												Received
											</div>
											<HSTooltip content='The total number of proposals received across all RFPs sent.'>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-700'
												/>
											</HSTooltip>
										</div>
										<div className='text-2xl font-semibold leading-none text-gray-900'>
											<div className='flex items-center gap-1'>
												<span>{formatNumber(metrics.requestsProposed)}</span>
												<span className='text-sm text-gray-500'>proposals</span>
											</div>
										</div>
									</div>
								</div>
								<div className='border-l' />
								<div className='w-1/3'>
									<div className='flex flex-col gap-2.5'>
										<div className='flex items-center gap-2'>
											<div className='text-sm font-bold text-gray-700'>
												Response Rate
											</div>
											<HSTooltip content='The percentage of hotels that responded to your RFPs, either by turning it down or submitting a proposal'>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-700'
												/>
											</HSTooltip>
										</div>
										<div className='text-2xl font-semibold leading-none text-gray-900'>
											{`${formatNumber(metrics.responseRate * 100)}%`}
										</div>
									</div>
								</div>
								<div className='border-l' />
								<div className='w-1/3'>
									<div className='flex flex-col gap-2.5'>
										<div className='flex items-center gap-2'>
											<div className='text-sm font-bold text-gray-700'>
												Bid Rate
											</div>
											<HSTooltip content='The percentage of hotels that submitted a proposal after receiving your RFP.'>
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-700'
												/>
											</HSTooltip>
										</div>
										<div className='text-2xl font-semibold leading-none text-gray-900'>
											{`${formatNumber(metrics.bidRate * 100)}%`}
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className='flex flex-row gap-4'>
						<div className='w-1/4'>
							<div className='card h-96'>
								<div className='flex flex-col gap-4 p-4'>
									<div className='flex items-center gap-2'>
										<div className='font-medium text-gray-700'>
											Opportunity Value
										</div>
										<HSTooltip content='The cumulative potential revenue from all RFPs sent, based on the proposals submitted from the requested hotels'>
											<FontAwesomeIcon
												icon={faInfoCircle}
												className='text-gray-700'
											/>
										</HSTooltip>
									</div>
									<MetricCard isLocked={isFeatureLocked} lockMessage=''>
										<div className='flex items-center justify-center'>
											<HSTooltip
												content={
													<div className='flex flex-col gap-2'>
														<HSLegend data={chart.data} />
													</div>
												}
												className='border border-gray-200 bg-white shadow-lg'
												placement='right'
											>
												<HSDonutChart
													data={chart.data}
													showTooltip={false}
													size={250}
													label={formatCurrencyToShortened(
														metrics.rfpValue,
														currency
													)}
													labelSize='text-6xl'
													tooltipContent={chainTooltipContent}
												/>
											</HSTooltip>
										</div>
									</MetricCard>
									{/* <div className='flex flex-col gap-3'>
										<HSLegend data={chart.data} />
									</div> */}
								</div>
							</div>
						</div>
						<div className='w-3/4'>
							<RfpResponseGrid
								isFeatureLocked={isFeatureLocked}
								metrics={metrics}
								currency={currency}
							/>
						</div>
					</div>
					<div className=''>
						<div className='flex items-center justify-between'>
							<div className='text-sm font-medium text-gray-900'>
								{eventPlans.length} RFPs
							</div>
							<div className='flex items-center gap-4'>
								<HSButton
									color='light'
									onClick={onClickExport}
									disabled={!eventPlans?.length}
								>
									{isDownloading ? (
										<Loader size='sm' />
									) : (
										<FontAwesomeIcon icon={faDownload} />
									)}
								</HSButton>
								<HSButton
									color='light'
									onClick={() => setShowColumnsModal(true)}
								>
									<FontAwesomeIcon icon={faColumns3} />
								</HSButton>
							</div>
						</div>
					</div>
					<div>
						<HSTable
							isLocked={isFeatureLocked}
							allowPaging
							rows={isFeatureLocked ? [] : (eventPlans ?? [])}
							defaultSort={{ field: 'name', direction: 'asc' }}
							columns={[
								{
									field: 'name',
									headerText: 'RFP Name/Code',
									render: rfpNameTemplate,
									width: 250,
									sortable: true,
									clipMode: 'ellipsis',
									freeze: 'left',
									visible: visibleColumns.includes('RFP Name/Code')
								},
								{
									field: 'status',
									headerText: 'Status',
									render: statusTemplate,
									width: 200,
									sortable: true,
									clipMode: 'ellipsis',
									visible: visibleColumns.includes('Status')
								},
								{
									field: 'ownerName',
									headerText: 'Owner',
									width: 300,
									sortable: true,
									visible: visibleColumns.includes('Assigned')
								},
								{
									field: 'rfpValue',
									headerText: 'RFP Value',
									render: (item: EventPlan) =>
										currencyTemplate(item.rfpValue, currency),
									sortable: true,
									clipMode: 'ellipsis',
									width: 150,
									visible: visibleColumns.includes('RFP Value')
								},
								{
									field: 'submitted',
									headerText: 'Sent',
									width: 150,
									render: (item: EventPlan) => dateTemplate(item.submitted),
									sortable: true,
									clipMode: 'ellipsis',
									visible: visibleColumns.includes('Sent')
								},
								{
									field: 'responsesDueDate',
									headerText: 'Response Due',
									render: (item: EventPlan) =>
										dateTemplate(item.responsesDueDate),
									width: 150,
									sortable: true,
									clipMode: 'ellipsis',
									visible: visibleColumns.includes('Responses due')
								},
								{
									field: 'responseRate',
									headerText: 'Response Rate',
									width: 250,
									sortable: true,
									clipMode: 'ellipsis',
									visible: visibleColumns.includes('Response rate'),
									render: (item: EventPlan) => (
										<div className='flex flex-col gap-1'>
											<div className='flex items-center justify-between'>
												<div className='text-sm font-medium text-gray-600'>
													{(item.requestsResponded && item.requestsTotal
														? (item.requestsResponded / item.requestsTotal) *
															100
														: 0
													).toFixed(0)}
													%
												</div>
												<div className='text-xs font-normal text-gray-500'>
													{`${formatNumber(item.requestsResponded)}/${formatNumber(item.requestsTotal)} hotels`}
												</div>
											</div>
											<HSProgress
												progress={
													item.requestsResponded && item.requestsTotal
														? (item.requestsResponded / item.requestsTotal) *
															100
														: 0
												}
												size='sm'
												color='primary'
											/>
										</div>
									)
								},
								{
									field: 'selectionDate',
									headerText: 'Decision Due',
									render: (item: EventPlan) => dateTemplate(item.selectionDate),
									width: 150,
									sortable: true,
									clipMode: 'ellipsis',
									visible: visibleColumns.includes('Decision due')
								},
								{
									field: 'averageRoomRate',
									headerText: 'AVG Room Rate',
									render: (item: EventPlan) =>
										currencyTemplate(item.averageRoomRate, currency),
									width: 150,
									sortable: true,
									visible: visibleColumns.includes('Average Room Rate')
								},
								{
									field: 'requestsTotal',
									headerText: 'Requested',
									width: 150,
									sortable: true,
									visible: visibleColumns.includes('Requested')
								},

								{
									field: 'requestsAwaiting',
									headerText: 'Awaiting',
									width: 150,
									sortable: true,
									visible: visibleColumns.includes('Awaiting')
								},
								{
									field: 'requestsResponded',
									headerText: 'Responses',
									width: 150,
									sortable: true,
									visible: visibleColumns.includes('Responses')
								},
								{
									field: 'hotelsList',
									headerText: 'Hotels Requested',
									width: 300,
									render: formatHotelsList,
									visible: visibleColumns.includes('Hotels Requested'),
									sortable: true,
									clipMode: 'ellipsis'
								}
							]}
						/>
					</div>
				</div>
			</div>
			<CustomizeColumnsModal
				show={showColumnsModal}
				onClose={() => setShowColumnsModal(false)}
				context='bidHistory'
				selectedColumns={visibleColumns}
				onColumnsChange={setVisibleColumns}
			/>
		</>
	)
}

export default BidHistory
