import { faInfoCircle } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import useHotelierDashboardData from './data'
import Loader from 'components/loader'
import { useEffect, useMemo } from 'react'
import hotelierDashboardStore from './dataStore'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import headerStore from 'components/header/headerStore'
import ActiveLeads from './activeLeads'
import OpportunityAwardedComparison from './opportunityAwarded'
import RecentActivity from './recentActivity'
import RfpDetail from './rfpDetail'
import RFPDue from './rfpDue'
import RFPBoard from './rfps'

const HotelierDashboard = () => {
	const { resetData } = useHotelierDashboardData()
	const { isLoading } = hotelierDashboardStore()
	const { setLeftComponent, reset, setHide } = headerStore()

	useEffect(() => {
		resetData()
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	const leftComponent = useMemo(
		() => (
			<div className='text-xs font-medium leading-none text-gray-900'>
				My RFP Dashboard
			</div>
		),
		[]
	)

	useEffect(() => {
		setHide(false)
		setLeftComponent(leftComponent)
		return () => {
			reset()
		}
	}, [leftComponent, reset, setHide, setLeftComponent])

	if (isLoading) {
		return <Loader />
	}

	return (
		<div
			className='!overflow-auto p-4'
			style={{ maxHeight: 'calc(100vh - 3.3rem)' }}
		>
			<div className='bg-gray-50'>
				<div className='flex flex-col gap-4'>
					<div className='flex gap-4'>
						<div className='basis-1/5'>
							<ActiveLeads />
						</div>
						<div className='basis-1/5'>
							<RfpDetail />
						</div>
						<div className='basis-1/5'>
							<RFPDue />
						</div>
						<div className='basis-2/5'>
							<OpportunityAwardedComparison />
						</div>
					</div>
					<div className='flex gap-4'>
						<div className='!w-4/5'>
							<div className='card'>
								<RFPBoard />
							</div>
						</div>
						<div className='!w-1/5'>
							<div className='card'>
								<div className='border-b p-4'>
									<div className='flex items-center gap-2'>
										<div className='font-medium text-gray-700'>
											Recent Activity
										</div>
										<TooltipComponent content='View status updates from your last 10 RFP activities, including actions taken by both planners and hotels'>
											<FontAwesomeIcon
												icon={faInfoCircle}
												className='text-gray-700'
											/>
										</TooltipComponent>
									</div>
									<div className='text-xs font-normal text-gray-500'>
										Last 10 Actions
									</div>
								</div>

								<div>
									<RecentActivity />
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}

export default HotelierDashboard
