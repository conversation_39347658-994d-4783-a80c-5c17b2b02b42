/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-confusing-void-expression */
/* eslint-disable @typescript-eslint/use-unknown-in-catch-callback-variable */
/* eslint-disable @typescript-eslint/prefer-optional-chain */
/* eslint-disable unicorn/no-keyword-prefix */
import { useState, useEffect, useCallback } from 'react'
import HSTable from 'components/table'
import HSButton from 'components/button'
import HSToggleSwitch from 'components/toggleSwitch'
import { faPlus, faTrashXmark } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	supplierContactRoles,
	supplierContactSources
} from 'lib/helpers/supplierContacts'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import { deleteSupplierContact } from 'lib/services/organizations.service'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import HSDropdownButton from 'components/dropdown'
import AddContactsFromToolboxDrawer from 'components/event/planner/drawers/addContactsFromToolboxDrawer'
import DeleteRfpContacts from './deleteRfpContacts'
import SupplierContactModal from 'pages/organization/contacts/supplierContactModal'
import analytics from 'lib/analytics/segment/load'

const contactNameTemplate = (row: ISupplierContact) => (
	<div>
		<div className='text-sm font-medium text-gray-900'>
			{`${row.firstName} ${row.lastName}`}
		</div>
		<div className='text-sm text-gray-500'>{row.id}</div>
	</div>
)

const organizationTemplate = (row: ISupplierContact) => (
	<div className='text-sm text-gray-900'>{row.companyName}</div>
)

const notesTemplate = (row: ISupplierContact) => (
	<div className='text-sm text-gray-900'>{row.plannerNotes}</div>
)

const proposalAccessTemplate = (
	row: ISupplierContact,
	handleToggleChange: (
		id: string,
		field: 'proposalAccess' | 'suggestHotels',
		checked: boolean
	) => void
) => (
	<div className='flex justify-center'>
		<HSToggleSwitch
			checked={row.allowMessaging ?? false}
			onChange={checked =>
				handleToggleChange(row.id ?? '', 'proposalAccess', checked)
			}
		/>
	</div>
)

const suggestHotelsTemplate = (
	row: ISupplierContact,
	handleToggleChange: (
		id: string,
		field: 'proposalAccess' | 'suggestHotels',
		checked: boolean
	) => void
) => (
	<div className='flex justify-center'>
		<HSToggleSwitch
			checked={row.requestSuggestions ?? false}
			onChange={checked =>
				handleToggleChange(row.id ?? '', 'suggestHotels', checked)
			}
		/>
	</div>
)

const actionsTemplate = (
	row: ISupplierContact,
	handleDelete: (contact: ISupplierContact) => void
) => (
	<div className='flex justify-center'>
		<HSButton color='light' size='sm' onClick={() => handleDelete(row)}>
			<FontAwesomeIcon icon={faTrashXmark} className='text-red-600' />
		</HSButton>
	</div>
)

interface RowSelectEventArguments {
	data: ISupplierContact | null
}

interface RowDeselectEventArguments {
	data: ISupplierContact | null
}

const RfpContacts = ({ onUpdate }: { onUpdate?: () => Promise<void> }) => {
	const { eventInfo: eventPlan, setProperty } = eventInfoStore()
	const [selectedContacts, setSelectedContacts] = useState<string[]>([])
	const [showAddContactModal, setShowAddContactModal] = useState(false)
	const [showToolboxModal, setShowToolboxModal] = useState(false)
	const [showDeleteModal, setShowDeleteModal] = useState(false)
	const [contactToDelete, setContactToDelete] =
		useState<ISupplierContact | null>(null)
	const [selectedSupplierContact, setSelectedSupplierContact] =
		useState<ISupplierContact | null>(null)

	const filterSupplierContacts = useCallback(
		(supplierContact: ISupplierContact) =>
			supplierContact.role !== supplierContactRoles.hot?.key &&
			!supplierContact.isDelegate &&
			supplierContact.source !== supplierContactSources.comparable.key,
		[]
	)

	const [supplierContacts, setSupplierContacts] = useState<ISupplierContact[]>(
		eventPlan?.supplierContacts?.filter(filterSupplierContacts) || []
	)

	useEffect(() => {
		const filteredContacts =
			eventPlan?.supplierContacts?.filter(contact =>
				filterSupplierContacts(contact)
			) || []
		setSupplierContacts(filteredContacts)
	}, [eventPlan?.supplierContacts, filterSupplierContacts])

	const handleToggleChange = useCallback(
		async (
			contactId: string,
			field: 'proposalAccess' | 'suggestHotels',
			value: boolean
		) => {
			const updatedContacts = supplierContacts.map(contact => {
				if (contact.id === contactId) {
					const updatedContact = {
						...contact,
						allowMessaging:
							field === 'proposalAccess' ? value : contact.allowMessaging,
						requestSuggestions:
							field === 'suggestHotels' ? value : contact.requestSuggestions
					}
					return updatedContact
				}
				return contact
			})
			setSupplierContacts(updatedContacts)

			setProperty('supplierContacts', [
				...(eventPlan?.supplierContacts?.filter(
					c => !filterSupplierContacts(c)
				) ?? []),
				...updatedContacts
			])

			analytics.track('RFP Contact Updated', {
				contactId,
				field,
				value,
				organizationId: eventPlan?.organizationId
			})

			if (onUpdate) await onUpdate()
		},
		[
			supplierContacts,
			onUpdate,
			eventPlan?.supplierContacts,
			setProperty,
			filterSupplierContacts,
			eventPlan?.organizationId
		]
	)

	const handleDelete = useCallback((contact: ISupplierContact) => {
		setSelectedContacts([])
		setContactToDelete(contact)
		setShowDeleteModal(true)
	}, [])

	const handleConfirmDelete = useCallback(async () => {
		if (!contactToDelete) return

		try {
			const updatedContacts = supplierContacts.filter(
				c => c.id !== contactToDelete.id
			)
			setSupplierContacts(updatedContacts)

			setProperty('supplierContacts', [
				...(eventPlan?.supplierContacts?.filter(
					c => !filterSupplierContacts(c)
				) ?? []),
				...updatedContacts
			])

			analytics.track('Industry Contact Removed from RFP', {
				contactId: contactToDelete.id,
				contactName: `${contactToDelete.firstName} ${contactToDelete.lastName}`,
				organizationId: eventPlan?.organizationId
			})

			if (onUpdate) await onUpdate()
			setShowDeleteModal(false)
		} catch (error: unknown) {
			console.error('Failed to remove contact:', error)
		}
	}, [
		contactToDelete,
		supplierContacts,
		setProperty,
		eventPlan?.supplierContacts,
		filterSupplierContacts,
		onUpdate,
		eventPlan?.organizationId
	])

	const handleBulkDelete = useCallback(() => {
		if (selectedContacts.length === 0) return
		setContactToDelete(null)
		setShowDeleteModal(true)
	}, [selectedContacts])

	const handleConfirmBulkDelete = useCallback(async () => {
		try {
			await Promise.all(
				selectedContacts.map(async contactId => {
					const contactToRemove = supplierContacts.find(c => c.id === contactId)
					if (contactToRemove) {
						await deleteSupplierContact(
							eventPlan?.organizationId ?? '',
							contactToRemove
						)
					}
				})
			)

			const updatedContacts = supplierContacts.filter(
				c => !selectedContacts.includes(c.id ?? '')
			)
			setSupplierContacts(updatedContacts)

			setProperty('supplierContacts', [
				...(eventPlan?.supplierContacts?.filter(
					c => !filterSupplierContacts(c)
				) ?? []),
				...updatedContacts
			])

			setSelectedContacts([])
			if (onUpdate) {
				await onUpdate()
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : 'Unknown error'
			console.error('Failed to delete contacts:', errorMessage)
		}
		setShowDeleteModal(false)
	}, [
		selectedContacts,
		supplierContacts,
		eventPlan?.organizationId,
		onUpdate,
		setProperty,
		eventPlan?.supplierContacts,
		filterSupplierContacts,
		setShowDeleteModal
	])

	const handleAddFromToolbox = useCallback(
		(contactsToAdd: ISupplierContact[]) => {
			const updatedContacts = [...supplierContacts, ...contactsToAdd]
			setSupplierContacts(updatedContacts)

			setProperty('supplierContacts', [
				...(eventPlan?.supplierContacts?.filter(
					c => !filterSupplierContacts(c)
				) ?? []),
				...updatedContacts
			])

			analytics.track('RFP Contact Added', {
				contactCount: contactsToAdd.length,
				contactIds: contactsToAdd.map(c => c.id),
				organizationId: eventPlan?.organizationId,
				source: 'toolbox'
			})

			if (onUpdate) {
				onUpdate().catch(error => {
					console.error('Update failed:', error)
				})
			}
		},
		[
			supplierContacts,
			onUpdate,
			setProperty,
			eventPlan?.supplierContacts,
			filterSupplierContacts,
			eventPlan?.organizationId
		]
	)

	const handleAddNewContact = useCallback(
		(newContact: ISupplierContact) => {
			const updatedContacts = [...supplierContacts, newContact]
			setSupplierContacts(updatedContacts)

			setProperty('supplierContacts', [
				...(eventPlan?.supplierContacts?.filter(
					c => !filterSupplierContacts(c)
				) ?? []),
				...updatedContacts
			])

			analytics.track('RFP Contact Added', {
				contactId: newContact.id,
				contactName: `${newContact.firstName} ${newContact.lastName}`,
				organizationId: eventPlan?.organizationId,
				source: 'manual'
			})

			setShowAddContactModal(false)
			if (onUpdate) {
				onUpdate().catch(error => {
					console.error('Update failed:', error)
				})
			}
		},
		[
			supplierContacts,
			onUpdate,
			setProperty,
			eventPlan?.supplierContacts,
			filterSupplierContacts,
			eventPlan?.organizationId,
			setShowAddContactModal
		]
	)

	return (
		<div className='flex flex-col'>
			<div className='flex items-center justify-between border-b p-4'>
				<div>
					<div className='text-xl font-semibold text-gray-900'>
						Your RFP Contacts
					</div>
					<div className='text-sm text-gray-500'>
						Configure National Sales representatives and Sales Affiliate members
						who can support your RFP
					</div>
				</div>
				<div className='flex items-center gap-4'>
					<HSDropdownButton
						showDropdownIcon={false}
						items={[
							{
								id: '1',
								item: 'Create New',
								clickFunction: () => {
									setSelectedSupplierContact(null)
									setShowAddContactModal(true)
								}
							},
							{
								id: '2',
								item: 'Add From Toolbox',
								clickFunction: () => {
									setSelectedSupplierContact(null)
									setShowToolboxModal(true)
								}
							}
						]}
						label={
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faPlus} className='text-white' />
								<span className='text-white'>Add RFP Contact</span>
							</div>
						}
						color='primary'
						className='bg-[#027587] hover:bg-[#027587]/90'
					/>
				</div>
			</div>
			<div className='p-4'>
				<div className='mb-4 flex items-center justify-between'>
					<div className='text-sm text-gray-900'>
						{supplierContacts.length} contact
						{supplierContacts.length === 1 ? '' : 's'}
						{selectedContacts.length > 0 &&
							` (${selectedContacts.length} selected)`}
					</div>
					<HSButton
						color='light'
						onClick={handleBulkDelete}
						size='sm'
						disabled={selectedContacts.length === 0}
					>
						<div className='flex items-center gap-2'>
							<FontAwesomeIcon icon={faTrashXmark} className='text-gray-500' />
							<span>Remove Selected</span>
						</div>
					</HSButton>
				</div>
				<HSTable
					columns={[
						{
							field: 'contactName',
							headerText: 'CONTACT NAME',
							width: 200,
							render: row => contactNameTemplate(row)
						},
						{
							field: 'organization',
							headerText: 'ORGANIZATION',
							width: 200,
							render: row => organizationTemplate(row)
						},
						{
							field: 'notes',
							headerText: 'NOTES',
							width: 250,
							render: row => notesTemplate(row)
						},
						{
							field: 'proposalAccess',
							headerText: 'PROPOSAL ACCESS',
							width: 150,
							render: row => proposalAccessTemplate(row, handleToggleChange)
						},
						{
							field: 'suggestHotels',
							headerText: 'SUGGEST HOTELS',
							width: 150,
							render: row => suggestHotelsTemplate(row, handleToggleChange)
						},
						{
							field: 'actions',
							headerText: 'REMOVE',
							width: 50,
							render: row => actionsTemplate(row, handleDelete)
						}
					]}
					rows={supplierContacts}
					allowPaging
					allowSelection
					selectedRows={selectedContacts}
					onSelectionChange={(selectedIds: string[]) => {
						setSelectedContacts(selectedIds)
					}}
					rowSelected={(event: RowSelectEventArguments) => {
						const selectedId = event.data && event.data.id ? event.data.id : ''
						if (selectedId) {
							setSelectedContacts(previous => [...previous, selectedId])
						}
					}}
					rowDeselected={(event: RowDeselectEventArguments) => {
						const deselectedId = event.data?.id ?? ''
						if (deselectedId) {
							setSelectedContacts(previous =>
								previous.filter(id => id !== deselectedId)
							)
						}
					}}
					idField='id'
				/>
			</div>
			<AddContactsFromToolboxDrawer
				key={showToolboxModal ? 'open' : 'closed'}
				show={showToolboxModal}
				onClose={() => setShowToolboxModal(false)}
				onAddSelected={handleAddFromToolbox}
				organizationId={eventPlan?.organizationId ?? undefined}
			/>
			<SupplierContactModal
				key={showAddContactModal ? 'open' : 'closed'}
				isVisible={showAddContactModal}
				onClose={() => {
					setShowAddContactModal(false)
					setSelectedSupplierContact(null)
				}}
				isRFP
				isEdit={false}
				isAddNew
				supplierContact={selectedSupplierContact}
				onAddUpdate={async () => {
					if (selectedSupplierContact) {
						handleAddNewContact(selectedSupplierContact)
					}
				}}
				setSupplierContact={setSelectedSupplierContact}
			/>
			<DeleteRfpContacts
				open={showDeleteModal}
				onClose={() => setShowDeleteModal(false)}
				onConfirm={
					contactToDelete ? handleConfirmDelete : handleConfirmBulkDelete
				}
				contact={contactToDelete ?? undefined}
				selectedCount={
					selectedContacts.length > 0 && !contactToDelete
						? selectedContacts.length
						: undefined
				}
			/>
		</div>
	)
}

export default RfpContacts
