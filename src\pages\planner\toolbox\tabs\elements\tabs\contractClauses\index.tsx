/* eslint-disable react/no-danger */
/* eslint-disable unicorn/no-keyword-prefix */
import {
	faEdit,
	faInfoCircle,
	faPlus,
	faSearch,
	faTrashXmark
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import HSButton from 'components/button'
import ContractClauseModal from 'components/contractClauseModal'
import HSIcon from 'components/HSIcon'
import Loader from 'components/loader'
import SourcingProfileModal from 'components/sourcingProfileModal'
import HSTable from 'components/table'
import HSTextField from 'components/textField'
import HSToggleSwitch from 'components/toggleSwitch'
// import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import {
	deleteContractClause,
	updateContractClause,
	upsertSourcingProfile,
	useLoadContractClauses
} from 'lib/services/organizations.service'
import type { IContractClauses, ISourcingProfile } from 'models/organizations'
import filterStore from 'pages/organization/contractClauses/filterStore'
import { useCallback, useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'

const PlannerContractClauses = () => {
	const { organizationId } = useParams()
	const { refetch, isFetching } = useLoadContractClauses(
		organizationId ?? '',
		false
	)
	// const { userProfile } = useUserProfileContext()
	const [data, setData] = useState<IContractClauses[]>([])
	const [filteredItems, setFilteredItems] = useState<IContractClauses[]>([])

	const [contractClauseContext, setContractClauseContext] = useState<{
		showAddEditModal: boolean
		editExisting: boolean
		addNew: boolean
		contractClause: IContractClauses | object | undefined
	}>({
		showAddEditModal: false,
		editExisting: false,
		addNew: false,
		contractClause: {}
	})

	const [sourcingProfileContext, setSourcingProfileContext] = useState<{
		showAddEditModal: boolean
		editExisting: boolean
		addNew: boolean
		sourcingProfile: ISourcingProfile | object
	}>({
		showAddEditModal: false,
		editExisting: false,
		addNew: false,
		sourcingProfile: {}
	})
	const [contractClauses, setContractClauses] = useState<IContractClauses[]>([])

	const { body, globalSearch, setGlobalSearch, title } = filterStore()

	useEffect(() => {
		refetch()
			.then(response => {
				setData(response.data ?? [])
			})
			.catch((error: unknown) => {
				console.error(error)
			})
	}, [refetch])

	const formatText = (item: IContractClauses) => (
		<div className='text-wrap px-4 text-sm font-normal text-gray-600'>
			<div dangerouslySetInnerHTML={{ __html: item.text ?? '' }} />
		</div>
	)

	const formatTitle = (item: IContractClauses) => (
		<div className='px-4 text-sm font-normal text-gray-600'>{item.title}</div>
	)
	const alwaysIncludeTemplate = () => (
		<div className='flex items-center gap-1'>
			<div className='text-xs font-semibold text-gray-500'>Always Include</div>
			<TooltipComponent content='Date Hotel Started Reviewing RFP'>
				<FontAwesomeIcon icon={faInfoCircle} size='lg' />
			</TooltipComponent>
		</div>
	)
	const formatInclude = (item: IContractClauses) => (
		<HSToggleSwitch
			checked={item.includeByDefault ?? false}
			onChange={async (checked: boolean) =>
				updateContractClause(organizationId ?? '', item, {
					includeByDefault: checked
				})
					.then(() => {
						refetch().catch((error: unknown) => console.log(error))
					})
					.catch((error: unknown) => console.log(error))
			}
		/>
	)

	const onActionEdit = (item: IContractClauses) => {
		setContractClauseContext(value => ({
			...value,
			editExisting: true,
			contractClause: item,
			showAddEditModal: true
		}))
	}

	const onActionDelete = (item: IContractClauses) => {
		deleteContractClause(organizationId ?? '', item.id ?? '')
			.then(response => {
				setContractClauseContext(value => ({
					...value,
					contractClause: response
				}))
				refetch().catch((error: unknown) => console.log(error))
			})
			.catch((error: unknown) => console.log(error))
	}

	const formatActions = (item: IContractClauses) => (
		<div className='flex items-center justify-center gap-2'>
			<HSButton color='light' size='sm' onClick={() => onActionEdit(item)}>
				<FontAwesomeIcon icon={faEdit} />
			</HSButton>
			<HSButton color='light' size='sm' onClick={() => onActionDelete(item)}>
				<FontAwesomeIcon icon={faTrashXmark} className='text-red-600' />
			</HSButton>
		</div>
	)

	const onCancelAddEditSourcingProfile = () => {
		setSourcingProfileContext({
			showAddEditModal: false,
			editExisting: false,
			addNew: false,
			sourcingProfile: {}
		})
	}

	const onAddUpdateSourcingProfile = (item: ISourcingProfile) => {
		upsertSourcingProfile(organizationId ?? '', item.id ?? '', item)
			.then(() => {
				setSourcingProfileContext({
					showAddEditModal: false,
					editExisting: false,
					addNew: false,
					sourcingProfile: {}
				})

				refetch().catch((error: unknown) => console.log(error))
			})
			.catch((error: unknown) => console.log(error))
	}

	const onClickAddNewContractClause = useCallback(() => {
		setContractClauseContext(previousState => ({
			...previousState,
			showAddEditModal: true,
			addNew: true,
			editExisting: false,
			contractClause: {}
		}))
	}, [])

	const onCloseAddNewContractModal = () => {
		setContractClauseContext(v => ({
			...v,
			showAddEditModal: false,
			addNew: false,
			editExisting: false,
			contractClause: undefined
		}))
		refetch().catch((error: unknown) => console.log(error))
	}

	const onAddUpdate = (newContractClause: IContractClauses) => {
		setContractClauses([
			...contractClauses.filter(cc => cc.id !== newContractClause.id),
			{ ...newContractClause }
		])
	}

	const filterItems = useCallback(() => {
		let updatedData = data

		if (globalSearch !== '') {
			updatedData = updatedData.filter(item => {
				const search = globalSearch.toLowerCase()
				return (
					item.title?.toLowerCase().includes(search) ||
					item.text?.toLowerCase().includes(search)
				)
			})
		}
		setFilteredItems(updatedData)
	}, [data, globalSearch])

	useEffect(() => {
		filterItems()
	}, [globalSearch, body, title, filterItems, data])

	return (
		<div>
			<div className='flex flex-col gap-2'>
				{isFetching ? (
					<Loader />
				) : (
					<>
						<div className='flex items-center justify-between pb-2'>
							<div className='text-sm font-medium text-gray-900'>
								{filteredItems.length} Contract Clauses
							</div>
							<div className='flex gap-2'>
								<div className='w-80'>
									<HSTextField
										icon={HSIcon(faSearch)}
										placeholder='Search '
										showClearButton
										value={globalSearch}
										onChange={event => setGlobalSearch(event.target.value)}
									/>
								</div>
								<HSButton
									size='sm'
									onClick={() => onClickAddNewContractClause()}
								>
									<div className='flex items-center gap-2'>
										<FontAwesomeIcon icon={faPlus} />
										<span>Add New</span>
									</div>
								</HSButton>
							</div>
						</div>
						<div className=''>
							<div className='flex flex-col gap-2'>
								<HSTable
									allowPaging
									rows={filteredItems}
									defaultSort={{
										field: 'title',
										direction: 'asc'
									}}
									columns={[
										{
											headerText: 'Contract Clause Name',
											field: 'title',
											render: formatTitle,
											sortable: true,
											clipMode: 'ellipsis',
											width: 250
										},
										{
											headerText: 'Contract Clause Details',
											field: 'text',
											render: formatText,
											clipMode: 'ellipsis'
										},
										{
											headerText: 'Always Include',
											field: 'includeByDefault',
											render: formatInclude,
											width: 100,
											headerAlign: 'center',
											headerTemplate: alwaysIncludeTemplate
										},
										// {
										// 	headerText: 'Sourcing Profiles',
										// 	field: 'sourcingProfiles',
										// 	render: formatInclude,
										// 	sortable: true,
										// 	headerAlign: 'left',
										// 	visible:
										// 		userProfile?.previewExperiences?.SOURCINGPROFILES ??
										// 		false
										// },
										{
											headerText: 'Actions',
											field: 'actions',
											render: formatActions,
											width: 150,
											headerAlign: 'center'
										}
									]}
								/>
							</div>
							<SourcingProfileModal
								show={sourcingProfileContext.showAddEditModal}
								editExisting={sourcingProfileContext.editExisting}
								addNew={sourcingProfileContext.addNew}
								value={
									sourcingProfileContext.sourcingProfile as ISourcingProfile
								}
								onChange={sourcingProfile =>
									setSourcingProfileContext({
										...sourcingProfileContext,
										sourcingProfile
									})
								}
								onCancel={onCancelAddEditSourcingProfile}
								onAddUpdate={value => {
									onAddUpdateSourcingProfile(value as ISourcingProfile)
								}}
							/>
						</div>
					</>
				)}
			</div>
			{contractClauseContext.showAddEditModal ? (
				<ContractClauseModal
					onClose={() => onCloseAddNewContractModal()}
					isProfile
					isEdit={contractClauseContext.editExisting}
					editContractClause={
						contractClauseContext.contractClause as IContractClauses
					}
					onAddUpdate={onAddUpdate}
					userProfileId={organizationId ?? ''}
					contractClauses={contractClauses}
				/>
			) : null}
		</div>
	)
}

export default PlannerContractClauses
