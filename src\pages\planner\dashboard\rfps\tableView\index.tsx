/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable no-param-reassign */
/* eslint-disable unicorn/no-array-reduce */

import columns from './columnDefinition'
import type { EventsWithStats } from '../common'
import plannerDashboardStore from '../../dataStore'
import HSTable from 'components/table'
import type { GridColumn } from 'components/table/types'

const RFPTableView = ({ events }: { events: EventsWithStats[] }) => {
	const { gridColumns } = plannerDashboardStore()

	const tableColumns: GridColumn<EventsWithStats>[] = columns
		.filter(
			c => !gridColumns.some(item => item.name === c.name && item.isHidden)
		)
		.map(column => ({
			field: column.name,
			headerText: column.header,
			width: column.width,
			visible: !gridColumns.find(c => c.name === column.name)?.isHidden,
			freeze: column.name === 'actions' ? 'right' : undefined,
			render: column.format,
			sortable: true,
			clipMode: 'ellipsis'
		}))

	return (
		<div className=''>
			<HSTable
				columns={tableColumns}
				rows={events}
				defaultSort={{
					direction: 'asc',
					field: 'name'
				}}
			/>
		</div>
	)
}

export default RFPTableView
