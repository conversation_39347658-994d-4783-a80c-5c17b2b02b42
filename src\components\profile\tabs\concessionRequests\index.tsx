/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import type {
	GridComponent,
	RowDeselectEventArgs,
	RowSelectEventArgs
} from '@syncfusion/ej2-react-grids'
import {
	ColumnDirective,
	ColumnsDirective,
	Inject,
	Page,
	Selection,
	Sort
} from '@syncfusion/ej2-react-grids'
import { useSourcingProfiles } from 'lib/customHook'
import { requestGroups } from 'lib/helpers/requestGroups'
import {
	useDeleteConcessionRequest,
	useGetConcessionRequests,
	useUpdateConcessionRequest
} from 'lib/services/planner.service'
import type { IMappedPlanner, IPlanner } from 'models/planners'
import { useCallback, useEffect, useMemo, useState } from 'react'
import {
	getDefaultConcessionRequest,
	mapSourcingProfileToAssociatedObject
} from './helper'
import { actionTemplate, includeTemplate } from './templates'
import type { ISourcingProfile } from 'models/organizations'
import Loader from 'components/loader'
import SourcingProfileModal from 'components/sourcingProfileModal'
import { useUpsertSourcingProfile } from 'lib/services/organizations.service'
import ConcessionRequestModal from 'components/concessionRequestModal'
import DataGrid from 'components/dataGrid'
import HSButton from 'components/button'
import { faFilter, faPlus } from '@fortawesome/pro-light-svg-icons'
import HSPopover from 'components/popover'
import filterStore from './filterStore'
import HSTextField from 'components/textField'
import type { IUserProfile } from 'models/userProfiles'
import ShareModal from 'components/shareModal'

const ConcessionRequests = ({ userProfile }: { userProfile: IUserProfile }) => {
	let gridInstance: GridComponent | null
	const [gridData, setGridData] = useState<IMappedPlanner[]>([])
	const [showShareModal, setShowShareModal] = useState(false)
	const [showAddEditModal, setShowAddEditModal] = useState(false)
	const [sourcingProfileContext, setSourcingProfileContext] = useState<{
		showAddEditModal: boolean
		editExisting: boolean
		addNew: boolean
		sourcingProfile: ISourcingProfile | object
	}>({
		showAddEditModal: false,
		editExisting: false,
		addNew: false,
		sourcingProfile: {}
	})
	const [sourcingProfiles, setSourcingProfiles] = useSourcingProfiles(
		userProfile?.organizationId ?? ''
	)
	console.log(sourcingProfiles)
	const [selectedConcessionRequest, setSelectedConcessionRequest] = useState<
		Partial<IMappedPlanner>
	>(getDefaultConcessionRequest())
	const [selectedConcessionRequestIds, setSelectedConcessionRequestIds] =
		useState<string[]>([])
	const [editExisting, setEditExisting] = useState(false)
	const {
		refetch: getConcessionRequests,
		data: concessionRequests,
		isFetching
	} = useGetConcessionRequests(userProfile.id ?? '')

	const {
		clearAll,
		concessionRequestSearch,
		groupSearch,
		setConcessionRequestSearch,
		setGroupSearch
	} = filterStore()

	const refetchConcessionRequests = useCallback(() => {
		getConcessionRequests()
			.then((response: { data: IPlanner[] | undefined }) => {
				if (response.data)
					setGridData(
						response.data.map((c: IPlanner) => ({
							...c,
							groupName: requestGroups[c.requestGroupId ?? '']?.name ?? '',
							groupSortIndex:
								requestGroups[c.requestGroupId ?? '']?.sortIndex ?? 0
						}))
					)
			})
			.catch((error: unknown) => console.error(error))
	}, [getConcessionRequests])

	const { mutateAsync: updateConcessionRequest } = useUpdateConcessionRequest()
	const { mutateAsync: upsertSourcingProfile } = useUpsertSourcingProfile()
	const { mutateAsync: deleteConcessionRequest } = useDeleteConcessionRequest()

	const onAddNew = () => {
		setEditExisting(false)
		setSelectedConcessionRequest(getDefaultConcessionRequest())
		setShowAddEditModal(true)
	}

	const toggleInclude = (concessionRequest: IMappedPlanner) => {
		updateConcessionRequest({
			userProfileId: userProfile?.id ?? '',
			data: {
				...concessionRequest,
				includeByDefault: !concessionRequest.includeByDefault
			}
		})
			.then(() => {
				refetchConcessionRequests()
			})
			.catch((error: unknown) => console.error(error))
	}

	const onCancelAddEditSourcingProfile = () => {
		setShowAddEditModal(false)
		setSelectedConcessionRequest({})
		setSourcingProfileContext({
			showAddEditModal: false,
			editExisting: false,
			addNew: false,
			sourcingProfile: {}
		})
	}

	const onAddUpdateSourcingProfile = (item: ISourcingProfile) => {
		upsertSourcingProfile({
			organizationId: userProfile.organizationId ?? '',
			sourcingProfileId: item.id ?? '',
			sourcingProfile: item
		})
			.then((response: ISourcingProfile) => {
				updateConcessionRequest({
					userProfileId: userProfile.id ?? '',
					data: {
						...selectedConcessionRequest,
						sourcingProfiles: [
							...(selectedConcessionRequest.sourcingProfiles ?? []),
							mapSourcingProfileToAssociatedObject(response)
						]
					} as IMappedPlanner
				})
					.then(() => refetchConcessionRequests())
					.catch((error: unknown) => console.error(error))

				setSourcingProfiles(
					(s: ISourcingProfile[] | undefined) =>
						[
							...(s ?? []).filter(event => event.id !== item.id),
							item
						] as ISourcingProfile[]
				)
				setSelectedConcessionRequest({})
				setSourcingProfileContext({
					showAddEditModal: false,
					editExisting: false,
					addNew: false,
					sourcingProfile: {}
				})
			})
			.catch((error: unknown) => console.error(error))
	}

	const onAddUpdate = (stayOpen: boolean) => {
		setShowAddEditModal(false)
		refetchConcessionRequests()
		setEditExisting(false)
		setSelectedConcessionRequest(getDefaultConcessionRequest())
		setShowAddEditModal(stayOpen)
	}

	const onActionEdit = (item: IMappedPlanner) => {
		setEditExisting(true)
		setSelectedConcessionRequest(item)
		setShowAddEditModal(true)
	}

	const onActionDelete = (item: IMappedPlanner) => {
		deleteConcessionRequest({
			userProfileId: userProfile.id ?? '',
			itemId: item.id ?? ''
		})
			.then(() => console.log(`${item.id} deleted successfully`))
			.catch((error: unknown) => console.error(error))
	}

	useEffect(() => {
		if (concessionRequests) {
			setGridData(
				concessionRequests.map((c: IPlanner) => ({
					...c,
					groupName: requestGroups[c.requestGroupId ?? '']?.name ?? '',
					groupSortIndex: requestGroups[c.requestGroupId ?? '']?.sortIndex ?? 0
				}))
			)
		}
	}, [concessionRequests])

	const valueTemplate = (item: string) => (
		<div className='text-sm font-normal text-gray-600'>{item}</div>
	)

	const popOverContent = (
		<div className='flex max-h-96 w-80 flex-col gap-4 overflow-y-auto p-4'>
			<div className='flex flex-col gap-2 p-2'>
				<div className='flex items-center justify-between'>
					<div className='text-sm font-medium text-gray-900'>Filters</div>
					<div className='flex gap-2'>
						<HSButton size='xs'>Save View</HSButton>
						<HSButton size='xs' onClick={() => clearAll()}>
							Clear All
						</HSButton>
					</div>
				</div>
			</div>

			<HSTextField
				label='Group'
				placeholder='Start Typing Group'
				value={groupSearch}
				onChange={event => setGroupSearch(event.target.value)}
			/>

			<HSTextField
				label='Concession Request'
				placeholder='Start Typing Concession Request'
				value={concessionRequestSearch}
				onChange={event => setConcessionRequestSearch(event.target.value)}
			/>
		</div>
	)

	useEffect(() => {
		gridInstance?.clearFiltering()
		if (concessionRequestSearch) {
			gridInstance?.filterByColumn('text', 'contains', concessionRequestSearch)
		}
		if (groupSearch) {
			gridInstance?.filterByColumn('groupName', 'contains', groupSearch)
		}
		gridInstance?.refresh()
	}, [gridInstance, concessionRequestSearch, groupSearch])

	const isRowSelected = useMemo(
		() => gridInstance?.getSelectedRecords(),
		[gridInstance]
	)

	return (
		<>
			<div className='flex items-center justify-between px-6 py-4'>
				<div className='text-xl font-semibold text-gray-900'>
					My Concession Requests
				</div>
			</div>
			<div className='border-b' />
			<div className='flex flex-col gap-2 px-6 py-4'>
				<div className='flex items-center justify-between'>
					<div className='text-sm font-medium text-gray-900'>
						{gridData.length} Requests
					</div>
					<div className='flex items-center gap-4'>
						<div>
							<HSPopover
								content={popOverContent}
								aria-labelledby='filter'
								placement='bottom'
								arrow={false}
							>
								<div className='relative inline-block'>
									<HSButton color='light' size='sm'>
										<div className='flex items-center gap-2'>
											<FontAwesomeIcon icon={faFilter} />
											Filter
										</div>
									</HSButton>
								</div>
							</HSPopover>
						</div>
						<div className='h-6 border-l border-gray-400' />
						<HSButton
							color='light'
							// eslint-disable-next-line react/jsx-handler-names
							onClick={() => setShowShareModal(true)}
							disabled={isRowSelected?.length === 0}
							size='sm'
						>
							Share
						</HSButton>
						<HSButton onClick={() => onAddNew()} size='sm'>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faPlus} />
								<div>Create New</div>
							</div>
						</HSButton>
					</div>
				</div>
				{isFetching ? (
					<Loader />
				) : (
					<DataGrid
						ref={r => {
							gridInstance = r
						}}
						filterSettings={{ type: 'Menu' }}
						dataSource={gridData}
						loadingIndicator={{ indicatorType: 'Shimmer' }}
						allowSorting
						allowMultiSorting
						allowTextWrap
						autoFit
						allowPaging
						pageSettings={{ pageSize: 10, pageSizes: [5, 10, 15] }}
						allowSelection
						selectionSettings={{
							persistSelection: true,
							type: 'Multiple',
							checkboxOnly: true
						}}
						rowSelected={(event: RowSelectEventArgs) =>
							setSelectedConcessionRequestIds(selectedIds => [
								...selectedIds,
								(event.data as IMappedPlanner).id ?? ''
							])
						}
						rowDeselected={(event: RowDeselectEventArgs) =>
							setSelectedConcessionRequestIds(selectedIds =>
								selectedIds.filter(
									id => id !== (event.data as IMappedPlanner).id
								)
							)
						}
					>
						<ColumnsDirective>
							<ColumnDirective type='checkbox' width={60} />
							<ColumnDirective
								field='groupName'
								headerText='Group'
								template={(item: IMappedPlanner) =>
									valueTemplate(item.groupName ?? 'No Grouping Specified')
								}
								autoFit
							/>
							<ColumnDirective
								field='text'
								headerText='Concession Request'
								template={(item: IMappedPlanner) =>
									valueTemplate(item.text ?? '')
								}
								autoFit
							/>
							<ColumnDirective
								field='includeByDefault'
								headerText='Include in all new RFPs'
								allowFiltering={false}
								template={(item: IMappedPlanner) =>
									includeTemplate(item, toggleInclude)
								}
								width={200}
							/>

							<ColumnDirective
								field='actions'
								headerText='Actions'
								allowFiltering={false}
								template={(item: IMappedPlanner) =>
									actionTemplate(item, onActionEdit, onActionDelete)
								}
								width={150}
								textAlign='Center'
							/>
						</ColumnsDirective>
						<Inject services={[Sort, Page, Selection]} />
					</DataGrid>
				)}
			</div>
			{showShareModal ? (
				<ShareModal
					show={showShareModal}
					userProfileId={userProfile.id ?? ''}
					itemsToShare={gridData.filter(item =>
						selectedConcessionRequestIds.includes(item.id ?? '')
					)}
					// eslint-disable-next-line react/jsx-handler-names
					onClose={() => setShowShareModal(false)}
					shareType='concessionRequest'
				/>
			) : null}
			{sourcingProfileContext.showAddEditModal ? (
				<SourcingProfileModal
					show={sourcingProfileContext.showAddEditModal}
					editExisting={sourcingProfileContext.editExisting}
					addNew={sourcingProfileContext.addNew}
					value={sourcingProfileContext.sourcingProfile as ISourcingProfile}
					// eslint-disable-next-line react/jsx-handler-names
					onChange={sourcingProfile =>
						setSourcingProfileContext({
							...sourcingProfileContext,
							sourcingProfile
						})
					}
					onCancel={onCancelAddEditSourcingProfile}
					onAddUpdate={item =>
						onAddUpdateSourcingProfile(item as ISourcingProfile)
					}
				/>
			) : null}
			{showAddEditModal ? (
				<ConcessionRequestModal
					show={showAddEditModal}
					isEdit={editExisting}
					isProfile
					userProfileId={userProfile.id ?? ''}
					editConcessionRequest={{ ...selectedConcessionRequest }}
					// eslint-disable-next-line react/jsx-handler-names
					onClose={() => {
						setShowAddEditModal(false)
						gridInstance?.clearSelection()
					}}
					onAddUpdate={onAddUpdate}
				/>
			) : null}
		</>
	)
}

export default ConcessionRequests
