/* eslint-disable react/boolean-prop-naming */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { useCallback, useEffect, useState, useRef } from 'react'
import type { ILocation } from 'models/location'
import { Label, ListGroup } from 'flowbite-react'
import HSTextField from 'components/textField'
import { useMapsLibrary } from '@vis.gl/react-google-maps'
import { faSearch } from '@fortawesome/pro-light-svg-icons'
import './index.css'
import { parseAddressComponents } from './helper'
import HSIcon from 'components/HSIcon'

interface ILocationSelectorProperties {
	value: ILocation | null
	onChange: (location: ILocation) => void
	customPlaceholder?: string
	searchOptions?: google.maps.places.AutocompleteOptions
	clearAfterChange?: boolean
	clickToChange?: boolean
	label?: string
	disabled?: boolean
	isInvalid?: boolean
}

const LocationSelector = (properties: ILocationSelectorProperties) => {
	const {
		clearAfterChange,
		clickToChange,
		customPlaceholder,
		onChange,
		value,
		label,
		disabled,
		isInvalid
	} = properties
	const [placeAddress, setPlaceAddress] = useState(value?.name)
	const [location, setLocation] = useState(value)
	const [selectedItemIndex, setSelectedItemIndex] = useState(-1)
	const containerReference = useRef<HTMLDivElement>(null)

	const places = useMapsLibrary('places')
	const geoCoder = useMapsLibrary('geocoding')
	const [sessionToken, setSessionToken] =
		useState<google.maps.places.AutocompleteSessionToken>()

	const [autocompleteService, setAutocompleteService] =
		useState<google.maps.places.AutocompleteService | null>(null)

	const [placesService, setPlacesService] =
		useState<google.maps.places.PlacesService | null>(null)

	const [predictionResults, setPredictionResults] = useState<
		google.maps.places.AutocompletePrediction[]
	>([])

	const [geoCoderService, setGeoCoderService] =
		useState<google.maps.Geocoder | null>(null)

	useEffect(() => {
		if (value?.name) {
			setPlaceAddress(value.name)
		}
	}, [value?.name])

	useEffect(() => {
		if (places === null && geoCoder === null) return

		if (places && geoCoder) {
			setAutocompleteService(new places.AutocompleteService())
			setPlacesService(new places.PlacesService(document.createElement('div')))
			setSessionToken(new places.AutocompleteSessionToken())
			setGeoCoderService(new geoCoder.Geocoder())
		}
		// eslint-disable-next-line consistent-return, @typescript-eslint/consistent-return
		return () => {
			setAutocompleteService(null)
			setPlacesService(null)
			setGeoCoderService(null)
		}
	}, [geoCoder, places])

	const fetchPredictions = useCallback(
		async (inputValue: string) => {
			if (!autocompleteService || !inputValue) {
				setPredictionResults([])
				return
			}

			const request = { input: inputValue, sessionToken }
			const response = await autocompleteService.getPlacePredictions(request)

			setPredictionResults(response.predictions)
			// Reset selected index when new predictions arrive
			setSelectedItemIndex(-1)
		},
		[autocompleteService, sessionToken]
	)

	const onInputChange = useCallback(
		async (searchText: string) => {
			setPlaceAddress(searchText)
			await fetchPredictions(searchText)
		},
		[fetchPredictions]
	)

	const handleSuggestionClick = useCallback(
		(placeId: string) => {
			if (!places) return

			const detailRequestOptions: google.maps.places.PlaceDetailsRequest = {
				placeId,
				sessionToken,
				fields: [
					'name',
					'formatted_address',
					'place_id',
					'geometry',
					'types',
					'address_components'
				]
			}

			const detailsRequestCallback = async (
				placeDetails: google.maps.places.PlaceResult | null
			) => {
				await geoCoderService?.geocode({ placeId }, results => {
					setPredictionResults([])
					setPlaceAddress(placeDetails?.formatted_address ?? '')
					setSessionToken(new places.AutocompleteSessionToken())
					if (!placeDetails) return
					const addressComponents = parseAddressComponents(
						placeDetails.address_components
					)
					const updatedLocation: ILocation = {
						placeName: placeDetails.name ?? '',
						placeId: placeDetails.place_id ?? '',
						name: placeDetails.formatted_address ?? '',
						formattedAddress: placeDetails.formatted_address ?? '',
						latitude: placeDetails.geometry?.location?.lat() ?? 0,
						longitude: placeDetails.geometry?.location?.lng() ?? 0,
						locationType: String(results?.at(0)?.geometry.location_type) || '',
						addressComponents: { ...addressComponents },
						isLodging: placeDetails.types?.includes('lodging') ?? false
					}
					setLocation(updatedLocation)
				})
			}

			placesService?.getDetails(detailRequestOptions, detailsRequestCallback)
		},
		[geoCoderService, places, placesService, sessionToken]
	)
	const handleKeyDown = useCallback(
		(event: React.KeyboardEvent<HTMLDivElement>) => {
			if (predictionResults.length === 0) return

			switch (event.key) {
				case 'ArrowDown': {
					event.preventDefault()
					setSelectedItemIndex(previousIndex =>
						previousIndex < predictionResults.length - 1 ? previousIndex + 1 : 0
					)
					break
				}
				case 'ArrowUp': {
					event.preventDefault()
					setSelectedItemIndex(previousIndex =>
						previousIndex > 0 ? previousIndex - 1 : predictionResults.length - 1
					)
					break
				}
				case 'Enter': {
					event.preventDefault()
					if (
						selectedItemIndex >= 0 &&
						selectedItemIndex < predictionResults.length
					) {
						handleSuggestionClick(predictionResults[selectedItemIndex].place_id)
					}
					break
				}
				case 'Escape': {
					event.preventDefault()
					setPredictionResults([])
					break
				}
				default: {
					// No default action needed
					break
				}
			}
		},
		[predictionResults, selectedItemIndex, handleSuggestionClick]
	)

	useEffect(() => {
		if (location && location.name !== value?.name && !clickToChange) {
			onChange(location)
			if (clearAfterChange) {
				setPlaceAddress('')
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [location])

	return (
		<div>
			{label ? (
				<div className='mb-2 block text-sm font-medium'>
					<Label className='text-gray-900' value='Address' />
				</div>
			) : null}
			<div
				className='relative w-full'
				onKeyDown={handleKeyDown}
				ref={containerReference}
			>
				<div className='flex'>
					<HSTextField
						type='text'
						name='address'
						placeholder={customPlaceholder ?? 'Where to?'}
						value={placeAddress}
						onChange={async event => {
							await onInputChange(event.target.value)
						}}
						color='light'
						disabled={disabled}
						isInvalid={isInvalid}
						showClearButton
						icon={HSIcon(faSearch)}
						autoComplete='off'
					/>
				</div>

				{predictionResults.length > 0 && (
					<ListGroup className='absolute z-50 mt-2 w-full'>
						{predictionResults.map(({ place_id, description }, index) => (
							<ListGroup.Item
								key={place_id}
								onClick={() => handleSuggestionClick(place_id)}
								className={`list-item-dropdown items-start text-wrap ${
									selectedItemIndex === index ? 'bg-gray-100' : ''
								}`}
								theme={{
									base: 'items-start text-wrap'
								}}
								tabIndex={0}
							>
								<div className='flex items-start text-left text-sm font-normal text-gray-900'>
									{description}
								</div>
							</ListGroup.Item>
						))}
					</ListGroup>
				)}
			</div>
		</div>
	)
}

export default LocationSelector
