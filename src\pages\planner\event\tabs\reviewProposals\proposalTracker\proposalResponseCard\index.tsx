/* eslint-disable no-param-reassign */
/* eslint-disable unicorn/no-array-reduce */
import { faFileExport, faFileImport } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HorizontalSingleBar from 'components/charts/horizontalSingleBar'
import HSDropdownButton from 'components/dropdown'
import HSTable from 'components/table'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { comparableHotelSources } from 'lib/helpers/common'
import type { ProposalRequestStatusKey } from 'lib/helpers/statusMaps'
import { ProposalRequestStatusMap } from 'lib/helpers/statusMaps'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { ProposalRequest } from 'models/proposalResponseMonitor'
import { useEffect, useMemo, useState } from 'react'
import { formatName, statusTemplate } from './template'
import { formatNumber } from 'lib/helpers'
import { declinedReasons } from 'lib/helpers/declinedReasons'
import ResponseStatusDetails from './responseStatusDetails'
import DeclineRateDetails from './declineRateDetails'
import { useNavigate } from 'react-router-dom'
import FollowUpEmail from '../followUp'
import { faEllipsisVertical } from '@fortawesome/pro-regular-svg-icons'

interface ITrackedProposalRequest extends ProposalRequest {
	actions: {
		canViewProposals: boolean | null
		canFollowUp: string | boolean
		canRemove: boolean
		canDecline: boolean
		canAddBack: boolean
	}
}

const filterTrackedProposalRequests = (
	proposalRequest: ProposalRequest,
	isAdmin: boolean
) => {
	if (proposalRequest.status === 'Pending') {
		return false
	}
	if (isAdmin) {
		return true
	}

	if (
		proposalRequest.createdBy?.split('@')[1] === 'myhopskip.com' &&
		![
			'Active',
			'Reviewed',
			'ClosedLost',
			'ClosedWon',
			'ContractSigned'
		].includes(proposalRequest.status ?? '') &&
		proposalRequest.comparableHotelSource === comparableHotelSources.CompSet
	) {
		return false
	}
	return true
}

const PlannerProposalResponseCard = () => {
	const { eventInfo: eventPlan } = eventInfoStore()
	const { userProfile } = useUserProfileContext()
	const navigate = useNavigate()
	const [responseChartData, setResponseChartData] = useState<{
		statuses: { key: string; count: number }[]
		declines: { key: string; count: number }[]
		totalRate: {
			name: string
			percent: number
			fill: string
			stroke?: string
		}[]
		requested: number
		received: number
		declined: number
	}>({
		statuses: [],
		declines: [],
		totalRate: [],
		requested: 0,
		received: 0,
		declined: 0
	})
	const [trackedProposalRequests, setTrackedProposalRequests] = useState<
		ITrackedProposalRequest[]
	>([])
	const [showResponseStatusDetails, setShowResponseStatusDetails] =
		useState(false)
	const [showDeclineRateDetails, setShowDeclineRateDetails] = useState(false)
	const [showFollowUp, setShowFollowUp] = useState(false)
	const [selectedProposal, setSelectedProposal] = useState<ProposalRequest>(
		{} as ProposalRequest
	)
	const [isBulkAction, setIsBulkAction] = useState(false)

	const chartData = useMemo(
		() => [
			{
				name: 'RFP Statuses',
				...Object.fromEntries(
					responseChartData.statuses
						.sort(
							(a, b) =>
								(ProposalRequestStatusMap[a.key as ProposalRequestStatusKey]
									?.sort ?? 0) -
								(ProposalRequestStatusMap[b.key as ProposalRequestStatusKey]
									?.sort ?? 0)
						)
						.map(s => [
							ProposalRequestStatusMap[s.key as ProposalRequestStatusKey]
								?.label ?? '',
							s.count
						])
				)
			}
		],
		[responseChartData.statuses]
	)
	const bars = responseChartData.statuses
		.sort(
			(a, b) =>
				(ProposalRequestStatusMap[a.key as ProposalRequestStatusKey]?.sort ??
					0) -
				(ProposalRequestStatusMap[b.key as ProposalRequestStatusKey]?.sort ?? 0)
		)
		.map(status => ({
			dataKey:
				ProposalRequestStatusMap[status.key as ProposalRequestStatusKey]
					?.label ?? '',
			fill:
				ProposalRequestStatusMap[status.key as ProposalRequestStatusKey]
					?.hexColor ?? '#052C3F',

			value: status.count
		}))
	const declineChartData = useMemo(
		() => [
			{
				name: 'Declined Reasons',
				...(Object.fromEntries(
					responseChartData.declines.map(s => {
						const declinedReason = declinedReasons.Hotelier.find(
							r => r.reason === s.key
						) || {
							reason: s.key,
							abbr: s.key
						}

						return [declinedReason.abbr, s.count]
					})
				) as Record<string, number>)
			}
		],
		[responseChartData.declines]
	)

	const declineBars = responseChartData.declines.map(s => {
		const declinedReason = declinedReasons.Hotelier.find(
			r => r.reason === s.key
		) || {
			reason: s.key,
			abbr: s.key,
			color: '#052C3F'
		}

		return {
			dataKey: declinedReason.abbr ?? '',
			fill: declinedReason.color ?? '#052C3F',
			reason: declinedReason.reason ?? '',
			value: s.count
		}
	})

	useEffect(() => {
		if (eventPlan?.id && eventPlan.proposalRequests) {
			const proposalRequests = eventPlan.proposalRequests.filter(
				pr =>
					userProfile && filterTrackedProposalRequests(pr, userProfile.isAdmin)
			)
			const proposalRequestsNotRemoved = proposalRequests.filter(
				pr => pr.status !== ProposalRequestStatusMap.Removed?.key
			)

			setTrackedProposalRequests(
				proposalRequests.map(pr => ({
					...pr,
					actions: {
						canViewProposals: !!pr.submitted,
						canFollowUp:
							[
								ProposalRequestStatusMap.New?.key,
								ProposalRequestStatusMap.Received?.key
							].includes(pr.status ?? '') &&
							(pr.proposalContact?.email ||
								(pr.proposalRecipients?.length ?? 0) > 0),
						canRemove:
							[
								ProposalRequestStatusMap.Pending?.key,
								ProposalRequestStatusMap.New?.key,
								ProposalRequestStatusMap.Received?.key
							].includes(pr.status ?? '') && !pr.submitted,
						canDecline: [
							ProposalRequestStatusMap.Active?.key,
							ProposalRequestStatusMap.Reviewed?.key
						].includes(pr.status ?? ''),
						canAddBack: pr.status === ProposalRequestStatusMap.Removed?.key
					}
				}))
			)

			const proposalTotalRate =
				proposalRequestsNotRemoved.length > 0
					? Math.round(
							(proposalRequestsNotRemoved.filter(
								pr =>
									![
										ProposalRequestStatusMap.New?.key,
										ProposalRequestStatusMap.Received?.key
									].includes(pr.status ?? '')
							).length /
								proposalRequestsNotRemoved.length) *
								100
						)
					: null
			const declineCounts = proposalRequestsNotRemoved
				.filter(pr => ProposalRequestStatusMap.Declined?.key === pr.status)
				.reduce((a: Record<string, number>, c) => {
					for (const r of c.venueDeclinedReasons) {
						if (a[r]) {
							a[r] += 1
						} else {
							a[r] = 1
						}
					}
					return a
				}, {})
			const statusCounts: Record<string, number> =
				proposalRequestsNotRemoved.reduce((a: Record<string, number>, c) => {
					if (c.status !== null) {
						if (a[c.status]) {
							a[c.status] += 1
						} else {
							a[c.status] = 1
						}
					}
					return a
				}, {})
			const declinedRate =
				proposalRequestsNotRemoved.length > 0
					? Math.round(
							(proposalRequestsNotRemoved.filter(
								pr => ProposalRequestStatusMap.Declined?.key === pr.status
							).length /
								proposalRequestsNotRemoved.length) *
								100
						)
					: null

			setResponseChartData({
				statuses: Object.keys(statusCounts).map(k => ({
					key: k,
					count: statusCounts[k]
				})),
				declines: Object.keys(declineCounts).map(k => ({
					key: k,
					count: declineCounts[k]
				})),
				totalRate: [
					{
						name: 'Responded',
						percent: proposalTotalRate ?? 0,
						fill: (proposalTotalRate ?? 0) < 50 ? '#F86866' : '#027587'
					},
					{
						name: 'No Response',
						percent: 100 - (proposalTotalRate ?? 0),
						fill: '#fff',
						stroke: '#027587'
					},
					{
						name: 'Declined',
						percent: declinedRate ?? 0,
						fill: '#fff',
						stroke: '#027587'
					}
				],
				requested: proposalRequestsNotRemoved.length,
				received: proposalRequestsNotRemoved.filter(
					pr =>
						![
							ProposalRequestStatusMap.New?.key,
							ProposalRequestStatusMap.Received?.key,
							ProposalRequestStatusMap.Declined?.key
						].includes(pr.status ?? '')
				).length,
				declined: proposalRequestsNotRemoved.filter(
					pr => ProposalRequestStatusMap.Declined?.key === pr.status
				).length
			})
		}
	}, [eventPlan?.id, eventPlan?.proposalRequests, userProfile])

	const gridData = useMemo(
		() =>
			trackedProposalRequests.map(pr => ({
				name: pr.venueName,
				location: pr.venueLocation,
				status: pr.status,
				tracking: pr.venueName,
				accessToProposal:
					pr.supplierContacts.length === 0
						? 'None'
						: pr.supplierContacts
								.reduce((a: string[], c) => {
									if (c.companyName && !a.includes(c.companyName)) {
										a.push(c.companyName)
									}
									return a
								}, [])
								.join(', '),
				createdBy: pr.createdBy,
				proposalRequest: pr
			})),

		[trackedProposalRequests]
	)

	const formatActions = (item: {
		name: string | null
		location: string | null
		status: string | null
		tracking: string | null
		accessToProposal: string
		createdBy: string | null
		proposalRequest: ITrackedProposalRequest
	}) => (
		<div className='flex gap-2'>
			<HSButton color='light' size='sm' onClick={() => {}}>
				View Proposal
			</HSButton>
			<HSDropdownButton
				label={<FontAwesomeIcon icon={faEllipsisVertical} />}
				className='bg-white shadow-lg'
				color='light'
				showDropdownIcon={false}
				items={[
					{
						id: 'follow-up',
						item: 'Follow Up',
						clickFunction: () => {
							setShowFollowUp(true)
							setIsBulkAction(false)
							setSelectedProposal(item.proposalRequest)
						}
					},
					{
						id: 'decline',
						item: 'Decline',
						clickFunction: () => {}
					},
					{
						id: 'remove',
						item: 'Remove',
						clickFunction: () => {}
					},
					{
						id: 'add-back',
						item: 'Add Back To RFP',
						clickFunction: () => {}
					},
					{
						id: 'edit',
						item: 'Edit Contacts',
						clickFunction: () => {
							navigate(`/planner/event/${eventPlan?.id}/builder/rfp-contacts`)
						}
					}
				]}
			/>
		</div>
	)

	return (
		<div className='flex flex-col'>
			<div className='p-6'>
				<div className='card flex'>
					<div className='flex w-1/3 flex-col justify-between border-r'>
						<div className='flex gap-4 p-4'>
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-bold text-gray-500'>Requested</div>
								<div className='flex items-center gap-2'>
									<div className='flex items-center justify-center rounded-md bg-gray-100 p-2'>
										<FontAwesomeIcon icon={faFileExport} />
									</div>
									<div className='text-2xl font-semibold text-gray-900'>
										{responseChartData.requested}
									</div>
									<div className='text-xs font-medium text-gray-500'>
										hotels
									</div>
								</div>
							</div>

							<div className='border-r' />
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-bold text-gray-500'>Received</div>
								<div className='flex items-center gap-2'>
									<div className='flex items-center justify-center rounded-md bg-primary-100 p-2'>
										<FontAwesomeIcon icon={faFileImport} />
									</div>
									<div className='text-2xl font-semibold text-gray-900'>
										{responseChartData.received}
									</div>
									<div className='text-xs font-medium text-gray-500'>
										proposals
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className='flex w-1/3 flex-col gap-2 border-r p-4'>
						<div className='flex justify-between'>
							<div className='flex text-sm font-bold text-gray-500'>
								Response status (
								{formatNumber(responseChartData.totalRate[0]?.percent)}%)
							</div>
							<HSButton
								color='text'
								className='font-semibold text-primary-600'
								size='sm'
								onClick={() => {
									setShowResponseStatusDetails(true)
								}}
							>
								Details
							</HSButton>
						</div>
						<div>
							<HorizontalSingleBar
								data={chartData}
								bars={bars}
								totalValue={Object.entries(chartData[0])
									.filter(([key]) => key !== 'name')
									.reduce(
										(accumulator, [, value]) =>
											accumulator + (typeof value === 'number' ? value : 0),
										0
									)}
								barHeight={40}
								displayType='percentage'
							/>
						</div>
					</div>
					<div className='flex w-1/3 flex-col gap-2 border-r p-4'>
						<div className='flex justify-between'>
							<div className='flex text-sm font-bold text-gray-500'>
								Decline rate ({' '}
								{formatNumber(responseChartData.totalRate[2]?.percent)}%)
							</div>
							<HSButton
								color='text'
								className='font-semibold text-primary-600'
								size='sm'
								onClick={() => {
									setShowDeclineRateDetails(true)
								}}
							>
								Details
							</HSButton>
						</div>
						<div>
							<HorizontalSingleBar
								data={declineChartData}
								bars={declineBars}
								totalValue={Object.entries(declineChartData[0])
									.filter(([key]) => key !== 'name')
									.reduce(
										(accumulator, [, value]) =>
											accumulator + (typeof value === 'number' ? value : 0),
										0
									)}
								barHeight={40}
								displayType='percentage'
							/>
						</div>
					</div>
				</div>
			</div>
			<div className='flex flex-col gap-4 p-6'>
				<div className='flex justify-between gap-2'>
					<div className='flex flex-col'>
						<div className='text-xl font-semibold text-gray-900'>
							Hotels Requested for Proposals
						</div>
						<div className='text-sm font-normal text-gray-500'>
							You can view a hotel&apos;s proposal, follow up with a hotel, or
							remove a hotel by clicking the Actions button
						</div>
					</div>
					<div>
						<HSDropdownButton
							label='Actions'
							color='light'
							items={[
								{
									id: 'follow-up',
									item: 'Follow Up',
									clickFunction: () => {
										setShowFollowUp(true)
										setIsBulkAction(true)
									}
								},
								{
									id: 'decline',
									item: 'Decline',
									clickFunction: () => {}
								},
								{
									id: 'remove',
									item: 'Remove',
									clickFunction: () => {}
								},
								{
									id: 'add-back',
									item: 'Add Back To RFP',
									clickFunction: () => {}
								}
							]}
						/>
					</div>
				</div>
				<div>
					<HSTable
						allowSelection
						rows={gridData}
						columns={[
							{
								headerText: 'Hotel Name',
								field: 'name',
								sortable: true,
								clipMode: 'ellipsis',
								render: item =>
									formatName(
										item.name,
										item.createdBy,
										eventPlan?.supplierContacts ?? []
									),
								width: 250
							},

							{
								headerText: 'Status',
								field: 'status',
								sortable: true,
								clipMode: 'ellipsis',
								render: item => statusTemplate({ status: item.status ?? '' }),
								width: 200
							},
							{
								headerText: 'Proposal Tracking',
								field: 'location',
								sortable: true,
								clipMode: 'ellipsis',
								width: 200
							},
							{
								headerText: 'Actions',
								field: 'actions',
								render: formatActions,
								sortable: true,
								clipMode: 'ellipsis',
								width: 150
							}
						]}
					/>
				</div>
			</div>
			{showResponseStatusDetails ? (
				<ResponseStatusDetails
					showDetails={showResponseStatusDetails}
					setDetails={setShowResponseStatusDetails}
					chartData={bars}
					filterTrackedProposalRequests={filterTrackedProposalRequests}
				/>
			) : null}
			{showDeclineRateDetails ? (
				<DeclineRateDetails
					showDetails={showDeclineRateDetails}
					setDetails={setShowDeclineRateDetails}
					chartData={declineBars}
					filterTrackedProposalRequests={filterTrackedProposalRequests}
				/>
			) : null}
			{showFollowUp ? (
				<FollowUpEmail
					showDetails={showFollowUp}
					setDetails={setShowFollowUp}
					proposalRequest={selectedProposal}
					isBulk={isBulkAction}
				/>
			) : null}
		</div>
	)
}
export default PlannerProposalResponseCard
