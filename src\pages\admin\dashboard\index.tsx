/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable @typescript-eslint/no-floating-promises */
/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable unicorn/no-array-reduce */
import { faTable } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import type { DateRange } from '@syncfusion/ej2-react-calendars'
import {
	PresetDirective,
	PresetsDirective
} from '@syncfusion/ej2-react-calendars'
import Loader from 'components/loader'
import ReportCard, { reportCardHeights } from 'components/reportCard'
import GrowthChart, {
	GrowthPeriodPicker,
	growthPeriods
} from 'components/reporting/growthChart'
import { eachMonthOfInterval, format, parseISO, startOfMonth } from 'date-fns'
import {
	calculatePeriod,
	formatCurrency,
	formatNumber,
	periodTypes
} from 'lib/helpers'
import { calculateGrossMarketValue } from 'lib/helpers/proposalValues'
import { useLoadIataVerifications } from 'lib/services/iataVerification.service'
import {
	loadMetrics,
	useListAdminReports
} from 'lib/services/reporting.service'
import type {
	Destination,
	IMetric,
	IProposalRequestMetric
} from 'models/reporting'
import { useEffect, useState } from 'react'
import {
	LineChart,
	XAxis,
	YAxis,
	Line,
	ResponsiveContainer,
	Bar,
	BarChart,
	Tooltip
} from 'recharts'
import MetricTile from 'components/metricTile'
import { NavLink } from 'react-router-dom'
import { organizationsViews } from '../plannerOrganizations/helper'
import MarketValues, {
	firstSubmitted,
	formatEventPlanName,
	formatHotelCity,
	formatName,
	formatOrganizationName,
	formatPropertySeller,
	formatSupplierContacts
} from './helper'
import { DialogComponent } from '@syncfusion/ej2-react-popups'
import type { GridComponent } from '@syncfusion/ej2-react-grids'
import {
	ColumnChooser,
	ColumnDirective,
	ColumnsDirective,
	Inject,
	Page,
	Sort,
	Toolbar
} from '@syncfusion/ej2-react-grids'
import type {
	EventPlanStatusKey,
	ProposalRequestStatusKey
} from 'lib/helpers/statusMaps'
import {
	EventPlanStatusMap,
	ProposalRequestStatusMap
} from 'lib/helpers/statusMaps'
import { eventTypeOptions } from 'lib/helpers/eventTypes'
import { groupTypeOptions } from 'lib/helpers/groupTypes'
import industryTypeOptions from 'lib/helpers/industryTypes'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import type { ProposalValues } from 'models/proposalResponseMonitor'
import DataGrid from 'components/dataGrid'
import headerStore from 'components/header/headerStore'
import HSDateRangePicker from 'components/dateRangePicker'

const loadEventPlans = async (
	filterQuery: string | null,
	dateFilter: string | null
) => loadMetrics('eventplans', filterQuery, dateFilter)

const loadProposalRequests = async (
	filterQuery: string | null,
	dateFilter: string | null
) => loadMetrics('proposalrequests', filterQuery, dateFilter)

const rfpsSubmittedLabel = ({
	x,
	y,
	stroke,
	value
}: {
	x: number
	y: number
	stroke: string
	value: number
}) => (
	<text x={x} y={y} dy={-10} fill={stroke} fontSize={12} textAnchor='middle'>
		{value}
	</text>
)

const currencyTooltipFormatter = (value: number) => [
	formatCurrency(value),
	'Total Amount'
]

// const onClickExport = async (exportPath: string, query: string) => {
// 	const accessToken = await getAccessToken()
// 	const { host, endpointPath } = getServiceHostAndEndpointPath(
// 		`/rpt/api/${exportPath}?bearer=${accessToken}${query ? `&${query}` : ''}`
// 	)
// 	globalThis.location.href = `${host}${endpointPath}`
// }

const formatDestinationNames = (destinations: Destination[]) =>
	destinations
		// .map(dmo => dmo.name)
		.sort((c, n) => ((c.name ?? '') > (n.name ?? '') ? 1 : -1))
		.join(',')

const Dashboard = () => {
	let gridInstance: GridComponent | null
	const setGridInstance = (grid: GridComponent | null) => {
		gridInstance = grid
	}
	const [dateFilter, setDateFilter] = useState<DateRange>({
		start: startOfMonth(new Date()),
		end: new Date()
	})
	const { data: iataVerificationsData } = useLoadIataVerifications(true)
	const { data: adminReportsData } = useListAdminReports()

	// const [adminReports, setAdminReports] = useState<
	// 	{ key: string; name: string }[]
	// >([])
	const [iataVerifications, setIataVerifications] = useState<number>(0)
	const [showRawDataModal, setShowRawDataModal] = useState({
		show: false,
		title: '',
		data: { key: '', series: [] }
	})
	const [loading, setLoading] = useState({
		eventPlans: false,
		proposalRequests: false
	})
	const [growthPeriod, setGrowthPeriod] = useState(growthPeriods.month)
	const [eventPlans, setEventPlans] = useState<{
		raw: { series: IMetric[] }
		submitted: {
			count: number
			maxCount: number
			maxOpportunityValue: number
			series: IMetric[]
		}
	}>({
		raw: { series: [] },
		submitted: {
			count: 0,
			maxCount: 0,
			maxOpportunityValue: 0,
			series: []
		}
	})
	const [proposalRequests, setProposalRequests] = useState({
		sent: {
			count: 0,
			total: 0,
			maxCount: 0,
			maxTotal: 0,
			series: [],
			raw: [],
			opportunityValue: 0
		},
		won: {
			count: 0,
			total: 0,
			series: [],
			raw: [],
			opportunityValue: 0,
			maxTotal: 0
		},
		submitted: {
			count: 0,
			total: 0,
			series: [],
			raw: [],
			maxTotal: 0
		}
	})

	const { setHide, reset } = headerStore()

	useEffect(() => {
		setHide(false)
		return () => {
			reset()
		}
	}, [reset, setHide])

	useEffect(() => {
		if (iataVerificationsData && adminReportsData) {
			setIataVerifications(iataVerificationsData.length)
			// setAdminReports(adminReportsData)
		}
	}, [adminReportsData, iataVerificationsData])

	useEffect(() => {
		setLoading({ eventPlans: true, proposalRequests: true })
		const monthsInInterval = eachMonthOfInterval({
			start: dateFilter.start?.toISOString() ?? '',
			end: dateFilter.end?.toISOString() ?? ''
		})
		const dateFilterQuery = `startDate=${dateFilter.start?.toISOString()}&endDate=${dateFilter.end?.toISOString()}`
		loadEventPlans(null, dateFilterQuery)
			.then(response => {
				const r = response as IMetric[] | undefined
				const submitted = r?.reduce(
					(a, c) => {
						const d = parseISO(c.firstSubmitted)
						const m = a.find(
							item =>
								item.utcYear === d.getUTCFullYear() &&
								item.utcMonth === d.getUTCMonth()
						)
						if (m) {
							const value = calculateGrossMarketValue(
								c.proposalValues.roomCost === 0 &&
									c.proposalValues.foodAndBeverage === 0
									? c.proposalValuesAverage
									: c.proposalValues
							)
							m.count += 1
							m.opportunityValue += value
							if (c.contracting) {
								m.countAwarded += 1
								m.opportunityValueAwarded += value
							}
							m.awardedRate = (m.countAwarded / m.count) * 100
						}
						return { ...a, maxTotal: 0 }
					},
					monthsInInterval.map(m => ({
						month: m,
						utcMonth: m.getUTCMonth(),
						utcYear: m.getUTCFullYear(),
						name: format(m, 'MMM yy'),
						count: 0,
						opportunityValue: 0,
						countAwarded: 0,
						opportunityValueAwarded: 0,
						awardedRate: 0
					}))
				)
				setEventPlans({
					raw: {
						series: r
					},
					submitted: {
						...submitted.reduce(
							(a, c) => {
								a.count += c.count
								if (c.count > a.maxCount) {
									a.maxCount = c.count
								}
								if (c.opportunityValue > a.maxOpportunityValue) {
									a.maxTotal = c.opportunityValue
								}
								return a
							},
							{ count: 0, maxCount: 0, maxOpportunityValue: 0 }
						),
						series: submitted
					}
				})
				setLoading(s => ({ ...s, eventPlans: false }))
			})
			.catch((error: unknown) => {
				setLoading({ eventPlans: false, proposalRequests: false })
				console.log(error)
			})
		loadProposalRequests(null, dateFilterQuery)
			.then(response => {
				const r = response as IProposalRequestMetric
				const won = r.won.reduce(
					(a, c) => {
						const d = parseISO(c.won)
						const m = a.find(
							item =>
								item.utcYear === d.getUTCFullYear() &&
								item.utcMonth === d.getUTCMonth()
						)
						if (m) {
							m.count += 1
							m.opportunityValue += calculateGrossMarketValue(c.proposalValues)
						}
						return a
					},
					monthsInInterval.map(m => ({
						month: m,
						utcMonth: m.getUTCMonth(),
						utcYear: m.getUTCFullYear(),
						name: format(m, 'MMM yy'),
						count: 0,
						opportunityValue: 0
					}))
				)
				const submitted = r.submitted.reduce(
					(a, c) => {
						const d = parseISO(c.submitted)
						const m = a.find(
							item =>
								item.utcYear === d.getUTCFullYear() &&
								item.utcMonth === d.getUTCMonth()
						)
						if (m) {
							m.count += 1
							m.opportunityValue += calculateGrossMarketValue(c.proposalValues)
						}
						return a
					},
					monthsInInterval.map(m => ({
						month: m,
						utcMonth: m.getUTCMonth(),
						utcYear: m.getUTCFullYear(),
						name: format(m, 'MMM yy'),
						count: 0,
						opportunityValue: 0
					}))
				)
				const sent = r.sent.reduce(
					(a, c) => {
						const d = parseISO(c.sent)
						const m = a.find(
							item =>
								item.utcYear === d.getUTCFullYear() &&
								item.utcMonth === d.getUTCMonth()
						)
						if (m) {
							const value = calculateGrossMarketValue(c.proposalValues)
							m.count += 1
							m.opportunityValue += value
							if (!c.declined && !c.submitted) {
								m.countNoResponse += 1
							}
							if (c.declined) {
								m.countDeclined += 1
								m.opportunityValueDeclined += value
							}
							if (c.submitted) {
								m.countSubmitted += 1
								m.opportunityValueSubmitted += value
							}
							if (c.won) {
								m.countWon += 1
								m.opportunityValueWon += value
							}
						}
						return a
					},
					monthsInInterval.map(m => ({
						month: m,
						utcMonth: m.getUTCMonth(),
						utcYear: m.getUTCFullYear(),
						name: format(m, 'MMM yy'),
						count: 0,
						opportunityValue: 0,
						countSubmitted: 0,
						opportunityValueSubmitted: 0,
						countDeclined: 0,
						opportunityValueDeclined: 0,
						countWon: 0,
						opportunityValueWon: 0,
						countNoResponse: 0
					}))
				)
				setProposalRequests({
					won: {
						...won.reduce(
							(a, c) => {
								a.opportunityValue += c.opportunityValue
								a.count += c.count
								return a
							},
							{ opportunityValue: 0, count: 0 }
						),
						series: won,
						raw: r.won.map(d => ({
							...d,
							proposalValue: calculateGrossMarketValue(d.proposalValues),
							destinationNames: formatDestinationNames(d.destinations)
						}))
					},
					submitted: {
						...submitted.reduce(
							(a, c) => {
								a.opportunityValue += c.opportunityValue
								a.count += c.count
								return a
							},
							{ opportunityValue: 0, count: 0 }
						),
						series: submitted,
						raw: r.submitted.map(d => ({
							...d,
							proposalValue: calculateGrossMarketValue(d.proposalValues),
							destinationNames: formatDestinationNames(d.destinations)
						}))
					},
					sent: {
						...sent.reduce(
							(a, c) => {
								a.count += c.count
								a.opportunityValue += c.opportunityValue
								return a
							},
							{ count: 0, opportunityValue: 0 }
						),
						series: sent,
						...sent.reduce(
							(a, c) => {
								if (c.count > a.maxCount) {
									a.maxCount = c.count
								}
								if (c.opportunityValue > a.maxOpportunityValue) {
									a.maxTotal = c.opportunityValue
								}
								return a
							},
							{ maxCount: 0, maxOpportunityValue: 0 }
						),
						raw: r.sent.map(d => ({
							...d,
							proposalValue: calculateGrossMarketValue(d.proposalValues),
							destinationNames: formatDestinationNames(d.destinations)
						}))
					}
				})
				setLoading(s => ({ ...s, proposalRequests: false }))
			})
			.catch((error: unknown) => {
				setLoading({ eventPlans: false, proposalRequests: false })
				console.log(error)
			})
	}, [dateFilter])

	// const headerTemplate = (r: { key: string; name: string }) => (
	// 	<ButtonComponent
	// 		cssClass='e-link e-flat'
	// 		onClick={async () =>
	// 			onClickExport('reports/admin/excel', `templates=${r.key}`)
	// 		}
	// 	>
	// 		{r.name}
	// 	</ButtonComponent>
	// )

	const rfpsSubmittedTooltipFormatter = (value: number) => [
		formatNumber(value),
		'Submitted'
	]

	function rfpSubmitted() {
		return (
			<ReportCard
				title={`RFPs Submitted: ${formatNumber(eventPlans.submitted.count)}`}
				height={reportCardHeights.fit}
				tools={<FontAwesomeIcon icon={faTable} onClick={() => {}} />}
				isLoading={loading.eventPlans}
			>
				<div>
					{loading.eventPlans ? (
						<div className='spinner-container'>
							<Loader />
						</div>
					) : null}
					<ResponsiveContainer height={180} width='95%'>
						<LineChart
							// width={560}
							// height={180}
							margin={{ top: 20, right: 10 }}
							data={eventPlans.submitted.series}
						>
							<XAxis dataKey='name' />
							<YAxis />
							<Line
								isAnimationActive={false}
								dataKey='count'
								type='linear'
								dot={{ stroke: '#4FC0B0', strokeWidth: 10 }}
								fill='#021b27'
								stroke='#000'
								label={rfpsSubmittedLabel}
							/>
							<Tooltip formatter={rfpsSubmittedTooltipFormatter} />
						</LineChart>
					</ResponsiveContainer>
				</div>
			</ReportCard>
		)
	}

	function rfpSubmittedGrowth() {
		return (
			<ReportCard
				title='RFPs Submitted Growth'
				height={reportCardHeights.fit}
				tools={
					<div className='flex flex-row items-center space-x-2'>
						<GrowthPeriodPicker
							value={growthPeriod}
							onChange={event => setGrowthPeriod(event)}
						/>
						<FontAwesomeIcon
							icon={faTable}
							onClick={() =>
								setShowRawDataModal({
									show: true,
									title: `RFPs Submitted from ${format(dateFilter.start?.toISOString() ?? '', 'M/d/yyyy')} to ${format(dateFilter.end?.toISOString() ?? '', 'M/d/yyyy')}`,
									data: {
										key: 'eventPlans',
										series: eventPlans.raw.series
									}
								})
							}
						/>
					</div>
				}
				isLoading={loading.eventPlans}
			>
				<div>
					{loading.eventPlans ? (
						<div className='spinner-container'>
							<Loader />
						</div>
					) : null}
					<GrowthChart
						margin={{ top: 30, right: 10 }}
						dateFilter={dateFilter}
						growthPeriod={growthPeriod}
						data={eventPlans.raw.series}
						dateKey='firstSubmitted'
						growthMetricKey={null}
						width='95%'
						height='180'
					/>
				</div>
			</ReportCard>
		)
	}

	const onToolbarClick = () => {
		;(gridInstance as GridComponent).excelExport({
			includeHiddenColumn: true
		})
	}

	const proposalRequestFillRate = ({
		active,
		payload,
		label
	}: {
		active: boolean
		payload: { name: string; value: number }[] | undefined
		label: string
	}) => {
		if (active && payload?.length) {
			const received = payload.find(p => p.name === 'count')
			const declined = payload.find(p => p.name === 'countDeclined') as
				| {
						name: string
						value: number
				  }
				| undefined
			const submitted = payload.find(p => p.name === 'countSubmitted') as
				| {
						name: string
						value: number
				  }
				| undefined
			const won = payload.find(p => p.name === 'countWon') as
				| {
						name: string
						value: number
				  }
				| undefined

			const declinedRate =
				((declined?.value ?? 0) / (received?.value ?? 1)) * 100
			const submittedRate =
				((submitted?.value ?? 0) / (received?.value ?? 1)) * 100
			const responseRate = declinedRate + submittedRate

			return (
				<div className='custom-tooltip rounded border border-gray-300 bg-white p-2 shadow'>
					<p className='label'>{label}</p>
					<table>
						<thead>
							<tr>
								<th> </th>
								<th>Count</th>
								<th>Rate</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>Requests Received</td>
								<td>{formatNumber(received?.value ?? 0)}</td>
								<td>{formatNumber(responseRate)}% response</td>
							</tr>
							<tr>
								<td>Turned Down</td>
								<td>{formatNumber(declined?.value ?? 0)}</td>
								<td>{formatNumber(declinedRate)}%</td>
							</tr>
							<tr>
								<td>Proposals Submitted</td>
								<td>{formatNumber(submitted?.value ?? 0)}</td>
								<td>{formatNumber(submittedRate)}%</td>
							</tr>
							<tr>
								<td>Closed Won</td>
								<td>{formatNumber(won?.value ?? 0)}</td>
								<td>
									{formatNumber(
										((won?.value ?? 0) / (received?.value ?? 1)) * 100
									)}
									% of received
									<br />
									{formatNumber(
										((won?.value ?? 0) / (submitted?.value ?? 1)) * 100
									)}
									% of submitted
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			)
		}

		return null
	}

	return (
		<div className='p-6'>
			{/* <div className='flex flex-col gap-2 border bg-white p-6'>
				<HSBreadCrumb />
				<div className='text-3xl font-semibold text-gray-900'>Dashboard</div>
			</div> */}
			<div className='card'>
				<div className='flex items-center justify-between border-b p-4'>
					<div className='text-xl font-semibold text-gray-900'>
						Admin Dashboard
					</div>
					<div className='w-80'>
						<HSDateRangePicker
							placeholder='Select a range'
							value={dateFilter}
							onChange={({ value }: { value: DateRange }) =>
								setDateFilter(value)
							}
							format='dd MMM, yyyy'
						>
							<PresetsDirective>
								{Object.keys(periodTypes).map(period => {
									const {
										type: { label, key },
										startDate,
										endDate
									} = calculatePeriod(period)
									return (
										<PresetDirective
											key={key}
											label={label}
											start={new Date(startDate)}
											end={new Date(endDate)}
										/>
									)
								})}
							</PresetsDirective>
						</HSDateRangePicker>
					</div>
				</div>
				<div
					className='overflow-auto'
					style={{
						maxHeight: 'calc(100vh - 12rem)'
					}}
				>
					<div className='control-section p-4'>
						<div className='flex flex-col gap-4'>
							<div className='flex gap-4'>
								<div className='flex w-3/4 gap-4'>
									<div className='w-1/2'>{rfpSubmitted()}</div>
									<div className='w-1/2'>
										<ReportCard
											title={`GMV: ${formatCurrency(proposalRequests.won.opportunityValue)}`}
											height={reportCardHeights.fit}
											tools={
												<FontAwesomeIcon
													icon={faTable}
													onClick={() =>
														setShowRawDataModal({
															show: true,
															title: `GMV from ${format(parseISO(dateFilter.start?.toISOString() ?? ''), 'M/d/yyyy')} to ${format(parseISO(dateFilter.end?.toISOString() ?? ''), 'M/d/yyyy')}`,
															data: {
																key: 'proposalRequests',
																series: proposalRequests.won.raw
															}
														})
													}
												/>
											}
											isLoading={loading.proposalRequests}
										>
											<div>
												{loading.proposalRequests ? (
													<div className='spinner-container'>
														<Loader />
													</div>
												) : null}
												<ResponsiveContainer width='95%' height={180}>
													<LineChart
														margin={{ top: 20, left: 10 }}
														data={proposalRequests.won.series}
													>
														<XAxis dataKey='name' />
														<YAxis
															tickFormatter={value =>
																`${formatCurrency(value / 1000)}k`
															}
														/>
														<Line
															isAnimationActive={false}
															dataKey='opportunityValue'
															type='linear'
															dot={{ stroke: '#4FC0B0', strokeWidth: 10 }}
															fill='#021b27'
															stroke='#000'
														/>
														<Tooltip formatter={currencyTooltipFormatter} />
													</LineChart>
												</ResponsiveContainer>
											</div>
										</ReportCard>
									</div>
								</div>
								<div className='flex w-1/4 gap-4'>
									<div className='e-card'>
										<div className='e-card-header'>
											<div className='e-card-header-caption'>
												<div className='e-card-title'> Admin Reports</div>
											</div>
										</div>
										<div className='e-card-content'>
											{/* <ListViewComponent
												id='list'
												dataSource={adminReports}
												template={headerTemplate}
											/> */}
										</div>
									</div>
								</div>
							</div>
							<div className='flex gap-4'>
								<div className='flex w-3/4 gap-4'>
									<div className='w-1/2'>{rfpSubmittedGrowth()} </div>
									<div className='w-1/2'>
										<ReportCard
											title={`Opportunities Sent: ${formatCurrency(proposalRequests.sent.opportunityValue)}`}
											height={reportCardHeights.fit}
											tools={
												<FontAwesomeIcon
													icon={faTable}
													onClick={() =>
														setShowRawDataModal({
															show: true,
															title: `Opportunities Sent from ${format(parseISO(dateFilter.start?.toISOString() ?? ''), 'M/d/yyyy')} to ${format(parseISO(dateFilter.end?.toISOString() ?? ''), 'M/d/yyyy')}`,
															data: {
																key: 'proposalRequests',
																series: proposalRequests.sent.raw
															}
														})
													}
												/>
											}
											isLoading={loading.proposalRequests}
										>
											<div>
												{loading.proposalRequests ? (
													<div className='spinner-container'>
														<Loader />
													</div>
												) : null}
												<ResponsiveContainer width='95%' height={180}>
													<LineChart
														margin={{ top: 20, left: 10 }}
														data={proposalRequests.sent.series}
													>
														<XAxis dataKey='name' />
														<YAxis
															tickFormatter={value =>
																`${formatCurrency(value / 1000)}k`
															}
														/>
														<Line
															isAnimationActive={false}
															dataKey='opportunityValue'
															type='linear'
															dot={{ stroke: '#4FC0B0', strokeWidth: 10 }}
															fill='#021b27'
															stroke='#000'
														/>
														<Tooltip formatter={currencyTooltipFormatter} />
													</LineChart>
												</ResponsiveContainer>
											</div>
										</ReportCard>
									</div>
								</div>
								<div className='flex w-1/4 gap-4'>
									<div className='e-card'>
										<div className='e-card-header'>
											<div className='e-card-header-caption'>
												<div className='e-card-title'>Planner IATA Codes</div>
											</div>
										</div>
										<div className='e-card-content'>
											<NavLink
												to={`/admin/organizations/${organizationsViews.awaitingVerification}`}
											>
												<MetricTile
													metric={`${iataVerifications}`}
													subTitle='Awaiting Verification'
													scale='small'
													lock={undefined}
												/>
											</NavLink>
										</div>
									</div>
								</div>
							</div>
							<div className='flex w-3/4 gap-4'>
								<div className='w-1/2'>
									<ReportCard
										title='Proposal Fill Rates (received, t/d, bid, won)'
										height={reportCardHeights.fit}
										isLoading={loading.proposalRequests}
										tools={null}
									>
										<div>
											{loading.proposalRequests ? (
												<div className='spinner-container'>
													<Loader />
												</div>
											) : null}
											<ResponsiveContainer width='95%' height={180}>
												<BarChart
													margin={{ top: 30 }}
													data={proposalRequests.sent.series}
												>
													<XAxis dataKey='name' />
													<YAxis
														dataKey='count'
														yAxisId='left'
														domain={[0, proposalRequests.sent.maxCount]}
													/>
													<Tooltip content={proposalRequestFillRate} />
													<Bar
														yAxisId='left'
														stackId='a'
														fill='#F86866'
														dataKey='countDeclined'
													/>
													<Bar
														yAxisId='left'
														stackId='a'
														fill='#073d57'
														dataKey='countSubmitted'
													/>
													<Bar
														yAxisId='left'
														stackId='a'
														fill='#4FC0B0'
														dataKey='countWon'
													/>
													<Bar
														yAxisId='left'
														stackId='a'
														fill='#ffffff'
														dataKey='count'
													/>
												</BarChart>
											</ResponsiveContainer>
										</div>
									</ReportCard>
								</div>
								<div className='w-1/2'>
									<ReportCard
										title='Top 10 Market Opportunities'
										height={reportCardHeights.fit}
										tools={
											<FontAwesomeIcon
												icon={faTable}
												onClick={() =>
													setShowRawDataModal({
														show: true,
														title: `Opportunities Sent from ${format(parseISO(dateFilter.start?.toISOString() ?? ''), 'M/d/yyyy')} to ${format(parseISO(dateFilter.end?.toISOString() ?? ''), 'M/d/yyyy')}`,
														data: {
															key: 'proposalRequests',
															series: proposalRequests.sent.raw
														}
													})
												}
											/>
										}
										isLoading={loading.proposalRequests}
									>
										<div>
											{loading.proposalRequests ? (
												<div className='spinner-container'>
													<Loader />
												</div>
											) : null}
											<MarketValues
												series={proposalRequests.sent.raw}
												top={10}
											/>
										</div>
									</ReportCard>
								</div>
							</div>
							<div className='flex w-3/4 gap-4'>
								<div className='w-1/2' />
							</div>
							<div className='flex w-3/4 gap-4'>
								<div className='w-1/2'>
									<ReportCard
										title={`Proposals Submitted: ${formatNumber(proposalRequests.submitted.count)}`}
										height={reportCardHeights.fit}
										tools={
											<FontAwesomeIcon
												icon={faTable}
												onClick={() =>
													setShowRawDataModal({
														show: true,
														title: `Proposals Submitted from ${format(parseISO(dateFilter.start?.toISOString() ?? ''), 'M/d/yyyy')} to ${format(parseISO(dateFilter.end?.toISOString() ?? ''), 'M/d/yyyy')}`,
														data: {
															key: 'proposalRequests',
															series: proposalRequests.submitted.raw
														}
													})
												}
											/>
										}
										isLoading={loading.proposalRequests}
									>
										<div>
											{loading.proposalRequests ? (
												<div className='spinner-container'>
													<Loader />
												</div>
											) : null}
											<ResponsiveContainer width='95%' height={180}>
												<LineChart
													margin={{ top: 20, right: 10 }}
													data={proposalRequests.submitted.series}
												>
													<XAxis dataKey='name' />
													<YAxis />
													<Line
														isAnimationActive={false}
														dataKey='count'
														type='linear'
														dot={{ stroke: '#4FC0B0', strokeWidth: 10 }}
														fill='#021b27'
														stroke='#000'
														label={rfpsSubmittedLabel}
													/>
													<Tooltip formatter={rfpsSubmittedTooltipFormatter} />
												</LineChart>
											</ResponsiveContainer>
										</div>
									</ReportCard>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			{showRawDataModal.show ? (
				<DialogComponent
					visible={showRawDataModal.show}
					showCloseIcon
					close={() => {
						setShowRawDataModal({
							show: false,
							title: '',
							data: { key: '', series: [] }
						})
					}}
					height='80%'
					isModal
					title={showRawDataModal.title}
					header={showRawDataModal.title}
					position={{ X: 'center', Y: 'top' }}
					className='m-4 p-4'
				>
					<div className='m-4'>
						<div className='text-sm'>
							{showRawDataModal.data.series.length} items
						</div>
						<DataGrid
							ref={(grid: GridComponent | null) => {
								setGridInstance(grid)
							}}
							toolbarClick={onToolbarClick}
							loadingIndicator={{ indicatorType: 'Shimmer' }}
							dataSource={showRawDataModal.data.series}
							allowSorting
							sortSettings={{
								columns: [
									{
										field:
											showRawDataModal.data.key === 'eventPlans'
												? 'name'
												: 'eventPlanName',
										direction: 'Ascending'
									}
								]
							}}
							width='100%'
							className='w-full flex-1'
							allowTextWrap
							toolbar={['ColumnChooser']}
							gridLines='Both'
							showColumnChooser
						>
							{showRawDataModal.data.key === 'eventPlans' ? (
								<ColumnsDirective>
									<ColumnDirective
										visible
										field='name'
										headerText='RFP Name'
										template={formatName}
									/>
									<ColumnDirective
										visible
										field='organizationName'
										headerText='Planner Organization'
										template={formatOrganizationName}
									/>
									<ColumnDirective
										visible
										field='firstSubmitted'
										headerText='First Submitted'
										template={firstSubmitted}
									/>
									<ColumnDirective
										visible
										field='contracting'
										headerText='Began Contracting'
										template={(item: { contracting: string | undefined }) =>
											item.contracting?.split('T')[0]
										}
									/>
									<ColumnDirective
										visible
										field='firstContractSigned'
										headerText='Contract Signed'
										template={(item: {
											firstContractSigned: string | undefined
										}) => item.firstContractSigned?.split('T')[0]}
									/>
									<ColumnDirective
										visible
										field='status'
										headerText='Status'
										headerTemplate={(item: { status: string }) => (
											<div className='e-headertext'>
												{
													EventPlanStatusMap[item.status as EventPlanStatusKey]
														?.label
												}
											</div>
										)}
									/>
									<ColumnDirective
										visible
										field='startDate'
										headerText='Event Start Date'
										template={(item: { startDate: string }) =>
											item.startDate.split('T')[0]
										}
									/>
									<ColumnDirective
										visible
										field='totalRoomsRequested'
										headerText='Total Room Nights'
										template={(item: { totalRoomsRequested: number }) =>
											formatNumber(item.totalRoomsRequested)
										}
									/>
									<ColumnDirective
										visible
										field='peakRooms'
										headerText='Peak Room Nights'
										template={(item: { peakRooms: number }) =>
											formatNumber(item.peakRooms)
										}
									/>
									<ColumnDirective
										visible
										field='opportunityValue'
										headerText='Opportunity Value'
										template={(item: { opportunityValue: number }) =>
											formatCurrency(item.opportunityValue)
										}
									/>
									<ColumnDirective
										visible
										field='proposalValuesAverage'
										headerText='Opportunity Value (Avg)'
										template={(item: {
											proposalValuesAverage: ProposalValues
										}) =>
											formatCurrency(
												calculateGrossMarketValue(item.proposalValuesAverage)
											)
										}
									/>
									<ColumnDirective
										visible
										field='supplierContacts'
										headerText='Industry Contacts'
										template={(item: {
											supplierContacts: ISupplierContact[]
										}) => formatSupplierContacts(item.supplierContacts)}
									/>
									<ColumnDirective visible field='id' headerText='RFP ID' />
								</ColumnsDirective>
							) : (
								<ColumnsDirective>
									<ColumnDirective
										visible
										field='eventPlanName'
										headerText='RFP Name'
										template={formatEventPlanName}
									/>
									<ColumnDirective
										visible
										field='organizationName'
										headerText='Planner Org'
										template={formatOrganizationName}
									/>
									<ColumnDirective
										visible
										field='venueName'
										headerText='Hotel Name'
										template={formatHotelCity}
									/>
									<ColumnDirective
										visible
										field='venueCity'
										headerText='City'
									/>
									<ColumnDirective
										visible
										field='venueState'
										headerText='State'
									/>
									<ColumnDirective visible field='chainId' headerText='Chain' />
									<ColumnDirective visible field='brandId' headerText='Brand' />
									<ColumnDirective
										visible
										field='propertyManager.name'
										headerText='Property Manager'
										template={(item: {
											propertyManager: { name: string } | undefined
										}) => item.propertyManager?.name}
									/>
									<ColumnDirective
										visible
										field='propertySellers.name'
										headerText='Affiliate(s)'
										template={formatPropertySeller}
									/>
									<ColumnDirective
										visible
										field='destinationNames'
										headerText='Market(s)'
									/>
									<ColumnDirective
										visible
										field='proposalContact.name'
										headerText='Name'
										template={(item: {
											proposalContact: { name: string } | undefined
										}) => item.proposalContact?.name}
									/>
									<ColumnDirective
										visible
										field='proposalContact.email'
										headerText='Email'
										template={(item: {
											proposalContact: { email: string } | undefined
										}) => item.proposalContact?.email}
									/>
									<ColumnDirective
										visible
										field='sent'
										headerText='Opp Received'
										template={(item: { sent: string | null }) =>
											item.sent?.split('T')[0]
										}
									/>
									<ColumnDirective
										visible
										field='submitted'
										headerText='Proposal Submitted'
										template={(item: { submitted: string | null }) =>
											item.submitted?.split('T')[0]
										}
									/>
									<ColumnDirective
										visible
										field='won'
										headerText='Closed Won'
										template={(item: { won: string | null }) =>
											item.won?.split('T')[0]
										}
									/>
									<ColumnDirective
										visible
										field='status'
										headerText='Status'
										template={(item: { status: string }) =>
											ProposalRequestStatusMap[
												item.status as ProposalRequestStatusKey
											]?.hotelLabel ?? item.status
										}
									/>
									<ColumnDirective
										visible
										field='proposalValue'
										headerText='Value'
										template={(item: { proposalValue: number }) =>
											formatCurrency(item.proposalValue)
										}
									/>
									<ColumnDirective
										visible
										field='eventPlanType'
										headerText='Event Type'
										template={(item: { eventPlanType: string }) =>
											eventTypeOptions.find(o => o.value === item.eventPlanType)
												?.name
										}
									/>
									<ColumnDirective
										visible
										field='eventPlanGroupType'
										headerText='Group Type'
										template={(item: { eventPlanGroupType: string }) =>
											groupTypeOptions.find(
												o => o.value === item.eventPlanGroupType
											)?.name
										}
									/>
									<ColumnDirective
										visible
										field='eventPlanIndustryType'
										headerText='Industry'
										template={(item: { eventPlanIndustryType: string }) =>
											industryTypeOptions.find(
												o => o.value === item.eventPlanIndustryType
											)?.name
										}
									/>
									<ColumnDirective
										visible
										field='eventPlanId'
										headerText='RFP ID'
									/>
								</ColumnsDirective>
							)}
							<Inject services={[Sort, ColumnChooser, Page, Toolbar]} />
						</DataGrid>
					</div>
				</DialogComponent>
			) : null}
		</div>
	)
}

export default Dashboard
