import { render, screen, fireEvent } from '@testing-library/react'
import CopyText from '.'
import { describe, vi, beforeEach } from 'vitest'

vi.mock('navigator.clipboard', () => ({
	writeText: vi.fn().mockResolvedValue(null)
}))

describe.skip('CopyText Component', () => {
	const writeTextMock = vi.fn().mockResolvedValue(null)

	beforeEach(() => {
		Object.defineProperty(navigator, 'clipboard', {
			value: {
				writeText: writeTextMock
			},
			writable: true
		})
		vi.spyOn(console, 'error').mockImplementation(() => {})
	})

	it.skip('renders with the correct value', () => {
		render(<CopyText value='Test value' />)

		const copyTextBox = screen.getByTestId('copy-text-box')
		expect(copyTextBox).toBeInTheDocument()
		expect(copyTextBox).toHaveTextContent('Test value')
		expect(copyTextBox).toHaveAttribute('aria-readonly')
	})

	it.skip('renders with the empty value and calls writeText with empty string', async () => {
		render(<CopyText value={null} />)

		const copyTextBox = screen.getByTestId('copy-text-box')
		expect(copyTextBox).toHaveTextContent('')

		const button = screen.getByTestId('copy-text')
		fireEvent.click(button)

		// Wait for any asynchronous actions to complete
		await vi.waitFor(() => {
			// Verify that clipboard.writeText was called with an empty string
			expect(writeTextMock).toHaveBeenCalledWith('')
		})
	})

	it.skip('copies text to clipboard when button is clicked', async () => {
		render(<CopyText value='Copy this text' />)

		const button = screen.getByTestId('copy-text')
		fireEvent.click(button)
		vi.spyOn(console, 'log').mockImplementation(() => {})

		await vi.waitFor(() => {
			expect(writeTextMock).toHaveBeenCalledWith('Copy this text')
		})
		await vi.waitFor(() => {
			expect(console.log).toHaveBeenCalledWith('Copied')
		})
	})

	it.skip('logs an error when clipboard write fails', async () => {
		Object.defineProperty(navigator, 'clipboard', {
			value: {
				writeText: vi.fn().mockRejectedValueOnce(new Error('Error copying')) // Mock rejection with specific error
			},
			writable: true
		})
		render(<CopyText value='Error test' />)

		const button = screen.getByTestId('copy-text')
		fireEvent.click(button)

		await vi.waitFor(() => {
			expect(console.error).toHaveBeenCalledWith('Error copying')
		})
	})

	it.skip('shows tooltip on button hover', () => {
		render(<CopyText value='Tooltip test' />)

		const button = screen.getByTestId('copy-text')
		fireEvent.mouseOver(button)

		const tooltip = screen.getByText('Click to copy')
		expect(tooltip).toBeInTheDocument()
	})
})
