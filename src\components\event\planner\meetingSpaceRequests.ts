/* eslint-disable unicorn/no-keyword-prefix */
const dayNumberMultiple = 100_000
const indexMultiple = 1000

export function calculateSortIndex(dayNumber: number, index: number) {
	return (dayNumber + 1) * dayNumberMultiple + index * indexMultiple
}

export function updateSortIndex(
	dayNumber: number,
	sortIndex: number,
	newDayNumber: number
) {
	return (
		(sortIndex % ((dayNumber + 1) * dayNumberMultiple)) +
		(newDayNumber + 1) * dayNumberMultiple
	)
}
