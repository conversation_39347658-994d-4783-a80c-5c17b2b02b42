/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable unicorn/no-nested-ternary */
import {
	addBusinessDays,
	subBusinessDays,
	parseISO,
	isBefore,
	isAfter,
	addDays,
	formatISO,
	format
} from 'date-fns'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { useState, useEffect } from 'react'
import type { ValidationRuleType } from '../../common/sendRfpButton'
import SendRfpButton from '../../common/sendRfpButton'
import HSTooltip from 'components/tooltip'
import { faInfoCircle, faSearch } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSDatePicker from 'components/datePicker'
import type { ChangedEventArgs } from '@syncfusion/ej2-react-inputs'
import HSTextField from 'components/textField'
import HSIcon from 'components/HSIcon'
import HSTable from 'components/table'
import { formatRequestType } from 'pages/hotelier/proposal/tabs/summary/tabs/rfpSummary/helper'
import type { ProposalRequest } from 'models/proposalResponseMonitor'

const SendRfp = () => {
	const { eventInfo: event, setProperty, mergeProperties } = eventInfoStore()
	const { userProfile } = useUserProfileContext()

	const [validationRules, setValidationRules] = useState<
		Record<
			ValidationRuleType,
			{ label: string; min: Date | null; max: Date | null }
		>
	>({
		responsesDueDate: { label: 'Responses Due', min: null, max: null },
		selectionDate: { label: 'Estimated Decision Date', min: null, max: null }
	})

	const [submitValidationErrors, setSubmitValidationErrors] = useState<
		Record<ValidationRuleType, string | null>
	>({
		responsesDueDate: null,
		selectionDate: null
	})

	const [pendingProposalRequests, setPendingProposalRequests] = useState<
		ProposalRequest[]
	>([])
	const [existingProposalRequests, setExistingProposalRequests] = useState<
		ProposalRequest[]
	>([])

	useEffect(() => {
		if (userProfile) {
			const d = addBusinessDays(
				new Date(),
				userProfile.minimumResponsesDueDays ?? 4
			)
			d.setHours(0, 0, 0, 0)
			setValidationRules(s => ({
				...s,
				responsesDueDate: {
					...s.responsesDueDate,
					min: d
				}
			}))
		}
	}, [userProfile, userProfile?.minimumResponsesDueDays])

	useEffect(() => {
		if (event?.startDate) {
			setValidationRules(s => ({
				...s,
				responsesDueDate: {
					...s.responsesDueDate,
					max: subBusinessDays(parseISO(event.startDate), 1)
				},
				selectionDate: {
					...s.selectionDate,
					max: subBusinessDays(parseISO(event.startDate), 1)
				}
			}))
		}
	}, [event?.startDate])

	useEffect(() => {
		if (event?.responsesDueDate) {
			setValidationRules(s => ({
				...s,
				selectionDate: {
					...s.selectionDate,
					min: event.responsesDueDate
						? addBusinessDays(parseISO(event.responsesDueDate), 1)
						: null
				}
			}))
		}
	}, [event?.responsesDueDate])

	useEffect(() => {
		if (event && userProfile) {
			const responsesDueDate = event.responsesDueDate
				? parseISO(event.responsesDueDate)
				: null
			const selectionDate = event.selectionDate
				? parseISO(event.selectionDate)
				: null

			const messageR =
				responsesDueDate === null
					? 'required'
					: event.status === 'New' &&
						  validationRules.responsesDueDate.min &&
						  isBefore(responsesDueDate, validationRules.responsesDueDate.min)
						? `must be at least ${userProfile.minimumResponsesDueDays} business day${userProfile.minimumResponsesDueDays === 1 ? '' : 's'} from today`
						: validationRules.responsesDueDate.max &&
							  isAfter(responsesDueDate, validationRules.responsesDueDate.max)
							? 'must be before the event start date'
							: null

			const messageS =
				selectionDate === null
					? 'required'
					: responsesDueDate && isBefore(selectionDate, responsesDueDate)
						? 'must be after Responses Due Date'
						: validationRules.selectionDate.max &&
							  isAfter(selectionDate, validationRules.selectionDate.max)
							? 'must be before the event start date'
							: null

			setSubmitValidationErrors(s => ({
				...s,
				responsesDueDate: messageR,
				selectionDate: messageS
			}))
		}
	}, [
		validationRules,
		userProfile?.minimumResponsesDueDays,
		event?.status,
		event?.responsesDueDate,
		event?.selectionDate,
		event,
		userProfile
	])

	useEffect(() => {
		if (event?.id && event.proposalRequests) {
			setPendingProposalRequests(
				event.proposalRequests
					.filter(pr => {
						if (pr.supplierContacts.some(sc => sc.id === pr.createdBy)) {
							return false
						}
						if (
							!userProfile?.isAdmin &&
							pr.createdBy?.endsWith('@myhopskip.com')
						) {
							return false
						}
						return pr.status === 'Pending'
					})
					.reduce((a: ProposalRequest[], c: ProposalRequest) => {
						if (
							!a.some(proposalRequest => proposalRequest.venueId === c.venueId)
						) {
							a.push(c)
						}
						return a
					}, [])
			)

			setExistingProposalRequests(
				event.editingSentDateTime
					? event.proposalRequests
							.filter(pr => !['Pending', 'Removed'].includes(pr.status ?? ''))
							.reduce((a: ProposalRequest[], c: ProposalRequest) => {
								if (
									!a.some(
										proposalRequest => proposalRequest.venueId === c.venueId
									)
								) {
									a.push(c)
								}
								return a
							}, [])
					: []
			)
		}
	}, [
		event?.editingSentDateTime,
		event?.id,
		event?.proposalRequests,
		userProfile?.isAdmin
	])

	const onChangeDate = (
		name: 'startDate' | 'responsesDueDate' | 'selectionDate',
		value: Date | undefined | null
	) => {
		if (event && value) {
			if (
				name === 'startDate' &&
				(event.endDate === null || !isAfter(parseISO(event.endDate), value))
			) {
				mergeProperties({
					startDate: formatISO(value, { representation: 'date' }),
					endDate: formatISO(addDays(value, (event.totalDays || 2) - 1), {
						representation: 'date'
					})
				})
			} else if (
				name === 'responsesDueDate' &&
				(event.selectionDate === null ||
					!isAfter(parseISO(event.selectionDate), value))
			) {
				mergeProperties({
					responsesDueDate: formatISO(value, { representation: 'date' }),
					selectionDate: formatISO(addDays(value, 7), {
						representation: 'date'
					})
				})
			} else {
				setProperty(name, formatISO(value, { representation: 'date' }), 'date')
			}
		}
	}

	return (
		<div className='flex flex-col'>
			<div className='border-b px-6 py-4'>
				<div className='flex items-center justify-between gap-4'>
					<div className='text-lg font-semibold text-gray-900'>Send RFP</div>
					<SendRfpButton
						submitValidationErrors={submitValidationErrors}
						validationRules={validationRules}
					/>
				</div>
			</div>
			<div className='flex'>
				<div className='w-2/3 p-6'>
					<div className='card h-full'>
						<div className='flex flex-col gap-2 p-4'>
							<div className='text-sm font-bold text-gray-700'>RFP Summary</div>
							<div className='flex gap-4'>
								<div className='w-1/2'>
									<div className='flex flex-col gap-2 text-sm font-normal'>
										<div className='flex items-center gap-2'>
											<div className='text-gray-400'>Request Type:</div>
											<div className='text-gray-600'>
												{event ? formatRequestType(event) : ''}
											</div>
										</div>
										<div className='flex items-center gap-2'>
											<div className='text-gray-400'>Dates Requested:</div>

											<div className='flex items-center gap-2'>
												<div>
													{event?.alternateDates?.some(ad => ad.startDate) ? (
														<div className='flex items-center gap-2'>
															<div className='text-gray-600'>
																Multiple equal dates
															</div>
															<HSTooltip content='Multiple equal dates'>
																<FontAwesomeIcon
																	icon={faInfoCircle}
																	className='text-gray-400'
																/>
															</HSTooltip>
														</div>
													) : (
														<div className='flex items-center gap-2'>
															<div className='flex items-center gap-2'>
																<div className='text-gray-500'>
																	{event?.startDate
																		? format(
																				parseISO(event.startDate),
																				'MMM d, yyyy'
																			)
																		: ''}
																</div>
																<div className='text-xs font-medium text-gray-400'>
																	{event?.startDate
																		? format(parseISO(event.startDate), 'EEEE')
																		: ''}
																</div>
															</div>
															<div>-</div>
															<div className='flex items-center gap-2'>
																<div className='text-gray-500'>
																	{event?.endDate
																		? format(
																				parseISO(event.endDate),
																				'MMM d, yyyy'
																			)
																		: ''}
																</div>
																<div className='text-xs font-medium text-gray-400'>
																	{event?.endDate
																		? format(parseISO(event.endDate), 'EEEE')
																		: ''}
																</div>
															</div>
														</div>
													)}
												</div>
											</div>
										</div>
										<div className='flex items-center gap-2'>
											<div className='text-gray-400'>Hotels Requested:</div>
											{pendingProposalRequests.length > 0 ? (
												<div className='flex items-center gap-2'>
													<div className='text-gray-600'>
														{pendingProposalRequests.length}
													</div>
													<HSTooltip content='Multiple equal dates'>
														<FontAwesomeIcon
															icon={faInfoCircle}
															className='text-gray-400'
														/>
													</HSTooltip>
												</div>
											) : null}
										</div>
										<div className='flex items-center gap-2'>
											<div className='text-gray-400'>
												Destinations Requested:
											</div>
											<div className='text-gray-600'>
												{existingProposalRequests.length}
											</div>
										</div>
									</div>
								</div>
								<div className='border-r' />
								<div className='flex flex-col gap-2 text-sm'>
									<div className='flex items-center gap-2'>
										<div className='text-gray-400'>Peak Rooms:</div>
										<div className='text-gray-600'>110 rooms</div>
									</div>
									<div className='flex items-center gap-2'>
										<div className='text-gray-400'>Total Room Nights:</div>
										<div className='text-gray-600'>265 room nights</div>
									</div>
									<div className='flex items-center gap-2'>
										<div className='text-gray-400'>Largest Event Space:</div>
										<div className='text-gray-600'>4,800 sq. ft.</div>
									</div>
									<div className='flex items-center gap-2'>
										<div className='text-gray-400'>Peak Meeting Rooms:</div>
										<div className='text-gray-600'>2 meeting rooms</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className='flex w-1/3 flex-col gap-4 p-6'>
					<div className='card'>
						<div className='flex flex-col gap-2'>
							<div className='flex items-center gap-4 p-4'>
								<div className='flex flex-col gap-2'>
									<div className='flex items-center gap-2'>
										<div className='text-sm font-normal text-gray-700'>
											Response Due
										</div>
										<HSTooltip content='Response Due'>
											<FontAwesomeIcon
												icon={faInfoCircle}
												className='text-gray-700'
											/>
										</HSTooltip>
									</div>
									<HSDatePicker
										value={
											event?.responsesDueDate
												? parseISO(event.responsesDueDate)
												: undefined
										}
										min={
											event?.created
												? addBusinessDays(
														parseISO(event.created.split('T')[0]),
														userProfile?.minimumResponsesDueDays ?? 3
													)
												: undefined
										}
										max={
											event?.startDate
												? subBusinessDays(
														parseISO(event.startDate.split('T')[0]),
														1
													)
												: undefined
										}
										name='responsesDueDate'
										iserror={!event?.responsesDueDate}
										change={(event_: ChangedEventArgs) =>
											onChangeDate('responsesDueDate', event_.value)
										}
										showClearButton={false}
										format='MMM dd, yyyy'
									/>
								</div>
								<div className='flex flex-col gap-2'>
									<div className='flex items-center gap-2'>
										<div className='text-sm font-normal text-gray-700'>
											Decision Date
										</div>
										<HSTooltip content='Decision Date'>
											<FontAwesomeIcon
												icon={faInfoCircle}
												className='text-gray-700'
											/>
										</HSTooltip>
									</div>
									<HSDatePicker
										name='selectionDate'
										value={
											event?.selectionDate
												? parseISO(event.selectionDate)
												: undefined
										}
										min={
											event?.responsesDueDate
												? addBusinessDays(
														parseISO(event.responsesDueDate.split('T')[0]),
														1
													)
												: undefined
										}
										max={
											event?.startDate
												? subBusinessDays(
														parseISO(event.startDate.split('T')[0]),
														1
													)
												: undefined
										}
										iserror={!event?.selectionDate}
										change={(event_: ChangedEventArgs) =>
											onChangeDate('selectionDate', event_.value)
										}
										showClearButton={false}
										format='MMM dd, yyyy'
									/>
								</div>
							</div>
							<div className='border-b' />
							<div className='flex flex-col gap-1 p-4'>
								<div className='text-sm font-medium text-gray-900'>
									RFP Contacts
								</div>
								<div className='flex items-center gap-4 text-sm font-normal'>
									<div className='flex flex-col border-r pr-4'>
										<div className='flex items-center gap-2'>
											<div className='text-gray-400'>DMO/CVBs:</div>
											<div className='text-gray-600'>7</div>
										</div>
										<div className='flex items-center gap-2'>
											<div className='text-gray-400'>National Sales:</div>
											<div className='text-gray-600'>9</div>
										</div>
									</div>
									<div className='self-start'>
										<div className='flex items-center gap-2'>
											<div className='text-gray-400'>Sales Affiliate:</div>
											<div className='text-gray-600'>14</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className='flex items-center justify-between p-6'>
				<div className='flex flex-col gap-2'>
					<div className='text-lg font-semibold text-gray-900'>
						Hotels I&apos;ve selected to receive this RFP
					</div>
					<div className='text-sm font-medium text-gray-900'>6 Hotels</div>
				</div>
				<div className='w-80'>
					<HSTextField
						showClearButton
						placeholder='Search'
						icon={HSIcon(faSearch)}
						value=''
						onChange={() => {}}
					/>
				</div>
			</div>
			<div className='p-6'>
				<HSTable
					rows={[]}
					columns={[
						{
							field: 'name',
							headerText: 'Hotel Name'
						},
						{
							field: 'name',
							headerText: 'Location'
						},
						{
							field: 'name',
							headerText: 'Hotel Contacts'
						},
						{
							field: 'name',
							headerText: 'Status'
						},
						{
							field: 'name',
							headerText: 'Recent Activity'
						}
					]}
				/>
			</div>
		</div>
	)
}

export default SendRfp
