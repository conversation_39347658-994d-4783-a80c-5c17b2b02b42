/* eslint-disable react/no-array-index-key */
/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable unicorn/no-keyword-prefix */
import type React from 'react'
import { useEffect, useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faChevronDown, faChevronUp } from '@fortawesome/pro-regular-svg-icons'

interface CustomHSAccordionItemProperties {
	title: React.ReactNode | string
	content: React.ReactNode | string
	isExpanded?: boolean
}

interface CustomHSAccordionProperties {
	items: CustomHSAccordionItemProperties[]
	className?: string
}

const CustomHSAccordionItem: React.FC<CustomHSAccordionItemProperties> = ({
	title,
	content,
	isExpanded: initialIsExpanded = false
}) => {
	const [isExpanded, setIsExpanded] = useState(false)

	const toggleAccordion = () => {
		setIsExpanded(!isExpanded)
	}

	useEffect(() => {
		setIsExpanded(initialIsExpanded)
	}, [initialIsExpanded])

	return (
		<div className='mb-2'>
			<button
				type='button'
				className='focus:shadow-outline flex w-full items-center justify-between rounded-t-md px-4 py-2 text-left font-semibold hover:bg-gray-100 focus:outline-none'
				onClick={toggleAccordion}
			>
				<div className='flex items-center'>
					{isExpanded ? (
						<FontAwesomeIcon
							icon={faChevronUp}
							className='mr-2 text-gray-700'
						/>
					) : (
						<FontAwesomeIcon
							icon={faChevronDown}
							className='mr-2 text-gray-700'
						/>
					)}
					{title}
				</div>
			</button>
			{isExpanded ? (
				<div className='relative'>
					<div className='border-1 absolute left-6 top-0 h-full border-l border-gray-200' />
					<div className='ml-6 rounded-b-md bg-white px-4 py-2'>{content}</div>
				</div>
			) : null}
		</div>
	)
}

const CustomHSAccordion: React.FC<CustomHSAccordionProperties> = ({
	items,
	className
}) => (
	<div className={`custom-hs-accordion ${className || ''}`}>
		{items.map((item, index) => (
			<CustomHSAccordionItem key={index} {...item} />
		))}
	</div>
)

export default CustomHSAccordion
