{"name": "hopskip-sandbox", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "vite build", "commit": "cz", "dev": "vite dev --open --port 3000", "prepare": "husky", "preview": "vite preview", "preview:test": "start-server-and-test preview http://localhost:3000", "test:ui": "vitest --ui", "test": "vitest", "test:ci": "vitest run", "format": "prettier -uw --cache --ignore-path .gitignore .", "run-tsc": "tsc", "run-eslint": "eslint --cache --fix --ignore-path .gitignore --ext .ts,.tsx .", "run-stylelint": "stylelint --cache --fix --ignore-path .gitignore **/*.css", "lint": "run-p run-tsc run-eslint run-stylelint", "validate": "run-p lint test:ci", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@azure/cosmos": "^4.4.1", "@azure/msal-browser": "^4.12.0", "@azure/msal-react": "^3.0.12", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/pro-duotone-svg-icons": "^6.7.2", "@fortawesome/pro-light-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@hookform/resolvers": "^3.10.0", "@microsoft/applicationinsights-react-js": "^18.3.6", "@microsoft/applicationinsights-web": "^3.3.8", "@microsoft/signalr": "^8.0.7", "@pqina/pintura": "^8.92.14", "@pqina/react-pintura": "^9.0.4", "@react-google-maps/api": "^2.20.6", "@segment/analytics-next": "^1.80.0", "@stripe/stripe-js": "^5.10.0", "@syncfusion/ej2-base": "^26.2.10", "@syncfusion/ej2-lists": "^26.2.14", "@syncfusion/ej2-popups": "^26.2.11", "@syncfusion/ej2-react-base": "^26.2.10", "@syncfusion/ej2-react-buttons": "^26.2.10", "@syncfusion/ej2-react-calendars": "^26.2.12", "@syncfusion/ej2-react-dropdowns": "^26.2.13", "@syncfusion/ej2-react-grids": "^26.2.14", "@syncfusion/ej2-react-inputs": "^26.2.14", "@syncfusion/ej2-react-navigations": "^26.2.12", "@syncfusion/ej2-react-notifications": "^26.2.10", "@syncfusion/ej2-react-popups": "^26.2.11", "@syncfusion/ej2-react-richtexteditor": "^26.2.14", "@syncfusion/ej2-react-splitbuttons": "^26.2.10", "@tanstack/react-query": "5.77.2", "@testing-library/react": "16.3.0", "@twilio/conversations": "^2.6.2", "@vis.gl/react-google-maps": "^1.5.2", "@vitejs/plugin-react-swc": "^3.9.0", "@vitest/coverage-istanbul": "3.1.4", "axios": "^1.9.0", "date-fns": "^4.1.0", "es-toolkit": "^1.38.0", "flowbite": "^3.1.2", "flowbite-react": "^0.10.2", "history": "^5.3.0", "immer": "^10.1.1", "libphonenumber-js": "^1.12.7", "posthog-js": "^1.237.1", "react": "^19.1.0", "react-date-range": "^2.0.1", "react-device-detect": "^2.2.3", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-ga4": "^2.1.0", "react-hook-form": "^7.56.1", "react-international-phone": "^4.5.0", "react-router-dom": "^6.30.1", "react-slick": "^0.30.3", "react-text-mask": "^5.5.0", "react-toastify": "^11.0.5", "react-window": "^1.8.11", "recharts": "^2.15.3", "text-mask-addons": "^3.8.0", "tinycolor2": "^1.6.0", "use-sync-external-store": "^1.5.0", "uuid": "^11.1.0", "vitest": "^3.1.4", "zod": "^3.25.30", "zustand": "^5.0.5"}, "devDependencies": {"@nabla/vite-plugin-eslint": "2.0.5", "@playwright/test": "^1.52.0", "@tailwindcss/forms": "0.5.10", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/user-event": "14.6.1", "@types/css-mediaquery": "0.1.4", "@types/node": "^20.17.47", "@types/react": "^19.1.4", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^19.1.5", "@types/react-slick": "^0.23.13", "@types/react-text-mask": "^5.4.14", "@types/tinycolor2": "^1.4.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "autoprefixer": "10.4.21", "css-mediaquery": "0.1.2", "cssnano": "^7.0.6", "eslint": "8.57.1", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-base": "15.0.0", "eslint-config-airbnb-typescript": "18.0.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-react-prefer-function-component": "3.4.0", "eslint-plugin-testing-library": "6.5.0", "eslint-plugin-unicorn": "56.0.1", "husky": "9.1.7", "jsdom": "26.1.0", "lint-staged": "15.5.2", "msw": "2.7.5", "npm-run-all": "4.1.5", "postcss": "8.5.3", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.11", "rollup-plugin-visualizer": "^6.0.0", "sharp": "^0.34.1", "start-server-and-test": "2.0.12", "stylelint": "16.19.1", "stylelint-config-standard": "37.0.0", "svgo": "^3.3.2", "tailwindcss": "^3.4.17", "typescript": "5.8.3", "vite": "6.3.5", "vite-plugin-image-optimizer": "^1.1.8", "vite-plugin-preload": "^0.4.2", "vite-plugin-pwa": "^1.0.0", "vite-tsconfig-paths": "5.1.4", "vitest-canvas-mock": "^0.3.3", "whatwg-fetch": "3.6.20", "workbox-build": "7.3.0", "workbox-window": "7.3.0"}, "browserslist": {"production": "Edge >= 18, Firefox >= 60, Chrome >= 61, Safari >= 11, Opera >= 48", "development": ["last 1 chrome version", "last 1 firefox version"]}, "lint-staged": {"*": "prettier -uw --cache", "*.{ts,tsx}": ["eslint --cache --fix", "vitest related --run --coverage=false"]}, "pnpm": {"overrides": {"headers-polyfill": "4.0.3"}}}