# Opt out of CI triggers
trigger: none

pr:
  branches:
    include:
      - '*' # All branches
  paths:
    include:
      - '*' # All files
    exclude:
      - 'pipelines/**' # Exclude pipeline folder
      - '**/*.toml' # Exclude all toml files
      - '**/*.yaml' # Exclude all yaml files
      - '**/*.yml' # Exclude all yml files
  drafts: true # Allow draft PRs
  autoCancel: true

resources:
  repositories:
    - repository: self
      type: git
      name: browser-app-v2

stages:
  - stage: pr_agent
    displayName: 'PR Agent Stage'
    jobs:
      - job: pr_agent_job
        displayName: 'PR Agent Job'
        pool:
          vmImage: 'ubuntu-latest'
        container:
          image: codiumai/pr-agent:latest
          options: --entrypoint ""
        variables:
          - group: pr_agent
        steps:
          - script: |
              echo "Running PR Agent action step"

              # Construct PR_URL
              PR_URL="${SYSTEM_COLLECTIONURI}${SYSTEM_TEAMPROJECT}/_git/${BUILD_REPOSITORY_NAME}/pullrequest/${SYSTEM_PULLREQUEST_PULLREQUESTID}"
              echo "PR_URL=$PR_URL"

              # Extract organization URL from System.CollectionUri
              ORG_URL=$(echo "$(System.CollectionUri)" | sed 's/\/$//') # Remove trailing slash if present
              echo "Organization URL: $ORG_URL"

              export azure_devops__org="$ORG_URL"
              export config__git_provider="azure"
              export config__model="gemini/gemini-1.5-flash"
              export config__fallback_models=["gemini/gemini-2.0-flash-exp", "gemini/gemini-1.5-pro"]
              export ignore__glob=['*.yaml','*.md','*.txt','*.yml']

              pr-agent --pr_url="$PR_URL" describe
              pr-agent --pr_url="$PR_URL" review
              pr-agent --pr_url="$PR_URL" improve
            env:
              azure_devops__pat: $(ado_pat)
              GEMINI_API_KEY: $(gemini_api_key)
              GROQ_API_KEY: $(groq_api_key)
            displayName: 'Run Qodo Merge'
