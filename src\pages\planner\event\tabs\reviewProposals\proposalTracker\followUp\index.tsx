import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import MultiSelectDropdown from 'components/multiSelect'
import StatusTimeline from 'components/statusTimeline'
import HSTextEditor from 'components/textEditor'
import HSTextField from 'components/textField'
import { Drawer } from 'flowbite-react'
import type { ProposalRequest } from 'models/proposalResponseMonitor'

interface IFollowUp {
	showDetails: boolean
	setDetails: React.Dispatch<React.SetStateAction<boolean>>
	proposalRequest: ProposalRequest
	isBulk: boolean
}

const FollowUpEmail = (properties: IFollowUp) => {
	const { showDetails, setDetails, proposalRequest, isBulk } = properties

	return (
		<div>
			<HSDrawer
				position='right'
				onClose={() => {
					setDetails(false)
				}}
				open={showDetails}
				style={{ width: '70%' }}
			>
				<Drawer.Header titleIcon={() => null} title='Send Follow-up Email' />
				<Drawer.Items
					style={{ height: 'calc(100vh - 10rem)' }}
					className='overflow-auto'
				>
					<div className={isBulk ? '' : 'flex gap-2'}>
						<div
							className={`flex w-2/3 flex-col gap-4 px-4 ${isBulk ? 'w-full' : ''}`}
						>
							<div className='card flex w-full items-center justify-between gap-4 p-4'>
								<div className='text-sm font-medium text-gray-900'>
									Hotels to Follow-up
								</div>
								<div className='text-sm font-normal text-gray-600'>
									W Hotel Philadelphia
								</div>
							</div>
							<div>
								<MultiSelectDropdown
									options={[]}
									onChange={() => {}}
									selectedOptions={[]}
									label='Recipients'
								/>
							</div>
							<div>
								<MultiSelectDropdown
									options={[]}
									onChange={() => {}}
									selectedOptions={[]}
									label='CC'
								/>
							</div>
							<HSTextField
								label='Subject'
								value=''
								onChange={() => {}}
								placeholder='Enter Subject'
							/>
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-medium text-gray-900'>
									Message Preview
								</div>
								<div className='card flex flex-col gap-3 p-4'>
									<div className='flex flex-col items-center gap-3'>
										<div className='text-3xl font-bold text-gray-500'>
											Joe from Meeting Encore Ltd. is following up with you
										</div>
										<div className='text-sm font-medium text-gray-500'>
											RFP CODE: HSZN5S4C4X6F
										</div>
										<div className='text-sm font-medium text-gray-500'>
											Joe from Meeting Encore Ltd. would like a status update on
											the Novo Nordisk Canada Inc. Investigator Meeting RFP.
										</div>
									</div>
									<div className='flex flex-col gap-2'>
										<div className='text-sm font-medium text-gray-900'>
											Custom Message (Editable)
										</div>
										<HSTextEditor
											value=''
											onChange={() => {}}
											placeholder='Add the Body of the contract clause...'
										/>
									</div>
									<div className='flex items-center justify-center text-lg font-normal text-gray-500'>
										Respond to Joe?
									</div>
								</div>
							</div>
							<div className='flex items-center gap-4'>
								<HSButton color='light' size='xs' onClick={() => {}}>
									Cancel
								</HSButton>
								<HSButton color='primary' size='xs' onClick={() => {}}>
									Send Email
								</HSButton>
							</div>
						</div>
						{isBulk ? null : (
							<div className='flex w-1/3 flex-col gap-4'>
								<div className='text-sm font-medium text-gray-900'>
									Request history
								</div>
								<StatusTimeline
									proposalRequest={proposalRequest}
									showInfoCard={false}
									hidePlannerEmail
									hideNotes={false}
								/>
							</div>
						)}
					</div>
				</Drawer.Items>
			</HSDrawer>
		</div>
	)
}

export default FollowUpEmail
