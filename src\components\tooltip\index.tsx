/* eslint-disable unicorn/no-keyword-prefix */
import type { TooltipProps } from 'flowbite-react'
import { Tooltip } from 'flowbite-react'
import { memo } from 'react'

const HSTooltip = memo((properties: TooltipProps) => {
	const { children, className, ...rest } = properties
	return (
		<Tooltip {...rest} className={`z-[100] flex max-w-sm ${className}`}>
			{children}
		</Tooltip>
	)
})

export default HSTooltip
