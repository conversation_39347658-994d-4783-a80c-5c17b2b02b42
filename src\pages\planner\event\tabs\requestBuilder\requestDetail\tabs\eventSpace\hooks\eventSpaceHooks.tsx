/* eslint-disable no-underscore-dangle */
/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable @typescript-eslint/naming-convention */
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { TargetProperties } from '../templates'
import type {
	EventPlan,
	IFoodAndBeverageRequest,
	IMeetingSpaceRequest
} from 'models/proposalResponseMonitor'
import { roomLayoutOptions } from 'lib/helpers/roomLayouts'
import analytics from 'lib/analytics/segment/load'
import api from 'lib/interceptor/axios.interceptor'
import type { Attachment } from 'lib/store/attachmentsStore'
import useAttachmentsStore from 'lib/store/attachmentsStore'
import type { AxiosResponse } from 'axios'
import { toast } from 'react-toastify'

const useEventSpaceHooks = () => {
	const {
		eventInfo,
		mergeProperties,
		setProperty,
		replaceInArray,
		addToArray
	} = eventInfoStore()
	const { setObject: setAttachments, attachments } = useAttachmentsStore()

	const onChangeMeetingSpace = (target: TargetProperties) => {
		const { name, targetType, value } = target
		if (name === 'meetingSpaceRequired' && eventInfo) {
			let { type } = eventInfo
			if (value) {
				if (type === 'roomsonly') {
					type = null
				}
			} else {
				type = 'roomsonly'
			}

			mergeProperties({
				[name as keyof EventPlan]: value,
				type: targetType
			})
		}
	}

	const onChange = (id: string | null, target: TargetProperties) => {
		const { name, targetType, value } = target
		let value_
		switch (targetType) {
			case 'number': {
				value_ = value ? Number(value) : null
				break
			}
			default: {
				value_ = value
				break
			}
		}
		if (id) {
			const index =
				eventInfo?.meetingSpaceRequests?.findIndex(msr => msr.id === id) ?? 0
			if (eventInfo) {
				const msr = {
					...eventInfo.meetingSpaceRequests?.at(index),
					[name]: value_
				}
				if (name === 'layoutStyle' && !msr.areaTotalEdited) {
					msr.areaPerAttendee = (
						roomLayoutOptions[value as keyof typeof roomLayoutOptions] || {
							areaPerAttendee: null
						}
					).areaPerAttendee
					msr.areaTotal = (msr.areaPerAttendee || 0) * (msr.capacity || 0)
				}
				if (name === 'capacity' && !msr.areaTotalEdited) {
					msr.areaTotal = (msr.areaPerAttendee || 0) * (msr.capacity || 0)
				}
				if (name === 'areaTotal') {
					msr.areaTotalEdited = true
				}

				replaceInArray('meetingSpaceRequests', msr, index)
			}
		} else {
			setProperty(name, value_, targetType)
		}
	}

	const onChangeMsrTime = (
		id: string,
		prefix: 'start' | 'end',
		hours: number,
		minutes: number
	) => {
		if (eventInfo) {
			const h = hours ? Number(hours) : 0
			const m = minutes ? Number(minutes) : 0
			if (prefix === 'end' && hours === 0) {
				return
			}
			const index =
				eventInfo.meetingSpaceRequests?.findIndex(msr => msr.id === id) ?? 0
			const msrToEdit = eventInfo.meetingSpaceRequests?.find(
				msr => msr.id === id
			)

			if (msrToEdit) {
				const msr = {
					...msrToEdit,
					[`${prefix}Time`]: h,
					[`${prefix}Minutes`]: m
				}
				replaceInArray('meetingSpaceRequests', msr, index)
			}
		}
	}

	const addUpdateRequest = (msr: IMeetingSpaceRequest) => {
		if (eventInfo) {
			const index =
				eventInfo.meetingSpaceRequests?.findIndex(
					request => request.id === msr.id
				) ?? 0
			if (index >= 0) {
				replaceInArray('meetingSpaceRequests', msr, index)
			} else {
				addToArray('meetingSpaceRequests', msr)
				analytics.track(`Meeting Space Added to RFP`)
			}
		}
	}

	const addUpdateFoodAndBeverage = (fbrs: IFoodAndBeverageRequest[]) => {
		if (eventInfo) {
			for (const fbr of fbrs) {
				const index =
					eventInfo.foodAndBeverageRequests?.findIndex(
						request => request.id === fbr.id
					) ?? 0
				if (index >= 0) {
					replaceInArray('foodAndBeverageRequests', fbr, index)
				} else {
					addToArray('foodAndBeverageRequests', fbr)
					analytics.track(`Food and Beverage Added to RFP`)
				}
			}
		}
	}

	const setPropertyFoodAndBeverages = (fbrs: IFoodAndBeverageRequest[]) => {
		setProperty('foodAndBeverageRequests', fbrs)
	}

	const onAddAttachments = (
		files: File[],
		visibleToVenues: boolean,
		signatureRequired: boolean,
		documentContext: { venueId: string } | null,
		meetingSpaceRequestId: string
	) => {
		toast.info(`Uploading ${files.length} file(s)...`)
		const formData = new FormData()
		let f = 0
		for (const file of files) {
			formData.append(`file${f}`, file, file.name)
			formData.append(
				`props${f}`,
				JSON.stringify({ visibleToVenues, signatureRequired, documentContext })
			)
			f += 1
		}
		const eventId = eventInfo?.id

		const url = meetingSpaceRequestId
			? `/doc/api/attachments/eventplans/${eventId}/${documentContext ? `${documentContext.venueId}/` : ''}msr/${meetingSpaceRequestId}`
			: `/doc/api/attachments/eventplans/${eventId}/${documentContext ? documentContext.venueId : ''}`

		api
			.post(url, formData)
			.then((response: AxiosResponse<Attachment[]>) => {
				toast.success(`${files.length} files uploaded successfully`)
				const ids = new Set(response.data.map(d => d.id))
				setAttachments([
					...attachments.filter(a => !ids.has(a.id)),
					...response.data
				])
			})
			.catch((error: unknown) => {
				console.error(error)
			})
	}

	const onCopyAttachments = (
		copyOperations: {
			sourceId: string
			targetId: string
		}[]
	) => {
		const url = `/doc/api/attachments/eventplans/${eventInfo?.id}/msr/all/copy`
		api
			.post(url, copyOperations)
			.then((response: AxiosResponse<Attachment[]>) => {
				setAttachments(response.data.filter(a => !a.name.includes('_signed')))
			})
			.catch((error: unknown) => {
				console.error(error)
			})
	}

	const onDeleteAttachments = (
		attachmentsToDelete: Attachment[],
		hideToast = true
	) => {
		if (!hideToast) {
			toast.info('Deleting attachment(s)...')
		}

		api
			.delete(`/doc/api/attachments/eventplans/${eventInfo?.id}`, {
				data: attachmentsToDelete.map(a => a.id)
			})
			.then((response: AxiosResponse<Attachment[]>) => {
				if (!hideToast) {
					toast.success('Attachment(s) deleted.')
				}
				setAttachments([...response.data])
			})
			.catch((error: unknown) => {
				console.error(error)
			})
	}

	return {
		onChangeMeetingSpace,
		onChange,
		onChangeMsrTime,
		addUpdateRequest,
		addUpdateFoodAndBeverage,
		onAddAttachments,
		onCopyAttachments,
		onDeleteAttachments,
		setPropertyFoodAndBeverages
	}
}

export default useEventSpaceHooks
