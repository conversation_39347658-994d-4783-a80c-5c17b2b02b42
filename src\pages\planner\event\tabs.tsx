/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable react/no-array-index-key */
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSTooltip from 'components/tooltip'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import type React from 'react'
import { useEffect, useMemo, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import EventPlannerRequestDetail from './tabs/requestBuilder/requestDetail'
import PlannerProposalTracker from './tabs/reviewProposals/proposalTracker'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import Search from './tabs/search'
import EventVenuePreferences from './tabs/requestBuilder/preferences'
import EventReports from './tabs/reports'
import PlannerHotelsAvailable from './tabs/reviewProposals/proposalTracker/comparableHotels/hotelsAvailable'
import RfpContacts from './tabs/requestBuilder/rfpContacts'
import SendRfp from './tabs/reviewSendRfp'
import useEventPlanValidationErrors from 'lib/store/plannerEvent/validation'
import useAttachmentsStore from 'lib/store/attachmentsStore'
import Agreement from './tabs/contract/agreement/index'
import CostSavings from './tabs/contract/costSavings/index'
import PlannerProposalDashboard from './tabs/reviewProposals/proposalDashboard'
import { faCircleExclamation } from '@fortawesome/pro-regular-svg-icons'
import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import useRfpBuilder from 'lib/customHook/eventPlan/rfpBuilder'

interface IChapterTab {
	chapterName: string
	chapterIcon?: string
	path: string
	visible?: boolean
	items: {
		itemName: string
		itemIcon?: string
		component?: React.ReactNode
		path: string
		error?: string[] | null
	}[]
}

const getChapterTabs = (
	isVisible: boolean,
	infoType: string | undefined,
	eventInfo: EventPlan | undefined,
	addRemoveProposalRequests: (
		suggestHotels: ProposalRequest[],
		callback: () => void,
		isAdmin: boolean
	) => Promise<void>,
	error?: {
		general: string[] | null
		roomblocks: string[] | null
		mespace: string[] | null
		contractTerms: string[] | null
		hotels: string[] | null
		contacts: string[] | null
	}
): IChapterTab[] => [
	{
		chapterName: 'Request Builder',

		items: [
			{
				itemName: 'Request Details',
				path: 'details',
				component: <EventPlannerRequestDetail />,
				error: [
					...(error?.general ?? []),
					...(error?.roomblocks ?? []),
					...(error?.mespace ?? []),
					...(error?.contractTerms ?? [])
				]
			},
			{
				itemName: 'Venue Preferences',
				path: 'venue-preferences',
				component: <EventVenuePreferences />
			},
			{
				itemName: 'Add Hotels',
				path: 'add-hotels',
				component: (
					<Search
						eventInfo={eventInfo}
						addRemoveProposalRequests={addRemoveProposalRequests}
					/>
				),
				error: error?.hotels ?? []
			},
			{
				itemName: 'RFP Contacts',
				path: 'rfp-contacts',
				component: <RfpContacts />,
				error: error?.contacts ?? []
			}
		],
		path: 'builder',
		visible: true
	},
	{
		chapterName: 'Send Request',
		items: [
			{
				itemName: 'Review & Send Request',
				path: 'review-send',
				component: <SendRfp />
			}
		],
		path: 'send-request',
		visible: isVisible
	},
	{
		chapterName: 'Review Proposals',
		visible: isVisible,
		path: 'review-proposals',
		items: [
			{
				itemName: 'Proposal Tracker',
				path:
					infoType === 'comparable-hotels'
						? 'comparable-hotels'
						: 'proposal-tracker',
				component:
					infoType === 'comparable-hotels' ? (
						<PlannerHotelsAvailable />
					) : (
						<PlannerProposalTracker />
					)
			},
			{
				itemName: 'Proposals Dashboard',
				path: 'dashboard',
				component: <PlannerProposalDashboard />
			},
			{
				itemName: 'Chat Messaging',
				path: 'chat-messaging',
				component: <div>Chat Messaging</div>
			}
		]
	},
	{
		chapterName: 'Reports & Presentations',
		visible: isVisible,
		path: 'reports',
		items: [
			{
				itemName: 'Reports',
				path: 'reports',
				component: <EventReports />
			},
			{
				itemName: 'Presentations',
				path: 'presentations',
				component: <div>Presentations</div>
			}
		]
	},
	{
		chapterName: 'Contract',
		visible: true,
		path: 'contract',
		items: [
			{
				itemName: 'Cost Savings',
				path: 'cost-savings',
				component: <CostSavings />
			},
			{
				itemName: 'Agreement',
				path: 'agreement',
				component: <Agreement />
			}
		]
	}
]

const EventPlannerTab = () => {
	const { userProfile } = useUserProfileContext()
	const { eventId, chapterName, infoType } = useParams()

	const navigate = useNavigate()
	const { eventInfo } = eventInfoStore()
	const { attachments } = useAttachmentsStore()
	const { addRemoveProposalRequests } = useRfpBuilder()

	const { general, roomblocks, mespace, contractTerms, hotels, contacts } =
		useEventPlanValidationErrors({
			eventPlan: eventInfo,
			attachments,
			userProfile
		})

	const [activeTab, setActiveTab] = useState<{
		parent: number
		child: number
	}>({ parent: 0, child: 0 })

	const chapterTabs = useMemo(
		() =>
			eventInfo
				? getChapterTabs(
						eventInfo.itemType === 'eventPlan',
						infoType,
						eventInfo,
						addRemoveProposalRequests,
						{
							general,
							roomblocks,
							mespace,
							contractTerms,
							hotels,
							contacts
						}
					)
				: [],
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[
			contacts,
			contractTerms,
			eventInfo,
			general,
			hotels,
			infoType,
			mespace,
			roomblocks
		]
	)

	useEffect(() => {
		const parent = chapterTabs.findIndex(
			tab => tab.path === chapterName?.toLowerCase()
		)
		const child = chapterTabs[parent]?.items.findIndex(
			item => item.path === infoType?.toLowerCase()
		)
		if (parent !== -1 && child !== -1) setActiveTab({ parent, child })
	}, [chapterName, chapterTabs, infoType])

	return (
		<div className='flex gap-4'>
			<div
				className='flex w-60 flex-col overflow-y-auto rounded-lg border bg-white p-4'
				style={{ maxHeight: `calc(100vh - 12rem)` }}
			>
				<div className='flex flex-col gap-2'>
					{chapterTabs
						.filter(c => c.visible)
						.map((tab, index) => (
							<div className='flex flex-col gap-3' key={index}>
								<div className='text-sm font-medium text-gray-900'>
									{tab.chapterName}
								</div>
								<div className='flex flex-col gap-1'>
									{tab.items.map((item, childIndex) => (
										<div
											className={`h-max-[60vh] flex items-start justify-start rounded-lg px-4 py-2 text-sm font-medium first:ml-0 focus:outline-none disabled:cursor-not-allowed disabled:text-gray-400 disabled:dark:text-gray-500 ${activeTab.child === childIndex && activeTab.parent === index ? 'border bg-gray-100 text-primary-700 ring-1 ring-primary-700 dark:bg-gray-800 dark:text-primary-500' : 'border border-transparent text-gray-500 hover:bg-gray-50 hover:text-gray-600 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300'}`}
											key={childIndex}
											role='button'
											onClick={() => {
												setActiveTab({ parent: index, child: childIndex })
												navigate(
													`/${userProfile?.role?.toLowerCase()}/event/${eventId}/${tab.path}/${item.path}`
												)
											}}
											onKeyDown={event => {
												if (event.key === 'Enter') {
													setActiveTab({ parent: index, child: childIndex })
													navigate(
														`/${userProfile?.role?.toLowerCase()}/event/${eventId}/${tab.path}/${item.path}`
													)
												}
											}}
											tabIndex={0}
										>
											<div className='flex flex-1 items-center justify-between'>
												<div className='whitespace-nowrap'>{item.itemName}</div>
												<div>
													{item.error && item.error.length > 0 ? (
														<HSTooltip
															content={
																<div className='flex flex-col gap-1'>
																	{item.error.map(errorMessage => (
																		<div
																			className='text-left text-sm'
																			key={errorMessage}
																		>
																			{errorMessage}
																		</div>
																	))}
																</div>
															}
															placement='right'
														>
															<FontAwesomeIcon
																className='text-red-600'
																icon={faCircleExclamation}
															/>
														</HSTooltip>
													) : null}
												</div>
											</div>
										</div>
									))}
								</div>
							</div>
						))}
				</div>
			</div>

			<div
				className='h-fit w-full overflow-y-auto rounded-lg border bg-white'
				style={{ maxHeight: `calc(100vh - 12rem)` }}
			>
				{chapterTabs[activeTab.parent]?.items[activeTab.child]?.component}
			</div>
		</div>
	)
}

export default EventPlannerTab
