/* eslint-disable @typescript-eslint/max-params */
export const viewModes = {
	grid: 'grid',
	map: 'map',
	rank: 'rank'
}

export const sortModes = {
	distance: 'distance-asc',
	profileScore: 'profileScore/total-desc'
}

export const searchModes = {
	default: 'default',
	comparableDestinations: 'comparableDestinations',
	destination: 'destination'
}

export const sortOptions = [
	{ value: sortModes.distance, label: 'Sort closest first' },
	{ value: 'guestRoomQuantity-asc', label: 'Sort least guest rooms first' },
	{ value: 'guestRoomQuantity-desc', label: 'Sort most guest rooms first' },
	{ value: 'meetingRoomQuantity-asc', label: 'Sort least meeting rooms first' },
	{ value: 'meetingRoomQuantity-desc', label: 'Sort most meeting rooms first' },
	{
		value: 'meetingSpaceSquareFeet-asc',
		label: 'Sort least meeting space first'
	},
	{
		value: 'meetingSpaceSquareFeet-desc',
		label: 'Sort most meeting space first'
	},
	{ value: 'starRating-asc', label: 'Sort lowest rating first' },
	{ value: 'starRating-desc', label: 'Sort highest rating first' },
	{ value: 'name-asc', label: 'Sort alphabetically by name' }
]

export const rangeFilters = [
	{
		title: 'Guest Rooms',
		prompt: 'Guest Rooms',
		label: 'Guest Rooms',
		filterKey: 'guestRoomQuantity',
		maskKey: 'numberMask'
	},
	{
		title: 'Meeting Space',
		prompt: 'Square Feet',
		label: 'Meeting Space',
		filterKey: 'meetingSpaceSquareFeet',
		maskKey: 'areaMask'
	},
	{
		title: 'Largest Space',
		prompt: 'Square Feet',
		label: 'Largest Space',
		filterKey: 'largestMeetingSpaceSquareFeet',
		maskKey: 'areaMask'
	},
	{
		title: 'Meeting Rooms',
		prompt: 'Meeting Rooms',
		label: 'Meeting Rooms',
		filterKey: 'meetingRoomQuantity',
		maskKey: 'numberMask'
	},
	{
		title: 'Average Rating',
		prompt: 'Average Rating',
		label: 'Average Rating',
		filterKey: 'averageRating',
		maskKey: 'numberMask'
	}
]

export function metersToMiles(meters: number) {
	return Number(meters / 1609.34).toFixed(1)
}

export function kilometersToMiles(kilometersToMiles_: number) {
	return Number(kilometersToMiles_ / 1.609_34).toFixed(1)
}

export function milesToKilometers(miles: number) {
	return Number(miles * 1.609_344).toFixed(2)
}

const ToRadian = (v: number) => v * (Math.PI / 180)
const DiffRadian = (v1: number, v2: number) => ToRadian(v2) - ToRadian(v1)

export class GeoCodeDistanceCalculator {
	private readonly EarthRadiusInMiles = 3956

	private readonly EarthRadiusInKilometers = 6367

	/**
	 * @param  {The latitude of the first geocode} lat1
	 * @param  {The longitude of the first geocode} lng1
	 * @param  {The latitude of the second geocode} lat2
	 * @param  {The longitude of the second geocode} lng2
	 * @param  {The distance units i.e mi or km} units="mi"
	 */
	public calculateDistance(
		lat1: number,
		lng1: number,
		lat2: number,
		lng2: number,
		units = 'mi'
	): number {
		let radius = 0
		switch (units.toLowerCase()) {
			case 'm': {
				radius = this.EarthRadiusInKilometers * 1000
				break
			}
			case 'km': {
				radius = this.EarthRadiusInKilometers
				break
			}
			case 'ft': {
				radius = this.EarthRadiusInMiles * 5282
				break
			}

			default: {
				radius = this.EarthRadiusInMiles
			}
		}
		return (
			radius *
			2 *
			Math.asin(
				Math.min(
					1,
					Math.sqrt(
						Math.sin(DiffRadian(lat1, lat2) / 2) ** 2 +
							Math.cos(ToRadian(lat1)) *
								Math.cos(ToRadian(lat2)) *
								Math.sin(DiffRadian(lng1, lng2) / 2) ** 2
					)
				)
			)
		)
	}
}
