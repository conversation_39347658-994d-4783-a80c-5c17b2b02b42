/* eslint-disable unicorn/no-nested-ternary */
import {
	faSearch,
	faShareNodes,
	faPlus
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSIcon from 'components/HSIcon'
import HSTable from 'components/table'
import HSTextField from 'components/textField'
import { format, parseISO } from 'date-fns'
import { useGetMyTemplates } from 'lib/services/event.service'
import {
	renderActions,
	renderName,
	renderPattern,
	renderRequest
} from './templates'
import { useEffect, useState } from 'react'
import type { EventPlan } from 'models/proposalResponseMonitor'
import PageLoader from 'components/pageLoader'
import ShareTemplates from './share'
import DeleteTemplate from './deleteTemplate'
import EventPlanDuplicateModal from 'components/eventPlanDuplicateModal'

interface IImportTemplatesProperties {
	onComplete: () => void
}

const ImportTemplates = (properties: IImportTemplatesProperties) => {
	const { onComplete } = properties
	const [showShareDrawer, setShowShareDrawer] = useState<boolean>(false)
	const { data, isFetching, refetch: loadTemplates } = useGetMyTemplates()
	const [selectedTemplates, setSelectedTemplates] = useState<EventPlan[]>([])
	const [selectedEventPlan, setSelectedEventPlan] = useState<EventPlan | null>(
		null
	)
	const [showCreateRfpDrawer, setShowCreateRfpDrawer] = useState<boolean>(false)
	const [showDeleteTemplate, setShowDeleteTemplate] = useState<boolean>(false)
	const [searchText, setSearchText] = useState<string>('')
	const [filteredItems, setFilteredItems] = useState<EventPlan[]>([])

	// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
	if (typeof onComplete === 'function') {
		console.log('onComplete')
	}
	useEffect(() => {
		let updatedData = data ?? []
		if (searchText !== '') {
			updatedData = updatedData.filter(item =>
				item.name?.toLowerCase().includes(searchText.toLowerCase())
			)
		}
		setFilteredItems(updatedData)
	}, [data, searchText])

	const handleDelete = (item: EventPlan) => {
		setSelectedEventPlan(item)
		setShowDeleteTemplate(true)
	}

	const handleShare = (item: EventPlan) => {
		setSelectedEventPlan(item)
		setShowShareDrawer(true)
	}

	const handleCreate = (item: EventPlan) => {
		setSelectedEventPlan(item)
		setShowCreateRfpDrawer(true)
	}
	return (
		<div>
			<div className='flex flex-col'>
				<div className='flex flex-col border-b p-4'>
					<div className='text-lg font-semibold text-gray-900'>
						My RFP Templates
					</div>
					<div className='text-sm font-normal text-gray-500'>
						Copy existing RFPs to reuse them later as a templates
					</div>
				</div>
				<div
					className='flex flex-col gap-2 overflow-auto p-4'
					style={{
						maxHeight: 'calc(100vh - 12rem)'
					}}
				>
					<div className='flex items-center justify-between gap-4'>
						<div className='text-sm font-medium text-gray-900'>
							{filteredItems.length} template
							{filteredItems.length > 1 ? 's' : ''}
						</div>
						<div className='flex items-center gap-4'>
							<div className='w-80'>
								<HSTextField
									showClearButton
									placeholder='Search'
									icon={HSIcon(faSearch)}
									value={searchText}
									onChange={event => setSearchText(event.target.value)}
								/>
							</div>
							<HSButton
								color='light'
								disabled={selectedTemplates.length === 0}
								onClick={() => setShowShareDrawer(true)}
							>
								<div className='flex items-center gap-2'>
									<FontAwesomeIcon icon={faShareNodes} />
									Share selected
								</div>
							</HSButton>
							<HSButton>
								<div className='flex items-center gap-2'>
									<FontAwesomeIcon icon={faPlus} />
									Add New
								</div>
							</HSButton>
						</div>
					</div>
					{isFetching ? (
						<PageLoader />
					) : (
						<HSTable
							rows={filteredItems}
							allowSelection
							rowSelected={event => {
								if (Array.isArray(event.data)) {
									setSelectedTemplates([...selectedTemplates, ...event.data])
								} else {
									setSelectedTemplates([...selectedTemplates, event.data])
								}
							}}
							rowDeselected={event => {
								if (Array.isArray(event.data)) {
									const selectedIds = new Set(event.data.map(item => item.id))
									setSelectedTemplates(
										selectedTemplates.filter(item => !selectedIds.has(item.id))
									)
								} else {
									setSelectedTemplates(
										selectedTemplates.filter(item => item !== event.data)
									)
								}
							}}
							columns={[
								{
									field: 'name',
									headerText: 'Name',
									render: renderName
								},
								{
									field: 'created',
									headerText: 'Created',
									render: item =>
										item.created ? format(parseISO(item.created), 'PP') : ''
								},
								{
									field: 'pattern',
									headerText: 'Pattern',
									render: renderPattern
								},
								{
									field: 'request',
									headerText: 'Request',
									render: renderRequest
								},
								{
									field: 'action',
									headerText: 'Actions',
									render: item =>
										renderActions(
											item,
											handleDelete,
											handleShare,
											handleCreate
										),
									width: 150
								}
							]}
						/>
					)}
				</div>
			</div>
			{showShareDrawer &&
			(selectedTemplates.length > 1 || selectedEventPlan) ? (
				<ShareTemplates
					templates={
						selectedTemplates.length > 1
							? selectedTemplates
							: selectedEventPlan
								? [selectedEventPlan]
								: []
					}
					onClose={() => {
						setShowShareDrawer(false)
					}}
				/>
			) : null}
			{showCreateRfpDrawer && selectedEventPlan ? (
				<EventPlanDuplicateModal
					onClose={() => {
						setShowCreateRfpDrawer(false)
						setSelectedEventPlan(null)
					}}
					duplicateAction='createRfpFromTemplate'
					createItemType='eventPlan'
					show
					template={selectedEventPlan}
					source='toolbox'
				/>
			) : null}

			{showDeleteTemplate && selectedEventPlan ? (
				<DeleteTemplate
					eventPlan={selectedEventPlan}
					onClose={async () => {
						setShowDeleteTemplate(false)
						setSelectedEventPlan(null)
						await loadTemplates()
					}}
				/>
			) : null}
		</div>
	)
}

export default ImportTemplates
