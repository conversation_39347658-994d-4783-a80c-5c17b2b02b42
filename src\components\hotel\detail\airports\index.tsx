/* eslint-disable react/no-array-index-key */
import { faArrowUpWideShort } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import { airportTypes } from 'lib/common/hotel'
import { formatNumber } from 'lib/helpers'
import type { Venue } from 'models/venue'
import { useState } from 'react'

interface HotelAirportsProperties {
	hotel: Venue
}

const tierOrder = ['large_airport', 'medium_airport', 'small_airport']
type SortMode = 'distance' | 'tier'

const sortByTier = (
	a: {
		type: string
	},
	b: {
		type: string
	}
) => tierOrder.indexOf(a.type) - tierOrder.indexOf(b.type)

const HotelAirports = (properties: HotelAirportsProperties) => {
	const { hotel } = properties
	const [sortMode, setSortMode] = useState<SortMode>('distance')
	return (
		<div className='card flex flex-col'>
			<div className='bg-gray-200 px-4 py-2'>
				<div className='flex items-center justify-between gap-4'>
					<div className='font-medium text-gray-700'>
						{`${hotel.city}, ${hotel.state}`}
					</div>
				</div>
			</div>
			<div
				className='flex flex-col gap-3 overflow-auto p-4'
				style={{ maxHeight: '18rem' }}
			>
				<div className='flex flex-col gap-2'>
					<div className='flex items-center justify-between gap-4'>
						<div className='text-sm font-bold text-gray-700'>
							Closest Airport{hotel.airports.length === 1 ? '' : 's'}:
						</div>
						<HSButton
							color='text'
							onClick={() => {
								if (sortMode === 'distance') {
									setSortMode('tier')
								} else {
									setSortMode('distance')
								}
							}}
						>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faArrowUpWideShort} />
								Sort By {sortMode === 'distance' ? 'Distance' : 'Tier'}
							</div>
						</HSButton>
					</div>
					{hotel.airports
						.sort((c, n) => {
							if (sortMode === 'distance')
								return c.distanceMiles - n.distanceMiles
							return sortByTier(c, n)
						})

						.map((airport, airportIndex) => (
							<div key={airportIndex} className='flex flex-col'>
								<div className='text-base font-normal text-gray-700'>
									{airport.name} ({airport.iataCode})
								</div>
								<div className='text-xs font-normal text-gray-500'>
									<div className='flex items-center gap-1'>
										<span>
											{airportTypes.find(t => t.value === airport.type)?.label}
										</span>
										|
										<span>
											{formatNumber(airport.distanceMiles, '0', 2)} miles
										</span>
									</div>
								</div>
							</div>
						))}
				</div>
			</div>
		</div>
	)
}

export default HotelAirports
