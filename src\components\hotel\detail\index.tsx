/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable react/no-array-index-key */
import { useNavigate } from 'react-router-dom'
import {
	useGetComparableHotels,
	useGetImages,
	useGetPlaces,
	useGetVenue
} from 'lib/services/hotels.service'
import HSBadge from 'components/badge'
import HSButton from 'components/button'
import CopyText from 'components/copyText'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HotelMarker from 'components/googleMapV2/components/hotelMarker'
import {
	faArrowLeft,
	faCommentLines,
	faGlobe,
	faList,
	faRibbon
} from '@fortawesome/pro-light-svg-icons'
import Loader from 'components/loader'
import type { ProposalRequestStatusKey } from '../../../lib/helpers/statusMaps'
import { MarketAvailabilityStatusMap } from '../../../lib/helpers/statusMaps'
import { useEffect, useMemo, useState } from 'react'
import {
	externalContentLinkTypes,
	getExternalContentLink
} from 'lib/helpers/hotels'
import type { IExternalContentLinks } from 'models/venue'
import analytics from 'lib/analytics/segment/load'
import { chainDisplayOptions, useGetChains } from 'lib/services/chains.service'
import { useGetAffiliateOrganizations } from 'lib/services/affiliateOrganizations.service'
import { getHopSkipConfig } from 'lib/auth/auth.config'
import { getYear } from 'date-fns'
import type { ICurrency } from 'lib/helpers'
import { formatCurrency, formatImageUrl, formatNumber } from 'lib/helpers'
import ScrollableText from 'components/scrollableText'
import GoogleMapsV2 from 'components/googleMapV2'
import MeetingSpaceSummary from './meetingSpace/meetingSpace'
import ComparableHotels from './comparableHotels'
import HotelStarRating from './starRating/index'
import {
	summarizeAvailability,
	useHandleAddToRfp
} from 'lib/services/comparableMarket'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import type { sleepingRoomTypeOptions } from 'lib/helpers/sleepingRoomType'
import VenueNotesModal from 'components/venueNotesModal'
import { useGetComments } from 'lib/services/comments.service'
import { collectionNames } from 'components/profile/tabs/userProfileComments/helper'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import AddViewVenueLists from 'components/venueList'
import HotelAirports from './airports'
import type { IVenueList } from 'models/organizations'
import { useGetVenueList } from 'lib/services/organizations.service'
import LocationMapMarker from 'components/googleMapV2/components/locationMapMarker'
import {
	faBedFront,
	faHandshake,
	faPlaneCircleCheck
} from '@fortawesome/pro-regular-svg-icons'
import hotelDetailStore from './store'
import meetingRoom from 'assets/images/meetingRoom.svg'
import { ROLE_PLANNER } from 'lib/helpers/roles'
import type { EventPlan } from 'models/proposalResponseMonitor'

const hopSkipConfig = getHopSkipConfig()

const NotListed = () => (
	<div className='text-sm font-medium text-gray-500'>Not listed</div>
)

interface HotelDetailProperties {
	venueId: string
	onBackClick?: () => void
	showCompHotelAddToRfp?: boolean
	eventPlan?: EventPlan | null
}
const HotelDetail = (properties: HotelDetailProperties) => {
	const { venueId, onBackClick, showCompHotelAddToRfp, eventPlan } = properties
	const { userProfile } = useUserProfileContext()
	const navigate = useNavigate()
	const { data: hotel, isFetching } = useGetVenue(venueId, !!venueId)
	const { currencies } = useCurrencyContext()
	const [currentLists, setCurrentLists] = useState<IVenueList[]>([])
	const { handleAddToRfp } = useHandleAddToRfp()
	const { goBackLabel, goBackUrl, showAvailability, showAddToRfp } =
		hotelDetailStore()

	const { data: comparableHotels } = useGetComparableHotels(
		'venue',
		venueId,
		!!venueId
	)

	const { data: comments } = useGetComments(
		collectionNames.userProfiles,
		userProfile?.id ?? '',
		'hotel.property',
		hotel?.id ?? ''
	)

	const { data: chains = [] } = useGetChains(
		chainDisplayOptions.siteCriteria.key
	)
	const { data: places } = useGetPlaces(venueId, !!venueId)
	const { refetch: listAffiliateOrganizations } = useGetAffiliateOrganizations(
		{
			filter: '',
			top: '100',
			skip: '0',
			orderBy: { field: 'name', direction: 1 }
		},
		false
	)
	const { data: images = [] } = useGetImages(venueId, false, false, !!venueId)
	const { data: venueLists, refetch: reloadVenueList } = useGetVenueList(
		userProfile?.organizationId ?? '',
		!!userProfile?.organizationId
	)
	const [websiteLink, setWebsiteLink] = useState<IExternalContentLinks>()

	// const [paidAffiliates, setPaidAffiliates] =
	// 	useState<Partial<IAffiliateOrganization>[]>()
	const [availability, setAvailability] = useState<{
		roomTypesOffered:
			| {
					roomType: (typeof sleepingRoomTypeOptions)[number]
					rate: number
					rateCount: number
			  }[]
			| undefined
		meetingSpaceAvailable: boolean | undefined
		proposalCurrency: ICurrency | undefined
	}>()
	const [showNotes, setShowNotes] = useState(false)
	const [showVenueList, setShowVenueList] = useState(false)

	const summaryProposalRequest = useMemo(
		() => eventPlan?.summaryProposalRequests?.find(p => p.venueId === venueId),
		[eventPlan?.summaryProposalRequests, venueId]
	)
	const currency = useMemo(
		() => currencies[summaryProposalRequest?.currencyCode ?? ''],
		[currencies, summaryProposalRequest?.currencyCode]
	)

	const handleBackClick = () => {
		if (goBackUrl) navigate(goBackUrl)
		if (onBackClick) onBackClick()
	}

	useEffect(() => {
		setCurrentLists(
			typeof venueLists === 'object' && Array.isArray(venueLists)
				? venueLists.filter(list => list.venues?.some(v => v.id === venueId))
				: []
		)
	}, [venueLists, venueId])

	useEffect(() => {
		if (summaryProposalRequest) {
			const currentAvailability = summarizeAvailability({
				summaryProposalRequest,
				proposalCurrency: currency
			})
			setAvailability(currentAvailability)
		}
	}, [currencies, currency, summaryProposalRequest])

	useEffect(() => {
		listAffiliateOrganizations()
			.then(() => {
				// setPaidAffiliates(
				// 	r.data
				// 		?.filter(
				// 			affiliateOrganization =>
				// 				affiliateOrganization.subscriptionInfos.length > 0
				// 		)
				// 		.filter(a => hotel?.propertySellers?.some(ps => ps.id === a.id))
				// 		.map(a => ({
				// 			id: a.id,
				// 			name: a.name,
				// 			filterId: 'propertySellerId',
				// 			logoImageUrl: a.logoImageUrl
				// 		}))
				// )
			})
			.catch((error: unknown) => {
				console.log(error)
			})
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [hotel?.propertySellers?.length])

	useEffect(() => {
		if (hotel) {
			setWebsiteLink(
				getExternalContentLink({
					hotel: { externalContentLinks: hotel.externalContentLinks },
					externalContentLinkTypeKey: externalContentLinkTypes.website.key
				})
			)
		}
	}, [hotel, hotel?.externalContentLinks])

	const getHotelBrand = useMemo(() => {
		const hotelChain = chains.find(chain => chain.id === hotel?.chainId)
		if (hotelChain) {
			return hotelChain.brands.find(brand => brand.id === hotel?.brandId)?.name
		}
		return null
	}, [chains, hotel?.brandId, hotel?.chainId])

	const getHotelChainName = useMemo(
		() => chains.find(chain => chain.id === hotel?.chainId)?.name,
		[chains, hotel?.chainId]
	)

	const supportedAffiliateOrganizations = hotel
		? [...hotel.destinations, ...(hotel.propertySellers ?? [])]
		: []

	if (isFetching || !hotel) {
		return <Loader />
	}

	return (
		<div className='card flex flex-col'>
			{/* Header Section */}
			<div className='flex items-center justify-between border-b p-4'>
				<div className='flex items-center gap-2'>
					<h1 className='text-xl font-semibold text-gray-900'>{hotel.name}</h1>
					{summaryProposalRequest ? (
						<HSBadge
							color={
								MarketAvailabilityStatusMap[
									summaryProposalRequest.status as ProposalRequestStatusKey
								]?.color ?? 'gray'
							}
							size='xs'
						>
							{MarketAvailabilityStatusMap[
								summaryProposalRequest.status as ProposalRequestStatusKey
							]?.label ?? ''}
						</HSBadge>
					) : null}
				</div>
				<div className='flex items-center gap-2'>
					<HSButton color='light' size='sm' onClick={handleBackClick}>
						<div className='flex items-center gap-2'>
							<FontAwesomeIcon icon={faArrowLeft} />
							{goBackLabel ?? 'Back'}
						</div>
					</HSButton>

					{!showAddToRfp ||
					eventPlan?.proposalRequests?.some(
						pr => pr.venueId === summaryProposalRequest?.venueId
					) ? null : (
						<HSButton
							size='sm'
							onClick={() => {
								if (summaryProposalRequest) {
									handleAddToRfp(summaryProposalRequest).catch(
										(error: unknown) => {
											console.error(error)
										}
									)
								}
							}}
						>
							Add To RFP
						</HSButton>
					)}
				</div>
			</div>
			{showAvailability ? (
				<div className='border-b px-6 py-2'>
					<div className='flex items-center gap-6'>
						<div className='text-sm font-bold text-gray-700'>Availability</div>
						<div className='flex items-center gap-2'>
							<span className='text-sm font-normal text-gray-400'>Rooms:</span>
							<div
								className={`text-sm font-medium ${availability?.roomTypesOffered ? 'text-green-600' : 'text-red-600'}`}
							>
								{availability?.roomTypesOffered ? 'Available' : 'Not Available'}
							</div>
						</div>
						<div className='flex items-center gap-2'>
							<span className='text-sm font-normal text-gray-400'>
								Event Space:
							</span>
							<span
								className={`text-sm font-medium ${availability?.meetingSpaceAvailable ? 'text-green-600' : 'text-red-600'}`}
							>
								{availability?.meetingSpaceAvailable
									? 'Available'
									: 'Not Available'}
							</span>
						</div>
						<div className='flex items-center gap-2'>
							<span className='text-sm font-normal text-gray-400'>
								Room Rate:
							</span>
							<div className='flex items-center gap-2'>
								{availability?.roomTypesOffered?.map((rto, index) => (
									<span key={`roomTypeBadge${index}`}>
										<HSBadge color='gray' size='xs' className='p-1'>
											{rto.roomType.name} | {formatCurrency(rto.rate, currency)}
										</HSBadge>
										{(availability.roomTypesOffered?.length ?? 0) - 1 ===
										index ? null : (
											<span className='text-sm font-normal text-gray-400'>
												,
											</span>
										)}
									</span>
								))}
							</div>
						</div>
					</div>
				</div>
			) : null}

			<div className='flex flex-col gap-4 p-6'>
				<div className='flex flex-col gap-4'>
					<div className='flex items-center justify-between gap-4'>
						<div className='text-lg font-semibold text-gray-900'>
							Hotel Overview
						</div>
						{userProfile?.role === ROLE_PLANNER ? (
							<div className='flex items-center gap-4'>
								<HSButton
									color='text'
									onClick={() => setShowNotes(true)}
									size='sm'
								>
									<div className='flex items-center gap-2'>
										<FontAwesomeIcon icon={faCommentLines} /> Notes (
										{comments?.length})
									</div>
								</HSButton>
								<HSButton
									color='text'
									onClick={() => setShowVenueList(true)}
									size='sm'
								>
									<div className='flex items-center gap-2'>
										<FontAwesomeIcon icon={faList} /> Collection (
										{currentLists.length})
									</div>
								</HSButton>
							</div>
						) : null}
					</div>

					<div className='flex gap-4'>
						<div className='w-2/6'>
							<div className='card flex flex-col gap-2.5 p-4'>
								{/* {showAvailability ? (
									<>
										<div className='flex flex-col gap-1'>
											<div className='text-sm font-bold text-gray-700'>
												Availability
											</div>
											<div className='flex items-center gap-2'>
												<span className='text-sm font-normal text-gray-400'>
													Rooms:
												</span>
												<div
													className={`text-sm font-medium ${availability?.roomTypesOffered ? 'text-green-600' : 'text-red-600'}`}
												>
													{availability?.roomTypesOffered
														? 'Available'
														: 'Not Available'}
												</div>
											</div>
											<div className='flex items-center gap-2'>
												<span className='text-sm font-normal text-gray-400'>
													Event Space:
												</span>
												<span
													className={`text-sm font-medium ${availability?.meetingSpaceAvailable ? 'text-green-600' : 'text-red-600'}`}
												>
													{availability?.meetingSpaceAvailable
														? 'Available'
														: 'Not Available'}
												</span>
											</div>
											<div className='flex items-center gap-2'>
												<span className='text-sm font-normal text-gray-400'>
													Room Rate:
												</span>
												<div className='flex items-center gap-2'>
													{availability?.roomTypesOffered?.map((rto, index) => (
														<HSBadge
															color='gray'
															size='xs'
															key={`roomTypeBadge${index}`}
															className='p-1'
														>
															{rto.roomType.name} |{' '}
															{formatCurrency(rto.rate, currency)}
														</HSBadge>
													))}
												</div>
											</div>
										</div>
										<div className='border-b' />
									</>
								) : null} */}
								<div className='flex flex-col gap-1'>
									<div className='text-sm font-bold text-gray-700'>
										Star Rating
									</div>
									<HotelStarRating hotel={hotel} />
								</div>
								<div className='border-b' />
								<div className='flex flex-col gap-1'>
									<div className='text-sm font-bold text-gray-700'>Address</div>
									<span className='text-sm font-normal text-gray-500'>{`${hotel.address} ${hotel.city}, ${hotel.state} ${hotel.zip}`}</span>
								</div>
								<div className='border-b' />
								<div className='flex flex-col gap-1'>
									<div className='text-sm font-bold text-gray-700'>Phone</div>
									<div className='flex items-center gap-2'>
										<CopyText value={hotel.phone} />
									</div>
								</div>
								<div className='border-b' />
								<div className='flex flex-col gap-2'>
									<div className='text-sm font-bold text-gray-700'>Website</div>
									{places?.website || !!websiteLink?.url ? (
										<a
											rel='noreferrer noopener'
											href={websiteLink?.url || places?.website}
											target='_blank'
											onClick={() => {
												analytics.track(`Hotel Visit Website Clicked`, {
													hotelId: hotel.id,
													hotelName: hotel.name
												})
											}}
										>
											{
												((websiteLink?.url || places?.website) ?? '')
													.split('?')[0]
													.split('//')[1]
													.split('/')[0]
											}
										</a>
									) : null}
								</div>
								<div className='border-b' />
								<div className='flex items-center gap-6'>
									<div className='flex w-48 flex-col gap-2'>
										<div className='text-sm font-bold text-gray-700'>
											Hotel Chain
										</div>
										<span className='text-sm font-normal text-gray-500'>
											{chains.length > 0 ? (getHotelChainName ?? '') : 'None'}
										</span>
									</div>
									<div className='flex w-48 flex-col gap-2'>
										<div className='text-sm font-bold text-gray-700'>
											Hotel Brand
										</div>
										<span className='text-sm font-normal text-gray-500'>
											{getHotelBrand || 'None'}
										</span>
									</div>
								</div>
								{/* Hotel Type Section */}
								<div className='flex flex-col gap-2'>
									<div className='text-sm font-bold text-gray-700'>
										Hotel Type
									</div>
									<span className='text-sm font-normal text-gray-900'>
										{hotel.hotelTypes && hotel.hotelTypes.length > 0 ? (
											<div className='text-sm font-normal text-gray-500'>
												{hotel.hotelTypes.join(', ')}
											</div>
										) : null}
									</span>
								</div>
								<div className='border-b' />
								{/* Affiliated Organizations Section */}

								<div className='flex flex-col gap-2'>
									<div className='text-sm font-bold text-gray-700'>
										Affiliated organizations that support this hotel
									</div>

									<div className='flex flex-wrap gap-8'>
										{supportedAffiliateOrganizations.length > 0 ? (
											supportedAffiliateOrganizations.map((a, ai) => (
												<div className='flex items-center gap-2' key={ai}>
													{a.logoImageUrl ? (
														<img
															className='h-8 w-8 rounded-md border object-cover'
															key={ai}
															src={`${hopSkipConfig.imagesEndpoint.url}/${a.id}/${a.logoImageUrl}`}
															alt={a.name ?? ''}
														/>
													) : (
														<div className='flex h-8 w-8 items-center justify-center rounded-md border bg-gray-200 p-2'>
															<FontAwesomeIcon
																key={ai}
																icon={
																	a.itemType === 'destination.organization'
																		? faGlobe
																		: faRibbon
																}
																className='text-primary-700'
															/>
														</div>
													)}
													<div className='text-sm font-normal text-gray-700'>
														{a.name}
													</div>
												</div>
											))
										) : (
											<NotListed />
										)}
									</div>
								</div>
								<div className='border-b' />
								<div className='flex items-center gap-6'>
									<div className='flex flex-col gap-2'>
										<div className='text-sm font-bold text-gray-700'>
											Date Opened
										</div>
										<span className='text-sm font-normal text-gray-900'>
											{hotel.openingDate ? (
												<>{getYear(new Date(hotel.openingDate))}</>
											) : (
												<NotListed />
											)}
										</span>
									</div>

									<div className='flex flex-col gap-2'>
										<div className='text-sm font-bold text-gray-700'>
											Last Renovated
										</div>
										<span className='text-sm font-normal text-gray-900'>
											{hotel.lastRenovationDate ? (
												<>{getYear(new Date(hotel.lastRenovationDate))}</>
											) : (
												<NotListed />
											)}
										</span>
									</div>
								</div>
								<div className='border-b' />
								<div className='flex flex-col gap-2'>
									<div className='text-sm font-bold text-gray-700'>
										Awards & Recognition
									</div>
									<div className='text-sm font-normal text-gray-600'>
										{hotel.awardOrRecognitions?.length ? (
											<ul>
												{hotel.awardOrRecognitions.map(award => (
													<li key={award.name}>{award.name}</li>
												))}
											</ul>
										) : (
											<NotListed />
										)}
									</div>
								</div>
							</div>
						</div>
						<div className='w-3/6'>
							<div className='card'>
								<img
									src={
										hotel.imageUrl?.includes('coming-soon') || !hotel.imageUrl
											? '/images/hotel-coming-soon.png'
											: formatImageUrl(hotel.imageUrl, hotel.id ?? '')
									}
									alt='Hotel'
									className='w-full rounded-lg object-cover'
								/>
							</div>
						</div>
						<div className='w-1/6'>
							<div className='flex flex-col gap-4'>
								{formatImageUrl(images.at(0)?.url ?? '', hotel.id ?? '') ? (
									<div className='card'>
										<img
											src={formatImageUrl(
												images.at(0)?.url ?? '',
												hotel.id ?? ''
											)}
											alt='Hotel Interior'
											className='h-full w-full rounded-lg object-cover'
										/>
									</div>
								) : null}
								{formatImageUrl(images.at(1)?.url ?? '', hotel.id ?? '') ? (
									<div className='card'>
										<img
											src={formatImageUrl(
												images.at(1)?.url ?? '',
												hotel.id ?? ''
											)}
											alt='Hotel Meeting Room'
											className='h-full w-full rounded-lg object-cover'
										/>
									</div>
								) : null}
								{formatImageUrl(images.at(2)?.url ?? '', hotel.id ?? '') ? (
									<div className='relative'>
										<img
											src={formatImageUrl(
												images.at(2)?.url ?? '',
												hotel.id ?? ''
											)}
											alt='Hotel Room'
											className='h-full w-full rounded-lg object-cover'
										/>
										{/* <div className='absolute bottom-2 right-2'>
											<HSButton color='light' size='xs'>
												Show all images
											</HSButton>
										</div> */}
									</div>
								) : null}
							</div>
						</div>
					</div>
				</div>

				<div className='flex flex-col gap-2'>
					<div className='text-lg font-semibold text-gray-900'>
						Rooms and Event Space Summary
					</div>
					<div className='flex justify-between gap-4'>
						<div className=''>
							<div className='card flex flex-col gap-4 p-4'>
								<div className='flex items-center gap-2.5'>
									<div className='flex w-48 flex-col gap-2.5'>
										<div className='text-sm font-medium'>Guestrooms</div>
										<div className='flex items-center gap-2'>
											<div className='flex h-6 w-6 items-center justify-center rounded-lg bg-gray-100'>
												<FontAwesomeIcon
													icon={faBedFront}
													className='text-gray-500'
												/>
											</div>
											<span className='text-lg font-semibold'>
												{formatNumber(hotel.guestRoomQuantity)}
											</span>
											<span className='text-sm text-gray-500'>rooms</span>
										</div>
									</div>
									<div className='flex w-48 flex-col gap-2.5'>
										<div className='text-sm font-medium'>Meeting Space</div>
										<div className='flex items-center gap-2'>
											<div className='flex h-6 w-6 items-center justify-center rounded-lg bg-gray-100'>
												<FontAwesomeIcon
													icon={faHandshake}
													className='text-gray-500'
												/>
											</div>
											<span className='text-lg font-semibold'>
												{formatNumber(hotel.meetingSpaceSquareFeet)}
											</span>
											<span className='text-sm text-gray-500'>ft²</span>
										</div>
									</div>
								</div>
								<div className='border-b' />
								<div className='flex items-center justify-between gap-4'>
									<div className='flex w-48 flex-col gap-2.5'>
										<div className='text-sm font-medium'>Meeting Rooms</div>
										<div className='flex items-center gap-2'>
											<div className='flex h-6 w-6 items-center justify-center rounded-lg bg-gray-100'>
												<img
													src={meetingRoom}
													className='text-gray-500'
													alt='meeting room'
												/>
											</div>
											<span className='text-lg font-semibold'>
												{formatNumber(hotel.meetingRoomQuantity)}
											</span>
											<span className='text-sm text-gray-500'>rooms</span>
										</div>
									</div>

									<div className='flex w-48 flex-col gap-2.5'>
										<div className='text-sm font-medium'>Largest room</div>
										<div className='flex items-center gap-2'>
											<div className='flex h-6 w-6 items-center justify-center rounded-lg bg-gray-100'>
												<FontAwesomeIcon
													icon={faHandshake}
													className='text-gray-500'
												/>
											</div>
											<span className='text-lg font-semibold'>
												{formatNumber(hotel.largestMeetingSpaceSquareFeet)}
											</span>
											<span className='text-sm text-gray-500'>ft²</span>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div className='flex-1'>
							<div className='card h-full'>
								<div className='flex flex-col gap-2'>
									<div className='px-4 pt-4 text-sm font-medium'>
										Description
									</div>
									<ScrollableText
										content={hotel.description ?? ''}
										rows={5}
										className='px-4 py-2'
									/>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div className='flex flex-col gap-2'>
					<div className='text-lg font-semibold text-gray-900'>
						Hotel Location
					</div>
					<div className='flex justify-between gap-4'>
						<div className='flex w-1/3'>
							<HotelAirports hotel={hotel} />
						</div>
						<div className='w-2/3'>
							<div className='card h-full rounded-md'>
								<GoogleMapsV2
									defaultCenter={{
										lat: hotel.latitude ?? 0,
										lng: hotel.longitude ?? 0
									}}
									defaultZoom={13}
									markerComponent={[
										...hotel.airports.map(airport => (
											<div key={airport.id}>
												<LocationMapMarker
													position={{
														lat: airport.latitude,
														lng: airport.longitude
													}}
													icon={faPlaneCircleCheck}
													backgroundColor='#2563EB'
													iconClassName='text-white text-lg'
												/>
											</div>
										)),
										<HotelMarker
											key={hotel.id}
											position={{
												lat: hotel.latitude ?? 0,
												lng: hotel.longitude ?? 0
											}}
											onClick={() => {}}
										/>
									]}
								/>
							</div>
						</div>
					</div>
				</div>

				<MeetingSpaceSummary hotel={hotel} />
				{(comparableHotels?.length ?? 0) > 0 ? (
					<div className='flex flex-col gap-2'>
						<div className='text-lg font-semibold text-gray-700'>
							Comparable Hotels
						</div>
						<ComparableHotels
							venueId={hotel.id}
							showAddToRfp={showCompHotelAddToRfp}
							eventPlan={eventPlan}
						/>
					</div>
				) : null}
			</div>
			{showNotes ? (
				<VenueNotesModal
					venue={hotel}
					show
					onHide={() => {
						setShowNotes(false)
					}}
				/>
			) : null}
			{showVenueList ? (
				<AddViewVenueLists
					onClose={() => {
						setShowVenueList(false)
						reloadVenueList().catch((error: unknown) => console.error(error))
					}}
					venue={hotel}
				/>
			) : null}
		</div>
	)
}

export default HotelDetail
