import { useMsal } from '@azure/msal-react'
import HSDropdownButton from 'components/dropdown'
import type { ReactNode } from 'react'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import {
	ROLE_ADMIN,
	ROLE_AFFILIATE,
	ROLE_PLANNER,
	ROLE_DESTINATION_MANAGER,
	ROLE_SALES_OPS,
	ROLE_SUPPLIER,
	ROLE_HOTELIER
} from 'lib/helpers/roles'
import { useNavigate } from 'react-router-dom'
// import { getOldRoute } from 'lib/support/routeMap'
import { getOrganizationLink } from 'lib/common/organizationRoute'
import posthog from 'posthog-js'

const UserAvatar = ({ isSummary }: { isSummary?: boolean }) => {
	const { userProfile } = useUserProfileContext()
	const { instance } = useMsal()
	const navigate = useNavigate()

	const onLogoutClick = () => {
		instance
			.logoutRedirect()
			.then(() => {
				console.log('Logged out successfully')
				posthog.reset()
			})
			.catch((error: unknown) => console.error(error))
	}

	return (
		<HSDropdownButton
			showDropdownIcon={false}
			placement={isSummary ? 'bottom' : 'right'}
			color='none'
			label={
				<div
					className={`z-50 flex items-center justify-center rounded-full ${isSummary ? 'h-8 w-8 bg-primary-100' : 'h-10 w-10 bg-black p-3 text-white'}`}
				>
					<div
						className={
							isSummary ? 'p-1.5 text-xs font-medium text-primary-800' : ''
						}
					>
						{userProfile?.firstName?.charAt(0).toUpperCase()}
						{userProfile?.lastName?.charAt(0).toUpperCase()}
					</div>
				</div>
			}
			items={[
				{
					item: (
						<div className='flex flex-col items-start'>
							<div className='text-sm font-bold text-gray-700'>
								{userProfile?.firstName} {userProfile?.lastName}
							</div>
							<div className='text-xs font-normal text-gray-500'>
								{userProfile?.email}
							</div>
						</div>
					) as ReactNode,
					id: 'info',
					clickFunction: () => {},
					disabled: true,
					cssName:
						'bg-gray-50 cursor-default border-b border-gray-200 hover:bg-gray-50'
				},
				{
					id: 'My Profile',
					item: 'My Profile',
					cssName: 'text-sm font-bold',
					clickFunction: () => {
						navigate('/my-profile')
					}
				},

				[
					ROLE_ADMIN,
					ROLE_AFFILIATE,
					ROLE_DESTINATION_MANAGER,
					ROLE_PLANNER
				].includes(userProfile?.role ?? '')
					? {
							id: 'My Organization',
							item: 'My Organization',
							cssName: 'text-sm font-bold',
							clickFunction: () => {
								const route = getOrganizationLink(
									userProfile?.role ?? '',
									userProfile?.organizationId ?? ''
								)
								navigate(route)
							}
						}
					: null,
				[ROLE_AFFILIATE, ROLE_SALES_OPS, ROLE_SUPPLIER].includes(
					userProfile?.role ?? ''
				)
					? null
					: {
							id: 'Subscriptions and Billing',
							item: 'Subscriptions and Billing',
							cssName: 'text-sm font-bold',
							clickFunction: async () =>
								navigate(
									userProfile?.role === ROLE_HOTELIER
										? `/hotel-profiles/account-management/${userProfile.associatedVenues?.[0]?.id}/subscriptions-billing`
										: `/dmo-profiles/subscription/${userProfile?.organizationId}/subscriptions`
								)
						},
				// {
				// 	id: 'legacyUI',
				// 	item: 'Go back to Legacy UI',
				// 	cssName: 'text-sm font-bold',
				// 	clickFunction: () => {
				// 		const oldRoute = getOldRoute()
				// 		globalThis.open(oldRoute ?? '')
				// 	}
				// },
				{
					id: 'Log out',
					item: 'Log out',

					clickFunction: () => {
						onLogoutClick()
					},
					cssName: 'text-red-600 text-sm font-bold'
				}
			]}
			showTooltip={false}
		/>
	)
}

export default UserAvatar
