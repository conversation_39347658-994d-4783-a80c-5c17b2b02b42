/* eslint-disable react/no-array-index-key */
import { faStar } from '@fortawesome/pro-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSCheckbox from 'components/checkbox'

interface IHSStarRating {
	s: number
	onChangeStarRatings: (s: number, checked: boolean) => void
	checked: boolean
}

const HSStarRating = (properties: IHSStarRating) => {
	const { s, onChangeStarRatings, checked } = properties
	return (
		<div className='flex items-center gap-4'>
			<HSCheckbox
				name='preferredStarRatings'
				style={{ fontSize: 'small' }}
				checked={checked}
				onChange={event => onChangeStarRatings(s, event.target.checked)}
			/>
			{s === 0 && 'No Preference'}
			{s > 0 && (
				<div className='flex items-center gap-1'>
					{Array.from({ length: 5 }).map((_, index) => (
						<FontAwesomeIcon
							key={`ratingStar-${s}-${index}`}
							icon={faStar}
							className={index < s ? 'text-yellow-300' : 'text-gray-300'}
						/>
					))}
				</div>
			)}

			<span className='checkmark' style={{ fontSize: 'small' }} />
		</div>
	)
}

export default HSStarRating
