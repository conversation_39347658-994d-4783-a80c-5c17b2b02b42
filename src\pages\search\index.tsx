import headerStore from 'components/header/headerStore'
import { useEffect } from 'react'
import HotelSearchList from './searchList'
import { useParams } from 'react-router-dom'
import HotelListTab from './tabs'

const leftComponent = (
	<div className='text-sm font-medium text-gray-700'> Pre-RFP Hotel Search</div>
)

const SiteSearch: React.FC = () => {
	const { reset, setHide, setLeftComponent } = headerStore()

	const { view } = useParams()

	useEffect(() => {
		setHide(false)
		setLeftComponent(leftComponent)
		return () => {
			reset()
		}
	}, [reset, setHide, setLeftComponent])

	return (
		<div className=''>
			{view === 'search' ? (
				<HotelListTab />
			) : (
				<div className='p-6'>
					<div className='card'>
						<HotelSearchList />
					</div>
				</div>
			)}
		</div>
	)
}

export default SiteSearch
