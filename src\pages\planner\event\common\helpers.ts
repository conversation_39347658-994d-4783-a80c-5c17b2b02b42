/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable import/prefer-default-export */
import { differenceInDays, isAfter } from 'date-fns'
import type { EventPlan } from 'models/proposalResponseMonitor'

export const reducingNumberOfDays = (
	startDate: Date | null,
	endDate: Date | null,
	event: EventPlan
) => {
	if (startDate && endDate && isAfter(endDate, startDate)) {
		const newTotalDays = differenceInDays(endDate, startDate) + 1
		const orphanedMeetingSpaceRequests = event.meetingSpaceRequests?.some(
			msr => (msr.dayNumber ?? 0) >= newTotalDays
		)
		let orphanedRoomNights = false
		if (
			event.roomBlockRequests?.some(rbr =>
				rbr.roomTypeRequests.some(rtr => rtr.roomNights)
			)
		) {
			orphanedRoomNights =
				event.roomBlockRequests.flatMap(rbr =>
					rbr.roomTypeRequests.filter(rtr =>
						rtr.roomNights?.some(rn => rn.dayNumber > newTotalDays - 2)
					)
				).length > 0
		}
		// totalDays = 5
		// possible dayNumbers = 0, 1, 2, 3, 4
		// newTotalDays = 4
		return {
			reducing:
				newTotalDays < (event.totalDays ?? 0) &&
				(orphanedMeetingSpaceRequests || orphanedRoomNights),
			newTotalDays
		}
	}
	return { reducing: false, newTotalDays: 0 }
}
