import { defaultQuoteRequest } from './index'
import { describe, it, expect, beforeEach } from 'vitest'
import { QuoteRequestValidator } from '../../quoteRequestStore'
import type { QuoteRequest } from 'models/hotelier'
import type { ProposalRequest } from 'models/proposalResponseMonitor'

describe('QuoteRequestValidator', () => {
	// Create reusable test objects
	let quoteRequest: QuoteRequest
	let errors: Record<string, Set<string>>

	beforeEach(() => {
		// Reset objects before each test
		quoteRequest = {
			...defaultQuoteRequest,
			eventPlan: {
				...defaultQuoteRequest.eventPlan,
				id: 'event-123',
				roomBlocksRequired: false,
				meetingSpaceRequired: false
			}
		} as QuoteRequest

		errors = {
			roomblocksErrors: new Set<string>(),
			submitErrors: new Set<string>(),
			mespaceErrors: new Set<string>(),
			fandbErrors: new Set<string>(),
			commentsErrors: new Set<string>(),
			generalErrors: new Set<string>()
		}
	})

	describe('validateRoomBlocks', () => {
		it('should not add errors when roomBlocksRequired is false', () => {
			// Create a proposal request with complete data but room blocks not required
			const proposalRequest = {
				...quoteRequest.proposalRequest,
				currentBid: {
					...quoteRequest.proposalRequest?.currentBid,
					taxesFeesAssessments: '',
					commissionPercent: '',
					attritionRate: ''
				}
			}

			// Call the function with room blocks not required
			QuoteRequestValidator.validateRoomBlocks(
				quoteRequest,
				errors,
				proposalRequest
			)

			// Verify no errors were added since room blocks aren't required
			expect(errors.roomblocksErrors.size).toBe(0)
		})

		it('should add errors when roomBlocksRequired is true and data is empty', () => {
			const proposalRequest: ProposalRequest = {
				...quoteRequest.proposalRequest,
				currentBid: {
					...quoteRequest.proposalRequest?.currentBid,
					taxesFeesAssessments: '',
					commissionPercent: '',
					attritionRate: '',
					roomRates: [], // hotelier did not update room rates
					meetingSpaceAvailable: false
				}
			}

			quoteRequest = {
				...quoteRequest,
				eventPlan: {
					...quoteRequest.eventPlan,
					roomBlocksRequired: true // required Room blocks
				},
				proposalRequest
			}

			// Call the function with room blocks not required
			QuoteRequestValidator.validateRoomBlocks(
				quoteRequest,
				errors,
				quoteRequest.proposalRequest as ProposalRequest
			)

			// Verify errors were added since room blocks are required
			expect(errors.roomblocksErrors.size).toBe(1)
		})

		it('should add errors when roomBlocksRequired is true and room rate is provided', () => {
			const proposalRequest: ProposalRequest = {
				...quoteRequest.proposalRequest,
				currentBid: {
					...quoteRequest.proposalRequest?.currentBid,
					taxesFeesAssessments: '',
					attritionRate: '',
					meetingSpaceAvailable: false,
					roomRates: [
						{
							key: '0.0.2025.11.01',
							roomBlockIndex: 0,
							dayNumber: 1,
							roomType: 0,
							rate: 5,
							requested: 20,
							offered: 20
						},
						{
							key: '0.0.2025.11.01',
							roomBlockIndex: 0,
							dayNumber: 0,
							roomType: 0,
							rate: 5,
							requested: 10,
							offered: 1
						},
						{
							key: '0.5.2025.11.01',
							roomBlockIndex: 0,
							dayNumber: 0,
							roomType: 5,
							rate: 50,
							requested: 0,
							offered: 5
						},
						{
							key: '0.5.2025.11.01',
							roomBlockIndex: 0,
							dayNumber: 1,
							roomType: 5,
							rate: 50,
							requested: 0,
							offered: 5
						}
					]
				}
			}

			quoteRequest = {
				...quoteRequest,
				eventPlan: {
					...quoteRequest.eventPlan,
					roomBlocksRequired: true
				},
				proposalRequest: quoteRequest.proposalRequest
			}

			// Call the function with room blocks not required
			QuoteRequestValidator.validateRoomBlocks(
				quoteRequest,
				errors,
				proposalRequest
			)

			// Verify no errors were added since room blocks aren't required
			expect(errors.roomblocksErrors.size).toBe(0)
		})

		it('should add errors when total expected room nights has rate provided', () => {
			const proposalRequest: ProposalRequest = {
				...quoteRequest.proposalRequest,
				currentBid: {
					...quoteRequest.proposalRequest?.currentBid,
					taxesFeesAssessments: '',
					attritionRate: '',
					meetingSpaceAvailable: false,
					proposalDates: [
						{
							key: '2025.11.01',
							startDate: '2025-11-01T00:00:00',
							endDate: '2025-11-03T00:00:00',
							declineToBid: false,
							isVaryingRate: true,
							isOfferedDifferentThanRequested: true,
							preferred: true,
							alternate: false,
							suggested: false,
							contracted: false,
							value: null,
							proposalValues: {
								totalRoomsOffered: 0,
								averageRoomRate: 0,
								isAverageWeighted: false,
								roomCost: 0,
								roomTaxRate: 0,
								roomTax: 0,
								roomFees: 0,
								rebateAmount: null,
								rebateRequestBasis: null,
								foodAndBeverage: 0,
								foodAndBeverageTaxRate: 0,
								foodAndBeverageTax: 0,
								roomRental: 0,
								roomRentalTaxRate: 0,
								roomRentalTax: 0,
								serviceChargeRate: 0,
								serviceCharge: 0,
								serviceChargeTaxRate: 0,
								serviceChargeTax: 0,
								concessionValueProposed: null,
								concessionValuePlanned: null
							}
						}
					],
					roomRates: [
						{
							key: '0.0.2025.11.01',
							roomBlockIndex: 0,
							dayNumber: 0,
							roomType: 0,
							rate: 2,
							requested: 10,
							offered: 10
						},
						{
							key: '0.0.2025.11.01',
							roomBlockIndex: 0,
							dayNumber: 1,
							roomType: 0,
							rate: 2,
							requested: 20,
							offered: 20
						},
						{
							key: '1.0.2025.11.01',
							roomBlockIndex: 1,
							dayNumber: 0,
							roomType: 0,
							rate: 5,
							requested: 10,
							offered: 10
						},
						{
							key: '1.0.2025.11.01',
							roomBlockIndex: 1,
							dayNumber: 1,
							roomType: 0,
							rate: 0, // rate is required but not provided
							requested: 20,
							offered: 20
						},
						{
							key: '1.1.2025.11.01',
							roomBlockIndex: 1,
							dayNumber: 0,
							roomType: 1,
							rate: 5,
							requested: 5,
							offered: 5
						},
						{
							key: '1.1.2025.11.01',
							roomBlockIndex: 1,
							dayNumber: 1,
							roomType: 1,
							rate: 5, // rate not required but provided
							requested: 0,
							offered: 0
						}
					]
				}
			}

			quoteRequest = {
				...quoteRequest,
				eventPlan: {
					...quoteRequest.eventPlan,
					roomBlocksRequired: true,
					roomBlockRequests: [
						{
							id: 'a438055c-e935-478b-bd3a-307d6c1466cc',
							name: 'Room Block #1',
							peakSize: 20,
							paymentMethod: 'individualsPay',
							allowPartialProposals: false,
							minimumPeakRooms: null,
							roomTypeRequests: [
								{
									roomType: 0,
									budget: 0,
									budgetLow: null,
									roomNights: [
										{
											dayNumber: 0,
											roomsRequested: 10
										},
										{
											dayNumber: 1,
											roomsRequested: 20
										}
									],
									notes: null
								}
							],
							created: '2025-05-03T09:17:13+00:00'
						},
						{
							id: '329d2d32-b390-4c3c-9606-55fbda07c9a6',
							name: 'Room Block #2',
							peakSize: 20,
							paymentMethod: 'individualsPay',
							allowPartialProposals: null,
							minimumPeakRooms: null,
							roomTypeRequests: [
								{
									roomType: 0,
									budget: 0,
									budgetLow: null,
									roomNights: [
										{
											dayNumber: 0,
											roomsRequested: 10
										},
										{
											dayNumber: 1,
											roomsRequested: 20
										}
									],
									notes: null
								},
								{
									roomType: 1,
									budget: 0,
									budgetLow: null,
									roomNights: [
										{
											dayNumber: 0,
											roomsRequested: 5
										},
										{
											dayNumber: 1,
											roomsRequested: 0
										}
									],
									notes: null
								}
							],
							created: '2025-05-05T10:37:20+00:00'
						}
					]
				},
				proposalRequest
			}

			// Call the function with room blocks not required
			QuoteRequestValidator.validateRoomBlocks(
				quoteRequest,
				errors,
				proposalRequest
			)

			// Verify no errors were added since room blocks aren't required
			expect(errors.roomblocksErrors.size).toBe(1)
		})
	})
})
