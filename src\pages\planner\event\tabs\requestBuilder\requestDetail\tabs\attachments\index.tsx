/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/max-params */
import { getAccessToken } from 'lib/interceptor/axios.interceptor'
import type { Attachment } from 'lib/store/attachmentsStore'
import useAttachmentsStore from 'lib/store/attachmentsStore'
import type { IAttachment } from 'models/attachments'
import { toast } from 'react-toastify'
import { SearchField } from './helper'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useParams } from 'react-router-dom'
import HSDropdownButton from 'components/dropdown'
import HSButton from 'components/button'
import HSDropzone from 'components/dropzone'
import { Drawer } from 'flowbite-react'
import HSDrawer from 'components/drawer'
import { downloadFiles } from 'components/documents/helper'
import {
	actionsTemplate,
	formatSharedWith,
	nameTemplate,
	ownerTemplate
} from './templates'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faFile } from '@fortawesome/pro-light-svg-icons'
import { convertBytes } from 'lib/helpers/attachments'
import HSTextField from 'components/textField'
import { getRfpAttachmentUrl } from 'lib/helpers/document'
import {
	useAddAttachments,
	useDeleteAttachments,
	useShareAttachments,
	useUpdateAttachmentMetadata
} from 'lib/services/eventPlans.service'
import { debounce } from 'es-toolkit'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import HSTable from 'components/table'
import HSModal from 'components/modal'
import HSCheckbox from 'components/checkbox'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'

const EventAttachments = () => {
	const { eventId } = useParams()
	const tableReference = useRef<{ resetSelection: () => void }>(null)
	const { eventInfo } = eventInfoStore()
	const { attachments, setObject: setAttachments } = useAttachmentsStore()
	const { userProfile } = useUserProfileContext()
	const [searchString, setSearchString] = useState('')
	const [showUploadDrawer, setShowUploadDrawer] = useState<boolean>(false)
	const [showShareModal, setShowShareModal] = useState<boolean>(false)
	const [shareWithAll, setShareWithAll] = useState<boolean>(false)
	const [shareWithHotels, setShareWithHotels] = useState<
		Record<string, string>
	>({})
	const [acceptedFiles, setAcceptedFiles] = useState<File[]>()
	const [selectedAttachmentIds, setSelectedAttachmentIds] = useState<string[]>(
		[]
	)
	const [attachmentMetaData, setAttachmentMetaData] = useState<{
		displayName: string
		visibleToVenues: boolean
	}>({
		displayName: '',
		visibleToVenues: false
	})
	const [selectedAttachment, setSelectedAttachment] =
		useState<Attachment | null>(null)
	const [showEditDrawer, setShowEditDrawer] = useState<boolean>(false)
	const [gridData, setGridData] = useState<Attachment[] | null>(null)

	const { mutateAsync: deleteAttachments } = useDeleteAttachments()
	const { mutateAsync: shareAttachments } = useShareAttachments()
	const { mutateAsync: addAttachments } = useAddAttachments()
	const { mutateAsync: updateMetadata } = useUpdateAttachmentMetadata()

	const setAttachmentsState = (
		attachments_: IAttachment[],
		accessToken: string
	) => {
		setAttachments(
			attachments_.map((a: IAttachment) => ({
				...a,
				url: getRfpAttachmentUrl(a, accessToken),
				folder: a.tags.length > 0 ? a.tags[0] : ''
			}))
		)
	}
	const onDeleteAttachments = (attachmentsToDelete: Attachment[]) => {
		toast.info('Deleting attachment(s)...')

		getAccessToken()
			.then(accessToken => {
				deleteAttachments({
					eventId: eventId ?? '',
					venueId: '',
					data: attachmentsToDelete.map(a => a.id)
				})
					.then(response => {
						toast.success('Attachment(s) deleted.')
						setAttachmentsState([...response], accessToken as string)
					})
					.catch((error: unknown) => {
						toast.error('Failed to delete attachments')
						console.error('Failed to delete attachments:', error)
					})
					.finally(() => {
						setSelectedAttachmentIds([])
						tableReference.current?.resetSelection()
					})
			})
			.catch((error: unknown) => {
				toast.error('Failed to delete attachments')
				console.error('Failed to get token:', error)
			})
		setSelectedAttachmentIds([])
		tableReference.current?.resetSelection()
	}

	const onAddAttachments = (
		acceptedFiles_: File[] | undefined,
		signatureRequired: boolean | null,
		documentContext: unknown
	) => {
		if (acceptedFiles_ === undefined) {
			toast.warn(`Please select a file to upload`)
			return
		}
		toast.info(`Uploading ${acceptedFiles_.length} files`)
		const formData = new FormData()
		let f = 0
		for (const file of acceptedFiles_) {
			formData.append(`file${f}`, file, file.name)
			formData.append(
				`props${f}`,
				JSON.stringify({
					signatureRequired,
					documentContext: documentContext === false ? null : documentContext,
					visibleToVenues: true
				})
			)
			f += 1
		}

		getAccessToken()
			.then(accessToken => {
				addAttachments({
					venueId: '',
					eventId: eventId ?? '',
					meetingSpaceRequestIds: null,
					data: formData
				})
					.then(response => {
						toast.success(`${acceptedFiles_.length} files uploaded`)
						setAttachmentsState([...response], accessToken as string)
					})
					.catch((error: unknown) => {
						toast.error('Failed to upload files')
						console.error('Failed to upload files:', error)
					})
			})
			.catch((error: unknown) => {
				toast.error('Failed to upload files')
				console.error('Failed to get token:', error)
			})
	}

	const onUpdateAttachmentMetadata = (
		selectedAttachment_: Attachment | null,
		attachmentMetaData_: {
			displayName: string
			visibleToVenues: boolean
		}
	) => {
		updateMetadata({
			attachmentId: selectedAttachment_?.id ?? '',
			eventId: eventId ?? '',
			venueId: '',
			data: { ...selectedAttachment_, ...attachmentMetaData_ }
		})
			.then(response => {
				console.log('Updated attachment metadata', response)
				const updatedAttachments = [
					...attachments.filter(a => a.id !== response.id),
					{
						...attachments.find(a => a.id === response.id),
						displayName: response.displayName,
						id: response.id
					} as Attachment
				]
				setAttachments(updatedAttachments)
			})
			.catch((error: unknown) =>
				console.error('Error updating attachment metadata', error)
			)
			.finally(() => {
				setShowEditDrawer(false)
			})
	}

	const resetMetadata = () => {
		setAttachmentMetaData({
			displayName: '',
			visibleToVenues: false
		})
	}

	const onHide = () => {
		setShowUploadDrawer(false)
		setAcceptedFiles(undefined)
		resetMetadata()
	}

	const onHideEditDrawer = () => {
		setShowEditDrawer(false)
		setSelectedAttachment(null)
		resetMetadata()
	}

	const onClickDownload = async (selectedIds: string[]) => {
		const accessToken = await getAccessToken()
		const selectedAttachments = attachments.filter(a =>
			selectedIds.includes(a.id)
		)
		downloadFiles(
			selectedAttachments.map(index => ({
				href: getRfpAttachmentUrl(index, accessToken ?? ''),
				filename: index.name
			}))
		).catch((error: unknown) => {
			console.error('Error downloading files', error)
		})
		setSelectedAttachmentIds([])
		tableReference.current?.resetSelection()
	}

	const onClickShareModal = async (selectedIds: string[]) => {
		setSelectedAttachmentIds(selectedIds)
		setShowShareModal(true)
	}

	const onCloseShareModal = () => {
		setSelectedAttachmentIds([])
		tableReference.current?.resetSelection()
		setShowShareModal(false)
		setShareWithAll(false)
		setShareWithHotels({})
	}

	const onClickShare = async (selectedIds: string[]) => {
		shareAttachments({
			attachmentIds: selectedIds,
			eventId: eventId ?? '',
			shareWithAll,
			shareWith: shareWithAll ? {} : shareWithHotels,
			includeInReports: false
		})
			.then(response => {
				toast.success('Attachment(s) shared with hotels')
				console.log('Updated attachment metadata', response)

				setAttachments(
					response.map(
						(a: IAttachment) =>
							({
								...attachments.find(attach => attach.id === a.id),
								...a
							}) as Attachment
					)
				)
			})
			.catch((error: unknown) => {
				console.error('Error sharing attachments', error)
				toast.error('Failed to share attachment(s)')
			})
			.finally(() => {
				setSelectedAttachmentIds([])
				tableReference.current?.resetSelection()
				setShareWithAll(false)
				setShareWithHotels({})
			})
		onCloseShareModal()
	}

	const onEditAttachment = (attachment: Attachment) => {
		console.log('Edit clicked', attachment)
		setSelectedAttachment(attachment)
		setAttachmentMetaData({
			displayName: attachment.displayName ?? attachment.name,
			visibleToVenues: attachment.visibleToVenues
		})
		setShowEditDrawer(true)
	}

	const updateAttachments = () => {
		onUpdateAttachmentMetadata(selectedAttachment, attachmentMetaData)
	}

	const uploadAttachment = () => {
		onAddAttachments(acceptedFiles, true, null)
		setShowUploadDrawer(false)
		setAcceptedFiles(undefined)
		resetMetadata()
	}

	const onSearch = useCallback(() => {
		if (searchString)
			setGridData(
				attachments.filter(
					h =>
						h.name.toLowerCase().includes(searchString.toLowerCase()) ||
						h.owner.toLowerCase().includes(searchString.toLowerCase())
				)
			)
		else setGridData(attachments)
	}, [searchString, attachments])

	useEffect(() => {
		const debouncedSearch = debounce(onSearch, 300)
		debouncedSearch()

		return () => {
			debouncedSearch.cancel()
		}
	}, [onSearch])

	return (
		<>
			<div className='flex flex-col gap-4 p-6'>
				<div className='flex flex-col gap-4'>
					<div className='flex items-center justify-between'>
						<div className='flex flex-col'>
							<div className='text-lg font-semibold text-gray-900'>
								Manage Your Uploaded RFP Documents
							</div>
							<div className='text-xs font-normal text-gray-500'>
								Review all uploaded files here. You can rename, organize, or
								remove files as needed before sharing them with hotels
							</div>
						</div>
						<div className='flex w-96 flex-row gap-2'>
							<SearchField
								searchString={searchString}
								setSearchString={setSearchString}
							/>
						</div>
					</div>
					<div className='flex items-center justify-between pt-2'>
						<div className='flex gap-[10px]'>
							<div className='text-sm font-medium text-gray-900'>
								{attachments.length} Document{attachments.length > 1 ? 's' : ''}
							</div>
						</div>
						<div className='flex flex-row gap-[10px]'>
							<HSDropdownButton
								label='Actions'
								color='light'
								items={[
									{
										id: 'download',
										item: 'Download',
										clickFunction: async () =>
											onClickDownload(selectedAttachmentIds),
										disabled: selectedAttachmentIds.length === 0
									},
									{
										id: 'share',
										item: 'Share',
										clickFunction: async () =>
											onClickShareModal(selectedAttachmentIds),
										disabled: selectedAttachmentIds.length === 0
									},
									{
										id: 'delete',
										item: 'Delete',
										clickFunction: () =>
											onDeleteAttachments(
												attachments.filter(a =>
													selectedAttachmentIds.includes(a.id)
												)
											),
										disabled: selectedAttachmentIds.length === 0
									}
								]}
							/>
							<HSButton
								color='primary'
								onClick={() => setShowUploadDrawer(true)}
							>
								Upload New Document
							</HSButton>
						</div>
					</div>
				</div>
				<div>
					<HSTable
						ref={tableReference}
						allowSelection
						allowPaging
						rows={gridData ?? []}
						columns={[
							{
								headerText: 'Document Name',
								field: 'name',
								render: (item: Attachment) => nameTemplate(item),
								sortable: true,
								clipMode: 'ellipsis',
								width: 300
							},
							{
								headerText: 'Document Owner',
								field: 'owner',
								render: (item: Attachment) => ownerTemplate(item, eventInfo),
								sortable: true,
								clipMode: 'ellipsis'
							},
							{
								headerText: 'Shared With',
								field: 'visibleToVenues',
								render: (item: Attachment) => formatSharedWith(item),
								sortable: true
							},
							{
								headerText: 'Actions',
								headerAlign: 'center',
								field: 'id',
								render: (item: Attachment) =>
									actionsTemplate(
										item,
										onDeleteAttachments,
										onEditAttachment,
										onClickDownload,
										userProfile?.email ?? ''
									)
							}
						]}
						rowSelected={event => {
							if (Array.isArray(event.data)) {
								setSelectedAttachmentIds(selectedIds => [
									...selectedIds,
									...(event.data as Attachment[]).map(a => a.id)
								])
							} else {
								setSelectedAttachmentIds(selectedIds => [
									...selectedIds,
									(event.data as Attachment).id
								])
							}
						}}
						rowDeselected={event => {
							if (Array.isArray(event.data)) {
								setSelectedAttachmentIds(selectedIds =>
									selectedIds.filter(
										id =>
											!(event.data as Attachment[]).map(a => a.id).includes(id)
									)
								)
							} else {
								setSelectedAttachmentIds(selectedIds =>
									selectedIds.filter(id => id !== (event.data as Attachment).id)
								)
							}
						}}
					/>
				</div>
			</div>
			{showUploadDrawer ? (
				<HSDrawer
					id='add-new-document-dialog'
					onClose={onHide}
					open={showUploadDrawer}
					position='right'
					style={{ width: '400px' }}
				>
					<div className='flex h-[calc(100vh-2rem)] flex-col justify-between'>
						<div className='flex flex-col gap-5'>
							<Drawer.Header
								title='Upload New Documents'
								titleIcon={() => null}
							/>

							<div>
								<HSDropzone
									acceptedFiles={acceptedFiles}
									setAcceptedFiles={setAcceptedFiles}
									type='document'
									acceptedFileTypes={{
										'application/pdf': [],
										'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
											[],
										'image/jpeg': [],
										'image/png': []
									}}
									subText='PDF, DOCX, JPG, and PNG.'
								/>
							</div>
						</div>
						<div className='flex gap-4'>
							<HSButton color='light' className='grow' onClick={onHide}>
								Cancel
							</HSButton>
							<HSButton
								className='grow'
								onClick={uploadAttachment}
								disabled={(acceptedFiles?.length ?? 0) <= 0}
							>
								Upload
							</HSButton>
						</div>
					</div>
				</HSDrawer>
			) : null}
			{showEditDrawer ? (
				<HSDrawer
					id='add-new-document-dialog'
					onClose={onHideEditDrawer}
					open={showEditDrawer}
					position='right'
					style={{ width: '400px' }}
				>
					<div
						className='flex flex-col justify-between'
						style={{
							height: 'calc(100vh - 2rem)'
						}}
					>
						<div className='flex flex-col gap-5'>
							<Drawer.Header title='Edit Document' titleIcon={() => null} />
							<div className='flex gap-3'>
								<div className='text-2xl'>
									<FontAwesomeIcon className='text-gray-500' icon={faFile} />
								</div>
								<div className='flex flex-1 flex-col'>
									<div className='text-sm font-medium text-gray-900'>
										{selectedAttachment?.displayName ??
											selectedAttachment?.name}
										.{selectedAttachment?.extension}
									</div>
									<div className='text-xs text-gray-500'>
										{convertBytes(Number(selectedAttachment?.fileSize))}
									</div>
								</div>
							</div>
							<div>
								<HSTextField
									label='Document Name'
									value={attachmentMetaData.displayName}
									onChange={event =>
										setAttachmentMetaData(previousState => ({
											...previousState,
											displayName: event.target.value
										}))
									}
								/>
							</div>
						</div>
						<div className='flex gap-4'>
							<HSButton
								color='light'
								className='grow'
								onClick={onHideEditDrawer}
							>
								Cancel
							</HSButton>
							<HSButton className='grow' onClick={updateAttachments}>
								Update
							</HSButton>
						</div>
					</div>
				</HSDrawer>
			) : null}
			{showShareModal ? (
				<HSModal
					openModal={showShareModal}
					onClose={onCloseShareModal}
					header='Share with Hotel(s)'
					size='lg'
				>
					<div className='flex flex-col gap-2 p-4'>
						<HSCheckbox
							label='Share with All Hotels'
							checked={shareWithAll}
							onClick={() =>
								setShareWithAll((previous: boolean) => {
									if (!previous) {
										setShareWithHotels({})
									}
									return !previous
								})
							}
						/>
						{shareWithAll
							? null
							: eventInfo?.proposalRequests
									?.filter(item => item.venueId !== '')
									.map(item => (
										<HSCheckbox
											key={item.id}
											label={item.venueName ?? ''}
											checked={!!shareWithHotels[item.venueId ?? '']}
											onClick={() => {
												setShareWithHotels(previous => {
													const updated = { ...previous }
													if (updated[item.venueId ?? '']) {
														Reflect.deleteProperty(updated, item.venueId ?? '')
													} else {
														updated[item.venueId ?? ''] = item.venueName ?? ''
													}
													console.log('updated', updated)
													return updated
												})
											}}
										/>
									))}
						<div className='mt-4 flex justify-center gap-4'>
							<HSButton
								color='light'
								className='w-full'
								onClick={onCloseShareModal}
							>
								Cancel
							</HSButton>
							<HSButton
								className='w-full'
								color='danger'
								onClick={async () => onClickShare(selectedAttachmentIds)}
							>
								Share
							</HSButton>
						</div>
					</div>
				</HSModal>
			) : null}
		</>
	)
}

export default EventAttachments
