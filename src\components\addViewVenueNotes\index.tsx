import { collectionNames } from 'components/profile/tabs/userProfileComments/helper'
import VenueNotesModal from 'components/venueNotesModal'
import { useGetComments } from 'lib/services/comments.service'
import type { IComment } from 'models/comments'
import type { Venue } from 'models/venue'
import { useEffect, useState } from 'react'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import HSButton from 'components/button'

interface IAddViewVenueNotesProperties {
	venue: Partial<Venue>
}

const AddViewVenueNotes = (properties: IAddViewVenueNotesProperties) => {
	const { venue } = properties
	const { userProfile } = useUserProfileContext()
	const [comments, setComments] = useState<IComment[] | undefined>([])
	const [showModal, setShowModal] = useState(false)
	const { data } = useGetComments(
		collectionNames.userProfiles,
		userProfile?.id ?? '',
		'hotel.property',
		venue.id ?? ''
	)

	useEffect(() => {
		if (data) setComments(data)
	}, [data])

	return (
		<div>
			<HSButton color='text' onClick={() => setShowModal(s => !s)}>
				{comments?.length} Note{comments && comments.length > 1 ? 's' : 's'}
			</HSButton>
			<VenueNotesModal
				venue={venue}
				show={showModal}
				onHide={() => {
					// getComments()
					// 	.then(r => setComments(r.data))
					// 	.catch((error: unknown) => console.error(error))
					setShowModal(false)
				}}
			/>
		</div>
	)
}

export default AddViewVenueNotes
