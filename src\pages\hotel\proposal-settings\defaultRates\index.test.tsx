import { render, screen, fireEvent } from '@testing-library/react'
import DefaultRates from '.'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import type { ICurrency } from 'lib/helpers'
import useHotelStore from 'lib/store/hotelStore'
import type { MockedFunction } from 'vitest'
import { vi } from 'vitest'

vi.mock('lib/store/hotelStore')
const useHotelStoreMock = vi.mocked(useHotelStore)

vi.mock('lib/contexts/currency.context', () => ({
	useCurrencyContext: vi.fn()
}))

const mockCurrencies: Record<string, ICurrency> = {
	USD: {
		symbol: '$',
		name: 'United States Dollar',
		code: 'USD'
	},
	EUR: {
		symbol: '€',
		name: 'Euro',
		code: 'EUR'
	}
}

describe('DefaultRates Component', () => {
	const setPropertyMock = vi.fn()
	beforeEach(() => {
		useHotelStoreMock.mockReturnValue({
			venue: {},
			setProperty: setPropertyMock
		})
		;(
			useCurrencyContext as MockedFunction<typeof useCurrencyContext>
		).mockReturnValue({
			currencies: mockCurrencies,
			setCurrencies: vi.fn()
		})
	})

	afterEach(() => {
		vi.clearAllMocks()
	})

	it('renders correctly', () => {
		render(<DefaultRates />)

		expect(screen.getByText('Proposal General Settings')).toBeInTheDocument()
		expect(screen.getByTestId('currency-picker')).toHaveValue('USD')
		expect(screen.getByTestId('currency-picker')).toHaveTextContent(
			'United States Dollar'
		)
		expect(screen.getByTestId('proposal-expiration-date')).toHaveValue('')
		expect(screen.getByTestId('reservation-cut-off-date')).toHaveValue('')
		expect(screen.getByTestId('comments')).toHaveValue('')
	})

	it('renders with default values', () => {
		useHotelStoreMock.mockReturnValue({
			venue: {
				currencyCode: 'USD',
				defaultProposalExpirationDays: 30,
				defaultCutOffDaysBeforeCheckIn: 10,
				defaultComments: 'Default comment text'
			}
		})
		render(<DefaultRates />)

		expect(screen.getByTestId('currency-picker')).toHaveValue('USD')
		expect(screen.getByTestId('currency-picker')).toHaveTextContent(
			'United States Dollar'
		)
		expect(screen.getByTestId('proposal-expiration-date')).toHaveValue('30')
		expect(screen.getByTestId('reservation-cut-off-date')).toHaveValue('10')
		expect(screen.getByTestId('comments')).toHaveValue('Default comment text')
	})

	it('handles currency change', () => {
		render(<DefaultRates />)
		const currencyPicker = screen.getByRole('combobox')
		fireEvent.change(currencyPicker, { target: { value: 'EUR' } })
		expect(setPropertyMock).toHaveBeenCalledWith('currencyCode', 'EUR')
	})

	it('handles proposal expiration date change', () => {
		render(<DefaultRates />)
		const input = screen.getByTestId('proposal-expiration-date')
		fireEvent.change(input, { target: { value: '45' } })
		expect(setPropertyMock).toHaveBeenCalledWith(
			'defaultProposalExpirationDays',
			'45'
		)
	})

	it('handles cut-off date change', () => {
		render(<DefaultRates />)
		const input = screen.getByTestId('reservation-cut-off-date')
		fireEvent.change(input, { target: { value: '15' } })
		expect(setPropertyMock).toHaveBeenCalledWith(
			'defaultCutOffDaysBeforeCheckIn',
			'15'
		)
	})

	it('handles comments change', () => {
		render(<DefaultRates />)
		const textarea = screen.getByTestId('comments')
		fireEvent.change(textarea, { target: { value: 'Updated comment text' } })
		expect(setPropertyMock).toHaveBeenCalledWith(
			'defaultComments',
			'Updated comment text'
		)
	})
})
