import HSButton from 'components/button'
import HSDropdownButton from 'components/dropdown'
import AddHotelToSearch from 'components/hotelSearch/drawers/addToSearch'
import Loader from 'components/loader'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { contentTypes, engagementTypes } from 'lib/helpers/contentEngagement'
import { trackEngagement } from 'lib/services/contentEngagement.service'
import { createSummaryProposalRequest } from 'lib/services/hotel.hub.service'
import type { EventPlan, ProposalRequest } from 'models/proposalResponseMonitor'
import type { Venue } from 'models/venue'
import { useEffect, useState } from 'react'

interface IAddRemoveDestinationButton {
	eventPlan?: EventPlan | null
	hotel: Venue
	disabledFields: boolean
	addRemoveProposalRequests: (
		proposalRequest: Partial<ProposalRequest>[],
		callback: () => void,
		isAdmin: boolean
	) => Promise<void>
	suppressProfileConversion?: boolean
}
const AddRemoveHotelButton = (properties: IAddRemoveDestinationButton) => {
	const {
		addRemoveProposalRequests,
		disabledFields,
		eventPlan,
		hotel,
		suppressProfileConversion
	} = properties
	const { userProfile } = useUserProfileContext()
	const [isDisabled, setIsDisabled] = useState(disabledFields)
	const [working, setWorking] = useState(false)
	const [isAdded, setIsAdded] = useState(
		eventPlan?.proposalRequests?.some(pr => pr.venueId === hotel.id)
	)
	const [showSiteSearchDrawer, setShowSiteSearchDrawer] = useState(false)

	useEffect(() => {
		if (!disabledFields) {
			setIsDisabled(false)
		} else if (
			typeof addRemoveProposalRequests === 'function' &&
			eventPlan?.proposalRequests
		) {
			const pr = eventPlan.proposalRequests.find(
				item => item.venueId === hotel.id
			)
			if (pr === undefined) {
				setIsDisabled(false)
				return
			}
			setIsDisabled(pr.status !== 'Pending')
		} else {
			setIsDisabled(true)
		}
	}, [hotel.id, addRemoveProposalRequests, disabledFields, eventPlan])

	useEffect(() => {
		if (eventPlan) {
			setIsAdded(
				eventPlan.proposalRequests?.some(pr => pr.venueId === hotel.id)
			)
		}
	}, [eventPlan, eventPlan?.proposalRequests, hotel.id])

	const onButtonClick = async (comparable: boolean) => {
		if (eventPlan?.id) {
			setWorking(true)
			setIsDisabled(true)
			if (typeof addRemoveProposalRequests === 'function' && userProfile) {
				if (comparable) {
					await createSummaryProposalRequest(eventPlan.id, hotel, userProfile)
					setWorking(false)
				} else {
					await addRemoveProposalRequests(
						[
							{
								status: 'Pending',
								selected: false,
								venueId: hotel.id ?? '',
								venueName: hotel.name,
								venueLocation: `${hotel.city}, ${hotel.state}`,
								geolocation: hotel.geolocation,
								destinations: hotel.destinations,
								propertySellers: hotel.propertySellers,
								createdBy: userProfile.isAdmin
									? (eventPlan.planners?.[0]?.id ?? eventPlan.createdBy ?? '')
									: userProfile.id,
								createdByOrganizationId: userProfile.isAdmin
									? (eventPlan.organizationId ?? '')
									: userProfile.organizationId,
								createdByOrganizationName: userProfile.isAdmin
									? (eventPlan.organizationName ?? '')
									: (userProfile.organizationName ?? '')
							}
						],
						() => {
							setWorking(false)
						},
						userProfile.isAdmin
					)

					if (
						!isAdded &&
						eventPlan.itemType === 'eventPlan' &&
						!suppressProfileConversion
					) {
						await trackEngagement(hotel.id ?? '', {
							contentItemType: contentTypes.profile,
							contentItemId: hotel.id ?? '',
							engagementType: engagementTypes.conversion
						})
					}
				}
			}
		} else {
			setShowSiteSearchDrawer(true)
		}
	}

	if (!isAdded && userProfile?.isAdmin && eventPlan?.itemType === 'eventPlan') {
		return (
			<HSDropdownButton
				items={[
					{
						item: 'Add as Hopskip Suggestion',
						id: 'hopskip-suggestion',
						clickFunction: async () => onButtonClick(true)
					},
					{
						item: 'Add as Planner',
						id: 'planner',
						clickFunction: async () => onButtonClick(false)
					}
				]}
			/>
		)
	}

	return (
		<>
			<HSButton
				color={isAdded ? 'failure' : 'primary'}
				onClick={async () => onButtonClick(false)}
				size='sm'
				disabled={isDisabled}
				className='w-32'
				outline
			>
				{working ? (
					<Loader size='sm' />
				) : (
					<div>
						{isAdded
							? 'Remove'
							: `Add  to ${eventPlan?.itemType === 'eventPlan.siteSearch' ? 'List' : 'RFP'}`}
					</div>
				)}
			</HSButton>
			{showSiteSearchDrawer ? (
				<AddHotelToSearch
					onClose={() => {
						setShowSiteSearchDrawer(false)
					}}
					venueToAdd={{ ...hotel }}
				/>
			) : null}
		</>
	)
}

export default AddRemoveHotelButton
