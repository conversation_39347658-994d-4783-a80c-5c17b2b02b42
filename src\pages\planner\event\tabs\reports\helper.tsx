import type { EventPlan } from 'models/proposalResponseMonitor'
import type { IUserProfile } from 'models/userProfiles'

interface ReportOption {
	label: string
	default: boolean
	hide?: (event: EventPlan | null, userProfile: IUserProfile) => boolean
}

interface Report {
	name: string
	format: string
	licenseTiers: string[]
	combine: boolean
	disabled: (event: EventPlan | null, override?: boolean) => boolean
	disabledMessage?: (event: EventPlan | null) => string
	description: string
	options?: Record<string, ReportOption>
	hasCurrency?: boolean
	template?: string
	beta?: boolean
	hide?: (event: EventPlan | null, userProfile: IUserProfile) => boolean
	adminOnly?: boolean
	afterDownload?: (parameters: {
		isAdmin: boolean
		eventPlan: EventPlan | null
		shortlistId: string
	}) => void
}

// Report formats
export const reportFormats: Record<
	string,
	{ key: string; label: string; color: string }
> = {
	xlsx: { key: 'xlsx', label: 'Excel', color: 'green' },
	pdf: { key: 'pdf', label: 'PDF', color: 'red' },
	pptx: { key: 'pptx', label: 'PowerPoint', color: 'orange' }
}

const ProposalsNotYetReceived = new Set(['New', 'Submitted'])

// Reports definition
export const reports: Record<string, Report> = {
	rfpsummary: {
		name: 'RFP',
		format: reportFormats.pdf.key,
		licenseTiers: ['Essential', 'Premium', 'Pro'],
		combine: false,
		disabled: () => false,
		description: 'Your RFP in a PDF format',
		options: {
			programinfo: { label: 'Program Information', default: true },
			rooms: { label: 'Room Blocks', default: true },
			meeting: { label: 'Meeting Agenda', default: true },
			fandb: { label: 'Food & Beverage', default: true },
			questions: { label: 'Questions', default: true },
			concessionRequests: { label: 'Concession Requests', default: true },
			contract: { label: 'Contract Information', default: true },
			clauses: { label: 'Requested Contract Clauses', default: true },
			attachments: { label: 'Attached Documents', default: true },
			notes: { label: 'Notes (Blank Page)', default: true }
		},
		hasCurrency: true
	},
	proposalcomparison: {
		name: 'Hotel Proposal Comparison',
		format: reportFormats.xlsx.key,
		licenseTiers: ['Premium', 'Pro'],
		combine: true,
		disabled: (event, override) => {
			if (override) return false
			if (event?.status !== 'New') return false
			return event.proposalRequests?.length === 0
		},
		disabledMessage: event => {
			if (Number(event?.proposalRequests?.length) > 0) {
				return 'This report will be available once you have sent your RFP to hotels'
			}
			return 'This report will be available when one or more hotels have been added to the RFP'
		},
		description:
			'Compare the key criteria across all of the proposals you received from hotels',
		options: {
			responseComments: { label: 'Display Comments', default: true },
			displayAttrition: {
				label: 'Display Attrition %',
				default: true,
				hide: event => event?.doNotCollectAttritionRate === true
			},
			displayCommission: {
				label: 'Display Commission %',
				default: false,
				hide: event => !event?.commissionable
			},
			hotelNotes: { label: 'Notes from the hotelier', default: true },
			noresponse: { label: 'Hotels that did not respond', default: true },
			hotelturneddown: { label: 'Hotels that declined', default: true },
			hotelContacts: { label: 'Hotel contact details', default: true },
			notes: { label: 'My Proposal Notes', default: true },
			'sort-hotel-city': { label: 'Sort Hotels by City', default: false }
		},
		hasCurrency: true,
		afterDownload: ({ isAdmin, eventPlan, shortlistId }) => {
			if (isAdmin) return

			const activeProposals = eventPlan?.proposalRequests?.filter(
				pr =>
					![
						'New',
						'Pending',
						'Cancelled',
						'Declined',
						'Received',
						'Removed'
					].includes(pr.status ?? '')
			)

			if (shortlistId) {
				const shortlist = eventPlan?.shortlists?.find(
					sl => sl.id === shortlistId
				)
				if (shortlist) {
					for (const venueId of Object.keys(shortlist.hotels).filter(v =>
						activeProposals?.some(pr => pr.venueId === v)
					))
						console.log('trackView', eventPlan?.id, venueId)

					//  trackView(eventPlan.id, venueId)
				}
			} else {
				// 	for (const pr of activeProposals) trackView(eventPlan.id, pr.venueId)
			}
		}
	},
	proposalfinancials: {
		name: 'Proposal Financials',
		format: reportFormats.xlsx.key,
		licenseTiers: ['Premium', 'Pro'],
		combine: true,
		disabled: event => ProposalsNotYetReceived.has(event?.status ?? ''),
		disabledMessage: () =>
			'This report will be available when hotels have submitted proposals',
		description:
			'Your estimated total proposal cost for each hotel broken down by the largest cost areas',
		hasCurrency: true
	},
	meetingagenda: {
		name: 'Meeting Agenda',
		format: reportFormats.xlsx.key,
		licenseTiers: ['Premium', 'Pro'],
		combine: true,
		disabled: event =>
			event?.meetingSpaceRequired === false ||
			event?.meetingSpaceRequests?.length === 0,
		disabledMessage: event =>
			ProposalsNotYetReceived.has(event?.status ?? '')
				? 'This report will be available when hotels have submitted proposals'
				: 'This report is available for RFPs that include meeting space',
		description:
			'Your meeting agenda that was generated from your meeting space request'
	},
	meetingproposal: {
		name: 'Meeting Space Proposals',
		format: reportFormats.xlsx.key,
		licenseTiers: ['Premium', 'Pro'],
		combine: true,
		disabled: event =>
			ProposalsNotYetReceived.has(event?.status ?? '') ||
			event?.meetingSpaceRequired === false ||
			event?.meetingSpaceRequests?.length === 0,
		disabledMessage: event =>
			ProposalsNotYetReceived.has(event?.status ?? '')
				? 'This report will be available when hotels have submitted proposals'
				: 'This report is available for RFPs that include meeting space',
		description:
			"A breakdown of each hotel's meeting space proposal showing details of the hotel meeting rooms assigned to each meeting room request"
	},
	foodandbeverageproposal: {
		name: 'Food & Beverage Proposals',
		format: reportFormats.xlsx.key,
		licenseTiers: ['Premium', 'Pro'],
		combine: true,
		disabled: event =>
			ProposalsNotYetReceived.has(event?.status ?? '') ||
			event?.meetingSpaceRequired === false ||
			event?.meetingSpaceRequests?.length === 0 ||
			event?.foodAndBeverageRequests?.length === 0,
		disabledMessage: event =>
			ProposalsNotYetReceived.has(event?.status ?? '')
				? 'This report will be available when hotels have submitted proposals'
				: 'This report is available for RFPs that include meeting space and food & beverage',
		description:
			"A breakdown of each hotel's food and beverage proposal showing details of the hotel F&B quantities and rates",
		hasCurrency: true
	},
	commissionearnings: {
		name: 'My Commission Earnings',
		format: reportFormats.xlsx.key,
		licenseTiers: ['Premium', 'Pro'],
		combine: false,
		disabled: event => ProposalsNotYetReceived.has(event?.status ?? ''),
		disabledMessage: () =>
			'This report will be available when hotels have submitted proposals',
		hide: event => !event?.commissionable,
		description: `Estimate the projected commission earned per hotel proposal`,
		hasCurrency: true
	},
	presentation: {
		name: 'Presentation',
		format: reportFormats.pptx.key,
		licenseTiers: ['Premium', 'Pro'],
		beta: false,
		combine: false,
		disabled: event => ProposalsNotYetReceived.has(event?.status ?? ''),
		disabledMessage: () =>
			'This report will be available when hotels have submitted proposals',
		description:
			'Prepare a PowerPoint presentation based on proposals received',
		template: 'powerpoint',
		hasCurrency: true
	}
}
