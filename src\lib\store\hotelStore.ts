/* eslint-disable @typescript-eslint/max-params */
import { updateVenue } from 'lib/services/hotels.service'
import { debounce } from 'es-toolkit'
import type { Venue } from 'models/venue'
import type { ProfileInfoAction } from 'pages/hotel/helper'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

interface State {
	saving: boolean
	venue: Venue
}

interface Actions {
	setObject: (value: Venue, clientOnly?: boolean) => void
	setProperty: (
		name: string,
		value: unknown,
		type?: string,
		clientOnly?: boolean
	) => void
	mergeProperties: (value: Partial<Venue>, clientOnly?: boolean) => void
	setSaving: (saving: boolean) => void
}

const debouncedUsePutIntent = debounce(
	async (
		id: string | null,
		intent: ProfileInfoAction,
		mergeProperties: (value: Partial<Venue>, clientOnly?: boolean) => void,
		setSaving: (saving: boolean) => void
	) => {
		if (id) {
			setSaving(true)
			try {
				const response = await updateVenue(id, {
					type: intent.type,
					value:
						intent.type === 'mergeProperties'
							? Object.keys(intent.value).map((k: string) => ({
									name: k,
									value: intent.value[k]
								}))
							: (intent.type === 'setProperty'
								? { name: intent.name, value: intent.value.value }
								: {
										...(Object.hasOwn(intent, 'name')
											? { name: (intent as { name: string }).name }
											: {}),
										value: (intent as { value: unknown }).value
									})
				})
				mergeProperties(
					{
						geolocation: { ...response.geolocation },
						conferenceFacilities: response.conferenceFacilities
					},
					true
				)
			} catch (error) {
				console.error(error)
			} finally {
				setSaving(false)
			}
		}
	},
	450
)

const useHotelStore = create<Actions & State>()(
	devtools((set, get) => ({
		venue: {} as Venue,
		saving: false,
		setSaving: (saving: boolean) => set({ saving }),
		setObject: async (value, clientOnly) => {
			set({ venue: value })
			if (!clientOnly) {
				debouncedUsePutIntent(
					value.id,
					{ type: 'setObject', value, clientOnly },
					get().mergeProperties,
					get().setSaving
				)
			}
		},
		setProperty: async (name, value, type, clientOnly) => {
			const t = type ?? typeof value
			set((state: State) => ({
				venue: {
					...state.venue,
					[name]: t === 'number' ? Number(value) : value
				}
			}))
			if (!clientOnly) {
				debouncedUsePutIntent(
					get().venue.id,
					{
						type: 'setProperty',
						name,
						value: {
							value: type === 'number' ? Number(value) : value
						},
						clientOnly
					},
					get().mergeProperties,
					get().setSaving
				)
			}
		},
		mergeProperties: async (value: Partial<Venue>, clientOnly) => {
			set((state: State) => ({
				venue: {
					...state.venue,
					...value
				}
			}))
			if (!clientOnly) {
				debouncedUsePutIntent(
					get().venue.id,
					{
						type: 'mergeProperties',
						value,
						clientOnly
					},
					get().mergeProperties,
					get().setSaving
				)
			}
		}
	}))
)

export default useHotelStore
