/* eslint-disable unicorn/prevent-abbreviations */
/* eslint-disable react/no-array-index-key */
/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable react/no-danger */
import { faCommentLines } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSDrawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import { useCurrencyContext } from 'lib/contexts/currency.context'
import { formatCurrency } from 'lib/helpers'
import { requestGroups } from 'lib/helpers/requestGroups'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { IConcessionEvent } from 'models/proposalResponseMonitor'

interface ConcessionResponseDrawerProperties {
	concessionEvent: IConcessionEvent
	onClose: () => void
}

const ConcessionResponseDrawer = (
	properties: ConcessionResponseDrawerProperties
) => {
	const { onClose, concessionEvent } = properties
	const { currencies } = useCurrencyContext()
	const { eventInfo } = eventInfoStore()

	const responses = concessionEvent.responses
		.filter(
			r =>
				!['New', 'Pending', 'Received', 'Declined'].includes(
					r.proposalRequestStatus ?? ''
				)
		)
		.sort((c, n) => ((c.venueName ?? '') > (n.venueName ?? '') ? 1 : -1))

	const awaitingResponses = concessionEvent.responses
		.filter(r =>
			['New', 'Pending', 'Received'].includes(r.proposalRequestStatus ?? '')
		)
		.sort((c, n) => ((c.venueName ?? '') > (n.venueName ?? '') ? 1 : -1))

	const declinedResponses = concessionEvent.responses
		.filter(r => r.proposalRequestStatus === 'Declined')
		.sort((c, n) => ((c.venueName ?? '') > (n.venueName ?? '') ? 1 : -1))

	return (
		<div>
			<HSDrawer
				onClose={onClose}
				open
				style={{ width: '400px' }}
				position='right'
			>
				<Drawer.Header title='Question Details' titleIcon={() => null} />
				<Drawer.Items>
					<div className='flex flex-col gap-6'>
						<div className='flex flex-col gap-1'>
							<div className='text-sm font-bold text-gray-700'>
								{
									requestGroups[
										concessionEvent.concessionRequest.requestGroupId ?? ''
									]?.name
								}
							</div>
							<div className='text-base font-normal text-gray-500'>
								{concessionEvent.concessionRequest.text}
							</div>
						</div>
						<div className='card'>
							<div className='border-b bg-gray-200 p-2'>
								<div className='text-sm font-medium text-gray-700'>
									Responses ({responses.length})
								</div>
							</div>
							{responses.map(response => (
								<div
									key={`qa-res-${response.venueId}-${response.id}`}
									className='flex flex-col gap-2 border-b p-2 last:border-b-0'
								>
									<div className='text-sm font-medium text-primary-600 underline'>
										{response.venueName ?? ''}
									</div>
									<div className='text-sm font-normal text-gray-500'>
										<div>
											{response.responses?.at(0)?.text ?? ''}:{' '}
											{formatCurrency(
												response.concessionValueProposed,
												currencies[eventInfo?.currencyCode ?? ''],
												''
											)}
										</div>
									</div>
									<div className='flex items-start gap-2 text-wrap'>
										<FontAwesomeIcon icon={faCommentLines} />
										<div className='text-sm font-normal text-gray-500'>
											{response.comment ?? 'None'}
										</div>
									</div>
								</div>
							))}
						</div>
						<div className='card'>
							<div className='border-b bg-gray-200 p-2'>
								<div className='text-sm font-medium text-gray-700'>
									Awaiting Responses ({awaitingResponses.length})
								</div>
							</div>
							{awaitingResponses.map(response => (
								<div
									key={`qa-res-${response.venueId}-${response.id}`}
									className='flex flex-col gap-2 border-b p-2 last:border-b-0'
								>
									<div className='text-sm font-medium text-primary-600 underline'>
										{response.venueName ?? ''}
									</div>
								</div>
							))}
						</div>
						<div className='card'>
							<div className='border-b bg-gray-200 p-2'>
								<div className='text-sm font-medium text-gray-700'>
									Declined to submit Proposal ({declinedResponses.length})
								</div>
							</div>
							{declinedResponses.map(response => (
								<div
									key={`qa-res-${response.venueId}-${response.id}`}
									className='flex flex-col gap-2 border-b p-2 last:border-b-0'
								>
									<div className='text-sm font-medium text-primary-600 underline'>
										{response.venueName ?? ''}
									</div>
								</div>
							))}
						</div>
					</div>
				</Drawer.Items>
			</HSDrawer>
		</div>
	)
}

export default ConcessionResponseDrawer
