import {
	faBuilding,
	faCrosshairsSimple,
	faPlaneUp,
	faRoute
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSCheckbox from 'components/checkbox'
import useMapStore from '../../store/mapStore'
import analytics from 'lib/analytics/segment/load'

const MapControl = () => {
	const {
		showTraffic,
		showTransit,
		setShowTraffic,
		setShowTransit,
		addedOnly,
		setAddedOnly
	} = useMapStore()

	return (
		<div>
			<div className='flex border-b px-6 py-4'>
				<div className='font-medium text-gray-700'>Map Controls</div>
			</div>
			<div className='flex flex-col gap-2 border-b px-6 py-4'>
				<div className='text-sm font-medium text-gray-900'>Show on Map</div>
				<div className='flex items-center gap-2'>
					<HSCheckbox
						checked={addedOnly}
						onChange={event => {
							setAddedOnly(event.currentTarget.checked)
							analytics.track(
								`Search Map Added Only Toggled ${event.currentTarget.checked ? 'On' : 'Off'}`
							)
						}}
					/>
					<div className='text-sm font-normal text-gray-700'>
						Only hotels I&apos;ve added to this Search
					</div>
				</div>
				<div className='flex items-center gap-2'>
					<HSCheckbox
						checked={showTraffic}
						onChange={event => {
							setShowTraffic(event.currentTarget.checked)
							analytics.track(
								`Search Map Traffic Layer Toggled ${event.currentTarget.checked ? 'On' : 'Off'}`
							)
						}}
					/>
					<div className='text-sm font-normal text-gray-700'>
						Traffic Overlay
					</div>
				</div>
				<div className='flex items-center gap-2'>
					<HSCheckbox
						checked={showTransit}
						onChange={event => {
							setShowTransit(event.currentTarget.checked)
							analytics.track(
								`Search Map Transit Layer Toggled ${event.currentTarget.checked ? 'On' : 'Off'}`
							)
						}}
					/>
					<div className='text-sm font-normal text-gray-700'>
						Transit Routes
					</div>
				</div>
			</div>
			<div className='flex flex-col gap-2 border-b px-6 py-4'>
				<div className='text-sm font-medium text-gray-900'>Map Markers</div>
				<div className='flex items-center gap-2'>
					<HSCheckbox />
					<div className='text-sm font-normal text-gray-700'>Hotels</div>
					<FontAwesomeIcon icon={faBuilding} className='text-gray-700' />
				</div>
				<div className='flex items-center gap-2'>
					<HSCheckbox />
					<div className='text-sm font-normal text-gray-700'>Airports</div>
					<FontAwesomeIcon icon={faPlaneUp} className='text-gray-700' />
				</div>
			</div>
			<div className='flex flex-col gap-2 px-6 py-4'>
				<div className='text-sm font-medium text-gray-900'>Actions</div>
				<div className='flex items-center gap-2'>
					<HSButton color='light'>
						<div className='flex items-center gap-2'>
							<FontAwesomeIcon
								icon={faCrosshairsSimple}
								className='text-gray-700'
							/>
							Select hotels within a radius
						</div>
					</HSButton>
				</div>
				<div className='flex items-center gap-2'>
					<HSButton color='light'>
						<div className='flex items-center gap-2'>
							<FontAwesomeIcon icon={faRoute} className='text-gray-700' />
							Find distance and directions
						</div>
					</HSButton>
				</div>
			</div>
		</div>
	)
}

export default MapControl
