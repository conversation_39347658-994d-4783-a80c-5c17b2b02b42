import HSTable from 'components/table'
import { useGetSiteSearch } from 'lib/services/siteSearch.service'
import {
	renderActions,
	renderCreatedBy,
	renderDateCreated,
	renderHotelCount,
	renderName,
	renderViewList
} from './templates'
import { useEffect, useMemo, useState } from 'react'
import type { EventPlan } from 'models/proposalResponseMonitor'
import DeleteHotelSearchModal from 'components/hotelSearch/drawers/deleteSearch'
import ShareHotelSearch from 'components/hotelSearch/drawers/shareSearch'
import EditHotelSearch from 'components/hotelSearch/drawers/editSearch'
import Loader from 'components/loader'
import HSTextField from 'components/textField'
import HSIcon from 'components/HSIcon'
import { faSearch, faShareNodes } from '@fortawesome/pro-light-svg-icons'
import HSButton from 'components/button'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useNavigate } from 'react-router-dom'

const HotelSearchList = () => {
	const { refetch: reloadSiteSearch, isFetching } = useGetSiteSearch()
	const navigate = useNavigate()
	const [eventPlans, setEventPlans] = useState<EventPlan[]>([])
	const [selectedSiteSearch, setSelectedSiteSearch] = useState<EventPlan>()
	const [selectedGridItems, setSelectedGridItems] = useState<EventPlan[]>([])
	const [showDeleteSiteSearch, setShowDeleteSiteSearch] = useState(false)
	const [showShareSearch, setShowShareSearch] = useState(false)
	const [showBulkShareSearch, setShowBulkShareSearch] = useState(false)
	const [showEditSiteSearch, setShowEditSiteSearch] = useState(false)
	const [filteredItems, setFilteredItems] = useState<EventPlan[]>()
	const [searchText, setSearchText] = useState<string>('')

	const mappedItems = useMemo(
		() =>
			eventPlans.map(ep => ({
				...ep,
				locations: ep.siteSearch?.alternateLocations.map(l => l.name).join('; ')
			})),
		[eventPlans]
	)

	useEffect(() => {
		reloadSiteSearch()
			.then(response => {
				setEventPlans(response.data || [])
			})
			.catch((error: unknown) => {
				console.error(error)
			})
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	useEffect(() => {
		let updatedItems = mappedItems
		if (searchText !== '') {
			updatedItems = updatedItems.filter(item =>
				item.name?.toLowerCase().includes(searchText.toLowerCase())
			)
		}
		setFilteredItems(updatedItems)
	}, [mappedItems, searchText])

	return (
		<div
			className='flex flex-col overflow-auto'
			style={{ height: 'calc(100vh - 8rem)' }}
		>
			<div className='border-b p-4'>
				<div className='flex items-center justify-between'>
					<div className='text-lg font-semibold'>My Saved Searches</div>
				</div>
			</div>
			<div className='flex flex-col gap-4 p-6'>
				<div className='flex items-center justify-between gap-4'>
					<div className='text-sm font-medium text-gray-900'>
						{filteredItems?.length} Item
						{(filteredItems?.length ?? 0) > 1 ? 's' : ''}
					</div>
					<div className='flex items-center gap-4'>
						<div className='w-80'>
							<HSTextField
								showClearButton
								icon={HSIcon(faSearch)}
								placeholder='Search'
								value={searchText}
								onChange={event => setSearchText(event.target.value)}
							/>
						</div>
						<HSButton
							color='light'
							disabled={selectedGridItems.length === 0}
							onClick={() => setShowBulkShareSearch(true)}
						>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faShareNodes} />
								Share Selected
							</div>
						</HSButton>
						<HSButton onClick={() => navigate('/site-search/search')}>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faSearch} />
								Start new Search
							</div>
						</HSButton>
					</div>
				</div>
				{isFetching ? (
					<Loader />
				) : (
					<HSTable
						allowSelection
						defaultSort={{
							field: 'created',
							direction: 'desc'
						}}
						rows={filteredItems}
						rowSelected={event => {
							setSelectedGridItems([
								...selectedGridItems,
								event.data as EventPlan
							])
						}}
						rowDeselected={event => {
							setSelectedGridItems(
								selectedGridItems.filter(
									item => item.id !== (event.data as EventPlan).id
								)
							)
						}}
						allRowsSelected={selected => {
							if (selected) {
								setSelectedGridItems(eventPlans)
							} else {
								setSelectedGridItems([])
							}
						}}
						columns={[
							{
								field: 'name',
								headerText: 'List Name',
								render: renderName,
								sortable: true
							},
							{
								field: 'locations',
								headerText: 'Location',
								sortable: true
							},
							{
								field: 'hotels',
								headerText: 'Hotel(s)',
								render: renderHotelCount,
								sortable: true,
								width: 150
							},
							{
								field: 'created',
								headerText: 'Date Created',
								render: renderDateCreated,
								sortable: true,
								width: 150
							},
							{
								field: 'createdBy',
								headerText: 'Created By',
								render: renderCreatedBy,
								sortable: true,
								width: 200
							},
							{
								field: 'view',
								headerText: 'View List',
								render: renderViewList,
								width: 100
							},
							{
								field: 'action',
								headerText: 'Action',
								render: item =>
									renderActions(
										item,
										() => {
											setSelectedSiteSearch(item)
											setShowDeleteSiteSearch(true)
										},
										() => {
											setSelectedSiteSearch(item)
											setShowShareSearch(true)
										},
										() => {
											setSelectedSiteSearch(item)
											setShowEditSiteSearch(true)
										}
									),
								width: 100
							}
						]}
					/>
				)}
			</div>
			{showDeleteSiteSearch && selectedSiteSearch ? (
				<DeleteHotelSearchModal
					onClose={() => {
						setShowDeleteSiteSearch(false)
						setSelectedSiteSearch(undefined)
					}}
					onConfirmDelete={() => {
						setEventPlans(
							eventPlans.filter(ep => ep.id !== selectedSiteSearch.id)
						)
					}}
					siteSearch={selectedSiteSearch}
				/>
			) : null}
			{showShareSearch && selectedSiteSearch ? (
				<ShareHotelSearch
					onClose={() => {
						setShowShareSearch(false)
						setSelectedSiteSearch(undefined)
					}}
					siteSearches={[selectedSiteSearch]}
				/>
			) : null}
			{showBulkShareSearch && selectedGridItems.length > 0 ? (
				<ShareHotelSearch
					onClose={() => {
						setShowBulkShareSearch(false)
						setSelectedGridItems([])
					}}
					siteSearches={selectedGridItems}
				/>
			) : null}
			{showEditSiteSearch && selectedSiteSearch ? (
				<EditHotelSearch
					onClose={eventPlan => {
						setShowEditSiteSearch(false)
						setSelectedSiteSearch(undefined)
						if (eventPlan) {
							setEventPlans(
								eventPlans.map(ep =>
									ep.id === eventPlan.id ? { ...ep, ...eventPlan } : ep
								)
							)
						}
					}}
					siteSearch={selectedSiteSearch}
				/>
			) : null}
		</div>
	)
}

export default HotelSearchList
