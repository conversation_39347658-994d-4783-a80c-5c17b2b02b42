import type { AccordionProps, CustomFlowbiteTheme } from 'flowbite-react'
import {
	Accordion,
	AccordionContent,
	AccordionPanel,
	AccordionTitle
} from 'flowbite-react'

type IHSAccordion = AccordionProps

const HSAccordionComponent = (properties: IHSAccordion) => {
	const { children, ...rest } = properties
	const customTheme: CustomFlowbiteTheme['accordion'] = {
		root: {
			base: 'divide-y divide-gray-200 border-gray-200 dark:divide-gray-700 dark:border-gray-700',
			flush: {
				off: 'rounded-lg border',
				on: 'border-b'
			}
		},
		content: {
			base: 'p-2 first:rounded-t-lg last:rounded-b-lg dark:bg-gray-900'
		},
		title: {
			arrow: {
				base: 'h-6 w-6 shrink-0',
				open: {
					off: '',
					on: 'rotate-180'
				}
			},
			base: 'flex w-full items-center justify-between p-2 text-left font-medium text-gray-500 first:rounded-t-lg last:rounded-b-lg dark:text-gray-400',
			flush: {
				off: 'hover:bg-gray-100 focus:ring-2 focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800',
				on: 'bg-transparent dark:bg-transparent'
			},
			heading: '',
			open: {
				off: '',
				on: 'bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-white'
			}
		}
	}
	return (
		<Accordion theme={customTheme} {...rest} flush>
			{children}
		</Accordion>
	)
}

// export default HSAccordion
// eslint-disable-next-line import/prefer-default-export
const HSAccordion = Object.assign(HSAccordionComponent, {
	Panel: AccordionPanel,
	Title: AccordionTitle,
	Content: AccordionContent
})

export default HSAccordion
