/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import type { ISupplierContact } from 'models/affiliateOrganizations'
import type { HotelChain } from 'models/chains'
import type { PropertyPartnerSeller } from 'models/propertyPartner'
import type { IUserProfile } from 'models/userProfiles'

export const supplierContactRoles: Record<
	string,
	{ key: string; label: string; portfolioType: string } | null
> = {
	hot: {
		key: 'hot',
		label: 'Hotelier (On-Property)',
		portfolioType: 'property'
	},
	nso: { key: 'nso', label: 'National Sales (Chain)', portfolioType: 'chain' },
	gso: { key: 'gso', label: 'Global Sales (Chain)', portfolioType: 'chain' },
	soa: {
		key: 'soa',
		label: 'Sales Office (Affiliate)',
		portfolioType: 'portfolio'
	},
	dmo: { key: 'dmo', label: 'DMO/CVB', portfolioType: 'market' },
	dmc: { key: 'dmc', label: 'DMC', portfolioType: 'market' }
}

export const supplierContactSources: Record<
	string,
	{ key: string; label: string; title: string; plannerDescription?: string }
> = {
	search: {
		key: 'search',
		label: 'added from search',
		title: 'Planner Search'
	},
	comparable: {
		key: 'comparable',
		label: 'added from comparable',
		title: 'HopSkip Comparable'
	},
	directory: {
		key: 'directory',
		label: 'added from directory',
		title: 'Cc by Planner'
	},
	rfp: { key: 'rfp', label: 'added new in rfp', title: 'Cc by Planner' },
	autocopy: { key: 'autocopy', label: 'autocopy', title: 'Paid Autocopy' },
	addpaid: {
		key: 'addpaid',
		label: 'addpaid',
		title: 'Added by Planner',
		plannerDescription: 'added by opting in during Send RFP'
	}
}

export const matchCriteria = {
	chainId: { key: 'chainId', label: 'Chain' },
	propertySellerId: { key: 'propertySellerId', label: 'Sales Office' }
}

export const geolocationExcludeVenueIds = 'geolocationExcludeVenueIds'
export const geolocationIncludeVenueIds = 'geolocationIncludeVenueIds'

export const invitationMessages = [
	{
		requestSuggestions: false,
		role: supplierContactRoles.hot.key,
		message:
			"Hi {{supplierContact.firstName}},\n\nI'm sending you this RFP using the HopSkip platform. Can you please respond to this request with a proposal using the HopSkip platform?\n\nThanks!"
	},
	{
		requestSuggestions: false,
		role: '*',
		message:
			"Hi {{supplierContact.firstName}},\n\nI'm sending you this RFP using the HopSkip platform. Can you please make sure that the hotels I've selected will receive my RFP and submit proposals using the HopSkip platform?\n\nThanks!"
	},
	{
		requestSuggestions: true,
		role: '*',
		message:
			"Hi {{supplierContact.firstName}},\n\nI'm sending you this RFP using the HopSkip platform. Can you please review my RFP, suggest hotels from your {{supplierContact.portfolioType}} you think would be best, and send them my request using the HopSkip platform?\n\nThanks!"
	}
]

export const formatInvitationMessage = (context: {
	supplierContact: Partial<ISupplierContact> | null
	currentUser?: Partial<IUserProfile>
}) => {
	const message = invitationMessages.find(
		im =>
			im.requestSuggestions ===
				(context.supplierContact?.requestSuggestions ?? false) &&
			(context.supplierContact?.role === im.role || im.role === '*')
	)
	if (message?.message) {
		const regex = /{{(\w+).(\w+)}}/g
		return message.message.replaceAll(
			regex,
			(match: string, objectKey: string, propertyKey: string) => {
				const object = context[objectKey as keyof typeof context]
				const value = (object as Record<string, string>)[propertyKey] ?? 'there'
				return String(value)
			}
		)
	}
	return ''
}

export const formatVenueMatchCriteria = (
	venueMatchCriteria: Record<string, string[]>,
	chains: HotelChain[] | undefined,
	propertySellers: PropertyPartnerSeller[] | undefined
) => {
	const matchKeys = Object.keys(venueMatchCriteria).filter(
		k => ![geolocationExcludeVenueIds, geolocationIncludeVenueIds].includes(k)
	)
	const formattedMatches: string[] = []

	for (const k of matchKeys) {
		for (const c of venueMatchCriteria[k]) {
			if (k === 'chainId') {
				const chain = chains?.find((ch: HotelChain) => ch.id === c)
				formattedMatches.push(chain ? (chain.name ?? '') : 'Unknown Chain')
			} else if (k === 'propertySeller') {
				const seller = propertySellers?.find(ps => ps.id === c)
				formattedMatches.push(
					seller ? (seller.name ?? '') : 'Unknown Property Seller'
				)
			} else {
				formattedMatches.push('Geography')
			}
		}
	}

	return formattedMatches.length === 0 ? 'None' : formattedMatches.join(', ')
}

export const getSupplierLeadSource = (
	supplierContact: ISupplierContact | undefined
) => {
	const supplierContactSource =
		supplierContactSources[supplierContact?.source ?? '']
	// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
	if (supplierContactSource) {
		return {
			isAutoCopy:
				supplierContact?.source === supplierContactSources.autocopy.key,
			isAddPaid: supplierContact?.source === supplierContactSources.addpaid.key,
			...supplierContactSource
		}
	}
	return null
}

export const findSupplierContactOrTeammate = (
	supplierContacts: ISupplierContact[] | null,
	createdBy: string | null,
	createdByOrganizationId?: string
): {
	organizationId: string | null
	role: string | null
	id: string | null
	firstName: string | null
	lastName: string | null
	isHopSkip?: boolean
} | null => {
	if (!createdBy) return null
	if (createdBy.endsWith('@myhopskip.com'))
		return {
			id: createdBy,
			firstName: 'HopSkip',
			lastName: '',
			isHopSkip: true
		}
	const supplierContact = supplierContacts?.find(sc => sc.id === createdBy)
	if (supplierContact) {
		return supplierContact
	}
	const teammate = supplierContacts?.find(
		sc => sc.organizationId === createdByOrganizationId
	)
	if (teammate) {
		return teammate
	}
	return null
}
