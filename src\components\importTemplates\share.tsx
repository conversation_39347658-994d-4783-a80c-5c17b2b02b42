/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable react/no-array-index-key */
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import HSText<PERSON>ield from 'components/textField'
import { Drawer } from 'flowbite-react'
import type { EventPlan } from 'models/proposalResponseMonitor'
import {
	faPlus,
	faInfoCircle,
	faTrashXmark
} from '@fortawesome/pro-light-svg-icons'
import { useGetMembersForOrganization } from 'lib/services/organizations.service'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { useEffect, useState } from 'react'
import type { Member } from 'models/propertyPartner'
import HSCheckbox from 'components/checkbox'
import HSTooltip from 'components/tooltip'
import { isEmail } from 'lib/helpers'
import { shareEventPlanTemplates } from 'lib/services/eventPlans.service'
import { toast } from 'react-toastify'
import Loader from 'components/loader'

interface IShareTemplatesProperties {
	templates: EventPlan[]
	onClose: () => void
}

interface ExtendedMember extends Member {
	share: boolean
}

const ShareTemplates = (properties: IShareTemplatesProperties) => {
	const { templates, onClose } = properties
	const { userProfile } = useUserProfileContext()
	const { refetch: getMembers, isFetching } = useGetMembersForOrganization(
		userProfile?.organizationId ?? '',
		!!userProfile?.organizationId
	)
	const [orgPlanners, setOrgPlanners] = useState<ExtendedMember[]>([])
	const [emails, setEmails] = useState<string[]>([''])

	useEffect(() => {
		if (userProfile) {
			getMembers()
				.then(response => {
					const orgMembers =
						response.data
							?.filter(d => d.id !== userProfile.id)
							.map(d => ({
								...d,
								share: !!templates.some(item =>
									item.planners?.some(planner => planner.id === d.id)
								)
							})) ?? []
					setOrgPlanners(orgMembers)
				})
				.catch((error: unknown) => {
					console.error(error)
				})
		}
	}, [getMembers, templates, userProfile])

	const title =
		templates.length > 1
			? `Share ${templates.length} RFP Templates`
			: 'Share template with planners'

	const onClickSubmit = async () => {
		const emailsToShare: {
			emails: {
				email: string
			}[]
		} = {
			emails: [
				...orgPlanners
					.filter(member => member.share)
					.map(member => ({
						email: member.id ?? ''
					})),
				...emails
					.filter(email => email !== '')
					.map(email => ({
						email
					}))
			]
		}
		await Promise.all(
			templates.map(async template =>
				shareEventPlanTemplates(template.id ?? '', emailsToShare).catch(
					(error: unknown) => {
						console.error(error)
						toast.error(`Failed to share template: ${template.name}`)
					}
				)
			)
		)
		onClose()
	}

	return (
		<HSDrawer
			onClose={onClose}
			style={{ width: '30rem' }}
			position='right'
			open
			noPadding
		>
			<Drawer.Header title={title} titleIcon={() => null} className='p-6' />
			<div className='flex flex-col gap-4'>
				<Drawer.Items
					className='overflow-auto'
					style={{ maxHeight: 'calc(100vh - 12rem)' }}
				>
					<div className='flex flex-col gap-4 px-4 pb-4 pt-0'>
						<div>
							<div className='text-sm font-medium text-gray-900'>
								Share Selected RFP Templates with Your Team
							</div>
							<div className='text-xs font-normal text-gray-500'>
								Team members will receive editable copies of these templates
								while your originals remain unchanged
							</div>
						</div>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-medium text-gray-900'>
								Share with other planners (optional)
							</div>
							<div className='card'>
								<div className='flex flex-col gap-2'>
									{emails.map((email, index) => (
										<div
											className='flex items-center gap-4 border-b p-2'
											key={`email-${index}`}
										>
											<HSTextField
												placeholder='Enter Email'
												onChange={event => {
													const updatedEmails = [...emails]
													updatedEmails[index] = event.target.value
													setEmails(updatedEmails)
												}}
												value={email}
												color={
													isEmail(email)
														? 'success'
														: email === ''
															? 'light'
															: 'failure'
												}
												sizing='md'
											/>
											<HSButton
												color='light'
												onClick={() =>
													setEmails(
														emails.filter(
															(_, currentIndex) => currentIndex !== index
														)
													)
												}
												size='sm'
											>
												<FontAwesomeIcon
													icon={faTrashXmark}
													className='text-red-500'
													size='lg'
												/>
											</HSButton>
										</div>
									))}
									<div className='p-2'>
										<HSButton
											color='text'
											onClick={() => setEmails([...emails, ''])}
										>
											<div className='flex items-center gap-2 text-gray-600'>
												<FontAwesomeIcon icon={faPlus} />
												Add New
											</div>
										</HSButton>
									</div>
								</div>
							</div>
						</div>
						<div className='flex items-center gap-2'>
							<hr className='flex-grow border-gray-300' />
							<span className='text-sm text-gray-500'>Or</span>
							<hr className='flex-grow border-gray-300' />
						</div>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-medium text-gray-900'>
								Select Team Members to Share
							</div>
							<div className='card'>
								<div className='flex items-center gap-2 border-b bg-gray-100 p-2'>
									<HSCheckbox
										checked={orgPlanners.every(item => item.share)}
										onChange={() => {
											const alreadySelected = orgPlanners.every(
												item => item.share
											)
											if (alreadySelected) {
												setOrgPlanners(
													orgPlanners.map(item => ({ ...item, share: false }))
												)
											} else {
												setOrgPlanners(
													orgPlanners.map(item => ({ ...item, share: true }))
												)
											}
										}}
									/>
									<div className='text-sm font-medium text-gray-600'>
										SELECT ALL
									</div>
								</div>
								{isFetching ? (
									<Loader />
								) : (
									orgPlanners.map((planner, index) => (
										<div
											className='flex items-center gap-2 border-b p-2'
											key={planner.id}
										>
											<HSCheckbox
												checked={planner.share}
												onChange={() => {
													const updatedPlanners = [...orgPlanners]
													updatedPlanners[index].share =
														!updatedPlanners[index].share
													setOrgPlanners(updatedPlanners)
												}}
												disabled={templates.some(event =>
													event.planners?.some(pl => pl.id === planner.id)
												)}
											/>
											<div className='flex flex-col'>
												<div className='flex items-center gap-2'>
													<div className='text-sm font-medium text-gray-600'>
														{planner.firstName} {planner.lastName}
													</div>
													{templates.some(event =>
														event.planners?.some(pl => pl.id === planner.id)
													) ? (
														<HSTooltip content='The owner cannot be removed'>
															<FontAwesomeIcon
																icon={faInfoCircle}
																className='text-gray-600'
															/>
														</HSTooltip>
													) : null}
												</div>
												<div className='text-xs font-medium text-gray-400'>
													{planner.id ?? ''}
												</div>
											</div>
										</div>
									))
								)}
							</div>
						</div>
					</div>
				</Drawer.Items>
				<div className='flex items-center justify-between gap-2 p-4'>
					<HSButton color='light' onClick={onClose} className='grow'>
						Cancel
					</HSButton>
					<HSButton
						disabled={
							!orgPlanners.some(member => member.share) &&
							emails
								.filter(email => email !== '')
								.every(email => !isEmail(email))
						}
						className='grow'
						onClick={onClickSubmit}
					>
						{`Share (${orgPlanners.filter(member => member.share).length + emails.filter(email => isEmail(email)).length})`}
					</HSButton>
				</div>
			</div>
		</HSDrawer>
	)
}

export default ShareTemplates
