import HSButton from 'components/button'
import PlannerProposalResponseCard from './proposalResponseCard'
import { useNavigate, useParams } from 'react-router-dom'
import HSTooltip from 'components/tooltip'
import { faExclamationCircle } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { ProposalRequest } from 'models/proposalResponseMonitor'
import { useEffect, useState } from 'react'
import { MarketAvailabilityStatusMap } from 'lib/helpers/statusMaps'
import HSBadge from 'components/badge'

const PlannerProposalTracker = () => {
	const { eventId } = useParams()
	const navigate = useNavigate()
	const { eventInfo: eventPlan } = eventInfoStore()
	const [items, setItems] = useState<ProposalRequest[]>([])
	const [totalSummaryProposalRequests, setTotalSummaryProposalRequests] =
		useState(0)

	useEffect(() => {
		const summaryProposalRequests = eventPlan?.summaryProposalRequests ?? []
		const proposalRequests = eventPlan?.proposalRequests ?? []

		setTotalSummaryProposalRequests(summaryProposalRequests.length)
		setItems(
			summaryProposalRequests.filter(spr => {
				if (proposalRequests.some(pr => pr.venueId === spr.venueId)) {
					return false
				}
				return (
					spr.status === MarketAvailabilityStatusMap.Active?.key ||
					spr.status === MarketAvailabilityStatusMap.Reviewed?.key
				)
			})
		)
	}, [eventPlan?.summaryProposalRequests, eventPlan?.proposalRequests])

	return (
		<div className='flex flex-col'>
			<div className='border-b'>
				<div className='flex justify-between gap-2'>
					<div className='flex flex-col gap-1 p-4'>
						<div className='text-xl font-semibold text-gray-900'>
							Proposal Tracker
						</div>
					</div>
					<div className='flex items-center gap-2 border-l p-4'>
						<div className='flex flex-col gap-1'>
							<div className='flex items-center gap-2'>
								<div className='text-gary-700 text-sm font-normal'>
									Comparable Hotels
								</div>
								<div>
									<HSTooltip content="Explore hotels with availability in the same locations you're requesting proposals for">
										<FontAwesomeIcon icon={faExclamationCircle} />
									</HSTooltip>
								</div>
							</div>
							<div className='flex items-center gap-2'>
								<div className='flex items-center gap-1 border-r pr-2'>
									<HSBadge className='w-fit text-center'>
										{items.length}
									</HSBadge>
									<div className='text-xxs font-normal text-gray-400'>
										New Hotels
									</div>
								</div>
								<div className='flex items-center gap-1'>
									<div className='text-xxs font-normal text-gray-600'>
										{totalSummaryProposalRequests}
									</div>
									<div className='text-xxs font-normal text-gray-400'>
										Total Hotels
									</div>
								</div>
							</div>
						</div>
						<HSButton
							size='sm'
							onClick={() => {
								navigate(
									`/planner/event/${eventId}/review-proposals/comparable-hotels`
								)
							}}
						>
							View Comparable Hotels
						</HSButton>
					</div>
				</div>
			</div>
			<PlannerProposalResponseCard />
		</div>
	)
}

export default PlannerProposalTracker
