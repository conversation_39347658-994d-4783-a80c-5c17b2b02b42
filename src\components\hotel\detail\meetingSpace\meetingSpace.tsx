/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable no-param-reassign */
/* eslint-disable unicorn/no-array-reduce */
import { useGetMeetingRooms } from 'lib/services/hotels.service'
import {
	externalContentLinkTypes,
	getExternalContentLink
} from 'lib/helpers/hotels'
import { useState, useEffect } from 'react'
import type { IExternalContentLinks, IMeetingRoom, Venue } from 'models/venue'
import HSButton from 'components/button'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faFilePdf, faGlobe } from '@fortawesome/pro-light-svg-icons'
import { useGetAttachments } from 'lib/services/attachments.service'
import { getVenueAttachmentUrl } from 'lib/helpers/document'
import HSTable from 'components/table'
import { Button } from 'flowbite-react'
import { roomLayoutOptions } from 'lib/helpers/roomLayouts'
import {
	renderDimensions,
	renderMaxCapacity,
	renderRoomSizeHeader,
	renderCeilingHeightHeader,
	renderCapacity
} from './templates'
import type { GridColumn } from 'components/table/types'
import analytics from 'lib/analytics/segment/load'
import { formatNumber } from 'lib/helpers'

interface IMeetingSpaceSummary {
	hotel: Venue
}

type ViewMode = 'dimensions' | 'capacity'

const MeetingSpaceSummary = (properties: IMeetingSpaceSummary) => {
	const { hotel } = properties
	const { data: meetingRooms = [] } = useGetMeetingRooms(
		hotel.id ?? '',
		!!hotel.id
	)
	const { data: attachments = [] } = useGetAttachments(
		hotel.id ?? '',
		!!hotel.id
	)

	const ceilingHeight = meetingRooms.reduce(
		(a, c) => Math.max(a, c.height ?? 0),
		0
	)
	const [floorPlanLink, setFloorPlanLink] = useState<IExternalContentLinks>()
	const [viewMode, setViewMode] = useState<ViewMode>('dimensions')

	const floorPlans = attachments.filter(a => a.profileDefault === 'floorplan')
	const layouts = meetingRooms
		.flatMap(d => d.layouts?.filter(ls => ls.capacity > 0))
		.reduce(
			(a: { count: number; items: Record<string, string> }, c) => {
				const l = a.items[c?.layoutStyle ?? '']
				if (!l && !roomLayoutOptions[c?.layoutStyle ?? '']?.hideInSearch) {
					a.items[c?.layoutStyle ?? ''] =
						roomLayoutOptions[c?.layoutStyle ?? '']?.name ?? ''
					a.count += 1
				}
				return a
			},
			{ count: 0, items: {} }
		)

	useEffect(() => {
		setFloorPlanLink(
			getExternalContentLink({
				hotel: { externalContentLinks: hotel.externalContentLinks },
				externalContentLinkTypeKey: externalContentLinkTypes.floorPlan.key
			})
		)
	}, [hotel.externalContentLinks])

	const getColumns = () => {
		const columns: GridColumn<IMeetingRoom>[] =
			viewMode === 'dimensions'
				? [
						{
							field: 'area',
							headerText: 'Room Size',
							headerTemplate: renderRoomSizeHeader,
							render: item =>
								item.area ? formatNumber(item.area, '-', 2) : '-',
							sortable: true
						},
						{
							field: 'height',
							headerText: 'Ceiling Height',
							headerTemplate: renderCeilingHeightHeader,
							sortable: true
						},
						{
							field: 'length',
							headerText: 'Dimensions (LxW)',
							render: renderDimensions,
							sortable: true
						},
						{
							field: 'capacity',
							headerText: 'Max Capacity',
							render: renderMaxCapacity,
							sortable: true
						}
					]
				: Object.keys(layouts.items).map(k => ({
						field: k,
						headerText: roomLayoutOptions[k]?.name ?? k,
						render: item => renderCapacity(item, k),
						sortable: true
					}))

		return columns
	}

	return (
		<div className='flex flex-col gap-4'>
			<div className='flex flex-col gap-2'>
				<div className='text-lg font-semibold text-gray-900'>Meeting Space</div>
				<div className='flex justify-between gap-4'>
					<div className='card w-1/2 p-4'>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-medium text-gray-900'>Summary</div>
							<div className='flex gap-4'>
								<div className='w-1/2'>
									<div className='flex flex-col gap-1'>
										<div className='flex items-center gap-2'>
											<div className='text-sm font-normal text-gray-400'>
												Total meeting space:
											</div>
											<div className='text-sm font-normal text-gray-600'>
												{hotel.meetingSpaceSquareFeet?.toLocaleString('en-US', {
													style: 'decimal'
												})}{' '}
												ft<sup>2</sup>
											</div>
										</div>
										<div className='flex items-center gap-2'>
											<div className='text-sm font-normal text-gray-400'>
												Total meeting rooms:
											</div>
											<div className='text-sm font-normal text-gray-600'>
												{hotel.meetingRoomQuantity?.toLocaleString('en-US', {
													style: 'decimal'
												})}{' '}
												Meeting Rooms
											</div>
										</div>
									</div>
								</div>
								<div className='border-r' />
								<div className='w-1/2'>
									<div className='flex flex-col gap-1'>
										<div className='flex items-center gap-2'>
											<div className='text-sm font-normal text-gray-400'>
												Largest room:
											</div>
											<div className='text-sm font-normal text-gray-600'>
												{hotel.largestMeetingSpaceSquareFeet?.toLocaleString(
													'en-US',
													{ style: 'decimal' }
												)}{' '}
												ft<sup>2</sup>
											</div>
										</div>
										<div className='flex items-center gap-2'>
											<div className='text-sm font-normal text-gray-400'>
												Highest Ceiling:
											</div>
											<div className='text-sm font-normal text-gray-600'>
												{ceilingHeight.toLocaleString('en-US', {
													style: 'decimal'
												})}{' '}
												Meeting Rooms
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className='card w-1/2 p-4'>
						<div className='flex flex-col gap-2'>
							<div className='text-sm font-medium text-gray-900'>
								Floor Plan
							</div>
							<div className='flex flex-wrap items-center gap-2'>
								{floorPlanLink ? (
									<HSButton
										color='light'
										href={floorPlanLink.url ?? ''}
										target='_blank'
										rel='noreferrer noopener'
										disabled={!floorPlanLink.url}
										onClick={() => {
											analytics.track('Hotel Floor Plan Clicked', {
												hotelId: hotel.id,
												hotelName: hotel.name,
												name: 'web page'
											})
										}}
									>
										<div className='flex items-center gap-2'>
											<FontAwesomeIcon icon={faGlobe} />
											<div className='text-sm'>View Floor Plan (web page)</div>
										</div>
									</HSButton>
								) : null}
								{floorPlans.map((floorPlan, fpi) => (
									<HSButton
										href={getVenueAttachmentUrl(floorPlan, '')}
										color='light'
										key={floorPlan.id}
										onClick={() => {
											analytics.track('Hotel Floor Plan Clicked', {
												hotelId: hotel.id,
												hotelName: hotel.name,
												name: floorPlan.displayName || floorPlan.name
											})
										}}
									>
										<div className='flex items-center gap-2'>
											<FontAwesomeIcon icon={faFilePdf} />
											<div className='text-sm'>
												{' '}
												{floorPlan.displayName ||
													`Floor Plan${floorPlans.length === 1 ? '' : ` #${fpi + 1}`}`}{' '}
												({floorPlan.mimeType.split('/')[1].toUpperCase()})
											</div>
										</div>
									</HSButton>
								))}
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className='card'>
				<div className='flex flex-col gap-2'>
					<div className='flex items-center justify-between gap-4 p-4'>
						<div className='text-sm font-medium text-gray-900'>
							Meeting Room Details
						</div>
						<div className='flex items-center gap-4'>
							<div className='text-sm font-medium text-gray-700'>View by</div>
							<Button.Group color='gray'>
								<Button
									color='gray'
									size='sm'
									onClick={() => {
										const updatedView: ViewMode =
											viewMode === 'dimensions' ? 'capacity' : 'dimensions'
										setViewMode(updatedView)
									}}
									className={
										viewMode === 'dimensions' ? 'bg-gray-300' : 'bg-white'
									}
								>
									Dimensions
								</Button>
								<Button
									color='gray'
									className={
										viewMode === 'capacity' ? 'bg-gray-300' : 'bg-white'
									}
									size='sm'
									onClick={() => {
										const updatedView: ViewMode =
											viewMode === 'capacity' ? 'dimensions' : 'capacity'
										setViewMode(updatedView)
									}}
								>
									Capacities
								</Button>
							</Button.Group>
						</div>
					</div>
					<HSTable
						rows={meetingRooms}
						defaultSort={{
							field: 'name',
							direction: 'asc'
						}}
						columns={[
							{
								field: 'name',
								headerText: 'Room Name',
								sortable: true
							},
							...getColumns()
						]}
					/>
				</div>
			</div>
		</div>
	)
}

export default MeetingSpaceSummary
