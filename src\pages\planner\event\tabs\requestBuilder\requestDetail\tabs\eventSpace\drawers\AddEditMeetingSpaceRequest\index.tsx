/* eslint-disable unicorn/no-keyword-prefix */
import HSDrawer from 'components/drawer'
import { customTabComponentTheme } from 'components/tab'
import { Drawer, Tabs } from 'flowbite-react'
import RoomRequirements from './roomRequirements'
import FoodBeverage from './foodBeverage'
import NotesAttachments from './notesAttachments'
import useEventSpaceHooks from '../../hooks/eventSpaceHooks'
import meetingSpaceRequestStore from './store'
import HSButton from 'components/button'
import { useEffect, useMemo, useState } from 'react'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import useAttachmentsStore from 'lib/store/attachmentsStore'

interface AddEditMeetingSpaceRequestDrawerProperties {
	onClose: () => void
	activeTabId?: number
}

const AddEditMeetingSpaceRequestDrawer = (
	properties: AddEditMeetingSpaceRequestDrawerProperties
) => {
	const { onClose, activeTabId } = properties
	const [activeTab, setActiveTab] = useState(activeTabId ?? 0)
	const { eventInfo } = eventInfoStore()
	const {
		addUpdateRequest,
		onAddAttachments,
		onDeleteAttachments,
		setPropertyFoodAndBeverages
	} = useEventSpaceHooks()
	const { meetingSpaceRequest, setFoodAndBeverages, foodAndBeverages } =
		meetingSpaceRequestStore()
	const [acceptedFiles, setAcceptedFiles] = useState<File[]>([])

	const { attachments } = useAttachmentsStore()

	useEffect(() => {
		if (activeTabId) {
			setActiveTab(activeTabId)
		}
	}, [activeTabId])

	const filteredAttachments = useMemo(
		() =>
			attachments.filter(
				a =>
					a.meetingSpaceRequestId === meetingSpaceRequest?.id &&
					a.venueId === null
			),
		[attachments, meetingSpaceRequest?.id]
	)

	const mappedFiles = useMemo(
		() =>
			filteredAttachments.map(attachment => ({
				name: attachment.name,
				size: attachment.fileSize,
				type: attachment.mimeType,
				extension: attachment.extension,
				id: attachment.id
			})),
		[filteredAttachments]
	)

	useEffect(() => {
		setAcceptedFiles(mappedFiles as unknown as File[])
	}, [mappedFiles])

	const handleAttachmentChange = () => {
		if (meetingSpaceRequest?.id) {
			// check if acceptedFiles is same as attachments by id
			const existingTotal = filteredAttachments.length
			const newTotal = acceptedFiles.length

			if (existingTotal > newTotal) {
				const removedFiles = filteredAttachments.filter(
					attachment => !acceptedFiles.some(file => file.id === attachment.id)
				)
				onDeleteAttachments(removedFiles, false)
			}
			if (existingTotal < newTotal) {
				const addedFiles = acceptedFiles.filter(
					file =>
						!filteredAttachments.some(attachment => attachment.id === file.id)
				)
				onAddAttachments(addedFiles, true, false, null, meetingSpaceRequest.id)
			}
		}
	}

	const handleFoodAndBeverageChange = () => {
		if (meetingSpaceRequest && eventInfo) {
			setPropertyFoodAndBeverages([
				...(eventInfo.foodAndBeverageRequests?.filter(
					fbr => fbr.meetingSpaceRequestId !== meetingSpaceRequest.id
				) ?? []),
				...(foodAndBeverages ?? [])
			])
		}
	}

	const onSave = () => {
		if (meetingSpaceRequest) {
			addUpdateRequest(meetingSpaceRequest)
		}
		handleFoodAndBeverageChange()
		handleAttachmentChange()

		onClose()
	}

	useEffect(() => {
		if (meetingSpaceRequest && eventInfo) {
			const fAndB =
				eventInfo.foodAndBeverageRequests?.filter(
					fbr => fbr.meetingSpaceRequestId === meetingSpaceRequest.id
				) ?? []
			setFoodAndBeverages(fAndB)
		}
	}, [eventInfo, meetingSpaceRequest, setFoodAndBeverages])

	return (
		<HSDrawer
			position='right'
			open
			onClose={onClose}
			noPadding
			style={{
				width: '600px'
			}}
		>
			<Drawer.Header
				title={`Add Room to Day ${(meetingSpaceRequest?.dayNumber ?? 0) + 1}`}
				titleIcon={() => null}
				className='px-6 pt-4'
			/>
			<Drawer.Items
				className='overflow-auto px-6 pb-4'
				style={{ height: 'calc(100vh - 10rem)' }}
			>
				<Tabs
					theme={customTabComponentTheme('horizontal', true, true)}
					onActiveTabChange={tabId => setActiveTab(tabId)}
				>
					<Tabs.Item title='Room Requirements' active={activeTab === 0}>
						<RoomRequirements />
					</Tabs.Item>
					<Tabs.Item title='Food And Beverage' active={activeTab === 1}>
						<FoodBeverage />
					</Tabs.Item>
					<Tabs.Item title='Notes And Attachments' active={activeTab === 2}>
						<NotesAttachments
							acceptedFiles={acceptedFiles}
							setAcceptedFiles={setAcceptedFiles}
						/>
					</Tabs.Item>
				</Tabs>
			</Drawer.Items>
			<Drawer.Items className='flex items-center justify-end gap-4 p-4'>
				<HSButton color='light' onClick={onClose} className='grow'>
					Cancel
				</HSButton>
				<HSButton color='primary' onClick={onSave} className='grow'>
					Save
				</HSButton>
			</Drawer.Items>
		</HSDrawer>
	)
}

export default AddEditMeetingSpaceRequestDrawer
