/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable unicorn/no-array-reduce */
import {
	faArrowTrendDown,
	faBalanceScale,
	faBuilding
} from '@fortawesome/pro-light-svg-icons'
import { useEffect, useState } from 'react'
import DataGrid from 'components/dataGrid'
import type {
	GridComponent,
	RowDataBoundEventArgs
} from '@syncfusion/ej2-react-grids'
import {
	ColumnDirective,
	ColumnsDirective,
	Inject,
	Sort
} from '@syncfusion/ej2-react-grids'
import ReportCard from '../../component/reportCard'
import { getValue } from '@syncfusion/ej2-base'
import type { Venue } from 'models/venue'
import type { IRowTemplate } from './templates'
import {
	addedFromSearchTemplate,
	hotelTemplate,
	rankTemplate,
	totalTemplate
} from './templates'
import type { IMetrics } from '../..'
import { engagementTypes } from 'lib/helpers/contentEngagement'
import WhatsThis from 'components/whatsThis'
import HSButton from 'components/button'
import { Button } from 'flowbite-react'

const Search = ({
	hotel,
	searchData,
	metrics,
	isFeatureLocked
}: {
	hotel: Venue | undefined
	searchData: IRowTemplate[] | null
	metrics: IMetrics | null
	isFeatureLocked: boolean
}) => {
	let gridInstance: GridComponent | null = null

	const [rankBy, setRankBy] = useState<'top-search' | 'add-search'>(
		'top-search'
	)

	const rowDataBound = (arguments_: RowDataBoundEventArgs) => {
		if (
			(arguments_.row && getValue('id', arguments_.data) === hotel?.id) ??
			''
		) {
			arguments_.row?.classList.add('bg-primary-50')
		}
	}

	useEffect(() => {
		const updatedSearchData =
			searchData?.map(a => ({
				...a,
				addedFromSearch:
					metrics?.conversionData?.find(b => b.id === a.id)?.leadSource
						?.search ?? 0
			})) ?? []
		gridInstance?.setProperties({
			dataSource: updatedSearchData
				.sort((a, b) =>
					rankBy === 'top-search'
						? (a.engagementMetrics?.[engagementTypes.impression]?.total ?? 0) <
							(b.engagementMetrics?.[engagementTypes.impression]?.total ?? 0)
							? 1
							: -1
						: (a.engagementMetrics?.[engagementTypes.conversion]?.total ?? 0) <
							  (b.engagementMetrics?.[engagementTypes.conversion]?.total ?? 0)
							? 1
							: -1
				)
				.map((item, index) => ({ ...item, rank: index + 1 }))
		})
	}, [gridInstance, metrics?.conversionData, rankBy, searchData, hotel])

	return (
		<div>
			<div className='flex flex-row gap-6 pb-6'>
				<ReportCard
					lockPaidFeature={isFeatureLocked}
					title='Total Searches'
					firstParameter={{
						title: 'Comp Set Avg',
						value:
							(searchData
								?.filter(d => d.id !== hotel?.id)
								.reduce(
									(accumulator, c) =>
										accumulator +
										(c.engagementMetrics?.[engagementTypes.impression]?.total ??
											0),
									0
								) ?? 0) /
							(searchData?.filter(d => d.id !== hotel?.id).length || 1),
						icon: faBalanceScale,
						iconColor: 'light'
					}}
					secondParameter={{
						title: 'My Hotel',
						value:
							searchData?.find(d => d.id === hotel?.id)?.engagementMetrics?.[
								engagementTypes.impression
							]?.total ?? 0,
						icon: faBuilding,
						iconColor: 'success'
					}}
					changeInfo='Change info'
					titleInfo='Title info'
				/>
				<ReportCard
					lockPaidFeature={isFeatureLocked}
					title='Added from search as a comparable market'
					firstParameter={{
						title: 'Comp Set',
						value:
							metrics?.conversionData
								?.filter(d => d.id !== hotel?.id)
								.reduce(
									(accumulator, c) => accumulator + (c.leadSource?.search ?? 0),
									0
								) ?? 0,
						icon: faArrowTrendDown,
						iconColor: 'red'
					}}
					secondParameter={{
						title: 'My Hotel',
						value:
							metrics?.conversionData?.find(d => d.id === hotel?.id)?.leadSource
								?.search ?? 0,
						icon: faBuilding,
						iconColor: 'success'
					}}
					changeInfo='Change info'
					titleInfo='Title info'
				/>
			</div>

			<div className='flex flex-col gap-3 pb-5'>
				<div className='flex items-center gap-2'>
					<div className='text-lg font-semibold text-gray-900'>
						Comparable Market Benchmarking: Search
					</div>
					<WhatsThis
						content=''
						key='comparable-market-search'
						id='info-comparable-market-search'
					/>
				</div>
				<div className='flex items-center justify-between pb-4 pt-2'>
					<div className='text-sm font-medium text-gray-900'>
						{(metrics?.conversionData?.length || 1) - 1} Comp Set items vs My
						Hotel
					</div>
					<div className='flex items-center gap-4'>
						<div className='text-sm font-medium text-gray-900'>Rank by</div>
						<div>
							<Button.Group className='w-full'>
								<HSButton
									color='gray'
									className={`${rankBy === 'top-search' ? '!bg-gray-100' : 'bg-white text-gray-900'} flex-1 text-nowrap rounded-r-none`}
									onClick={() => setRankBy('top-search')}
								>
									Total Searches
								</HSButton>
								<HSButton
									color='gray'
									className={`${rankBy === 'add-search' ? 'bg-white text-gray-900' : '!bg-gray-100'} flex-1 text-nowrap rounded-l-none`}
									onClick={() => setRankBy('add-search')}
								>
									Added from search
								</HSButton>
							</Button.Group>
						</div>
					</div>
				</div>
			</div>
			<div>
				<DataGrid
					ref={gridReference => {
						gridInstance = gridReference
					}}
					allowSorting
					allowPaging={false}
					allowFiltering={false}
					enableHover={false}
					rowDataBound={rowDataBound}
				>
					<ColumnsDirective>
						<ColumnDirective
							field='id'
							headerText='Rank'
							template={(item: IRowTemplate & { index: string }) =>
								rankTemplate(item, hotel)
							}
						/>
						<ColumnDirective
							field='name'
							headerText='Hotel'
							template={(item: IRowTemplate & { index: string }) =>
								hotelTemplate(item, hotel)
							}
						/>
						<ColumnDirective
							field='engagementMetrics.Impression.total'
							headerText='Total Searches'
							template={(item: IRowTemplate & { index: string }) =>
								totalTemplate(item, hotel)
							}
						/>
						<ColumnDirective
							field='addedFromSearch'
							headerText='Added from search'
							template={(item: IRowTemplate & { index: string }) =>
								addedFromSearchTemplate(item, hotel)
							}
						/>
					</ColumnsDirective>
					<Inject services={[Sort]} />
				</DataGrid>
			</div>
		</div>
	)
}

export default Search
