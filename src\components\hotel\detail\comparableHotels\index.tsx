import {
	faBedFront,
	faHandshake,
	faPeopleGroup,
	faRulerTriangle
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import AddRemoveHotelButton from 'components/event/planner/addRemoveHotelButton'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import useRfpBuilder from 'lib/customHook/eventPlan/rfpBuilder'
import { formatImageUrl, formatNumber } from 'lib/helpers'
import { contentTypes, engagementTypes } from 'lib/helpers/contentEngagement'
import { ROLE_ADMIN } from 'lib/helpers/roles'
import { trackEngagement } from 'lib/services/contentEngagement.service'
import { useGetComparableHotels } from 'lib/services/hotels.service'
import type { EventPlan } from 'models/proposalResponseMonitor'

interface ComparableHotelProperties {
	venueId: string | null
	showAddToRfp?: boolean
	eventPlan: EventPlan | null
}

const ComparableHotels = (properties: ComparableHotelProperties) => {
	const { venueId, showAddToRfp, eventPlan } = properties
	const { data: comparableHotels } = useGetComparableHotels(
		'venue',
		venueId ?? '',
		!!venueId
	)
	const { addRemoveProposalRequests } = useRfpBuilder()
	const { userProfile } = useUserProfileContext()

	return (
		<div className='flex gap-8'>
			{comparableHotels?.map(comparableHotel => (
				<div
					className='card flex h-fit max-w-80 flex-col'
					key={comparableHotel.id}
				>
					<img
						src={formatImageUrl(
							comparableHotel.imageUrl ?? '',
							comparableHotel.id ?? ''
						)}
						alt={comparableHotel.name ?? 'hotel-image'}
						className='rounded-t-md'
					/>
					<div className='flex flex-col gap-3 p-3'>
						<div className='flex flex-col'>
							<div className='text-sm font-semibold text-gray-900'>
								{comparableHotel.name}
							</div>
							<div className='text-sm font-normal text-gray-600'>
								{comparableHotel.city}, {comparableHotel.state}{' '}
								{comparableHotel.zip}
							</div>
						</div>
						<div className='flex flex-wrap gap-x-5 gap-y-1'>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon
									className='w-4 text-gray-500'
									icon={faBedFront}
								/>
								<div className='text-sm font-normal text-gray-600'>
									{formatNumber(comparableHotel.guestRoomQuantity)}
								</div>
							</div>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon
									className='w-6 text-gray-500'
									icon={faHandshake}
								/>
								<div className='flex items-center gap-1'>
									<div className='text-sm font-normal text-gray-600'>
										{formatNumber(comparableHotel.meetingSpaceSquareFeet)}
									</div>
									<div className='text-sm font-normal text-gray-500'>Ft2</div>
								</div>
							</div>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon
									className='w-6 text-gray-500'
									icon={faPeopleGroup}
								/>
								<div className='text-sm font-normal text-gray-600'>
									{formatNumber(comparableHotel.meetingRoomQuantity)}
								</div>
							</div>
							<div className='flex gap-2'>
								<FontAwesomeIcon
									className='w-4 text-gray-500'
									icon={faRulerTriangle}
								/>
								<div className='flex items-center gap-1'>
									<div className='text-sm font-normal text-gray-600'>
										{formatNumber(
											comparableHotel.largestMeetingSpaceSquareFeet
										)}
									</div>
									<div className='text-sm font-normal text-gray-500'>Ft2</div>
								</div>
							</div>
						</div>
						{showAddToRfp ? (
							<AddRemoveHotelButton
								eventPlan={eventPlan}
								hotel={comparableHotel}
								disabledFields={false}
								suppressProfileConversion
								addRemoveProposalRequests={async (
									proposalRequests,
									callback
								) => {
									await trackEngagement(comparableHotel.id ?? '', {
										contentItemType: contentTypes.comparableVenue,
										contentItemId: comparableHotel.id ?? '',
										engagementType: engagementTypes.conversion
									})
									await addRemoveProposalRequests(
										proposalRequests,
										callback,
										userProfile?.role === ROLE_ADMIN
									)
								}}
							/>
						) : null}
					</div>
				</div>
			))}
		</div>
	)
}

export default ComparableHotels
