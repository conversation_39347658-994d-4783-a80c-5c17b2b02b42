/* eslint-disable unicorn/no-nested-ternary */
import headerStore from 'components/header/headerStore'
import {
	useGetEventDetail,
	useCancelEditEventPlan
} from 'lib/services/eventPlans.service'
import { useCallback, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import EventPlannerTab from './tabs'
import HSBadge from 'components/badge'
import type { EventPlanStatusKey } from 'lib/helpers/statusMaps'
import { EventPlanStatusMap } from 'lib/helpers/statusMaps'
import CopyText from 'components/copyText'
import HSButton from 'components/button'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
	faBookCopy,
	faDownload,
	faPenToSquare,
	faShareNodes,
	faTag
} from '@fortawesome/pro-light-svg-icons'
import { faCircleInfo } from '@fortawesome/pro-regular-svg-icons'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import PageLoader from 'components/pageLoader'
import questionStore from 'lib/store/plannerEvent/questionStore'
import {
	requestEditPermission,
	useCancelEventPlan,
	useDeleteEventPlan,
	useGetQuestionForEvent
} from 'lib/services/planner.service'
import {
	useGetConcessionsByEventId,
	useGetContractClausesForEvent
} from 'lib/services/event.service'
import concessionRequestStore from 'lib/store/plannerEvent/concessionRequestStore'
import contractClauseStore from 'lib/store/plannerEvent/contractClauseStore'
import { useGetEventAttachments } from 'lib/services/attachments.service'
import useAttachmentsStore from 'lib/store/attachmentsStore'
import PlannerComparableHotels from './tabs/reviewProposals/proposalTracker/comparableHotels'
import RemoveProposalRequestDrawer from 'components/event/planner/removeProposalRequestModal'
import useRfpBuilder from 'lib/customHook/eventPlan/rfpBuilder'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import EventPlanDeleteModal from 'components/eventPlanDeleteModal'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { toast } from 'react-toastify'
import EventPlanDuplicateModal from 'components/eventPlanDuplicateModal'
import ShareModal from 'components/shareModal'
import type { EventsWithStats } from '../dashboard/rfps/common'
import { ROLE_ADMIN } from 'lib/helpers/roles'
import HSDropdownButton from 'components/dropdown'
import EventPlanCancelDrawer from 'components/eventPlanCancelDrawer'
import TransferEventPlanOrganizationModal from 'components/transferEventPlanOrganization'
import EditRfpModal from 'components/planner/editRfpModal'
import EventPlanContractSignedDrawer from 'components/eventPlanContractDrawer'
import HSTooltip from 'components/tooltip'
import { format, parseISO } from 'date-fns'
import { Button } from 'flowbite-react'
import Loader from 'components/loader'
import {
	costSavingsStore,
	viewModes
} from 'lib/store/plannerEvent/costSavingsStore'
import NegotiatedRates from './tabs/contract/costSavings/views/negotiatedRates'

const EventPlanner = () => {
	const { setReplace, reset, setHide } = headerStore()
	const { eventId, chapterName } = useParams()
	const navigate = useNavigate()
	const { refetch: loadEventInfo } = useGetEventDetail(eventId ?? '', false)
	const { refetch: getQuestionForEvent } = useGetQuestionForEvent(
		eventId ?? '',
		!!eventId
	)
	const { refetch: getConcessionRequests } = useGetConcessionsByEventId(
		eventId ?? '',
		!!eventId
	)
	const { refetch: getContractClause } = useGetContractClausesForEvent(
		eventId ?? '',
		!!eventId
	)
	const { refetch: getAttachements } = useGetEventAttachments(
		eventId ?? '',
		!!eventId
	)

	const { userProfile } = useUserProfileContext()
	const {
		eventInfo,
		setObject,
		setDisableFields,
		setCurrentUserIsEditor,
		currentUserIsEditor,
		mergeProperties
	} = eventInfoStore()
	const { setObject: setQuestions } = questionStore()
	const { setObject: setConcessionRequests } = concessionRequestStore()
	const { setObject: setContractClause } = contractClauseStore()
	const { setObject: setAttachments } = useAttachmentsStore()
	const { viewMode } = costSavingsStore()

	const { mutateAsync: deleteEvent } = useDeleteEventPlan()
	const { mutateAsync: cancelEvent } = useCancelEventPlan()
	const { mutateAsync: cancelEditEvent } = useCancelEditEventPlan()

	const [showDeleteModal, setShowDeleteModal] = useState(false)
	const [showDuplicateModal, setShowDuplicateModal] = useState(false)
	const [showShareModal, setShowShareModal] = useState(false)
	const [showCancelModal, setShowCancelModal] = useState(false)
	const [showEditRfpModal, setShowEditRfpModal] = useState(false)
	const [showAssignOwnerModal, setShowAssignOwnerModal] = useState(false)
	const [showContractSignedDrawer, setShowContractSignedDrawer] =
		useState(false)
	const [changeComment, setChangeComment] = useState<string>('')
	const [hotelMessage, setHotelMessage] = useState<string>('')
	const [isLoading, setIsLoading] = useState(false)

	const { removeProposalRequests, showRemoveProposalRequestModal } =
		useRfpBuilder()

	const isOwnerOrEditor =
		eventInfo?.rfpOwner?.email === userProfile?.id?.toLowerCase() ||
		userProfile?.organizationRole.isRfpEditor

	const isEditorOrAdmin =
		userProfile?.organizationRole.isRfpEditor || userProfile?.isAdmin

	const loadEvent = useCallback(() => {
		setIsLoading(true)
		loadEventInfo()
			.then(response => {
				if (response.data) {
					const updatedData = {
						...response.data,
						proposalsPaused: response.data.proposalsPaused ?? false
					}
					setObject(updatedData, true)
				}
			})
			.catch(() => {
				console.log('Error loading event info')
			})
			.finally(() => {
				setIsLoading(false)
			})
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [loadEventInfo])

	const loadQuestions = useCallback(() => {
		getQuestionForEvent()
			.then(response => {
				setQuestions(response.data ?? [])
			})
			.catch(() => {
				console.log('Error loading questions')
			})
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [eventId])

	const loadConcessionRequests = useCallback(() => {
		getConcessionRequests()
			.then(response => {
				setConcessionRequests(response.data ?? [])
			})
			.catch(() => {
				console.log('Error loading concession requests')
			})
	}, [getConcessionRequests, setConcessionRequests])

	const loadContractClauses = useCallback(() => {
		getContractClause()
			.then(response => {
				setContractClause(response.data ?? [])
			})
			.catch(() => {
				console.log('Error loading contract clauses')
			})
	}, [getContractClause, setContractClause])

	const loadAttachments = useCallback(() => {
		getAttachements()
			.then(response => {
				setAttachments(response.data ?? [])
			})
			.catch(() => {
				console.log('Error loading attachments')
			})
	}, [getAttachements, setAttachments])

	useEffect(() => {
		setHide(false)
		setReplace({
			planner: '',
			event: '',
			[eventId ?? '']: eventInfo?.name ?? '',
			'request-info': ''
		})

		return () => {
			reset()
		}
	}, [eventInfo?.name, eventId, reset, setHide, setReplace])

	useEffect(() => {
		if (eventId) {
			loadEvent()
			loadQuestions()
			loadConcessionRequests()
			loadContractClauses()
			loadAttachments()
		}
	}, [
		eventId,
		loadAttachments,
		loadConcessionRequests,
		loadContractClauses,
		loadEvent,
		loadQuestions
	])

	useEffect(
		() => () => {
			setObject(null)
			setQuestions([])
			setConcessionRequests([])
			setContractClause([])
			setAttachments([])
		},
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[]
	)

	useEffect(() => {
		if (eventInfo && userProfile) {
			let editable = false
			if (userProfile.role === ROLE_ADMIN) {
				editable = true
				setCurrentUserIsEditor(true)
			} else {
				// editable = this user can edit this RFP (planners where owner or editor includes this user)
				editable =
					eventInfo.planners?.some(
						p => p.id === userProfile.id && (p.owner || p.isEditor)
					) ?? false
				if (!editable) {
					// editable = this user can edit any RFP in the org?
					editable =
						(userProfile.organizationId === eventInfo.organizationId &&
							userProfile.organizationRoles?.isRfpEditor) ??
						false
				}
				setCurrentUserIsEditor(editable)
				if (eventInfo.status !== 'New') {
					editable = false
				}
			}
			setDisableFields(!editable)
		}
	}, [
		eventInfo,
		setCurrentUserIsEditor,
		setDisableFields,
		userProfile,
		userProfile?.id,
		userProfile?.organizationId,
		userProfile?.organizationRoles?.isRfpEditor,
		userProfile?.role
	])

	const onCancelDelete = () => {
		setShowDeleteModal(false)
	}

	const onDeleteEventPlan = () => {
		deleteEvent({ eventPlanId: eventInfo?.id ?? '' })
			.then(() => {
				setShowDeleteModal(false)
				toast.success('Event deleted successfully')
				navigate('/planner/dashboard')
			})
			.catch((error: unknown) => {
				toast.error('Failed to delete the event')
				console.log(error)
			})
	}

	const onCancelEvent = () => {
		cancelEvent({
			eventPlanId: eventInfo?.id ?? '',
			comment: changeComment,
			hotelMessage,
			suppressNotification: false
		})
			.then(() => {
				toast.success('Event cancelled successfully')
			})
			.catch((error: unknown) => {
				toast.error('Failed to cancel the event')
				console.log(error)
			})
			.finally(() => {
				setShowCancelModal(false)
				loadEvent()
			})
		setObject({} as EventsWithStats)
	}

	const onCancel = () => {
		setShowCancelModal(true)
		setObject(eventInfo as EventPlan)
	}

	const onCancelCancel = () => {
		setShowCancelModal(false)
	}

	const onChangeStatus = () => {
		setObject(eventInfo as EventPlan)
	}

	const onTransfer = () => {
		setShowAssignOwnerModal(true)
		setObject(eventInfo as EventPlan)
	}

	const onCancelTransfer = () => {
		setShowAssignOwnerModal(false)
	}

	const onMarkContractSigned = () => {
		setShowContractSignedDrawer(true)
		setObject(eventInfo as EventPlan)
	}

	const onCancelMarkContractSigned = () => {
		setShowContractSignedDrawer(false)
	}

	const onRequestEditPermission = () => {
		requestEditPermission(eventInfo?.id ?? '')
			.then(() => {
				toast.success('Your request has been sent.')
			})
			.catch((error: unknown) => {
				toast.error('Failed to send the request')
				console.log(error)
			})
	}

	const onClickCancelEditRfp = () => {
		cancelEditEvent({ eventPlanId: eventId ?? '' })
			.then(response => {
				setObject(response, true)
				toast.success('Edit cancelled successfully')
				loadEvent()
			})
			.catch((error: unknown) => {
				toast.error('Changes detected. Canceling the edit is not permitted.')
				console.log(error)
			})
	}

	if (isLoading) {
		return <Loader />
	}

	return (
		<div>
			{eventInfo ? (
				<>
					<div className='mt-2 border bg-white py-2'>
						<div className='flex items-center justify-between gap-4 px-4'>
							<div className='flex flex-col gap-2'>
								{eventInfo.name ? (
									<div className='text-sm font-semibold leading-none text-gray-900'>
										{eventInfo.name}
									</div>
								) : (
									<div className='text-sm font-semibold leading-none text-red-600'>
										Please enter a name for this RFP
									</div>
								)}
								<div className='flex items-center gap-4'>
									<HSBadge
										color={
											EventPlanStatusMap[eventInfo.status as EventPlanStatusKey]
												?.color ?? 'gray'
										}
										className='px-2'
									>
										{
											EventPlanStatusMap[eventInfo.status as EventPlanStatusKey]
												?.label
										}
									</HSBadge>
									<CopyText value={eventInfo.rfpCode ?? ''} />
									<HSButton color='text'>
										<FontAwesomeIcon icon={faDownload} />
									</HSButton>
									{eventInfo.templateId ? (
										<>
											<div className='h-0.5 border-r' />
											<div className='flex items-center gap-2'>
												<FontAwesomeIcon icon={faTag} />
												<div className='text-xs font-normal text-gray-400'>
													RFP Template:
												</div>
												<div className='text-xxs font-normal text-gray-600'>
													{eventInfo.templateId}
												</div>
											</div>
										</>
									) : null}
								</div>
							</div>
							<div className='flex gap-4 text-gray-600'>
								<div className='flex items-center'>
									<div className='flex items-center gap-2'>
										<div className='text-sm font-normal text-gray-700'>
											Stop Receiving Proposals
										</div>
										<HSTooltip content='Control whether this RFP can receive new proposals'>
											<HSButton color='text'>
												<FontAwesomeIcon
													icon={faCircleInfo}
													className='text-gray-500'
												/>
											</HSButton>
										</HSTooltip>
									</div>
								</div>
								<div className='text-gray-900'>
									{eventInfo.id ? (
										<Button.Group color='light'>
											<HSButton
												color='gray'
												className={`rounded-r-none text-xs ${
													eventInfo.proposalsPaused
														? 'bg-primary-700 text-white'
														: 'bg-white text-gray-600'
												}`}
												onClick={() =>
													mergeProperties({
														proposalsPaused: true,
														proposalsPausedBy: userProfile?.id,
														proposalsPausedAt: new Date().toISOString(),
														proposalsPausedComment: null
													})
												}
												size='sm'
											>
												Yes
											</HSButton>
											<HSButton
												size='sm'
												color='gray'
												className={`rounded-l-none text-xs ${
													eventInfo.proposalsPaused
														? 'bg-white text-gray-600'
														: 'bg-primary-700 text-white'
												}`}
												onClick={() =>
													mergeProperties({
														proposalsPaused: false,
														proposalsPausedBy: null,
														proposalsPausedAt: null,
														proposalsPausedComment: null
													})
												}
											>
												No
											</HSButton>
										</Button.Group>
									) : null}
								</div>
								<div className='border-l' />
								<div className='flex items-center gap-4'>
									{isOwnerOrEditor ? (
										<div className='flex items-center gap-4'>
											{eventInfo.status !== 'New' && (
												<HSTooltip content='Edit '>
													<HSButton
														color='text'
														onClick={() => setShowEditRfpModal(true)}
													>
														<FontAwesomeIcon
															className='text-gray-500'
															icon={faPenToSquare}
															size='sm'
														/>
													</HSButton>
												</HSTooltip>
											)}
											<HSTooltip content='Share '>
												<HSButton
													color='text'
													onClick={() => setShowShareModal(true)}
												>
													<FontAwesomeIcon
														className='text-gray-500'
														icon={faShareNodes}
														size='sm'
													/>
												</HSButton>
											</HSTooltip>
										</div>
									) : null}
									<HSTooltip content='Duplicate '>
										<HSButton
											color='text'
											onClick={() => setShowDuplicateModal(true)}
										>
											<FontAwesomeIcon
												className='text-gray-500'
												icon={faBookCopy}
												size='sm'
											/>
										</HSButton>
									</HSTooltip>
								</div>
								<div className='border-l' />
								<HSDropdownButton
									showDropdownIcon
									color='none'
									label={
										<div className='text-xs font-semibold text-gray-500'>
											Actions
										</div>
									}
									items={[
										...(eventInfo.status === 'Contracting' && isOwnerOrEditor
											? [
													{
														id: '1',
														item: 'Mark as Contract Signed',
														clickFunction: onMarkContractSigned
													}
												]
											: []),
										...(![
											'New',
											'Cancelled',
											'Abandoned',
											'Contracting'
										].includes(eventInfo.status ?? '') && isOwnerOrEditor
											? [
													{
														id: '2',
														item: 'Cancel',
														clickFunction: onCancel
													}
												]
											: []),

										...(currentUserIsEditor
											? []
											: [
													{
														id: '3',
														item: 'Request Edit Permission',
														clickFunction: onRequestEditPermission
													}
												]),

										...(eventInfo.status === 'New' && isOwnerOrEditor
											? [
													{
														id: '4',
														item: 'Delete',
														clickFunction: () => setShowDeleteModal(true)
													}
												]
											: []),

										...(userProfile?.isAdmin
											? [
													{
														id: '5',
														item: 'Change Status (ADMIN)',
														clickFunction: onChangeStatus
													}
												]
											: []),
										...(isEditorOrAdmin
											? [
													{
														id: '6',
														item: 'Assign Owner',
														clickFunction: onTransfer
													}
												]
											: []),
										...(currentUserIsEditor && eventInfo.editingSentDateTime
											? [
													{
														id: '7',
														item: 'Cancel Edit',
														clickFunction: onClickCancelEditRfp
													}
												]
											: [])
									]}
									showTooltip={false}
								/>
							</div>
						</div>
					</div>
					{eventInfo.proposalsPaused && eventInfo.proposalsPausedAt ? (
						<div className='flex border-b bg-red-50 px-6 py-1 text-xs font-semibold text-red-800'>
							Stopped accepting new proposals on{' '}
							{format(
								parseISO(eventInfo.proposalsPausedAt),
								'EEEE, MMMM d, yyyy h:mma'
							)}
						</div>
					) : null}
					<div className='bg-gray-50 p-6'>
						{chapterName === 'comparable-hotels' ? (
							<PlannerComparableHotels />
						) : viewMode.key === viewModes.edit.key ? (
							<NegotiatedRates />
						) : (
							<EventPlannerTab />
						)}
					</div>
				</>
			) : (
				<PageLoader />
			)}
			{showRemoveProposalRequestModal ? (
				<RemoveProposalRequestDrawer
					proposalRequests={removeProposalRequests}
					onClose={() => {}}
					onConfirm={() => {}}
				/>
			) : null}
			{showDeleteModal ? (
				<EventPlanDeleteModal
					eventPlan={eventInfo as EventPlan}
					onCancelDelete={onCancelDelete}
					onDeleteEventPlan={onDeleteEventPlan}
					showDeleteModal={showDeleteModal}
				/>
			) : null}
			{showDuplicateModal ? (
				<EventPlanDuplicateModal
					show={showDuplicateModal}
					duplicateAction='duplicateRfp'
					createItemType='eventPlan'
					template={eventInfo}
					onClose={() => {
						setShowDuplicateModal(false)
					}}
				/>
			) : null}
			{showShareModal ? (
				<ShareModal
					show={showShareModal}
					userProfileId={userProfile?.id ?? ''}
					itemsToShare={[eventInfo] as EventsWithStats[]}
					onClose={() => setShowShareModal(false)}
					shareType='template'
				/>
			) : null}
			{showCancelModal ? (
				<EventPlanCancelDrawer
					onCancelCancel={() => onCancelCancel()}
					eventPlan={eventInfo as EventPlan}
					onCancelEvent={onCancelEvent}
					hotelMessage={hotelMessage}
					setHotelMessage={setHotelMessage}
					changeComment={changeComment}
					setChangeComment={setChangeComment}
				/>
			) : null}
			{showAssignOwnerModal ? (
				<TransferEventPlanOrganizationModal
					show={showAssignOwnerModal}
					event={eventInfo as EventsWithStats}
					onClose={onCancelTransfer}
					refetchEvents={loadEvent}
				/>
			) : null}
			{showEditRfpModal ? (
				<EditRfpModal
					show={showEditRfpModal}
					onClose={() => setShowEditRfpModal(false)}
					eventId={eventInfo?.id ?? ''}
					setEventInfo={updatedPlan =>
						setObject(updatedPlan.value as EventPlan, true)
					}
				/>
			) : null}
			{showContractSignedDrawer ? (
				<EventPlanContractSignedDrawer
					showContractSignedDrawer={showContractSignedDrawer}
					onCancelMarkContractSigned={() => onCancelMarkContractSigned()}
					eventPlan={eventInfo as EventPlan}
				/>
			) : null}
		</div>
	)
}

export default EventPlanner
