trigger:
  - main

pool:
  vmImage: 'ubuntu-latest'

variables:
  pnpm_config_cache: $(Pipeline.Workspace)/.pnpm-store
  releaseNotesFile: $(System.DefaultWorkingDirectory)/release-notes.md
  releaseNotesVariable: 'ReleaseNotesContent'
  organization: 'saratogasandboxes' # Replace with your organization
  project: 'Doorbell' # Replace with your project
  repositoryId: 'browser-app-v2' # Replace with your repository ID
  PAT: $(PAT-TOKEN) # Securely store your PAT in a pipeline variable
  releaseDefinitionId: 44 # Replace with your release definition ID
  releaseDefinitionEnvironmentId: 124 # Replace with your release environment ID

steps:
  - task: UseNode@1
    inputs:
      version: '20.x'
    displayName: 'Install Node.js'

  - task: Cache@2
    inputs:
      key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml'
      path: $(pnpm_config_cache)
    displayName: Cache pnpm

  - script: |
      npm install -g pnpm@9
      pnpm config set store-dir $(pnpm_config_cache)
    displayName: 'Setup pnpm'

  - script: |
      pnpm install --frozen-lockfile
      npx syncfusion-license activate
      export NODE_OPTIONS="--max-old-space-size=4096"
      pnpm run build
    displayName: 'pnpm install and build'

  - script: |
      COVERAGE_THRESHOLD=0.1

      # Run unit tests with coverage report
      COVERAGE_OUTPUT=$(pnpm test -- --coverage 2>&1)
      echo "$COVERAGE_OUTPUT"

      # Extract the total coverage percentage for Statements
      COVERAGE=$(echo "$COVERAGE_OUTPUT" | grep 'Statements' | awk '{print $3}' | sed 's/%//')

      echo "Total Coverage: $COVERAGE%"

      # Ensure COVERAGE is not empty
      if [ -z "$COVERAGE" ]; then
        echo "Failed to extract coverage percentage. Failing the build."
        exit 1
      fi

      # Check if coverage is less than $COVERAGE_THRESHOLD%
      if (( $(echo "$COVERAGE < $COVERAGE_THRESHOLD" | bc -l) )); then
        echo "Code coverage is less than $COVERAGE_THRESHOLD%. Failing the build."
        exit 1
      else
        echo "Code coverage meets or exceeds the threshold of $COVERAGE_THRESHOLD%. Build passes."
      fi
    displayName: 'Run unit tests and check coverage'

  - task: AzurePowerShell@5
    displayName: 'Generate Release Notes'
    inputs:
      azureSubscription: 'Pay-As-You-Go hopskip (e2f27716-49d1-4f15-ac45-b7ce9704d261)'
      scriptType: 'FilePath'
      scriptPath: 'pipelines/scripts/generate-release-notes.ps1'
      scriptArguments: -Organization $(organization) -Project $(project) -RepositoryId $(repositoryId) -BuildId $(Build.BuildId) -ReleaseNotesFile $(releaseNotesFile) -ReleaseNotesVariable $(releaseNotesVariable) -PAT $(PAT) -ReleaseDefinitionId $(releaseDefinitionId) -ReleaseDefinitionEnvironmentId $(releaseDefinitionEnvironmentId)
      azurePowerShellVersion: 'LatestVersion'

  - task: PublishPipelineArtifact@1
    displayName: 'Publish Pipeline Artifact Web'
    inputs:
      targetPath: dist
      artifact: web

  - task: PublishPipelineArtifact@1
    displayName: 'Publish Pipeline Artifact Infrastructure'
    inputs:
      targetPath: infrastructure
      artifact: infrastructure
