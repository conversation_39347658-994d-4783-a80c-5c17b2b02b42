/* eslint-disable unicorn/no-nested-ternary */
import HSButton from 'components/button'
import HSTable from 'components/table'
import { Button } from 'flowbite-react'
import type { ICurrency } from 'lib/helpers'
import { formatCurrency } from 'lib/helpers'
import { useState, useMemo } from 'react'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons'
import MetricCard from 'components/metric/card'

const responseFilters = {
	byDestination: 'byDestination',
	byChain: 'byChain',
	proposalCost: 'proposalCost'
}
interface EventPlanItem {
	venueLocation: string
	value: number
	count: number
	chainId: string
	name: string
	averageRoomRate: number
}

interface RfpResponseGridProperties {
	metrics: {
		eventPlansByVenueLocation: EventPlanItem[]
		eventPlansByChain: EventPlanItem[]
		proposalsByChain: EventPlanItem[]
	}
	currency: ICurrency
	isFeatureLocked: boolean
}

const RfpResponseGrid = (properties: RfpResponseGridProperties) => {
	const { currency, metrics, isFeatureLocked } = properties
	const [typeFilter, setTypeFilter] = useState(responseFilters.byDestination)

	const filteredData = useMemo(() => {
		let responses = []
		if (typeFilter === responseFilters.byDestination) {
			responses = metrics.eventPlansByVenueLocation
				.sort((a: EventPlanItem, b: EventPlanItem) => b.value - a.value)
				.slice(0, 10)
				.map((item: EventPlanItem) => ({
					destination: item.venueLocation,
					value: formatCurrency(item.value, currency),
					count: item.count
				}))
		} else if (typeFilter === responseFilters.byChain) {
			responses = metrics.eventPlansByChain
				.sort((a: EventPlanItem, b: EventPlanItem) => b.value - a.value)
				.slice(0, 10)
				.map((item: EventPlanItem) => ({
					destination: item.name,
					value: formatCurrency(item.value, currency),
					count: item.count
				}))
		} else {
			responses = metrics.proposalsByChain
				.sort((a: EventPlanItem, b: EventPlanItem) => b.value - a.value)
				.slice(0, 10)
				.map((item: EventPlanItem) => ({
					destination: item.name,
					value: formatCurrency(item.value / item.count, currency),
					count: formatCurrency(item.averageRoomRate, currency)
				}))
		}
		return responses
	}, [currency, metrics, typeFilter])

	const destinationHeaderTemplate = () => {
		const tooltipContent =
			typeFilter === responseFilters.byDestination
				? 'Shows the top 10 destinations by value'
				: typeFilter === responseFilters.byChain
					? 'Top 10 RFP Responses by Hotel Chains'
					: 'The average total projected proposal cost, calculated from each proposal submitted across all RFPs from each of the top 10 hotel chains, offering insights into pricing trends by chain'

		return (
			<div className='flex items-center gap-2'>
				{typeFilter === responseFilters.byDestination ? 'Destination' : 'Chain'}
				<TooltipComponent content={tooltipContent}>
					<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
				</TooltipComponent>
			</div>
		)
	}

	const proposalByCostTemplate = () => (
		<div className='flex items-center gap-2'>
			{typeFilter === responseFilters.proposalCost ? 'Room Rate' : 'Count'}
		</div>
	)

	return (
		<div className='card flex h-96 flex-col'>
			<div className='flex items-center justify-between p-4'>
				<div className='text-base font-medium text-gray-700'>Top 10</div>
				<div className='flex items-center gap-4'>
					<div className='text-sm font-medium text-gray-900'>View by</div>
					<div className='text-gray-900'>
						<Button.Group className='w-fit'>
							<HSButton
								color='gray'
								className={`flex-1 rounded-r-none ${typeFilter === 'byDestination' ? 'bg-gray-200' : ''}`}
								onClick={() => setTypeFilter(responseFilters.byDestination)}
							>
								Destination
							</HSButton>
							<HSButton
								color='gray'
								className={`rounded-l-none rounded-r-none ${
									typeFilter === 'byChain' ? 'bg-gray-200' : ''
								}`}
								onClick={() => setTypeFilter(responseFilters.byChain)}
							>
								Chain
							</HSButton>
							<HSButton
								color='gray'
								className={`rounded-l-none ${typeFilter === 'proposalCost' ? 'bg-gray-200' : ''}`}
								onClick={() => setTypeFilter(responseFilters.proposalCost)}
							>
								Proposal Cost
							</HSButton>
						</Button.Group>
					</div>
				</div>
			</div>
			<div className='overflow-auto'>
				<MetricCard isLocked={isFeatureLocked}>
					<div className='disabled-rounded-border border-t'>
						<HSTable
							rows={filteredData}
							allowPaging={false}
							columns={[
								{
									field: 'destination',
									headerText: 'Destination',
									headerTemplate: destinationHeaderTemplate,
									width: 200,
									sortable: true
								},
								{
									field: 'value',
									headerText: 'Value',
									width: 150,
									sortable: true
								},
								{
									field: 'count',
									headerText: 'Proposal Count',
									width: 150,
									sortable: true,
									headerTemplate: proposalByCostTemplate
								}
							]}
						/>
					</div>
				</MetricCard>
			</div>
		</div>
	)
}

export default RfpResponseGrid
