{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"_artifactsLocation": {"type": "string", "metadata": {"description": "The blob storage container URL where templates, DSC resources, and custom scripts are uploaded for deployment."}}, "_artifactsLocationSasToken": {"type": "securestring", "metadata": {"description": "Auto-generated token to access _artifactsLocation including leading '?'"}}, "solutionPrefix": {"type": "string", "defaultValue": "drbl-mvp"}, "environmentSuffix": {"type": "string"}, "storageType": {"type": "string", "defaultValue": "Standard_LRS", "allowedValues": ["Standard_LRS", "Standard_ZRS", "Standard_GRS", "Standard_RAGRS", "Premium_LRS"]}}, "variables": {"templatesPath": "[concat(parameters('_artifactsLocation'), '/')]"}, "resources": [{"apiVersion": "2015-01-01", "type": "Microsoft.Resources/deployments", "name": "webDeployment", "dependsOn": [], "properties": {"mode": "Incremental", "templateLink": {"uri": "[concat(variables('templatesPath'), 'browser-app-v2.json', parameters('_artifactsLocationSasToken'))]", "contentVersion": "*******"}, "parameters": {"solutionPrefix": {"value": "[parameters('solutionPrefix')]"}, "environmentSuffix": {"value": "[parameters('environmentSuffix')]"}, "storageType": {"value": "[parameters('storageType')]"}}}}], "outputs": {"webStorageAccountName": {"type": "string", "value": "[reference('webDeployment').outputs.blobStorageAccountName.value]"}, "webStorageAccountKey": {"type": "string", "value": "[reference('webDeployment').outputs.blobStorageAccountKey.value]"}, "webStorageContainerName": {"type": "string", "value": "[reference('webDeployment').outputs.blobStorageContainerName.value]"}}}