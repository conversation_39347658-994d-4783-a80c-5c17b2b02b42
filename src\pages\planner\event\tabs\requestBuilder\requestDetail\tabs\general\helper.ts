import type { IEventTypeOption } from 'lib/helpers/eventTypes'
import type { EventPlan } from 'models/proposalResponseMonitor'

export const filterEventTypes = (
	selection: IEventTypeOption,
	event: EventPlan
) => {
	if (event.groupType === 'wedding') {
		return selection.value === 'wedding'
	}
	if (event.meetingSpaceRequired) {
		return !['wedding', 'roomsonly'].includes(selection.value)
	}
	return selection.value !== 'wedding'
}

export const filterIndustryTypes = (
	selection: {
		value: string
		name: string
		color: string
	},
	event: EventPlan
) =>
	['association', 'corporate', 'nonprofit'].includes(event.groupType ?? '') ||
	selection.value === 'other'
