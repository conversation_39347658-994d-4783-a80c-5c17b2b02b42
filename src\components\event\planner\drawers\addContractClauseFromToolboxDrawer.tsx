/* eslint-disable react/no-danger */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import HSButton from 'components/button'
import HSDrawer from 'components/drawer'
import PageLoader from 'components/pageLoader'
import HSTable from 'components/table'
import { Drawer } from 'flowbite-react'
import { addExistingContractClauseToEvent } from 'lib/services/event.service'
import { useLoadContractClauses } from 'lib/services/organizations.service'
import type { IContractClauses } from 'models/organizations'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { useState } from 'react'
import { toast } from 'react-toastify'

interface IAddContractClauseFromToolboxDrawer {
	onClose: () => void
	eventPlan: EventPlan
	onContractClauseAdded?: () => void
	existingContractClauses?: IContractClauses[]
}

const AddContractClauseFromToolboxDrawer = (
	properties: IAddContractClauseFromToolboxDrawer
) => {
	const { eventPlan, onClose, onContractClauseAdded, existingContractClauses } =
		properties
	const { data = [], isFetching } = useLoadContractClauses(
		eventPlan.organizationId ?? ''
	)
	const [isLoading, setIsLoading] = useState(false)

	const [selectedContractClauses, setSelectedContractClauses] = useState<
		IContractClauses[]
	>([])

	const handleAddSelected = () => {
		setIsLoading(true)
		const task = []
		if (selectedContractClauses.length > 0) {
			for (const item of selectedContractClauses) {
				task.push(
					addExistingContractClauseToEvent(
						eventPlan.id ?? '',
						item.id ?? '',
						item
					)
				)
			}
			Promise.all(task)
				.then(() => {
					onClose()
				})
				.catch((error: unknown) => {
					console.error('Error adding contract clauses:', error)
				})
				.finally(() => {
					setIsLoading(false)
					toast.success('Contract clauses added successfully')
					onClose()
					onContractClauseAdded?.()
				})
		}
	}

	return (
		<HSDrawer
			style={{
				width: '50%'
			}}
			onClose={onClose}
			open
			position='right'
		>
			<Drawer.Header
				title='Add Contract Clause From Toolbox'
				titleIcon={() => null}
			/>
			<Drawer.Items
				className='flex flex-col gap-2 overflow-auto'
				style={{
					height: 'calc(100vh - 10rem)'
				}}
			>
				<div className='text-sm font-normal text-gray-500'>
					Select items to include the to the RFP
				</div>
				{isFetching || isLoading ? (
					<PageLoader />
				) : (
					<HSTable
						allowSelection
						allRowsSelected={isAllRowSelected => {
							setSelectedContractClauses(isAllRowSelected ? data : [])
						}}
						rowSelected={item => {
							if (Array.isArray(item.data)) {
								setSelectedContractClauses([
									...selectedContractClauses,
									...item.data
								])
							}
							if (!Array.isArray(item.data)) {
								const selected = item.data

								setSelectedContractClauses([
									...selectedContractClauses,
									selected
								])
							}
						}}
						rowDeselected={item => {
							if (Array.isArray(item.data)) {
								const deSelected = item.data
								const updatedList = selectedContractClauses.filter(existing =>
									deSelected.map(cc => cc.id ?? '').includes(existing.id ?? '')
								)
								setSelectedContractClauses(updatedList)
							}
							if (!Array.isArray(item.data)) {
								const selected = item.data

								setSelectedContractClauses(
									selectedContractClauses.filter(
										existing => selected.id !== existing.id
									)
								)
							}
						}}
						rows={data
							.filter(
								cc =>
									!existingContractClauses?.map(ecc => ecc.id).includes(cc.id)
							)
							.sort((a, b) => (a.title ?? '').localeCompare(b.title ?? ''))}
						columns={[
							{
								field: 'contractClause.title',
								headerText: 'Contract Clause NAME',
								render: row => row.title
							},
							{
								field: 'description',
								headerText: 'Contract Clause Details',
								render: row => (
									<div
										dangerouslySetInnerHTML={{
											__html: row.text ?? ''
										}}
									/>
								)
							}
						]}
					/>
				)}
			</Drawer.Items>
			<div className='flex items-center justify-end gap-4'>
				<HSButton color='light' onClick={onClose}>
					Cancel
				</HSButton>
				<HSButton
					onClick={handleAddSelected}
					disabled={selectedContractClauses.length === 0}
				>
					Add Selected Items
				</HSButton>
			</div>
		</HSDrawer>
	)
}

export default AddContractClauseFromToolboxDrawer
