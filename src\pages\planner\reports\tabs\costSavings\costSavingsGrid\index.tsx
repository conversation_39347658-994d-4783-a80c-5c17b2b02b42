import MetricCard from 'components/metric/card'
import HSTable from 'components/table'
import type { ICurrency } from 'lib/helpers'
import { formatCurrency } from 'lib/helpers'

interface CostSavingsGridProperties {
	data: {
		label: string
		formattedValue: string
		value: number
	}[]
	currency: ICurrency
	isFeatureLocked: boolean
}

const CostSavingsGrid = (properties: CostSavingsGridProperties) => {
	const { data, currency, isFeatureLocked } = properties

	return (
		<MetricCard isLocked={isFeatureLocked}>
			<HSTable
				rows={data}
				allowPaging={false}
				columns={[
					{
						field: 'label',
						headerText: 'Concession Category',
						width: 150
					},
					{
						field: 'value',
						headerText: 'Value',
						width: 150,
						render: item => formatCurrency(item.value, currency)
					}
				]}
			/>
		</MetricCard>
	)
}

export default CostSavingsGrid
