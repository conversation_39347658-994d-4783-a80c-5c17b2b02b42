/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable import/prefer-default-export */

import type { ITab } from 'components/tab'
import Search from '../tabs/search'
import Opportunities from '../tabs/opportunities'
import Booking from '../tabs/booking'
import type { ICurrency } from 'lib/helpers'
import type { ProposalRequest } from 'models/proposalResponseMonitor'
import type { Venue } from 'models/venue'
import type { IEngagementMetric } from 'lib/services/contentEngagement.service'
import type { IMetrics } from '..'
import { contentTypes, engagementTypes } from 'lib/helpers/contentEngagement'
import {
	findSupplierContactOrTeammate,
	getSupplierLeadSource
} from 'lib/helpers/supplierContacts'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import { calculateProposalValue } from 'lib/helpers/proposalValues'

export const getCompSetTabs = (
	searchData: {
		id: string | null
		name: string | null
		engagementMetrics: Record<string, IEngagementMetric> | undefined
		rank?: number | null
	}[],
	hotel: Venue | undefined,
	metrics: IMetrics,
	currency: ICurrency,
	isFeatureLocked: boolean
): ITab[] => [
	{
		title: 'Search',
		children: (
			<div className='flex flex-col flex-wrap'>
				<Search
					hotel={hotel}
					searchData={searchData}
					metrics={metrics}
					isFeatureLocked={isFeatureLocked}
				/>
			</div>
		),
		key: 0,
		path: `${globalThis.location.pathname}?type=search`
	},
	{
		title: 'Opportunities',
		children: (
			<div className='flex flex-col flex-wrap'>
				<Opportunities
					metrics={metrics}
					hotel={hotel}
					currency={currency}
					isFeatureLocked={isFeatureLocked}
				/>
			</div>
		),
		key: 1,
		path: `${globalThis.location.pathname}?type=opportunities`
	},
	{
		title: 'Booking',
		children: (
			<div className='flex flex-col flex-wrap'>
				<Booking
					hotel={hotel}
					metrics={metrics}
					isFeatureLocked={isFeatureLocked}
				/>
			</div>
		),
		key: 2,
		path: `${globalThis.location.pathname}?type=booking`
	}
]

export const mapProposalsToConversionData = (
	id: string,
	name: string,
	proposals: ProposalRequest | undefined
) => {
	const wonMetrics = proposals?.won?.reduce(
		(a, c) => ({
			...a,
			count: a.count + 1,
			totalValue: a.totalValue + calculateProposalValue(c.proposalValues)
		}),
		{
			count: 0,
			totalValue: 0
		}
	)
	const notWonMetrics = [
		// ...(proposals?.received ?? []),
		...(proposals?.lost ?? []),
		...(proposals?.declined ?? [])
	].reduce(
		(a, c) => ({
			...a,
			count: a.count + 1,
			totalValue: a.totalValue + calculateProposalValue(c.proposalValues)
		}),
		{
			count: 0,
			totalValue: 0
		}
	)

	const received = proposals?.received?.length ?? 0
	const won = proposals?.won?.length ?? 0
	const declined = proposals?.declined?.length ?? 0
	const lost = proposals?.lost?.length ?? 0
	const notWon = received + lost + declined
	const uniqueRfps = [
		...(proposals?.received ?? []),
		...(proposals?.won ?? []),
		...(proposals?.lost ?? []),
		...(proposals?.declined ?? [])
	].reduce<Record<string, ProposalRequest>>((accumulator, c) => {
		const key = c.eventPlanId ?? ''
		const newAccumulator = { ...accumulator }
		newAccumulator[key] = c
		return newAccumulator
	}, {})

	const supplierContacts = Object.values(uniqueRfps)
		.filter(a =>
			a.supplierContacts.some(
				(sc: ISupplierContact) => sc.organizationId === id
			)
		)
		.filter(rfp =>
			findSupplierContactOrTeammate(rfp.supplierContacts, rfp.createdBy ?? '')
		)

	return {
		id,
		name,
		won,
		declined,
		lost,
		notWon,
		opportunities: Object.keys(uniqueRfps).length,
		rate: won + notWon > 0 ? (Number(won) / (Number(won) + notWon)) * 100 : 0,
		responseTimes: { ...proposals?.responseTimes },
		opportunitiesAverageValue:
			(wonMetrics?.count ?? 0) + notWonMetrics.count > 0
				? ((wonMetrics?.totalValue ?? 0) + notWonMetrics.totalValue) /
					((wonMetrics?.count ?? 0) + notWonMetrics.count)
				: null,
		bookingCount: wonMetrics?.count ?? 0,
		bookingTotalValue: wonMetrics?.totalValue ?? 0,
		bookingAverageValue:
			proposals?.won && proposals.won.length > 0
				? (wonMetrics?.totalValue ?? 0) / proposals.won.length
				: null,
		leadSource: supplierContacts
			.flatMap(a => a.supplierContacts)
			.reduce<Record<string, number>>((accumulator, c) => {
				const supplierLeadSource = getSupplierLeadSource(c)
				if (supplierLeadSource) {
					accumulator[supplierLeadSource.key] =
						(accumulator[supplierLeadSource.key] ?? 0) + 1
				}
				return accumulator
			}, {})
	}
}

export const getHotelEngagementMetricsFilter = (venueId: string) =>
	`contentItemType eq '${contentTypes.profile}' and search.in(engagementType, '${engagementTypes.impression} ${engagementTypes.viewStart} ${engagementTypes.viewEnd} ${engagementTypes.conversion}') and propertyId eq '${venueId}'`
