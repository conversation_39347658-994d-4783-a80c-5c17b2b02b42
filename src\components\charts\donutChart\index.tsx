/* eslint-disable react/no-array-index-key */
import { Cell, Label, Pie, Pie<PERSON><PERSON>, Tooltip } from 'recharts'
import type { ContentType } from 'recharts/types/component/Tooltip'

interface IHSDonutChart {
	data: { name: string; value: number; color: string }[]
	label?: string | number
	labelSize?: string
	size?: number
	showTooltip?: boolean
	tooltipContent?: ContentType<string, string>
}

const HSDonutChart = (properties: IHSDonutChart) => {
	const {
		data,
		label,
		labelSize,
		tooltipContent,
		size = 156,
		showTooltip = false
	} = properties

	const renderTooltip = () => {
		if (showTooltip) {
			if (tooltipContent) {
				return <Tooltip content={tooltipContent} />
			}
			return (
				<Tooltip
					contentStyle={{
						color: '#111827'
					}}
				/>
			)
		}
		return null
	}
	return (
		<PieChart width={size} height={size}>
			<Pie
				data={data}
				dataKey='value'
				nameKey='name'
				cx='50%'
				cy='50%'
				outerRadius={size / 2}
				innerRadius={size / 2 - 15}
			>
				{data.map((entry, index) => (
					<Cell key={`cell-${index}`} fill={entry.color} />
				))}
				{label ? (
					<Label
						position='center'
						className={`${labelSize ?? 'text-5xl'} font-semibold text-gray-900`}
						color='#111827'
					>
						{label}
					</Label>
				) : null}
			</Pie>
			{renderTooltip()}
		</PieChart>
	)
}

export default HSDonutChart
