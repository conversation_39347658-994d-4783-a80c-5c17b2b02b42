/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable react/no-array-index-key */
import { useNavigate, useParams } from 'react-router-dom'
import { defaultMergeOptions } from './helper'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { ROLE_ADMIN, ROLE_ADMIN_HOTEL } from 'lib/helpers/roles'
import {
	useCreateNewVenue,
	useDeleteVenue,
	useGetSubscriptions,
	useGetVenue,
	useMergeHotel,
	useUpdateSearchIndex
} from 'lib/services/hotels.service'
import type { Venue } from 'models/venue'
import type { IVenueList, SubscriptionInfo } from 'models/organizations'
import { getAccessToken } from 'lib/interceptor/axios.interceptor'
import { useGetAttachments } from 'lib/services/attachments.service'
import type { IAttachment } from 'models/attachments'
import { getVenueAttachmentUrl } from 'lib/helpers/document'
import Loader from 'components/loader'
import { format } from 'date-fns'
import HotelSelector from 'components/hotelSelector'
import AddViewVenueNotes from 'components/addViewVenueNotes'
import HSDropdownButton from 'components/dropdown'
import useHotelStore from 'lib/store/hotelStore'
import useSubscriptionStore from 'lib/store/subscriptionStore'
import useAttachmentsStore from 'lib/store/attachmentsStore'
import HotelTabs, { getHotelTabs } from './tabs'
import { toast } from 'react-toastify'
import HSDrawer from 'components/drawer'
import { Drawer } from 'flowbite-react'
import HSButton from 'components/button'
import HotelSelectorTrimmed from 'components/hotelSelector/trimmed'
import HSRadioButton from 'components/radioButton'
import headerStore from 'components/header/headerStore'

const HotelProfile = () => {
	const { venueId, infoType, chapterName } = useParams()
	const { userProfile } = useUserProfileContext()
	const navigate = useNavigate()
	const [lookupVenueId, setLookupVenueId] = useState<string | undefined>('')
	const { setObject: setSubscriptions } = useSubscriptionStore()
	const { setObject: setAttachments } = useAttachmentsStore()
	const { venue: profileInfo, setProperty, setObject, saving } = useHotelStore()
	const [defaultVenue, setDefaultVenue] = useState('')
	const [selectedVenue, setSelectedVenue] = useState<
		string | Venue | IVenueList
	>()
	const { data: venueData } = useGetVenue(defaultVenue, !!defaultVenue)
	const [showMergeModal, setShowMergeModal] = useState(false)
	const [mergeTarget, setMergeTarget] = useState<Venue | undefined>()
	const [mergeOptions, setMergeOptions] = useState({ ...defaultMergeOptions })

	const { refetch: getAttachments } = useGetAttachments(
		venueId ?? lookupVenueId ?? '',
		false
	)
	const { refetch: loadSubscriptions } = useGetSubscriptions(
		venueId ?? lookupVenueId ?? '',
		false
	)
	const { refetch: getVenueDetail, isLoading: isGettingVenue } = useGetVenue(
		lookupVenueId ?? '',
		false
	)
	const { mutateAsync: mergeHotel } = useMergeHotel()
	const { mutateAsync: createNewVenue } = useCreateNewVenue()
	const { mutateAsync: deleteVenue } = useDeleteVenue()

	const { mutateAsync: updateSearchIndex } = useUpdateSearchIndex()
	const { setRightComponent, reset, setHide, setMiddleComponent, setReplace } =
		headerStore()

	const venueSelected = useCallback(
		(venue: Venue) => {
			setSelectedVenue(venue)
			navigate(
				`/hotel-profiles/${chapterName ?? 'hotel-info'}/${venue.id}/${infoType ?? 'location'}`
			)
		},
		[navigate, chapterName, infoType]
	)
	const onClickNew = () => {
		createNewVenue({
			name: `New Hotel Profile Created ${format(
				new Date(),
				'eee M/d @ h:mmaaa'
			)}`
		})
			.then((response: Venue | undefined) => {
				if (response) navigate(`/hotel-profiles/new-profile/${response.id}/new`)
			})
			.catch((error: unknown) => console.error(error))
	}

	const onClickMerge = () => {
		setMergeOptions({ ...defaultMergeOptions })
		setMergeTarget(undefined)
		setShowMergeModal(true)
	}

	const onCancelMerge = () => {
		setMergeOptions({ ...defaultMergeOptions })
		setMergeTarget(undefined)
		setShowMergeModal(false)
	}

	const onConfirmMerge = () => {
		mergeHotel({
			hotelId: profileInfo.id ?? '',
			targetId: mergeTarget?.id ?? '',
			data: Object.keys(mergeOptions).reduce(
				(accumulator, currentKey) => {
					const currentOptionValue =
						mergeOptions[currentKey as keyof typeof mergeOptions].value
					return { ...accumulator, [currentKey]: currentOptionValue }
				},
				{ ...defaultMergeOptions }
			)
		})
			.then(() => {
				toast.success(`${profileInfo.name} merged into ${mergeTarget?.name}`)
				navigate(
					`/admin-tools/hotel-profiles/${chapterName ?? 'hotel-info'}/${mergeTarget?.id}/${infoType ?? 'location'}`
				)
			})
			.catch((error: unknown) => console.error(error))
			.finally(() => {
				setMergeOptions({ ...defaultMergeOptions })
				setMergeTarget(undefined)
				setShowMergeModal(false)
			})
	}

	const onChangeMergeOptions = (
		key: keyof typeof mergeOptions,
		value: string
	) => {
		setMergeOptions(s => ({
			...s,
			[key]: {
				...s[key],
				value
			}
		}))
	}

	const setAttachmentsState = (
		attachments_: IAttachment[],
		accessToken: string
	) => {
		setAttachments(
			attachments_.map((a: IAttachment) => ({
				...a,
				url: getVenueAttachmentUrl(a, accessToken),
				folder: a.tags.length > 0 ? a.tags[0] : ''
			}))
		)
	}

	const onClickToggleDelete = () => {
		const tryDelete = !profileInfo.isDeleted
		deleteVenue({ venueId: venueId ?? '', isDeleted: tryDelete })
			.then(() => {
				setProperty('isDeleted', tryDelete, 'boolean')
			})
			.catch((error: unknown) => console.error(error))
	}

	const loadAttachments = () => {
		getAccessToken()
			.then(accessToken => {
				getAttachments()
					.then((response_: { data: IAttachment[] | undefined }) => {
						if (response_.data)
							setAttachmentsState([...response_.data], accessToken ?? '')
					})
					.catch((error: unknown) => console.error(error))
			})
			.catch((error: unknown) => console.error(error))
	}

	const onClickIndex = () => {
		updateSearchIndex({ venueId: venueId ?? '' })
			.then(() => {
				toast.success('Search index is updated')
			})
			.catch((error: unknown) => {
				toast.error('Failed to update the search index')
				console.error(error)
			})
	}
	useEffect(() => {
		if (!venueId) {
			setSelectedVenue(venueData)
		}
	}, [defaultVenue, venueId, venueData])
	useEffect(() => {
		if (!venueId) {
			if ((userProfile?.associatedVenues?.length ?? 0) > 0) {
				setDefaultVenue(
					userProfile?.associatedVenues?.sort((c, n) =>
						(c.name ?? '') > (n.name ?? '') ? 1 : -1
					)[0].id ?? ''
				)
			} else if (userProfile?.associatedVenues?.length === 1) {
				setDefaultVenue(userProfile.associatedVenues[0].id ?? '')
			}
		}
	}, [userProfile?.associatedVenues, venueId])

	const middleComponent = useMemo(
		() => (
			<div className='flex items-center justify-between gap-2'>
				{[ROLE_ADMIN, ROLE_ADMIN_HOTEL].includes(userProfile?.role ?? '') ? (
					<AddViewVenueNotes venue={profileInfo} />
				) : null}

				<div>
					<HotelSelector
						selectedVenue={selectedVenue}
						venueSelected={venue => {
							venueSelected(venue)
						}}
						showDropdownIcon={false}
						showSortIcon
					/>
				</div>

				<div>
					{[ROLE_ADMIN, ROLE_ADMIN_HOTEL].includes(userProfile?.role ?? '') ? (
						<div className='flex gap-4'>
							<div className='border-l-2' />
							<HSDropdownButton
								label='Actions'
								items={[
									{
										item: 'Create New',
										id: 'Create New',
										clickFunction: onClickNew
									},
									{
										item: 'Merge...',
										id: 'Merge...',
										clickFunction: onClickMerge,
										disabled: userProfile?.role !== ROLE_ADMIN
									},
									{
										item: profileInfo.isDeleted
											? 'Include in Search'
											: 'Exclude from Search',
										id: profileInfo.isDeleted
											? 'Include in Search'
											: 'Exclude from Search',
										clickFunction: onClickToggleDelete
									},
									{
										item: 'Update Search',
										id: 'Update Search',
										clickFunction: onClickIndex,
										disabled: Boolean(profileInfo.isDeleted)
									}
								]}
								dismissOnClick
							/>
						</div>
					) : null}
				</div>
			</div>
		),
		[profileInfo, venueSelected]
	)

	const rightComponent = useMemo(
		() => (
			<div className='text-xs font-medium text-gray-400'>
				{saving ? `Saving...` : `All changes saved`}
			</div>
		),
		[saving]
	)

	useEffect(() => {
		setHide(false)
		setReplace({
			[venueId ?? '']: profileInfo.name ?? '',
			[infoType ?? '']: ''
		})
		setRightComponent(rightComponent)
		setMiddleComponent(middleComponent)
		return () => {
			reset()
		}
	}, [
		infoType,
		middleComponent,
		profileInfo.name,
		reset,
		saving,
		setHide,
		setMiddleComponent,
		setReplace,
		setRightComponent,
		venueId
	])

	useEffect(() => {
		if (
			venueId &&
			([ROLE_ADMIN, ROLE_ADMIN_HOTEL].includes(userProfile?.role ?? '') ||
				userProfile?.associatedVenues?.some(v => v.id === venueId))
		) {
			setLookupVenueId(venueId)
		} else if (Number(userProfile?.associatedVenues?.length) > 0) {
			setLookupVenueId(
				userProfile?.associatedVenues?.sort((c, n) =>
					(c.name ?? '') > (n.name ?? '') ? 1 : -1
				)[0].id ?? ''
			)
		} else {
			setLookupVenueId(undefined)
		}

		if (lookupVenueId && (venueId || defaultVenue)) {
			getVenueDetail()
				.then((response: { data: Venue | undefined }) => {
					if (response.data) {
						loadSubscriptions()
							.then((response_: { data: SubscriptionInfo[] | undefined }) => {
								if (response_.data) setSubscriptions(response_.data)
							})
							.catch((error: unknown) => console.error(error))
						loadAttachments()
						setObject(response.data, true)
					}
				})
				.catch((error: unknown) => console.error(error))
		} else {
			setObject({} as Venue, true)
			setSubscriptions([])
			setAttachments([])
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [venueId, lookupVenueId, userProfile?.role, userProfile?.associatedVenues])

	if (isGettingVenue) return <Loader />

	return (
		<div className='flex h-full flex-col'>
			<div className='flex-1'>
				<div
					id='hotel-profiles-container'
					className='flex h-full flex-col bg-gray-50 pb-6'
				>
					<HotelTabs chapterTabs={getHotelTabs()} venue={profileInfo} />
				</div>
			</div>

			{showMergeModal ? (
				<HSDrawer
					id='merge-drawer'
					open={showMergeModal}
					onClose={onCancelMerge}
					style={{ width: '600px' }}
					position='right'
				>
					<Drawer.Header title='Merge Hotel' titleIcon={() => null} />
					<div className='flex flex-col justify-between gap-4'>
						<div className='flex flex-col gap-8'>
							<div className='text-sm text-gray-500'>
								Select the target hotel to merge this hotel into. When the merge
								is complete, this profile will be excluded from search and the
								target profile will remain active.
							</div>
							<div className='flex flex-col gap-2'>
								<div className='flex flex-wrap'>Target hotel profile:</div>
								<div>
									<HotelSelectorTrimmed
										selectedVenue={mergeTarget}
										venueSelected={v => setMergeTarget(v as Venue)}
										asPopover
										popoverPosition='top'
									/>
								</div>
							</div>
							<div className='border-b border-gray-200' />
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-medium text-gray-900'>
									Merge options:
								</div>
								<div className='card flex h-fit flex-1 flex-col'>
									<div className='flex rounded-t-lg border-b border-gray-200 bg-gray-50 p-3'>
										<div className='basis-1/2 text-xs font-semibold uppercase text-gray-500'>
											Merge option
										</div>
										<div className='basis-1/4 text-center text-xs font-semibold uppercase text-gray-500'>
											Keep Source
										</div>
										<div className='basis-1/4 text-center text-xs font-semibold uppercase text-gray-500'>
											Keep Target
										</div>
										<div className='basis-1/4 text-center text-xs font-semibold uppercase text-gray-500'>
											Merge
										</div>
									</div>
									<div
										className={`${Object.keys(mergeOptions).length > 0 ? 'border-b border-gray-200' : ''} flex flex-col px-3 py-4`}
									>
										{Object.keys(mergeOptions).map(
											(optionKey: string, index: number) => (
												<div className='flex py-4' key={index}>
													<div className='basis-1/2 text-sm font-semibold text-gray-900'>
														{
															mergeOptions[
																optionKey as keyof typeof mergeOptions
															].label
														}
													</div>
													<div className='flex basis-1/4 justify-center text-sm font-semibold text-gray-900'>
														<HSRadioButton
															label=''
															name={`KeepSourceOnly-${index}`}
															value='KeepSourceOnly'
															selectedValue={
																mergeOptions[
																	optionKey as keyof typeof mergeOptions
																].value
															}
															onChange={() =>
																onChangeMergeOptions(
																	optionKey as keyof typeof mergeOptions,
																	'KeepSourceOnly'
																)
															}
														/>
													</div>
													<div className='flex basis-1/4 justify-center text-sm font-semibold text-gray-900'>
														<HSRadioButton
															label=''
															name={`KeepTargetOnly-${index}`}
															value='KeepTargetOnly'
															selectedValue={
																mergeOptions[
																	optionKey as keyof typeof mergeOptions
																].value
															}
															onChange={() =>
																onChangeMergeOptions(
																	optionKey as keyof typeof mergeOptions,
																	'KeepTargetOnly'
																)
															}
														/>
													</div>
													<div className='flex basis-1/4 justify-center text-sm font-semibold text-gray-900'>
														<HSRadioButton
															label=''
															name={`KeepBoth-${index}`}
															value='KeepBoth'
															selectedValue={
																mergeOptions[
																	optionKey as keyof typeof mergeOptions
																].value
															}
															onChange={() =>
																onChangeMergeOptions(
																	optionKey as keyof typeof mergeOptions,
																	'KeepBoth'
																)
															}
														/>
													</div>
												</div>
											)
										)}
									</div>
								</div>
							</div>
						</div>
						<div>
							<div className='flex gap-4'>
								<HSButton
									className='flex-1'
									color='light'
									onClick={() => onCancelMerge()}
								>
									Cancel
								</HSButton>
								<HSButton
									className='flex-1'
									onClick={onConfirmMerge}
									disabled={!mergeTarget?.id}
								>
									Merge Now
								</HSButton>
							</div>
						</div>
					</div>
				</HSDrawer>
			) : null}
		</div>
	)
}

export default HotelProfile
