/* eslint-disable unicorn/no-array-reduce */
import { faDownload, faPenToSquare } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSBadge from 'components/badge'
import HSButton from 'components/button'
import HSCheckbox from 'components/checkbox'
import HSDrawer from 'components/drawer'
import HSSelect from 'components/select'
// import { useCurrencyContext } from 'lib/contexts/currency.context'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { ROLE_PLANNER } from 'lib/helpers/roles'
import { useFeatureContext } from 'lib/providers/feature.provider'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import { downloadReport } from 'lib/utils'
import type { IUserProfile } from 'models/userProfiles'
import type React from 'react'
import { useMemo, useState } from 'react'
import { reportFormats, reports } from './helper'
// import type { ICurrency } from 'lib/helpers'
import { Drawer } from 'flowbite-react'

const EventReports: React.FC = () => {
	const { eventInfo: event } = eventInfoStore()
	const { userProfile } = useUserProfileContext()
	// const { currencies } = useCurrencyContext()
	const { getFeatureByKey } = useFeatureContext()

	const siteSearchFeature = getFeatureByKey('SITESEARCH')
	const reportsFeature = getFeatureByKey('REPORTS')

	const [selectedReports, setSelectedReports] = useState<
		Record<string, boolean>
	>({})
	const [selectedOptions, setSelectedOptions] = useState<
		Record<string, Record<string, boolean> | null>
	>({})
	const [selectedShortlistId, setSelectedShortlistId] = useState<string>('')
	const [showOptions, setShowOptions] = useState<string | null>(null)
	// const [showReportCurrencySelector, setShowReportCurrencySelector] =
	// 	useState<boolean>(false)

	// Initialize options with defaults
	useMemo(() => {
		const initialOptions = Object.keys(reports)
			.filter(k => reports[k].options)
			.reduce<Record<string, Record<string, boolean>>>((accumulator, key) => {
				if (!reports[key].options) return accumulator

				accumulator[key] = Object.fromEntries(
					Object.keys(reports[key].options).map(
						optKey =>
							[optKey, reports[key].options?.[optKey].default || false] as [
								string,
								boolean
							]
					)
				)
				return accumulator
			}, {})

		setSelectedOptions(initialOptions)
	}, [])

	const onDownloadSelected = (currencyCode: string | null) => {
		const selectedReportKeys = Object.keys(selectedReports).filter(
			k => selectedReports[k]
		)

		const options = Object.keys(selectedOptions)
			.reduce<string[]>(
				(accumulator, key) => [
					...accumulator,
					...Object.keys(selectedOptions[key] ?? {})
						.filter(optKey => selectedOptions[key]?.[optKey])
						.map(optKey => optKey)
				],
				[]
			)
			.join(',')

		downloadReport({
			openWindow: false,
			reportPath: 'excel',
			eventPlanId: event?.id ?? '',
			templates: selectedReportKeys,
			params: {
				shortlistId: selectedShortlistId,
				options,
				audience: ROLE_PLANNER,
				currencyCode
			}
		})
			.then(() => {})
			.catch(() => {})

		for (const key of selectedReportKeys) {
			const report = reports[key]
			if (typeof report.afterDownload === 'function') {
				report.afterDownload({
					isAdmin: userProfile?.isAdmin ?? false,
					eventPlan: event,
					shortlistId: selectedShortlistId
				})
			}
		}
	}

	const onDownloadSeparate = (key: string, currencyCode: string | null) => {
		const report = reports[key]
		const optionsValue = selectedOptions[key]
			? Object.keys(selectedOptions[key])
					.filter(optKey => selectedOptions[key]?.[optKey])
					.join(',')
			: ''

		downloadReport({
			openWindow: report.format === reportFormats.pdf.key,
			reportPath: report.template ?? 'excel',
			eventPlanId: event?.id ?? '',
			templates: key,
			params: {
				options: optionsValue,
				shortlistId: selectedShortlistId,
				audience: ROLE_PLANNER,
				currencyCode
			}
		})
			.then(() => {})
			.catch(() => {})

		if (typeof report.afterDownload === 'function') {
			report.afterDownload({
				isAdmin: userProfile?.isAdmin ?? false,
				eventPlan: event,
				shortlistId: selectedShortlistId
			})
		}
	}

	// const onSelectReportCurrency = (currency: ICurrency) => {
	// 	globalThis.analytics.track('Report Currency Changed', {
	// 		currencyCode: currency.code,
	// 		currencyName: currency.name
	// 	})

	// 	setShowReportCurrencySelector(false)

	// 	if (Object.keys(selectedReports).length === 1) {
	// 		onDownloadSeparate(Object.keys(selectedReports)[0], currency.code)
	// 	} else {
	// 		onDownloadSelected(currency.code)
	// 	}
	// }

	const onClickReport = (key: string, isDownloadOnly = false) => {
		if (isDownloadOnly) {
			onDownloadSeparate(key, event?.currencyCode ?? '')
			return
		}

		if (
			reports[key].hasCurrency &&
			event?.proposalRequests?.some(
				pr => pr.currencyCode !== event.currencyCode
			)
		) {
			setSelectedReports({
				[key]: true
			})
			// setShowReportCurrencySelector(true)
		} else {
			onDownloadSeparate(key, event?.currencyCode ?? '')
		}
	}

	const onClickDownloadSelected = () => {
		const selectedReportKeys = Object.keys(selectedReports).filter(
			k => selectedReports[k]
		)

		if (selectedReportKeys.length === 1) {
			onDownloadSeparate(selectedReportKeys[0], event?.currencyCode ?? '')
			return
		}

		if (
			Object.keys(selectedReports).some(k => reports[k].hasCurrency) &&
			event?.proposalRequests?.some(
				pr => pr.currencyCode !== event.currencyCode
			)
		) {
			// setShowReportCurrencySelector(true)
		} else {
			onDownloadSelected(event?.currencyCode ?? '')
		}
	}

	const filterReport = (reportKey: string): boolean => {
		if (!reportsFeature) return false

		const report = reports[reportKey]

		if (typeof report.hide === 'function' && !userProfile?.isAdmin) {
			return !report.hide(event, userProfile as IUserProfile)
		}

		if (report.adminOnly) {
			return userProfile?.isAdmin ?? false
		}

		return true
	}

	const reportKeysList = Object.keys(reports).filter(element =>
		filterReport(element)
	)
	const canDownloadSelected = Object.keys(selectedReports).some(
		k => selectedReports[k]
	)
	// const hasMultipleCurrencies = event?.proposalRequests?.some(
	// 	pr => pr.currencyCode !== event.currencyCode
	// )

	return (
		// 	{/* Currency selector modal */}
		// 	{/* {showReportCurrencySelector ? (
		// 		<CurrencySelectorModal
		// 			show={showReportCurrencySelector}
		// 			onClose={() => setShowReportCurrencySelector(false)}
		// 			onSelect={onSelectReportCurrency}
		// 			prompt="One or more proposals were priced in a foreign currency. Select the currency you'd like to view."
		// 			currentCurrency={currencies[event.currencyCode]}
		// 			currencyOptions={[
		// 				currencies[event.currencyCode],
		// 				...event.proposalRequests
		// 					.reduce<string[]>((accumulator, pr) => {
		// 						if (
		// 							!accumulator.includes(pr.currencyCode) &&
		// 							pr.currencyCode !== event.currencyCode
		// 						) {
		// 							return [...accumulator, pr.currencyCode]
		// 						}
		// 						return accumulator
		// 					}, [])
		// 					.map(codevent => currencies[code])
		// 			]}
		// 		/>
		// 	) : null} */}
		// </div>
		<>
			<div className='flex items-center justify-between border-b px-6 py-4'>
				<div className='text-xl font-semibold'>My Reports</div>
				<div className='flex gap-2'>
					{Number(event?.shortlists?.length) > 0 && (
						<div className='flex items-center gap-2'>
							<div className='text-sm text-gray-500'>Filter by Shortlist:</div>
							<HSSelect
								value={selectedShortlistId}
								onChange={event_ => setSelectedShortlistId(event_.target.value)}
								className='w-64'
								disabled={Number(event?.shortlists?.length) === 0}
								data-testid='shortlist-selector'
							>
								{[
									{ value: '', label: 'All' },
									...(event?.shortlists?.map(sl => ({
										value: sl.id,
										label: `${sl.name} (${Object.keys(sl.hotels).length})`
									})) ?? [])
								].map((t, index) => (
									// eslint-disable-next-line react/no-array-index-key
									<option key={`ept-${index}`} value={t.value}>
										{t.label}
									</option>
								))}
							</HSSelect>
						</div>
					)}
					<HSButton
						color='primary'
						onClick={onClickDownloadSelected}
						disabled={!canDownloadSelected}
					>
						Download Selected
					</HSButton>
				</div>
			</div>
			<div className='p-6'>
				<div className='flex max-w-5xl flex-col gap-4'>
					{reportKeysList.map(key => {
						const report = reports[key]
						const isDisabled = report.disabled(
							event,
							key === 'proposalcomparison' ? !!siteSearchFeature : false
						)
						const isLicensed =
							userProfile?.isAdmin ||
							report.licenseTiers.includes(userProfile?.licenseTier ?? '')
						const canSelect = report.combine && !isDisabled && isLicensed
						const isSelected = selectedReports[key] || false
						return (
							<div key={key} className='card flex gap-2 p-4'>
								<div className='flex items-start pt-1.5'>
									{userProfile?.licenseTier === 'Essential' ? null : (
										<HSCheckbox
											{...(canSelect
												? {}
												: { title: report.disabledMessage?.(event) })}
											disabled={!canSelect}
											onClick={() => {
												if (!canSelect) return
												setSelectedReports({
													...selectedReports,
													[key]: !isSelected
												})
											}}
											checked={isSelected}
										/>
									)}
								</div>
								<div className='flex-1'>
									<div className='flex items-center gap-1'>
										<div className='text-lg font-semibold text-gray-900'>
											{report.name}
										</div>
										<HSBadge color={reportFormats[report.format].color}>
											{reportFormats[report.format].label}
										</HSBadge>
									</div>
									<div className='text-sm font-normal text-gray-500'>
										{report.description}
									</div>
								</div>
								<div className='flex items-center justify-between gap-1'>
									{report.options && selectedOptions[key] ? (
										<div>
											<HSButton
												outline
												size='xs'
												color='light'
												onClick={() => setShowOptions(key)}
												disabled={isDisabled}
											>
												<FontAwesomeIcon icon={faPenToSquare} />
											</HSButton>
										</div>
									) : null}
									<div>
										<HSButton
											outline
											size='xs'
											color='primary'
											onClick={() => {
												if (!isLicensed) return
												onClickReport(key, true)
											}}
											{...(isDisabled
												? { title: report.disabledMessage?.(event) }
												: {})}
											disabled={isDisabled}
										>
											<FontAwesomeIcon icon={faDownload} />
										</HSButton>
									</div>
								</div>
							</div>
						)
					})}
				</div>
			</div>
			{showOptions ? (
				<HSDrawer
					open={!!showOptions}
					onClose={() => setShowOptions(null)}
					titleStyle='text-xl font-semibold text-gray-900'
					position='right'
					style={{ width: '500px', height: '100%' }}
				>
					<Drawer.Header title='Customize Report' titleIcon={() => null} />
					<Drawer.Items
						style={{ height: 'calc(100vh - 9rem)' }}
						className='overflow-auto'
					>
						<div className='flex flex-col gap-8'>
							<div className='card p-4'>
								<div className='flex-1'>
									<div className='flex items-center gap-1'>
										<div className='text-lg font-semibold text-gray-900'>
											{reports[showOptions].name}
										</div>
										<HSBadge
											color={reportFormats[reports[showOptions].format].color}
										>
											{reportFormats[reports[showOptions].format].label}
										</HSBadge>
									</div>
									<div className='text-sm font-normal text-gray-500'>
										{reports[showOptions].description}
									</div>
								</div>
							</div>
							<div className='flex flex-col gap-2'>
								<div className='text-sm font-medium text-gray-900'>
									Customize information to include
								</div>
								<div className='flex flex-col gap-3'>
									{Object.keys(reports[showOptions].options ?? {})
										.filter(optKey => {
											const option = reports[showOptions].options?.[optKey]
											if (!option) return false
											if (typeof option.hide === 'function') {
												return !option.hide(event, userProfile as IUserProfile)
											}
											return true
										})
										.map(optKey => (
											<div key={optKey} className='py-[2]'>
												<HSCheckbox
													label={reports[showOptions].options?.[optKey].label}
													checked={
														selectedOptions[showOptions]?.[optKey] || false
													}
													onChange={event_ => {
														setSelectedOptions({
															...selectedOptions,
															[showOptions]: {
																...selectedOptions[showOptions],
																[optKey]: event_.target.checked
															}
														})
													}}
													className='mr-2 h-4 w-4 rounded'
												/>
											</div>
										))}
								</div>
							</div>
						</div>
					</Drawer.Items>
					<div className='flex items-center gap-4'>
						<HSButton
							color='light'
							className='grow'
							onClick={() => setShowOptions(null)}
						>
							Cancel
						</HSButton>
						<HSButton
							className='grow'
							onClick={() => {
								setShowOptions(null)
							}}
						>
							Save
						</HSButton>
					</div>
				</HSDrawer>
			) : null}
		</>
	)
}

export default EventReports
