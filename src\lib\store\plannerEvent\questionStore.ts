import type { IQuestionEvent } from 'models/questions'
import { create } from 'zustand'

interface QuestionState {
	saving: boolean
	questions: IQuestionEvent[]
}

interface QuestionAction {
	setObject: (value: IQuestionEvent[], clientOnly?: boolean) => void
	addToArray: (value: IQuestionEvent) => void
	removeFromArray: (value: IQuestionEvent) => void
	replaceInArray: (value: IQuestionEvent) => void
	setSaving: (saving: boolean) => void
}

const questionStore = create<QuestionState & QuestionAction>(set => ({
	questions: [],
	saving: false,
	setSaving: (saving: boolean) => set({ saving }),
	setObject: value => {
		set({ questions: value })
	},
	addToArray: value => {
		set(state => {
			const questions = [...state.questions, value]
			return { questions }
		})
	},
	removeFromArray: value => {
		set(state => {
			const questions = state.questions.filter(
				(item: IQuestionEvent) => item.question.id !== value.question.id
			)
			return { questions }
		})
	},
	replaceInArray: value => {
		set(state => {
			const questions = state.questions.map((item: IQuestionEvent) =>
				item.question.id === value.question.id ? value : item
			)
			return { questions }
		})
	}
}))

export default questionStore
