/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable react/no-array-index-key */
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSCheckbox from 'components/checkbox'
import LocationSelector from 'components/locationSelector'
import MultiSelectDropdown from 'components/multiSelect'
import PageLoader from 'components/pageLoader'
import HSTable from 'components/table'
import HSTextArea from 'components/textarea'
import HSTooltip from 'components/tooltip'
import { hotelTypes } from 'lib/helpers/venueFeatures'
import { chainDisplayOptions, useGetChains } from 'lib/services/chains.service'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import type { HotelChain } from 'models/chains'
import { useState } from 'react'
import {
	renderLocation,
	renderPreferredLocation,
	renderRemoveLocation
} from './template'
import type { PreferredLocation } from 'models/reporting'
import HSButton from 'components/button'
import HSModal from 'components/modal'
import { Modal } from 'flowbite-react'
import HSTextField from 'components/textField'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import HSStarRating from 'components/starRating'

const defaultLocation: PreferredLocation = {
	name: '',
	customName: null,
	latitude: 0,
	longitude: 0,
	preferred: false,
	addedFromSearch: false
}

const EventVenuePreferences = () => {
	const { eventInfo, setProperty } = eventInfoStore()
	const { data: chains = [] } = useGetChains(
		chainDisplayOptions.siteCriteria.key
	)
	const { userProfile } = useUserProfileContext()
	const [showAddNewChain, setShowAddNewChain] = useState(false)
	const [newChainName, setNewChainName] = useState('')

	const chainsPerColumn = chains.length / 3

	const [alternateLocation, setAlternateLocation] =
		useState<PreferredLocation>(defaultLocation)

	const onChangeSiteSearchProperty = (
		name: string,
		value:
			| string
			| boolean
			| null
			| { rating: number }[]
			| {
					name: string | null
					key: string | null
			  }[]
			| PreferredLocation[]
			| string[]
	) => {
		if (!eventInfo) return
		setProperty('siteSearch', {
			...eventInfo.siteSearch,
			[name]: value
		})
	}

	const onChangeStarRatings = (s: number, checked: boolean) => {
		if (eventInfo) {
			if (checked) {
				if (
					eventInfo.siteSearch?.preferredStarRatings.some(
						r => r.rating === s
					) === false
				) {
					onChangeSiteSearchProperty('preferredStarRatings', [
						...eventInfo.siteSearch.preferredStarRatings,
						{ rating: s }
					])
				}
			} else {
				onChangeSiteSearchProperty(
					'preferredStarRatings',
					(eventInfo.siteSearch?.preferredStarRatings || []).filter(
						r => r.rating !== s
					)
				)
			}
		}
	}

	const onChangePreferredBrands = (chain: HotelChain, checked: boolean) => {
		if (eventInfo) {
			if (checked) {
				if (
					eventInfo.siteSearch?.preferredBrands.some(
						b => b.key === chain.id
					) === false
				) {
					onChangeSiteSearchProperty('preferredBrands', [
						...eventInfo.siteSearch.preferredBrands,
						{ ...chain, key: chain.id }
					])
				}
			} else {
				onChangeSiteSearchProperty(
					'preferredBrands',
					eventInfo.siteSearch?.preferredBrands.filter(
						b => b.key !== chain.id
					) ?? []
				)
			}
		}
	}

	const renderStarRating = (s: number) => (
		<HSStarRating
			s={s}
			onChangeStarRatings={(value: number, checked: boolean) => {
				onChangeStarRatings(s, checked)
			}}
			checked={
				eventInfo?.siteSearch?.preferredStarRatings.some(b => b.rating === s) ||
				false
			}
		/>
	)

	const renderChains = (chain: HotelChain, index: number) => (
		<div className='flex items-center gap-4' key={`chain-${chain.id}-${index}`}>
			<HSCheckbox
				name='preferredBrands'
				checked={
					eventInfo?.siteSearch?.preferredBrands.some(
						b => b.key === chain.id
					) || false
				}
				onChange={event =>
					onChangePreferredBrands(chain, event.currentTarget.checked)
				}
			/>
			<div className='text-sm font-normal text-gray-700'>{chain.name}</div>
		</div>
	)

	const removeAlternateLocation = (location: PreferredLocation) => {
		onChangeSiteSearchProperty(
			'alternateLocations',
			eventInfo?.siteSearch?.alternateLocations.filter(
				l => l.name !== location.name
			) ?? []
		)
	}

	const onChangeAlternateLocation = (location: PreferredLocation) => {
		const updatedAlternateLocations = [
			...(eventInfo?.siteSearch?.alternateLocations || [])
				.filter(al => al.name !== location.name)
				.map(al => ({ ...al, preferred: false })),
			{ ...location, preferred: true }
		]
		onChangeSiteSearchProperty('alternateLocations', updatedAlternateLocations)
	}

	const addAlternateLocation = (location: PreferredLocation) => {
		onChangeSiteSearchProperty('alternateLocations', [
			...(eventInfo?.siteSearch?.alternateLocations ?? []),
			{
				...location,
				name:
					location.name?.split(',').length === 3
						? location.name.slice(
								0,
								Math.max(0, location.name.lastIndexOf(','))
							)
						: location.name
			}
		])
		setAlternateLocation(defaultLocation)
	}

	return eventInfo ? (
		<>
			<div className='border-b px-6 py-4'>
				<div className='text-xl font-semibold'>Venue preferences</div>
			</div>
			<div className='gap-6 p-6'>
				<div className='flex gap-6'>
					<div className='w-1/2'>
						<div className='flex flex-col gap-2'>
							<div>
								<div className='text-lg font-semibold text-gray-900'>
									Preferences
								</div>
								<div className='text-sm font-normal text-gray-500'>
									You can use this configuration as Hotel filter later on
								</div>
							</div>
							<div className='card h-full'>
								{userProfile?.previewExperiences?.SEARCHFILTERHOTELTYPE ? (
									<div className='border-b p-4'>
										<MultiSelectDropdown
											options={Object.keys(hotelTypes)}
											label='Hotel Type'
											showHelperText
											selectedOptions={eventInfo.siteSearch?.hotelTypes ?? []}
											onChange={(option: string, checked: boolean) => {
												if (checked) {
													onChangeSiteSearchProperty('hotelTypes', [
														...(eventInfo.siteSearch?.hotelTypes ?? []),
														option
													])
												} else {
													onChangeSiteSearchProperty(
														'hotelTypes',
														eventInfo.siteSearch?.hotelTypes.filter(
															(h: string) => h !== option
														) ?? []
													)
												}
											}}
										/>
									</div>
								) : null}
								<div className='border-b p-4'>
									<div className='flex flex-wrap gap-6'>
										<div className='flex min-w-64 flex-col gap-2'>
											<div className='text-sm font-medium text-gray-900'>
												Service Offered
											</div>
											<HSCheckbox
												label='Full Service'
												name='serviceLevel'
												checked={eventInfo.siteSearch?.serviceLevel === 'full'}
												onChange={event =>
													onChangeSiteSearchProperty(
														'serviceLevel',
														event.currentTarget.checked ? 'full' : null
													)
												}
											/>
											<HSCheckbox
												label='Select Service'
												name='serviceLevel'
												checked={
													eventInfo.siteSearch?.serviceLevel === 'select'
												}
												onChange={event =>
													onChangeSiteSearchProperty(
														'serviceLevel',
														event.target.checked ? 'select' : null
													)
												}
											/>
										</div>
										<div className='flex flex-col gap-2'>
											<div className='text-sm font-medium text-gray-900'>
												Average Rating
											</div>
											{renderStarRating(3)}
											{renderStarRating(4)}
											{renderStarRating(5)}
										</div>
									</div>
								</div>
								<div className='border-b p-4'>
									<div className='flex flex-col gap-2'>
										<div className='flex items-center gap-2'>
											<div className='text-sm font-medium text-gray-900'>
												Preferred Chains
											</div>
											<HSTooltip content="Selections below may be auto-populated based on hotels you've already added to the RFP">
												<FontAwesomeIcon
													icon={faInfoCircle}
													className='text-gray-700'
												/>
											</HSTooltip>
										</div>
										<div className='flex gap-6'>
											<div className='flex flex-col gap-2'>
												{chains
													.slice(0, Number(chainsPerColumn) + 1)
													.map((c, index) => renderChains(c, index))}
											</div>
											<div className='flex flex-col gap-2'>
												{chains
													.slice(
														Number(chainsPerColumn) + 1,
														chainsPerColumn * 2 + 2
													)
													.map((c, index) => renderChains(c, index))}
											</div>
											<div className='flex flex-col gap-2'>
												{chains
													.slice(chainsPerColumn * 2 + 2)
													.map((c, index) => renderChains(c, index))}
												<HSButton
													color='text'
													onClick={() => setShowAddNewChain(true)}
												>
													+ Add new chain
												</HSButton>
											</div>
										</div>
									</div>
								</div>
								<div className='p-4'>
									<div className='flex flex-col gap-2'>
										<div className='text-sm font-medium text-gray-900'>
											Additional Criteria
										</div>
										<HSTextArea
											rows={7}
											value={eventInfo.siteSearch?.additionalComments || ''}
											placeholder='Provide additional criteria...'
											onChange={event =>
												onChangeSiteSearchProperty(
													'additionalComments',
													event.target.value
												)
											}
										/>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className='w-1/2'>
						<div className='flex flex-col gap-2'>
							<div>
								<div className='text-lg font-semibold text-gray-900'>
									Locations requested for this RFP
								</div>
								<div className='text-sm font-normal text-gray-500'>
									Add locations(s) to this RFP so your industry contacts can
									suggest appropriate hotels
								</div>
							</div>
							<LocationSelector
								value={alternateLocation}
								onChange={location => {
									addAlternateLocation(location as unknown as PreferredLocation)
									setAlternateLocation(defaultLocation)
								}}
								customPlaceholder='Lookup and select a city locations to add...'
							/>
							<HSTable
								rows={eventInfo.siteSearch?.alternateLocations ?? []}
								defaultSort={{
									field: 'name',
									direction: 'asc'
								}}
								columns={[
									{
										field: 'name',
										headerText: 'Locations',
										render: renderLocation
									},
									{
										field: 'preferred',
										headerText: 'Preferred',
										render: item =>
											renderPreferredLocation(item, onChangeAlternateLocation),
										cellAlign: 'center',
										width: 150
									},
									{
										field: 'remove',
										headerText: 'Remove',
										render: (item: PreferredLocation) =>
											renderRemoveLocation(item, removeAlternateLocation),
										width: 100
									}
								]}
							/>
						</div>
					</div>
				</div>
			</div>
			{showAddNewChain ? (
				<HSModal
					openModal
					onClose={() => {
						setShowAddNewChain(false)
					}}
					header='Add Another Chain'
					size='md'
				>
					<Modal.Body>
						<HSTextField
							label='Chain Name'
							value={newChainName}
							onChange={event => setNewChainName(event.target.value)}
							placeholder='Enter Chain Name'
						/>
					</Modal.Body>
					<Modal.Footer>
						<HSButton
							color='light'
							onClick={() => {
								setShowAddNewChain(false)
							}}
							className='grow'
						>
							Cancel
						</HSButton>
						<HSButton
							onClick={() => {
								onChangeSiteSearchProperty('otherBrands', newChainName)
								setNewChainName('')
							}}
							className='grow'
							disabled={newChainName === ''}
						>
							Add Chain
						</HSButton>
					</Modal.Footer>
				</HSModal>
			) : null}
		</>
	) : (
		<PageLoader />
	)
}

export default EventVenuePreferences
