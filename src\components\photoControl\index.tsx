import type React from 'react'
import { useEffect, useState } from 'react'
import Dropzone from 'react-dropzone'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { PinturaEditorModal } from '@pqina/react-pintura'
import '@pqina/pintura/pintura.css'
import type {
	ImageSource,
	PinturaDefaultImageReaderResult
} from '@pqina/pintura'
import {
	// editor
	locale_en_gb,
	createDefaultImageWriter,

	// plugins
	setPlugins,
	plugin_crop,
	plugin_crop_locale_en_gb,
	getEditorDefaults
} from '@pqina/pintura'
import { Label } from 'flowbite-react'
import { faTrashCanXmark, faUpload } from '@fortawesome/pro-light-svg-icons'
import { toast } from 'react-toastify'
import HSButton from 'components/button'

setPlugins(plugin_crop)

const editorDefaults = getEditorDefaults({
	imageReader: {
		preprocessImageFile: async (file: File) => {
			const lowerType = file.type.toLowerCase()
			if (
				/avif/.test(lowerType) ||
				/tif/.test(lowerType) ||
				/tiff/.test(lowerType)
			) {
				return null
			}
			return file
		}
	},

	imageWriter: createDefaultImageWriter(),

	imageCropLimitToImage: false,
	imageBackgroundColor: [255, 255, 255],

	locale: {
		...locale_en_gb,
		...plugin_crop_locale_en_gb
	}
})

interface IPhotoControl {
	imageClasses?: string | null
	imageSrc: string | null
	onPhotoAdded: (file: File) => void
	onPhotoDeleted: () => void
	size?: string
	cropAspectRatioOptions: {
		ratio: number
		targetSize: {
			width: number
			height: number
		}
	}
	isDisabled?: boolean
	style?: React.CSSProperties | undefined
}

const PhotoControl = (properties: IPhotoControl) => {
	const {
		imageSrc,
		imageClasses = '',
		isDisabled = false,
		onPhotoAdded,
		size,
		cropAspectRatioOptions,
		style,
		onPhotoDeleted
	} = properties
	const [imageSourceHash, setImageSourceHash] = useState(Date.now())
	const [showEditor, setShowEditor] = useState(false)
	const [editImage, setEditImage] = useState<ImageSource>()
	const [hoverImage, setHoverImage] = useState(false)

	useEffect(() => {
		setImageSourceHash(Date.now())
	}, [imageSrc])

	const addPhoto = (acceptedFiles: File[]) => {
		if (acceptedFiles.length > 1) {
			toast.error('Multiple images are not allowed.')
			return
		}

		if (acceptedFiles.length === 0) {
			toast.error('Please select a valid image file.')
			return
		}

		if (
			acceptedFiles.some(f => {
				const lowerType = f.type.toLowerCase()
				if (
					/avif/.test(lowerType) ||
					/tif/.test(lowerType) ||
					/tiff/.test(lowerType)
				) {
					return true
				}
				return false
			})
		) {
			toast.error('Invalid file format.')
			return
		}

		setEditImage(acceptedFiles[0])
		setShowEditor(true)
	}

	const onClickImage = (
		event_:
			| React.MouseEvent<HTMLButtonElement>
			| React.KeyboardEvent<HTMLButtonElement>
	) => {
		event_.stopPropagation()
		setEditImage(imageSrc ?? '')
		setShowEditor(true)
	}

	const closeEditor = () => {
		setEditImage(undefined)
		setImageSourceHash(Date.now())
		setShowEditor(false)
	}

	const deletePhoto = (event_: React.MouseEvent<HTMLButtonElement>) => {
		event_.stopPropagation()
		onPhotoDeleted()
	}

	return (
		<div
			className='photo-container h-full w-full'
			style={{
				...style
			}}
		>
			{showEditor ? (
				<PinturaEditorModal
					{...editorDefaults}
					src={editImage}
					imageCropAspectRatio={cropAspectRatioOptions.ratio}
					imageTargetSize={cropAspectRatioOptions.targetSize}
					onProcessstart={() => {}}
					onLoad={(result: PinturaDefaultImageReaderResult) =>
						console.log('load image', result.duration)
					}
					onClose={() => {
						closeEditor()
					}}
					onProcess={({ dest }) => {
						onPhotoAdded(dest)
						closeEditor()
					}}
					onUpdate={destination => {
						console.log(destination)
					}}
				/>
			) : (
				<Dropzone
					disabled={isDisabled}
					onDrop={addPhoto}
					accept={{
						'image/jpeg': [],
						'image/png': [],
						'image/svg': [],
						'image/gif': []
					}}
				>
					{({ getRootProps, getInputProps }) => (
						<div
							{...getRootProps()}
							className={`photo-add ${size ?? 'h-full'} `}
						>
							<input {...getInputProps()} />
							{imageSrc ? (
								<div
									className='relative flex h-full w-full items-center justify-center overflow-hidden rounded-lg border border-gray-200 shadow-sm'
									onMouseEnter={() => setHoverImage(true)}
									onMouseLeave={() => setHoverImage(false)}
								>
									{hoverImage ? (
										<div
											className='absolute right-0 top-0 bg-gray-200'
											style={{
												borderTopRightRadius: '6px',
												borderBottomLeftRadius: '6px'
											}}
										>
											<HSButton
												size='xl'
												color='text'
												onClick={event_ => deletePhoto(event_)}
											>
												<FontAwesomeIcon
													className='p-2 text-xl text-red-500'
													icon={faTrashCanXmark}
												/>
											</HSButton>
										</div>
									) : null}
									<button
										type='button'
										onClick={event_ => onClickImage(event_)}
										onKeyDown={event_ => onClickImage(event_)}
										className='flex h-full w-full items-center justify-center'
										style={{ padding: 0, border: 'none', background: 'none' }}
										aria-label='company logo'
									>
										<img
											src={`${imageSrc}?${imageSourceHash}`}
											alt='company-logo'
											className={imageClasses}
											crossOrigin='anonymous'
										/>
									</button>
								</div>
							) : (
								<div className='flex h-full w-full items-center justify-center'>
									<Label
										htmlFor='dropzone-file'
										className='flex h-full w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-white hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-gray-500 dark:hover:bg-gray-600'
									>
										<div className='flex flex-col items-center justify-center pb-6 pt-5'>
											<FontAwesomeIcon
												icon={faUpload}
												size='2xl'
												className='text-gray-400'
											/>
											<p className='mb-2 text-sm text-gray-500 dark:text-gray-400'>
												<span className='font-semibold'>Click to upload</span>{' '}
												or drag and drop
											</p>
											<p className='text-xs text-gray-500 dark:text-gray-400'>
												SVG, PNG, JPG or GIF (MAX. 800x400px)
											</p>
										</div>
									</Label>
								</div>
							)}
						</div>
					)}
				</Dropzone>
			)}
		</div>
	)
}

export default PhotoControl
