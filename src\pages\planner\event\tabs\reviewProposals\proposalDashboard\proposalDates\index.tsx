import { faFile, faInfoCircle } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSTable from 'components/table'
import type {
	IProposalDates,
	ProposalRequest
} from 'models/proposalResponseMonitor'
import { formatCurrency, type ICurrency } from 'lib/helpers'
import { format } from 'date-fns'
import { TooltipComponent } from '@syncfusion/ej2-react-popups'

interface IProposalDatesProperties {
	proposals: ProposalRequest[]
	currency: ICurrency | undefined
	proposalDates: IProposalDates[]
}

const proposalCostTemplate = () => (
	<div className='flex items-center gap-2'>
		<div>Proposal Cost</div>
		<TooltipComponent content='Average of highest proposed date range from each hotel'>
			<FontAwesomeIcon icon={faInfoCircle} className='text-gray-400' />
		</TooltipComponent>
	</div>
)

const PlannerProposalDates = (properties: IProposalDatesProperties) => {
	const { proposals, currency, proposalDates } = properties

	return (
		<div className='flex gap-4'>
			<div className='w-1/4'>
				<div className='card flex h-48 flex-col justify-center gap-2 px-4 py-3'>
					<div className='text-sm font-bold text-gray-500'>
						Ready for review
					</div>
					<div className='flex items-center gap-2'>
						<div className='flex items-center justify-center rounded-md bg-green-100 p-2'>
							<FontAwesomeIcon className='text-green-600' icon={faFile} />
						</div>
						<div className='text-2xl font-semibold leading-none text-gray-900'>
							{proposals.length}
						</div>
						<div className='text-xs font-medium text-gray-500'>Proposals</div>
					</div>
				</div>
			</div>
			<div className='card w-3/4'>
				<div className='disabled-rounded-border h-48 overflow-auto'>
					<HSTable
						rows={[
							...(proposals.length > 0
								? [
										{
											eventDate: 'All Date Ranges',
											proposalsReceived: `${proposals.length} hotel${proposals.length === 1 ? '' : 's'}`,
											proposalValue: formatCurrency(
												proposals.reduce(
													(a, c) => a + (c.proposalValue ?? 0),
													0
												) / proposals.length,
												currency
											)
										}
									]
								: []),
							...proposalDates
								.sort((c, n) => ((c.key ?? '') > (n.key ?? '') ? 1 : -1))
								.map(pd => ({
									eventDate: `${format(pd.startDate ?? '', 'MMM d, yyyy')}${
										pd.endDate ? ` - ${format(pd.endDate, 'MMM d, yyyy')}` : ''
									} (${pd.suggested ? 'Suggested' : 'Requested'})`,
									// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
									proposalsReceived: pd.count,
									proposalValue: formatCurrency(pd.averageValue, currency)
								}))
						]}
						allowPaging={false}
						columns={[
							{
								field: 'eventDate',
								headerText: 'The Event Dates',
								width: 200,
								sortable: true
							},
							{
								field: 'proposalsReceived',
								headerText: 'Received',
								width: 150,
								sortable: true
							},
							{
								field: 'proposalValue',
								headerText: 'Proposals Cost',
								width: 150,
								sortable: true,
								headerTemplate: proposalCostTemplate
							}
						]}
					/>
				</div>
			</div>
		</div>
	)
}

export default PlannerProposalDates
