/* eslint-disable react/no-array-index-key */
/* eslint-disable unicorn/consistent-function-scoping */
/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import CopyText from 'components/copyText'
import { format, parseISO } from 'date-fns'
import { Link, useNavigate } from 'react-router-dom'
import type { EventsWithStats } from '../../common'
import { EventPlanStatusMap } from 'lib/helpers/statusMaps'
import { useChatContext } from 'lib/contexts/chatContext'
import {
	faCircleExclamation,
	faClipboardList
} from '@fortawesome/pro-regular-svg-icons'
import { capitalize } from 'es-toolkit'
import HSButton from 'components/button'
import { memo, useCallback, useMemo } from 'react'
import {
	faCalendarLines,
	faEllipsisVertical,
	faUser
} from '@fortawesome/pro-light-svg-icons'
import HSProgress from 'components/progressBar'
import HSDropdownButton from 'components/dropdown'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import plannerDashboardStore from 'pages/planner/dashboard/dataStore'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'
import HSFontAwesomeIcon from 'components/fontAwesomIcon'

interface RFPCardProperties {
	lastIndex: boolean
	rfp: EventsWithStats
	setSelectedRFPContext: React.Dispatch<
		React.SetStateAction<
			| {
					open: boolean
					rfp: EventsWithStats | null
			  }
			| undefined
		>
	>
}

export const copyItemTypes = {
	eventPlan: 'eventPlan'
}

const RFPCard = memo((properties: RFPCardProperties) => {
	const { userProfile } = useUserProfileContext()
	const { rfp, lastIndex, setSelectedRFPContext } = properties
	const { unreadConversations, parseConversationUniqueName } = useChatContext()
	const { rfpId } = parseConversationUniqueName(
		unreadConversations && unreadConversations.length > 0
			? unreadConversations[0].uniqueName
			: ''
	)
	const navigate = useNavigate()
	const {
		setShowShareModal,
		setShowDuplicateModal,
		setShowDeleteModal,
		setShowAssignOwnerModal,
		setShowCancelModal,
		setShowContractSignedDrawer
	} = plannerDashboardStore()

	const { setObject } = eventInfoStore()

	const isOwnerOrEditor = useMemo(
		() =>
			rfp.ownerId?.toLowerCase() === userProfile?.id?.toLowerCase() ||
			userProfile?.organizationRole.isRfpEditor,
		[rfp.ownerId, userProfile]
	)

	const isEditorOrAdmin = useMemo(
		() => userProfile?.organizationRole.isRfpEditor || userProfile?.isAdmin,
		[userProfile]
	)

	const onDelete = useCallback(() => {
		setShowDeleteModal(true)
		setObject(rfp)
	}, [rfp, setShowDeleteModal, setObject])

	const onCancel = useCallback(() => {
		setShowCancelModal(true)
		setObject(rfp)
	}, [rfp, setShowCancelModal, setObject])

	const onChangeStatus = useCallback(() => {
		setObject(rfp)
	}, [rfp, setObject])

	const onTransfer = useCallback(() => {
		setShowAssignOwnerModal(true)
		setObject(rfp)
	}, [rfp, setShowAssignOwnerModal, setObject])

	const onDuplicate = useCallback(() => {
		setShowDuplicateModal(true)
		setObject(rfp)
	}, [rfp, setShowDuplicateModal, setObject])

	const onShare = useCallback(() => {
		setShowShareModal(true)
		setObject(rfp)
	}, [rfp, setShowShareModal, setObject])

	const onMarkContractSigned = useCallback(() => {
		setShowContractSignedDrawer(true)
		setObject(rfp)
	}, [rfp, setShowContractSignedDrawer, setObject])

	const dropdownItems = useMemo(() => {
		const items = []
		if (rfp.status === 'Contracting' && isOwnerOrEditor) {
			items.push({
				id: '3',
				item: 'Mark as Contract Signed',
				clickFunction: onMarkContractSigned
			})
		}
		if (isOwnerOrEditor) {
			items.push({
				id: '4',
				item: 'Share with planner(s)...',
				clickFunction: onShare
			})
		}
		items.push({
			id: '5',
			item:
				rfp.itemType === copyItemTypes.eventPlan
					? 'Duplicate '
					: 'Create RFP...',
			clickFunction: onDuplicate
		})
		if (rfp.status === 'New' && isOwnerOrEditor) {
			items.push({
				id: '6',
				item: 'Delete',
				clickFunction: onDelete
			})
		}
		if (
			!['New', 'Cancelled', 'Abandoned', 'Contracting'].includes(
				rfp.status ?? ''
			) &&
			isOwnerOrEditor
		) {
			items.push({
				id: '7',
				item: 'Cancel',
				clickFunction: onCancel
			})
		}
		if (userProfile?.isAdmin) {
			items.push({
				id: '8',
				item: 'Change Status (ADMIN)',
				clickFunction: onChangeStatus
			})
		}
		if (isEditorOrAdmin) {
			items.push({
				id: '9',
				item: 'Assign Owner',
				clickFunction: onTransfer
			})
		}
		return items
	}, [
		rfp,
		isOwnerOrEditor,
		isEditorOrAdmin,
		userProfile,
		onMarkContractSigned,
		onShare,
		onDuplicate,
		onDelete,
		onCancel,
		onChangeStatus,
		onTransfer
	])

	const respondedPercent = useMemo(
		() =>
			Math.round(
				((rfp.requestsResponded || 0) * 100) / (rfp.requestsTotal || 1)
			),
		[rfp.requestsResponded, rfp.requestsTotal]
	)

	const isOverdue = useMemo(() => {
		if (
			[
				EventPlanStatusMap.Submitted?.key,
				EventPlanStatusMap.Bidding?.key
			].includes(rfp.status ?? '') &&
			!!rfp.responsesDueDate
		) {
			try {
				return parseISO(rfp.responsesDueDate) < new Date()
			} catch {
				return false
			}
		}
		return false
	}, [rfp.status, rfp.responsesDueDate])

	return (
		<div
			className={` ${lastIndex ? 'rounded-b-md' : ''} border border-gray-200`}
		>
			<div className='flex flex-col gap-2'>
				<div>
					<div className='flex justify-between'>
						<div className='px-4 pt-4'>
							<Link to={`/planner/event/${rfp.id}`}>
								<div className='text-xs font-bold text-primary-600 underline'>
									{rfp.name}
								</div>
							</Link>
						</div>
						<div className='flex'>
							<div
								onClick={() => setSelectedRFPContext({ open: true, rfp })}
								color='light'
								role='button'
								onKeyDown={() => setSelectedRFPContext({ open: true, rfp })}
								tabIndex={0}
							>
								<div className='flex h-8 w-8 items-center justify-center rounded-bl-md border-b p-2 shadow-sm hover:bg-primary-100'>
									<HSFontAwesomeIcon
										icon={faClipboardList}
										className='h-4 w-4 text-gray-600'
									/>
								</div>
							</div>
							<div className='flex h-8 w-8 items-center justify-center border-b p-2 shadow-sm hover:bg-primary-100'>
								<HSDropdownButton
									showDropdownIcon={false}
									color='none'
									label={
										<HSFontAwesomeIcon
											icon={faEllipsisVertical}
											className='h-4 w-4 text-gray-600'
										/>
									}
									items={dropdownItems}
									showTooltip={false}
								/>
							</div>
						</div>
					</div>
					<div className='px-4'>
						<CopyText value={rfp.rfpCode} textSize='xs' />
					</div>
				</div>
				{rfp.status?.toLowerCase() !== 'new' || rfp.responsesDueDate ? (
					<div>
						{rfp.status?.toLowerCase() === 'new' ? null : (
							<div className='flex items-center gap-1 px-4'>
								<div className='text-xs font-normal text-gray-400'>
									<HSFontAwesomeIcon icon={faUser} className='h-3 w-3' />
								</div>
								<div className='text-xs font-normal text-gray-600'>
									{capitalize(rfp.ownerName ?? '') || 'N/A'}
								</div>
							</div>
						)}

						{rfp.responsesDueDate ? (
							<div className='flex items-center gap-1 px-4'>
								<div className='text-xs font-normal text-gray-400'>
									<HSFontAwesomeIcon
										icon={faCalendarLines}
										className='h-3 w-3'
									/>
								</div>
								<div className='text-xs font-normal text-gray-600'>
									{rfp.responsesDueDate
										? format(rfp.responsesDueDate, 'EEE MMM d, yyyy')
										: ''}
								</div>
							</div>
						) : null}
					</div>
				) : null}
				{rfp.status === EventPlanStatusMap.New?.key ? (
					<div className='px-4 text-xs font-normal italic text-gray-500'>
						Send to hotels to track responses
					</div>
				) : (
					<div className='flex flex-col gap-1 px-4'>
						<div className='flex items-center justify-between gap-1'>
							<div className='text-sm font-medium text-gray-600'>
								{respondedPercent}%
							</div>
							<div className='text-xs font-normal text-gray-500'>
								{rfp.requestsResponded}/{rfp.requestsTotal} hotels
							</div>
						</div>

						<div>
							<HSProgress
								progress={respondedPercent}
								size='sm'
								color='primary'
							/>
						</div>
					</div>
				)}
				<div>
					{isOverdue ? (
						<div className='flex w-full items-center gap-1'>
							<div className='w-full bg-red-100 p-2 text-xs font-medium text-red-800'>
								Response Overdue
							</div>
						</div>
					) : null}

					{rfp.id === rfpId && (
						<div className='flex w-full items-center justify-between gap-1 bg-primary-100'>
							<div className='flex items-center gap-2 p-2'>
								<HSFontAwesomeIcon icon={faCircleExclamation} />
								<div className='w-full text-xs font-medium text-primary-600'>
									Unread Message
								</div>
							</div>
							<div className='p-1 font-semibold'>
								<HSButton
									color='text'
									onClick={() => {
										navigate(
											`/planner/event/${rfp.id}/${rfp.id}/follow-up/message-planner`
										)
									}}
									size='xs'
								>
									View
								</HSButton>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	)
})

export default RFPCard
