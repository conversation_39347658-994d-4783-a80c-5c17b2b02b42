<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1.0, maximum-scale=1.0, shrink-to-fit=no, user-scalable=no"
		/>

		<meta name="description" content="HopSkip" />
		<meta name="author" content="The HopSkip Team" />
		<meta name="keyword" content="HopSkip" />
		<link
			rel="stylesheet"
			href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css"
			integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm"
			crossorigin="anonymous"
		/>
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		<link
			href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Noto+Sans+Mono:wght@100..900&display=swap"
			rel="stylesheet"
		/>
		<script>
			;(function () {
				var w = window
				var ic = w.Intercom
				if (typeof ic === 'function') {
					ic('reattach_activator')
					ic('update', w.intercomSettings)
				} else {
					var d = document
					var i = function () {
						i.c(arguments)
					}
					i.q = []
					i.c = function (args) {
						i.q.push(args)
					}
					w.Intercom = i
					var l = function () {
						var s = d.createElement('script')
						s.type = 'text/javascript'
						s.async = true
						s.src = 'https://widget.intercom.io/widget/tbzm7jeb'
						var x = d.getElementsByTagName('script')[0]
						x.parentNode.insertBefore(s, x)
					}
					if (w.attachEvent) {
						w.attachEvent('onload', l)
					} else {
						w.addEventListener('load', l, false)
					}
				}
			})()
		</script>
		<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
		<link rel="icon" type="image/png" href="/favicon.png" />
		<meta name="msapplication-TileColor" content="#da532c" />
		<meta name="theme-color" content="#ffffff" />

		<title>HopSkip for Professional Event Planners</title>
		<script>
			;(function () {
				var w = window
				var ic = w.Intercom
				if (typeof ic === 'function') {
					ic('reattach_activator')
					ic('update', w.intercomSettings)
				} else {
					var d = document
					var i = function () {
						i.c(arguments)
					}
					i.q = []
					i.c = function (args) {
						i.q.push(args)
					}
					w.Intercom = i
					var l = function () {
						var s = d.createElement('script')
						s.type = 'text/javascript'
						s.async = true
						s.src = 'https://widget.intercom.io/widget/tbzm7jeb'
						var x = d.getElementsByTagName('script')[0]
						x.parentNode.insertBefore(s, x)
					}
					if (w.attachEvent) {
						w.attachEvent('onload', l)
					} else {
						w.addEventListener('load', l, false)
					}
				}
			})()
		</script>
		<style>
			#browser-support-container {
				position: fixed;
				top: 0;
				left: 0;
				height: 35vh;
				width: 98%;
			}
			.row {
				padding: 0;
				margin: 0;
				height: 100%;
			}
			.left {
				padding: 0;
				margin: 0;
				height: 200px;
				background-color: #052c3f;
			}
			.left .container-copy,
			.left .container-title {
				color: #fff;
			}
			.right {
				padding: 0;
				margin: 0;
			}
			.right .container-copy,
			.right .container-title {
				color: #000;
			}
			.container-title {
				font-size: 1.5rem;
				font-weight: bold;
			}
			.container-logo {
				position: absolute;
				top: 1rem;
				right: 2rem;
			}
			.container-logo img {
				width: 180px;
			}
			.container-prompt {
				width: 100%;
				text-align: center;
				font-size: 1.5rem;
				font-weight: bold;
			}
			.container-hint {
				width: 100%;
				font-size: 1.15rem;
				text-align: center;
			}
			.container-input {
				width: 100%;
				margin-top: 1rem;
			}
			.list-group-item {
				cursor: pointer;
				border: 1px solid rgba(#9faeb5, 0.5);
				margin-bottom: 0.5rem;
				min-height: 4rem;
			}
			.item-title {
				font-weight: bold;
				font-size: 1rem;
			}
			a {
				color: #027587;
			}
			a:hover {
				color: #027587;
			}
			svg {
				width: 2rem;
				margin-right: 0.5rem;
			}
			.list-group-item:hover {
				background-color: rgba(#9faeb5, 0.5);
			}

			@media (min-width: 768px) {
				#browser-support-container {
					height: 100vh;
				}
				.row .left {
					height: 100%;
				}
				.inside-container {
					padding-top: 12rem;
					padding-left: 6rem;
					padding-right: 6rem;
				}
				.container-title {
					font-size: 2rem;
				}
				.container-copy {
					font-size: 1.25rem;
				}
			}
		</style>
	</head>

	<noscript>You need to enable JavaScript to run this app.</noscript>
	<body>
		<div id="browser-support-container">
			<div class="row">
				<div class="left col-lg-6 col-sm-12">
					<div class="inside-container">
						<div class="container-title">Your browser is not supported.</div>
						<div class="container-copy">
							HopSkip runs on modern browsers only
						</div>
					</div>
				</div>
				<div class="right col-lg-6 col-sm-12">
					<div class="inside-container">
						<div class="container-logo">
							<img src="/images/logo-h-text-primary.png" alt="logo" class />
						</div>
						<div class="container-prompt">
							HopSkip does not support your browser
						</div>
						<div class="container-hint">
							Please try one of the browsers listed below
						</div>
						<div class="container-input">
							<div class="list-group">
								<div class="list-group-item">
									<a href="https://www.google.com/chrome/">
										<div class="item-title">Google Chrome</div>
									</a>
								</div>
								<div class="list-group-item">
									<a href="https://www.microsoft.com/edge">
										<div class="item-title">Microsoft Edge</div>
									</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</body>
</html>
