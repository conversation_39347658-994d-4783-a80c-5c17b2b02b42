import type { roomLayoutOptions } from 'lib/helpers/roomLayouts'
import type { Hotelier } from './hotelier'
import type { SubscriptionInfo } from './organizations'
import type { PropertyPartnerSeller } from './propertyPartner'
import type { EventPlan, ProposalRequest } from './proposalResponseMonitor'

export interface VenueDetail {
	id: string | null
	venue: Venue
	distance: number
	location: string | null
	targetLocation: string | null
}

export interface IRfpsWithEligibleHotel extends EventPlan {
	eligibleMatches: VenueDetail[]
	hotels: ProposalRequest[] | null
}

export interface ODataVenueResponse {
	'@odata.context': string
	'@odata.count': number
	value: VenueODataResponse[]
}
export interface VenueODataResponse extends Venue {
	'@search.score': number
}

export interface Venue {
	openingDate: string | null
	lastRenovationDate: string | null
	chainId: string | null
	brandId: string | null
	propertyOwner: string | null
	propertyManager: PropertyPartnerSeller | null
	propertySeller: PropertyPartnerSeller | null
	propertySellers: PropertyPartnerSeller[] | null
	meetingSpaceDataIssue: string | null
	entertainment: string | null
	foodAndBeverage: string | null
	freeWifi: string | null
	serviceLevel: string | null
	venueRatings: Record<string, IVenueRating | null> | null
	hotelTypes: string[] | null
	hasRestaurantsOnSite: boolean | null
	onSiteRestaurants:
		| { description: string | null; name: string | null }[]
		| null
	awardOrRecognitions:
		| { description: string | null; name: string | null }[]
		| null
	venueAmenities: Record<
		string,
		{
			amenityTypeKey: string | null
			amenities: string[]
		}
	> | null
	maximumCeilingHeight: string | null
	guestRoomTypeQuantities: Record<string, number> | null
	startingRate: string | null
	defaultAttritionRatePercent: string | null
	defaultTaxRatePercent: string | null
	defaultPerRoomFees: string | null
	additionalTaxesAndFees: IAdditionalTaxesAndFees[]
	defaultCommissionRatePercent: string | null
	defaultFoodAndBeverageRates: IDefaultFoodAndBeverageRates[] | null
	defaultServiceChargePercent: string | null
	defaultFandBTaxRatePercent: string | null
	defaultRoomRentalTaxRatePercent: string | null
	defaultServiceChargeTaxRatePercent: string | null
	defaultProposalExpirationDays: string | null
	defaultCutOffDaysBeforeCheckIn: string | null
	defaultComments: string | null
	declinedReasons: IDeclinedReason[] | null
	hoteliers: Hotelier[]
	comparableVenues:
		| {
				id: string | null
				itemType: string | null
				name: string | null
				partitionKey: string | null
		  }[]
		| null
	id: string | null
	propertyId: string | null
	itemType: string | null
	paymentCustomerId: string | null
	currentSubscriptionInfo: SubscriptionInfo
	name: string | null
	address: string | null
	address2: string | null
	city: string | null
	state: string | null
	zip: string | null
	country: string | null
	currencyCode: string | null
	measurementSystemCode: string | null
	phone: string | null
	fax: string | null
	addMembersWithEmailDomain: string | null
	memberEmailDomain: string | null
	imageUrl: string | null
	latitude: number | null
	longitude: number | null
	geolocation: {
		type: string | null
		coordinates: [number, number]
		crs: {
			type: string | null
			properties: Record<string, string>
		}
	}
	destinations: IDestination[]
	airportCode: string | null
	airportDistance: string | null
	airportDistanceUnits: string | null
	airports: {
		distanceMiles: number
		iataCode: string
		name: string
		type: string
		id: string
		latitude: number
		longitude: number
	}[]
	guestRoomQuantity: number | null
	meetingRoomQuantity: number | null
	meetingSpaceSquareFeet: number | null
	largestMeetingSpaceSquareFeet: number | null
	conferenceFacilities: string | null
	starRating: number | null
	targetRanking: string | null
	description: string | null
	tagline: string | null
	lastSourceUpdateDate: string | null
	managedByPlatform: string | null
	firstAccountCreated: string | null
	receivedRfp: string | null
	respondedToRfp: string | null
	wonRfp: string | null
	invitationCode: {
		code: string | null
		expirationDate: string | null
	}
	reviewsCount: number
	ratingsMap: Record<string, string>
	averageRating: number
	expectationsDistribution: Record<number, number>
	expectationsSameOrBetter: number
	profileScore: {
		attributeScores: IAttributeScores | null
		total: number
		lastCalculated: string | null
	}
	platformSuggestion: {
		isEligible: boolean
		withinMiles: number
		enabledBy: string | null
		enabledAt: string | null
		disabledBy: string | null
		disabledAt: string | null
	}
	googlePlacesId: string | null
	externalContentLinks: IExternalContentLinks[] | null
	externalSourceIdentifiers: Record<string, string>
	created: string | null
	createdBy: string | null
	deleted: string | null
	deletedBy: string | null
	isDeleted: boolean | string | null
	modifiedBy: string | null
	modified: string | null
	documentFolders: string[] | null
	attachmentContainer: string | null
	contentEngagementMetricsSummary: {
		lastUpdated: string | null
		metrics: {
			Impression: number
			ViewStart: number
			ViewEnd: number
			Conversion: number
			Download: number
		}
	}
	weatherData: {
		monthlyTemperatures: {
			C: number[]
			F: number[]
		}
		monthlyRainfall: {
			in: number[]
			cm: number[]
		}
		monthlyWeatherPatterns: string[]
	}
	comparableHotelSource: string | null
}

export interface IAttributeScores {
	paid: IAttributeScoreValue | null
	admin: IAttributeScoreValue | null
	images: IAttributeScoreValue | null
	space: IAttributeScoreValue | null
	capacity: IAttributeScoreValue | null
	rooms: IAttributeScoreValue | null
	features: IAttributeScoreValue | null
	amenities: IAttributeScoreValue | null
	description: IAttributeScoreValue | null
}

export interface IAttributeScoreValue {
	rank: number
	scaledScore: number
	calculatedScore: number
	scaleMin: number
	scaleMax: number
}

export interface IDestination {
	id: string
	partitionKey: string
	itemType: string
	name: string
	properties: unknown
	logoImageUrl: string | null
}

export interface IVenueRating {
	ratingSystemKey: string | null
	rating: number | null
	effectiveDate: string | null
	expirationDate: string | null
}

export interface IExternalContentLinks {
	key: string | null
	name: string | null
	type: string | null
	url: string | null
	description: string | null
}

export interface IAdditionalTaxesAndFees {
	description: string | null
	index?: number | null
	label: string | null
	name: string | null
	rate: number | null
	type: string | null
}

export interface IDefaultFoodAndBeverageRates {
	id: string | null
	isTaxable: boolean
	rate: number | null | string
	rateUnit: string | null
	type: string | null
	unitMax: number | null
	unitMin: number | null
	name?: string | null
}

export interface IDeclinedReason {
	created: string | null
	createdBy: string | null
	disabled: boolean | null
	text: string | null
}

export interface IMeetingRoom {
	id: string | null
	propertyId: string | null
	itemType: string | null
	name: string | null
	group: string | null
	length: number | null
	width: number | null
	height: number | null
	area: number | null
	hasStructuralColumns: boolean
	layouts: MeetingRoomLayout[] | null
	created: string | null
	createdBy: string | null
	deleted: string | null
	deletedBy: string | null
	isDeleted: boolean
	externalSourceIdentifiers: Record<string, string>
	capacity: number | null
	layoutStyle: keyof typeof roomLayoutOptions
}

interface MeetingRoomLayout {
	layoutStyle: keyof typeof roomLayoutOptions
	capacity: number
}
