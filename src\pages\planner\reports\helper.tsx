/* eslint-disable @typescript-eslint/max-params */
/* eslint-disable unicorn/no-keyword-prefix */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable unicorn/no-nested-ternary */
import type { ICurrency } from 'lib/helpers'
import { calculatePeriod, periodTypes } from 'lib/helpers'
import {
	EventPlanStatusMap,
	ProposalRequestStatusMap
} from 'lib/helpers/statusMaps'
import type { EventPlan } from 'models/proposalResponseMonitor'
import { parseISO } from 'date-fns'
import {
	calculateOpportunityValue,
	calculateProposalValue,
	proposalValuesComponents
} from 'lib/helpers/proposalValues'
import { requestTypeFilters } from './common'
import CostSavings from './tabs/costSavings'
import Bookings from './tabs/bookings'
import BidHistory from './tabs/bidHistory'
import OpenPipeline from './tabs/openPipeline'

export interface ISetFilters {
	chainId: string | null
	brandId: string | null
	requestType: string | null
	includeCancelled: boolean
	dateFilter: {
		type: {
			key: string
			label: string
		}
		startDate: string
		endDate: string
	}
	currency: ICurrency
}

export const reportViews = (
	eventPlans: EventPlan[],
	currency: ICurrency,
	setFilters: React.Dispatch<React.SetStateAction<ISetFilters>>,
	filteredChainIds: string[],
	filters: ISetFilters,
	analyticsFeature: boolean,
	loading: boolean,
	hotelsListFilterFunction: (hotel: { status: string }) => boolean
) => [
	{
		key: 0,
		path: 'pipeline',
		title: 'Open Pipeline',
		subtitle:
			'Gain insights into your RFP funnel with a comprehensive overview of your active RFPs and their statuses. Make informed decisions with real-time data on opportunity values, response rates, and overdue actions',
		statuses: [
			EventPlanStatusMap.Bidding?.key,
			EventPlanStatusMap.Submitted?.key
		],
		hotelStatuses: [
			ProposalRequestStatusMap.Active?.key,
			ProposalRequestStatusMap.ClosedLost?.key,
			ProposalRequestStatusMap.Reviewed?.key
		],
		overrideDateFilter: calculatePeriod(periodTypes.allTime.key),
		dateFilterKey: 'firstSubmitted',
		gridFields: [
			'name',
			'responseRate',
			'firstSubmitted',
			'responsesDueDate',
			'selectionDate',
			'opportunityValue',
			'status',
			'chains',
			'brands'
		],
		children: (
			<OpenPipeline
				eventPlans={eventPlans}
				currency={currency}
				filters={filters}
				setFilters={setFilters}
				filteredChainIds={filteredChainIds}
				isFeatureLocked={analyticsFeature}
				isLoading={loading}
				hotelsListFilterFunction={hotelsListFilterFunction}
			/>
		)
	},
	{
		key: 1,
		path: 'bids',
		title: 'Bid History',
		subtitle:
			'Analyze past bid patterns and performance with the Bid History dashboard, providing a detailed historical record of proposals to empower your future negotiation strategies',
		statuses: [
			EventPlanStatusMap.Bidding?.key,
			EventPlanStatusMap.Submitted?.key,
			EventPlanStatusMap.Contracting?.key,
			EventPlanStatusMap.Contracted?.key,
			EventPlanStatusMap.CheckedOut?.key,
			EventPlanStatusMap.CommissionPaid?.key
		],
		hotelStatuses: [
			ProposalRequestStatusMap.Active?.key,
			ProposalRequestStatusMap.ClosedLost?.key,
			ProposalRequestStatusMap.ClosedWon?.key,
			ProposalRequestStatusMap.ContractSigned?.key,
			ProposalRequestStatusMap.Reviewed?.key
		],
		dateFilterKey: 'firstSubmitted',
		dateFilterHint: 'Filter RFPs by the date they were first sent to hotels',
		gridFields: [
			'name',
			'responseRate',
			'firstSubmitted',
			'responsesDueDate',
			'selectionDate',
			'opportunityValue',
			'status',
			'chains',
			'brands'
		],
		children: (
			<BidHistory
				eventPlans={eventPlans}
				currency={currency}
				setFilters={setFilters}
				filteredChainIds={filteredChainIds}
				filters={filters}
				isFeatureLocked={analyticsFeature}
				hotelsListFilterFunction={hotelsListFilterFunction}
			/>
		)
	},
	{
		key: 2,
		path: 'bookings',
		title: 'Bookings',
		subtitle: `Your bookings dashboard provides insights and trends on your organization's bookings performance across all your RFPs that went to contract.`,
		statuses: [
			EventPlanStatusMap.Contracted?.key,
			EventPlanStatusMap.CheckedOut?.key,
			EventPlanStatusMap.CommissionPaid?.key
		],
		hotelStatuses: [ProposalRequestStatusMap.ContractSigned?.key],
		dateFilterKey: 'firstContractSigned',
		dateFilterHint: 'Filter RFPs by the date the contract was signed',
		gridFields: [
			'name',
			'responseRate',
			'firstSubmitted',
			'responsesDueDate',
			'selectionDate',
			'proposalValue',
			'contractValue',
			'savings',
			'contracted',
			'proposalSubmitted',
			'status',
			'chains',
			'brands'
		],
		children: (
			<Bookings
				eventPlans={eventPlans}
				currency={currency}
				setFilters={setFilters}
				filteredChainIds={filteredChainIds}
				filters={filters}
				isFeatureLocked={analyticsFeature}
			/>
		)
	},
	{
		key: 3,
		path: 'savings',
		title: 'Cost Savings',
		subtitle:
			'Discover insights and trends on cost savings your organization has achieved to reduce expenses and maximize negotiation outcomes strategically.',
		statuses: [
			EventPlanStatusMap.Contracted?.key,
			EventPlanStatusMap.CheckedOut?.key,
			EventPlanStatusMap.CommissionPaid?.key
		],
		hotelStatuses: [ProposalRequestStatusMap.ContractSigned?.key],
		dateFilterKey: 'firstContractSigned',
		dateFilterHint: 'Filter RFPs by the date the contract was signed',
		gridFields: [
			'name',
			'responseRate',
			'firstSubmitted',
			'responsesDueDate',
			'selectionDate',
			'proposalValue',
			'contractValue',
			'savings',
			'contracted',
			'proposalSubmitted',
			'status',
			'chains',
			'brands'
		],
		children: (
			<CostSavings
				eventPlans={eventPlans}
				currency={currency}
				setFilters={setFilters}
				filteredChainIds={filteredChainIds}
				filters={filters}
				isFeatureLocked={analyticsFeature}
			/>
		)
	}
]

export const mapEventPlan = (eventPlan: EventPlan) => {
	const proposalRequests = Object.keys(
		eventPlan.proposalRequestStatuses
	).reduce(
		(
			accumulator: {
				requestsResponded: number
				requestsProposed: number
				requestsAwaiting: number
				requestsTotal: number
				chainIds: string[]
				brandIds: string[]
				currencyCodes: string[]
				venueLocations: string[]
				hotels: {
					venueId: string
					status: string
					venueName: string
					venueLocation: string
					chainId: string
					brandId: string
				}[]
				fastestResponseTimeTicks: number | null
				totalResponseTimeTicks: number
			},
			c
		) => {
			const newAccumulator = { ...accumulator }
			newAccumulator.requestsTotal +=
				c === ProposalRequestStatusMap.Pending?.key
					? 0
					: (eventPlan.proposalRequestStatuses[c]?.length ?? 0)
			newAccumulator.requestsResponded += [
				ProposalRequestStatusMap.Pending?.key,
				ProposalRequestStatusMap.New?.key,
				ProposalRequestStatusMap.Received?.key
			].includes(c)
				? 0
				: (eventPlan.proposalRequestStatuses[c]?.length ?? 0)
			newAccumulator.requestsProposed += [
				ProposalRequestStatusMap.Active?.key,
				ProposalRequestStatusMap.ClosedLost?.key,
				ProposalRequestStatusMap.ClosedWon?.key,
				ProposalRequestStatusMap.ContractSigned?.key,
				ProposalRequestStatusMap.Reviewed?.key
			].includes(c)
				? (eventPlan.proposalRequestStatuses[c]?.length ?? 0)
				: 0
			newAccumulator.requestsAwaiting =
				newAccumulator.requestsTotal - newAccumulator.requestsResponded
			for (const r of eventPlan.proposalRequestStatuses[c]) {
				if (!newAccumulator.chainIds.includes(r.chainId ?? ''))
					newAccumulator.chainIds.push(r.chainId ?? '')
				if (!newAccumulator.brandIds.includes(r.brandId))
					newAccumulator.brandIds.push(r.brandId)
				if (!newAccumulator.currencyCodes.includes(r.currencyCode))
					newAccumulator.currencyCodes.push(r.currencyCode)
				if (!newAccumulator.venueLocations.includes(r.venueLocation))
					newAccumulator.venueLocations.push(r.venueLocation)
				if (
					!newAccumulator.fastestResponseTimeTicks ||
					(!!r.responseTimeTicks &&
						r.responseTimeTicks < newAccumulator.fastestResponseTimeTicks)
				)
					newAccumulator.fastestResponseTimeTicks = r.responseTimeTicks
				newAccumulator.totalResponseTimeTicks += r.responseTimeTicks ?? 0
				if (!newAccumulator.hotels.some(h => h.venueId === r.venueId)) {
					newAccumulator.hotels.push({
						venueId: r.venueId,
						status: r.status,
						venueName: r.venueName,
						venueLocation: r.venueLocation,
						chainId: r.chainId ?? '',
						brandId: r.brandId
					})
				}
			}
			return newAccumulator
		},
		{
			requestsResponded: 0,
			requestsProposed: 0,
			requestsAwaiting: 0,
			requestsTotal: 0,
			chainIds: [],
			brandIds: [],
			currencyCodes: [],
			venueLocations: [],
			hotels: [],
			fastestResponseTimeTicks: null,
			totalResponseTimeTicks: 0
		}
	)
	const owner = eventPlan.planners?.find(p => p.owner)
	const proposalValues = [
		EventPlanStatusMap.Contracting?.key,
		EventPlanStatusMap.Contracted?.key
	].includes(eventPlan.status ?? '')
		? eventPlan.proposalValues
		: eventPlan.proposalValuesAverage
	return {
		...eventPlan,
		rfpValue: proposalValues ? calculateOpportunityValue(proposalValues) : 0,
		roomsValue: proposalValues
			? calculateProposalValue(proposalValues, [
					proposalValuesComponents.roomCost.key
				])
			: 0,
		roomNights: proposalValues?.totalRoomsOffered,
		averageRoomRate: proposalValues?.averageRoomRate,
		foodAndBeverageValue: proposalValues
			? calculateProposalValue(proposalValues, [
					proposalValuesComponents.foodAndBeverage.key
				])
			: 0,
		ownerId: owner ? owner.id : null,
		ownerName: owner ? `${owner.firstName} ${owner.lastName}` : null,
		...proposalRequests,
		bidRate:
			proposalRequests.requestsTotal > 0
				? (proposalRequests.requestsProposed / proposalRequests.requestsTotal) *
					100
				: 0,
		responseRate:
			proposalRequests.requestsTotal > 0
				? (proposalRequests.requestsResponded /
						proposalRequests.requestsTotal) *
					100
				: 0,
		requestType:
			eventPlan.meetingSpaceRequired && eventPlan.roomBlocksRequired
				? requestTypeFilters.roomsAndSpace?.key
				: eventPlan.meetingSpaceRequired
					? requestTypeFilters.spaceOnly?.key
					: requestTypeFilters.roomsOnly?.key,
		overdueForSelection:
			eventPlan.status !== EventPlanStatusMap.Contracting?.key &&
			!!eventPlan.selectionDate &&
			parseISO(eventPlan.selectionDate) < new Date(),
		overdueForResponses:
			proposalRequests.requestsResponded === 0 &&
			!!eventPlan.responsesDueDate &&
			new Date(eventPlan.responsesDueDate) < new Date(),
		averageResponseTimeTicks:
			proposalRequests.requestsResponded > 0
				? proposalRequests.totalResponseTimeTicks /
					proposalRequests.requestsResponded
				: null,
		currencyCodes: [
			eventPlan.currencyCode,
			...new Set(
				proposalRequests.currencyCodes.reduce((a: string[], c) => {
					if (!a.includes(c)) {
						a.push(c)
					}
					return a
				}, [])
			)
		],
		venueLocations: proposalRequests.venueLocations.reduce((a: string[], c) => {
			if (!a.includes(c)) a.push(c.toUpperCase())
			return a
		}, []),
		hotels: proposalRequests.hotels
	}
}
