/* eslint-disable unicorn/no-nested-ternary */
/* eslint-disable @typescript-eslint/naming-convention */
import HSButton from 'components/button'
import { formatISO } from 'date-fns'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import {
	itemTypes,
	contentTypes,
	engagementTypes
} from 'lib/helpers/contentEngagement'
import { EventPlanContractedStatuses } from 'lib/helpers/statusMaps'
import {
	supplierContactRoles,
	supplierContactSources
} from 'lib/helpers/supplierContacts'
import { trackEngagement } from 'lib/services/contentEngagement.service'
import type { ISupplierContact } from 'models/affiliateOrganizations'
import type { INewDestination } from 'models/destinations'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'

export interface IAddRemoveDestinationButton {
	event:
		| {
				supplierContacts: ISupplierContact[] | undefined
				status: string | null | undefined
				sent: string | null | undefined
				itemType: string | null
		  }
		| undefined
	destination: INewDestination
	addRemoveDestination: (value: INewDestination) => void
}

const AddRemoveDestinationButton = (
	properties: IAddRemoveDestinationButton
) => {
	const { addRemoveDestination, destination, event } = properties
	const { userProfile } = useUserProfileContext()
	const [isAdded, setIsAdded] = useState(false)
	const [isDisabled, setIsDisabled] = useState(false)

	useEffect(() => {
		if (event?.supplierContacts) {
			setIsAdded(
				event.supplierContacts.some(
					sc => sc.organizationId === destination.id && !sc.systemAdded
				)
			)
		} else {
			setIsAdded(false)
		}
	}, [event, destination.id, event?.supplierContacts])

	useEffect(() => {
		if (event && EventPlanContractedStatuses.includes(event.status ?? '')) {
			setIsDisabled(true)
		} else if (event?.supplierContacts) {
			if (typeof addRemoveDestination === 'function') {
				const supplierContact = event.supplierContacts.find(
					sc => sc.organizationId === destination.id && !sc.systemAdded
				)
				if (supplierContact === undefined) {
					setIsDisabled(false)
				} else {
					setIsDisabled(supplierContact.sent !== null)
				}
			} else {
				setIsDisabled(true)
			}
		} else {
			setIsDisabled(true)
		}
	}, [
		event,
		event?.status,
		event?.supplierContacts,
		destination.id,
		addRemoveDestination
	])

	const onButtonClick = () => {
		// e.stopPropagation()
		if (typeof addRemoveDestination === 'function') {
			const destinationManager = destination.destinationManagers.find(
				dm => dm.sendAllRFPs
			)
			if (destinationManager) {
				const destinationValue: Partial<INewDestination>[] = [
					{
						id: destinationManager.id,
						itemType: 'supplierContact',
						organizationId: destination.id ?? '',
						firstName: destinationManager.firstName,
						lastName: destinationManager.lastName,
						companyName: destination.name,
						role: supplierContactRoles.dmo?.key,
						venueMatchCriteria: destination.venueMatchCriteria,
						requestSuggestions: true,
						allowMessaging: true,
						invitationMessage: null,
						created: formatISO(new Date()),
						createdBy: userProfile?.id,
						sent: null,
						added: formatISO(new Date()),
						location: {
							name: `${destination.city}, ${destination.state}`,
							latitude: destination.latitude,
							longitude: destination.longitude
						},
						source: destination.source ?? supplierContactSources.search.key
					}
				]
				addRemoveDestination(destinationValue)
				if (
					!isAdded &&
					event?.itemType === 'eventPlan' &&
					!userProfile?.isAdmin &&
					destination.id
				) {
					trackEngagement(destination.id, {
						itemType: itemTypes.destination,
						contentItemType: contentTypes.destination,
						contentItemId: destination.id,
						engagementType: engagementTypes.conversion
					}).catch((error: unknown) => {
						console.error(error)
					})
				}
			} else {
				toast.info(`No contacts are setup for ${destination.name}`)
			}
		}
	}

	return (
		<HSButton
			style={{ width: '14rem' }}
			color={isAdded ? 'failure' : 'primary'}
			onClick={onButtonClick}
			disabled={isDisabled}
		>
			<span className='button__text'>
				{isAdded
					? isDisabled
						? 'Request Sent'
						: 'Remove'
					: `Add DMO to my ${event?.itemType === 'eventPlan.siteSearch' ? 'List' : 'RFP'}`}
			</span>
		</HSButton>
	)
}

export default AddRemoveDestinationButton
