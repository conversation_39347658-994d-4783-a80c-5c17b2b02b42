/* eslint-disable @typescript-eslint/consistent-return */
/* eslint-disable consistent-return */
import { useEffect } from 'react'

interface TrafficLayerProperties {
	map: google.maps.Map | null
}

const TrafficLayer = (properties: TrafficLayerProperties) => {
	const { map } = properties
	useEffect(() => {
		if (!map) return

		const trafficLayer = new google.maps.TrafficLayer({
			map,
			autoRefresh: false
		})
		return () => {
			trafficLayer.setMap(null)
		}
	}, [map])

	return <div />
}

export default TrafficLayer
