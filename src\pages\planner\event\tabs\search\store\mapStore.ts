import { create } from 'zustand'

interface MapStoreState {
	showTraffic: boolean
	showTransit: boolean
	addedOnly: boolean
}

interface MapStoreAction {
	setShowTraffic: (showTraffic: boolean) => void
	setShowTransit: (showTraffic: boolean) => void
	setAddedOnly: (addedOnly: boolean) => void
}

const useMapStore = create<MapStoreState & MapStoreAction>()(set => ({
	showTraffic: false,
	showTransit: false,
	addedOnly: false,
	setShowTraffic: (showTraffic: boolean) => set({ showTraffic }),
	setShowTransit: (showTransit: boolean) => set({ showTransit }),
	setAddedOnly: (addedOnly: boolean) => set({ addedOnly })
}))

export default useMapStore
