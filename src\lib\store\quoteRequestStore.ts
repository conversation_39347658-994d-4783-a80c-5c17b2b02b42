/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable @typescript-eslint/prefer-destructuring */
/* eslint-disable prefer-destructuring */
/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable unicorn/no-array-reduce */
/* eslint-disable @typescript-eslint/max-params */
import { parseISO } from 'date-fns'
import { debounce } from 'es-toolkit'
import { isEmail } from 'lib/helpers'
import { sleepingRoomTypeOptions } from 'lib/helpers/sleepingRoomType'
import { summaryProposalRequestsResponsesIntent } from 'lib/services/hotel.hub.service'
import { proposalRequestsResponsesIntent } from 'lib/services/proposals.service'
import isValidPhoneNumber from 'lib/utils/phoneNumber'
import type { QuoteRequest } from 'models/hotelier'
import type {
	ICurrentBid,
	IProposalDates,
	IRoomRate,
	ProposalRequest
} from 'models/proposalResponseMonitor'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface ErrorState {
	totalErrors: number
	submit: string[]
	mespace: string[]
	fandb: string[]
	roomblocks: string[]
	comments: string[]
	generalErrors?: string[]
}

interface State {
	saving: boolean
	quoteRequest: QuoteRequest
	errors: ErrorState
	suggestedProposalDate: {
		startDate?: string
		endDate?: string
		key?: string
		suggested?: boolean
	} | null
	isSummary: boolean
}

type PropertyName = keyof ProposalRequest

interface Actions {
	setObject: (value: QuoteRequest, clientOnly?: boolean) => void
	setProperty: <K extends PropertyName>(
		name: K,
		value: ProposalRequest[K],
		clientOnly?: boolean
	) => Promise<void>
	mergeProperties: (value: Partial<QuoteRequest>, clientOnly?: boolean) => void
	setSuggestedProposalDate: (date: {
		startDate?: string
		endDate?: string
		key?: string
		suggested?: boolean
	}) => void
	setSaving: (saving: boolean) => void
}

// Extracted validation logic into separate module for better separation of concerns
export const QuoteRequestValidator = {
	validate(quoteRequest: QuoteRequest): ErrorState {
		const errors = {
			roomblocksErrors: new Set<string>(),
			submitErrors: new Set<string>(),
			mespaceErrors: new Set<string>(),
			fandbErrors: new Set<string>(),
			commentsErrors: new Set<string>(),
			generalErrors: new Set<string>()
		}
		const proposalRequest =
			quoteRequest.summaryProposalRequest === null
				? quoteRequest.proposalRequest
				: quoteRequest.summaryProposalRequest

		if (proposalRequest) {
			this.validateRoomBlocks(quoteRequest, errors, proposalRequest)
			this.validateMeetingSpace(quoteRequest, errors, proposalRequest)
			this.validateFoodAndBeverage(quoteRequest, errors, proposalRequest)
			this.validatePromotion(quoteRequest, errors, proposalRequest)
			this.validateProposalContact(quoteRequest, errors, proposalRequest)
			this.validateProposalExpirationDate(quoteRequest, errors, proposalRequest)
		}

		return {
			totalErrors: Object.values(errors).reduce(
				(sum, errorSet) => sum + errorSet.size,
				0
			),
			roomblocks: [...errors.roomblocksErrors],
			submit: [...errors.submitErrors],
			mespace: [...errors.mespaceErrors],
			fandb: [...errors.fandbErrors],
			comments: [...errors.commentsErrors],
			generalErrors: [...errors.generalErrors]
		}
	},

	validateRoomBlocks(
		quoteRequest: QuoteRequest,
		errors: Record<string, Set<string>>,
		proposalRequest: ProposalRequest
	) {
		const { roomblocksErrors, submitErrors } = errors
		const roomBlocksRequired = quoteRequest.eventPlan?.roomBlocksRequired

		if (roomBlocksRequired) {
			// Room rates validation
			this.validateRoomRates(quoteRequest, roomblocksErrors, proposalRequest)

			// Additional room block validations
			this.validateTaxesCommissionAttrition(submitErrors, proposalRequest)
		}
	},

	validateRoomRates(
		quoteRequest: QuoteRequest,
		roomblocksErrors: Set<string>,
		proposalRequest: ProposalRequest
	) {
		const proposalDates =
			proposalRequest.currentBid?.proposalDates?.filter(
				pd => !pd.declineToBid
			) ?? []

		if (proposalRequest.currentBid?.roomRates?.length === 0) {
			roomblocksErrors.add(
				`Sleeping Room Rate${quoteRequest.eventPlan?.roomBlockRequests?.length === 1 ? ' is ' : 's are '}required`
			)
		}

		for (const pd of proposalDates) {
			const roomRates = proposalRequest.currentBid?.roomRates?.filter(rr => {
				if (pd.isOfferedDifferentThanRequested) {
					return rr.key?.endsWith(pd.key ?? '')
				}
				return (
					rr.key?.endsWith(pd.key ?? '') &&
					rr.rate !== null &&
					rr.rate > 0 &&
					rr.offered !== null &&
					rr.offered > 0
				)
			})

			const expectedRates =
				quoteRequest.eventPlan?.roomBlockRequests?.reduce(
					(a1, rbr) =>
						a1 +
						rbr.roomTypeRequests.reduce(
							(a2, rtr) =>
								a2 + rtr.roomNights.filter(rn => rn.roomsRequested > 0).length,
							0
						),
					0
				) ?? 0

			this.validateRoomRateDetails(
				pd,
				roomRates,
				expectedRates,
				roomblocksErrors
			)
		}
	},

	validateRoomRateDetails(
		pd: IProposalDates,
		roomRates: IRoomRate[] | undefined,
		expectedRates: number,
		roomblocksErrors: Set<string>
	) {
		if (pd.isOfferedDifferentThanRequested) {
			const hasOfferedRoomsWithoutRates = roomRates
				?.filter(rr => rr.offered || rr.rate)
				.some(rr => {
					if (pd.isVaryingRate) {
						return (rr.offered && !rr.rate) || (!rr.offered && rr.rate)
					}

					let result = false // default lets consider rooms are offered

					if ((rr.rate ?? 0) > 0) {
						result = roomRates // set up invalid scenario is not offered when rate>0
							?.filter(
								rrs =>
									rrs.roomType === rr.roomType &&
									rrs.roomBlockIndex === rr.roomBlockIndex
							)

							.every(rrs => Number(rrs.offered ?? 0) === 0)
					}
					if (rr.offered && !rr.rate) {
						result = true // set up invalid scenario is not offered when rate>0
					}
					return result
				})

			if (hasOfferedRoomsWithoutRates) {
				roomblocksErrors.add(
					'Rates/Rooms are required for all offered room nights'
				)
			}

			const roomTypeOfferings = roomRates?.reduce(
				(a, rr) => {
					const updatedA = [...a]
					const roomType = rr.roomType ?? 0
					if (updatedA[roomType] < 0) updatedA[roomType] = 0
					updatedA[roomType] += rr.offered || 0
					return updatedA
				},
				Array.from<number>({ length: sleepingRoomTypeOptions.length }).fill(-1)
			)
			if (!roomTypeOfferings?.some(rto => rto > 0)) {
				roomblocksErrors.add(
					'Rate cannot be provided without offering at least 1 room night'
				)
			}
		} else if ((roomRates ?? []).length < expectedRates) {
			roomblocksErrors.add(
				'Please provide a room rate response for each type of room requested'
			)
		}
	},

	validateTaxesCommissionAttrition(
		submitErrors: Set<string>,
		proposalRequest: ProposalRequest
	) {
		const currentBid = proposalRequest.currentBid

		if (
			currentBid?.taxesFeesAssessments === null ||
			currentBid?.taxesFeesAssessments === undefined ||
			currentBid?.taxesFeesAssessments === ''
		) {
			submitErrors.add('Taxes, Fees field is required (may be zero)')
		}

		if (
			proposalRequest.commissionable &&
			(currentBid?.commissionPercent === null ||
				currentBid?.commissionPercent === undefined ||
				currentBid.commissionPercent === '')
		) {
			submitErrors.add('Commission Rate is required (may be zero)')
		}

		if (
			currentBid?.attritionRate === null ||
			currentBid?.attritionRate === undefined ||
			currentBid.attritionRate === ''
		) {
			submitErrors.add('Attrition Rate is required (may be zero)')
		}
	},

	validateMeetingSpace(
		quoteRequest: QuoteRequest,
		errors: Record<string, Set<string>>,
		proposalRequest: ProposalRequest
	) {
		const { submitErrors, mespaceErrors } = errors
		const meetingSpaceRequired = quoteRequest.eventPlan?.meetingSpaceRequired

		if (meetingSpaceRequired) {
			this.validateMeetingRates(quoteRequest, submitErrors, proposalRequest)
			this.validateMeetingSpaceAssignments(
				quoteRequest,
				mespaceErrors,
				proposalRequest
			)
		}
	},

	validateMeetingRates(
		quoteRequest: QuoteRequest,
		submitErrors: Set<string>,
		proposalRequest: ProposalRequest
	) {
		const currentBid = proposalRequest.currentBid

		if (currentBid?.isMeetingRateDependentOnDate) {
			this.validateDateDependentMeetingRates(
				currentBid,
				submitErrors,
				quoteRequest
			)
		} else {
			this.validateNonDateDependentMeetingRates(
				currentBid,
				submitErrors,
				quoteRequest
			)
		}
	},

	validateDateDependentMeetingRates(
		currentBid: ICurrentBid,
		submitErrors: Set<string>,
		quoteRequest: QuoteRequest
	) {
		const dateKeys = new Set(
			(currentBid.proposalDates ?? [])
				.filter(pd => !pd.declineToBid)
				.map(pd => pd.key)
		)
		const meetingRates = currentBid.meetingRates?.filter(mr =>
			dateKeys.has(mr.key)
		)

		if (
			quoteRequest.summaryProposalRequest === null &&
			meetingRates?.some(
				mr =>
					Number.isNaN(mr.fbMinimum) ||
					mr.fbMinimum === null ||
					mr.fbMinimum === undefined ||
					mr.fbMinimum === ''
			)
		) {
			submitErrors.add('F&B Minimum is required for each date range')
		}
		if (
			quoteRequest.summaryProposalRequest === null &&
			meetingRates?.some(
				mr =>
					Number.isNaN(mr.roomRental) ||
					mr.roomRental === null ||
					mr.roomRental === undefined ||
					mr.roomRental === ''
			)
		) {
			submitErrors.add('Room Rental is required for each date range')
		}
		if (
			quoteRequest.summaryProposalRequest === null &&
			meetingRates?.some(
				mr =>
					Number.isNaN(mr.serviceChargeRate) ||
					mr.serviceChargeRate === null ||
					mr.serviceChargeRate === ''
			)
		) {
			submitErrors.add('Service Charge Rate is required for each date range')
		}
	},

	validateNonDateDependentMeetingRates(
		currentBid: ICurrentBid | null | undefined,
		submitErrors: Set<string>,
		quoteRequest: QuoteRequest
	) {
		if (
			Number.isNaN(currentBid?.fbMinimum) ||
			currentBid?.fbMinimum === null ||
			currentBid?.fbMinimum === '' ||
			currentBid?.fbMinimum === undefined
		) {
			submitErrors.add('F&B Minimum is required')
		}
		if (
			quoteRequest.summaryProposalRequest === null &&
			(Number.isNaN(currentBid?.roomRental) ||
				currentBid?.roomRental === null ||
				currentBid?.roomRental === undefined)
		) {
			submitErrors.add('Room Rental is required')
		}

		if (
			Number.isNaN(currentBid?.serviceChargeRate) ||
			currentBid?.serviceChargeRate === null ||
			currentBid?.serviceChargeRate === ''
		) {
			submitErrors.add('Service Charge Rate is required')
		}
	},

	validateMeetingSpaceAssignments(
		quoteRequest: QuoteRequest,
		mespaceErrors: Set<string>,
		proposalRequest: ProposalRequest
	) {
		const msrIds = quoteRequest.eventPlan?.meetingSpaceRequests?.map(
			msr => msr.id
		)
		const msaIds = new Set(
			proposalRequest.currentBid?.meetingSpaceAssignments
				?.filter(msa => msa.tbd || msa.meetingRoomId)
				.map(msa => msa.meetingSpaceRequestId)
		)

		if (
			msrIds?.some(ri => !msaIds.has(ri)) &&
			quoteRequest.summaryProposalRequest === null
		) {
			const errorMessage = quoteRequest.eventPlan?.requireNamedMeetingSpace
				? 'All event space requests must be assigned a room.'
				: 'All event space requests must be assigned a room or marked TBD.'

			mespaceErrors.add(errorMessage)
		}
		if (
			proposalRequest.currentBid?.meetingSpaceAvailable === null &&
			quoteRequest.summaryProposalRequest !== null
		) {
			mespaceErrors.add('Please specify whether space is available.')
		}
	},

	validateFoodAndBeverage(
		quoteRequest: QuoteRequest,
		errors: Record<string, Set<string>>,
		proposalRequest: ProposalRequest
	) {
		const { fandbErrors } = errors
		const foodAndBeverageRequests =
			quoteRequest.eventPlan?.foodAndBeverageRequests
		const currentBid = proposalRequest.currentBid

		if (
			(foodAndBeverageRequests?.length ?? 0) > 0 &&
			currentBid?.offerDetailedFoodAndBeverage === null &&
			quoteRequest.summaryProposalRequest === null
		) {
			fandbErrors.add(
				'Please specify whether you will offer detailed F&B rates.'
			)
		}

		if (
			(foodAndBeverageRequests?.length ?? 0) > 0 &&
			currentBid?.offerDetailedFoodAndBeverage &&
			currentBid.foodAndBeverageItems?.some(
				fbi =>
					(!fbi.quantity && fbi.quantity !== 0) ||
					Number.isNaN(fbi.quantity) ||
					fbi.rate === undefined ||
					fbi.rate === null ||
					fbi.rate === '' ||
					(fbi.quantity > 0 && (fbi.rate === '' || null || undefined)) ||
					(Number(fbi.rate) > 0 && !(fbi.quantity > 0)) ||
					Number.isNaN(fbi.rate)
			)
		) {
			fandbErrors.add('Each F&B quote requires a quantity and rate.')
		}
	},

	validatePromotion(
		quoteRequest: QuoteRequest,
		errors: Record<string, Set<string>>,
		proposalRequest: ProposalRequest
	) {
		const { generalErrors } = errors
		const currentBid = proposalRequest.currentBid

		if (
			proposalRequest.promotionId &&
			(currentBid?.isPromotionApplied === null ||
				currentBid?.isPromotionApplied === undefined)
		) {
			generalErrors.add(
				'Please mark the requested promotion as applied or not applied to this proposal.'
			)
		}
	},

	validateProposalContact(
		quoteRequest: QuoteRequest,
		errors: Record<string, Set<string>>,
		proposalRequest: ProposalRequest
	) {
		const { generalErrors } = errors
		const proposalContact = proposalRequest.proposalContact

		if (
			!proposalContact?.name ||
			!isEmail(proposalContact.email) ||
			!proposalContact.phone ||
			!isValidPhoneNumber(proposalContact.phone)
		) {
			generalErrors.add(
				'Please provide a Proposal Contact name, email address, and phone number'
			)
		}
	},

	validateProposalExpirationDate(
		quoteRequest: QuoteRequest,
		errors: Record<string, Set<string>>,
		proposalRequest: ProposalRequest
	) {
		const { generalErrors } = errors
		const status = proposalRequest.status
		const currentBid = proposalRequest.currentBid

		if (
			quoteRequest.summaryProposalRequest === null &&
			['New', 'Received'].includes(status ?? '')
		) {
			if (currentBid?.bidExpirationDate) {
				if (parseISO(currentBid.bidExpirationDate) < new Date()) {
					generalErrors.add(
						'Please provide a Proposal Expiration date in the future'
					)
				}
			} else {
				generalErrors.add('Please provide a Proposal Expiration date')
			}
		}
	}
}

// Extracted intent service call to improve separation of concerns
const debouncedPutIntent = debounce(
	async (
		eventPlanId: string | undefined,
		proposalRequestId: string | undefined,
		type: string,
		clientOnly: boolean,
		value: unknown,
		setSaving: (saving: boolean) => void,
		isSummary
	) => {
		if (eventPlanId && proposalRequestId) {
			try {
				if (isSummary) {
					console.log('Summary Proposal Request')

					await summaryProposalRequestsResponsesIntent(
						eventPlanId,
						proposalRequestId,
						{
							type,
							clientOnly,
							value
						}
					)
				} else {
					await proposalRequestsResponsesIntent(
						eventPlanId,
						proposalRequestId,
						{
							type,
							clientOnly,
							value
						}
					)
				}
			} catch (error) {
				console.error(error)
			} finally {
				setSaving(false)
			}
		} else {
			console.error('eventPlanId or id is undefined')
			setSaving(false)
		}
	},
	450
)

const useQuoteRequestStore = create<Actions & State>()(
	devtools((set, get) => ({
		quoteRequest: {} as QuoteRequest,
		saving: false,
		errors: {
			totalErrors: 0,
			submit: [],
			mespace: [],
			fandb: [],
			roomblocks: [],
			comments: []
		},
		isSummary: false,
		suggestedProposalDate: null,

		setSaving: (saving: boolean) => set({ saving }),

		setObject: async (value, clientOnly) => {
			const errors = QuoteRequestValidator.validate(value)
			const isSummary = value.summaryProposalRequest !== null
			set({
				quoteRequest: value,
				errors,
				isSummary
			})

			if (!clientOnly) {
				set({ saving: true })
				const proposalRequestId =
					get().quoteRequest.summaryProposalRequest === null
						? get().quoteRequest.proposalRequest?.id
						: get().quoteRequest.summaryProposalRequest?.id
				debouncedPutIntent(
					value.eventPlan?.id ?? '',
					proposalRequestId ?? '',
					'setObject',
					Boolean(clientOnly),
					value,
					get().setSaving,
					isSummary
				)
			}
		},

		setProperty: async (name, value, clientOnly) => {
			const isSummary = get().quoteRequest.summaryProposalRequest !== null
			const updatedQuoteRequest = {
				...get().quoteRequest,
				...(isSummary
					? {
							summaryProposalRequest: {
								...((get().quoteRequest.summaryProposalRequest ??
									{}) as ProposalRequest),
								[name]: value
							}
						}
					: {
							proposalRequest: {
								...((get().quoteRequest.proposalRequest ??
									{}) as ProposalRequest),
								[name]: value
							}
						})
			}

			const errors = QuoteRequestValidator.validate(updatedQuoteRequest)

			set({
				quoteRequest: updatedQuoteRequest,
				errors,
				isSummary
			})

			if (!clientOnly) {
				set({ saving: true })
				const proposalRequestId =
					get().quoteRequest.summaryProposalRequest === null
						? get().quoteRequest.proposalRequest?.id
						: get().quoteRequest.summaryProposalRequest?.id
				debouncedPutIntent(
					get().quoteRequest.eventPlan?.id ?? '',
					proposalRequestId ?? '',
					'setProperty',
					Boolean(clientOnly),
					{ name, value },
					get().setSaving,
					isSummary
				)
			}
		},

		mergeProperties: async (value, clientOnly) => {
			const updatedQuoteRequest = {
				...get().quoteRequest,
				...value
			}

			const errors = QuoteRequestValidator.validate(updatedQuoteRequest)
			const isSummary = updatedQuoteRequest.summaryProposalRequest !== null

			set({
				quoteRequest: updatedQuoteRequest,
				errors,
				isSummary
			})

			if (!clientOnly) {
				set({ saving: true })
				const proposalRequestId =
					get().quoteRequest.summaryProposalRequest === null
						? get().quoteRequest.proposalRequest?.id
						: get().quoteRequest.summaryProposalRequest?.id
				debouncedPutIntent(
					get().quoteRequest.eventPlan?.id ?? '',
					proposalRequestId ?? '',
					'mergeProperties',
					Boolean(clientOnly),
					value,
					get().setSaving,
					isSummary
				)
			}
		},

		setSuggestedProposalDate: date => {
			set({ suggestedProposalDate: date })
		}
	}))
)

export default useQuoteRequestStore
