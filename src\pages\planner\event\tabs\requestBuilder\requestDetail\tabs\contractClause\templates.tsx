/* eslint-disable react/no-danger */
import { faEdit, faTrashXmark } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSTooltip from 'components/tooltip'
import type { ContractClauseEvent } from 'models/organizations'

export const renderName = (item: ContractClauseEvent) => (
	<div className='flex items-center gap-2'>
		<div className='text-sm font-normal text-gray-600'>
			{item.contractClause.title}
		</div>
	</div>
)

export const renderDescription = (item: ContractClauseEvent) => (
	<HSTooltip
		content={
			<div
				dangerouslySetInnerHTML={{ __html: item.contractClause.text ?? '' }}
			/>
		}
		className='!max-w-4xl text-wrap border border-gray-200 bg-white text-gray-900 shadow-2xl'
		placement='top-start'
	>
		<div
			className='text-wrap'
			dangerouslySetInnerHTML={{ __html: item.contractClause.text ?? '' }}
		/>
	</HSTooltip>
)

export const renderActions = (
	item: ContractClauseEvent,
	handleRemove: (item: ContractClauseEvent) => void,
	handleEdit: (item: ContractClauseEvent) => void
) => (
	<div className='flex items-center gap-4'>
		<HSButton color='light' size='xs' onClick={() => handleEdit(item)}>
			<HSTooltip content='Edit' placement='left'>
				<FontAwesomeIcon icon={faEdit} size='lg' />
			</HSTooltip>
		</HSButton>
		<HSButton color='light' size='xs' onClick={() => handleRemove(item)}>
			<HSTooltip content='Delete' placement='right'>
				<FontAwesomeIcon
					icon={faTrashXmark}
					className='text-red-600'
					size='lg'
				/>
			</HSTooltip>
		</HSButton>
	</div>
)
