import { faPlus } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import ConcessionRequestModal from 'components/concessionRequestModal'
import { getDefaultConcessionRequest } from 'components/profile/tabs/concessionRequests/helper'
import { actionTemplate } from 'components/profile/tabs/concessionRequests/templates'
import HSTable from 'components/table'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import { requestGroups } from 'lib/helpers/requestGroups'
import {
	useAddConcessionsToEvent,
	useDeleteConcessionFromEvent,
	useGetConcessionsForEvent
} from 'lib/services/planner.service'
import concessionRequestStore from 'lib/store/plannerEvent/concessionRequestStore'
import type { IMappedPlanner, IPlanner } from 'models/planners'
import type { IConcessionEvent } from 'models/proposalResponseMonitor'
import { useState } from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import ConcessionResponseDrawer from './responseDrawer'

const EventConcessions = () => {
	const { eventId } = useParams()
	const { concessionRequests, setObject: setConcessions } =
		concessionRequestStore()
	const { userProfile } = useUserProfileContext()

	const { mutateAsync: addConcession } = useAddConcessionsToEvent()
	const { refetch: getConcessionsForEvent } = useGetConcessionsForEvent(
		eventId ?? '',
		!!eventId
	)
	const { mutateAsync: deleteConcession } = useDeleteConcessionFromEvent()
	const [showCreateConcessionDrawer, setShowCreateConcessionDrawer] =
		useState(false)

	const [selectedConcession, setSelectedConcession] = useState<
		Partial<IPlanner>
	>(getDefaultConcessionRequest())
	const [editExisting, setEditExisting] = useState(false)
	const [responseDrawerContext, setResponseDrawerContext] = useState<{
		show: boolean
		concession: IMappedPlanner
	}>({
		show: false,
		concession: {} as IMappedPlanner
	})

	const refetchConcessions = () => {
		getConcessionsForEvent()
			.then((response: { data: IConcessionEvent[] | undefined }) => {
				if (response.data) setConcessions(response.data)
			})
			.catch(() => {
				console.log('Error loading questions')
			})
	}

	const onActionEdit = (item: IPlanner) => {
		setEditExisting(true)
		setSelectedConcession(item)
		setShowCreateConcessionDrawer(true)
	}

	const closeCreateQuestionDrawer = () => {
		setShowCreateConcessionDrawer(false)
		setEditExisting(false)
		setSelectedConcession(getDefaultConcessionRequest())
	}

	const onActionDelete = (item: IPlanner) => {
		deleteConcession({
			eventId: eventId ?? '',
			itemId: item.id ?? ''
		})
			.then(() => {
				refetchConcessions()
			})
			.catch((error: unknown) => console.error(error))
	}

	const onAddUpdate = (stayOpen: boolean, concessionsRequest_?: IPlanner) => {
		addConcession({
			eventPlanId: eventId ?? '',
			concessionsRequest: concessionsRequest_
		})
			.then(() => {
				toast.success(
					`Concessions request ${editExisting ? 'updated' : 'added'} successfully.`
				)
				refetchConcessions()
			})
			.catch((error: unknown) => {
				console.error(error)
				toast.error('Error adding concessions request.')
			})
			.finally(() => {
				setEditExisting(false)
				setSelectedConcession(getDefaultConcessionRequest())
				setShowCreateConcessionDrawer(stayOpen)
			})
	}

	return (
		<>
			<div className='flex flex-col gap-4 p-4'>
				<div className='flex items-center justify-between gap-4'>
					<div className='flex flex-col'>
						<div className='text-lg font-semibold text-gray-900'>
							Concession Requests
						</div>
						<div className='text-sm font-normal text-gray-500'>
							Define Your Concession Requests
						</div>
					</div>
					<div className='flex items-center gap-4'>
						<HSButton onClick={() => setShowCreateConcessionDrawer(true)}>
							<div className='flex items-center gap-2'>
								<FontAwesomeIcon icon={faPlus} />
								Create New
							</div>
						</HSButton>
					</div>
				</div>
				<HSTable
					defaultSort={{ field: 'requestGroupId', direction: 'asc' }}
					columns={[
						{
							field: 'requestGroupId',
							headerText: 'Concession Category',
							render: item => (
								<div>
									{requestGroups[item.requestGroupId ?? '']?.name ??
										'No Grouping Specified'}
								</div>
							),
							sortable: true,
							width: 200
						},
						{
							field: 'text',
							headerText: 'Concession Request',
							sortable: true,
							render: item => (
								<div className='text-wrap text-sm font-normal text-gray-600'>
									{item.text}
								</div>
							)
						},
						{
							field: 'requestGroupId',
							headerText: 'Actions',
							headerAlign: 'center',
							render: item =>
								actionTemplate(
									item as IMappedPlanner,
									onActionEdit,
									onActionDelete,
									() =>
										setResponseDrawerContext({
											show: true,
											concession: item as IMappedPlanner
										})
								),
							width: 150
						}
					]}
					rows={concessionRequests.map(item => item.concessionRequest)}
				/>
			</div>
			{showCreateConcessionDrawer ? (
				<ConcessionRequestModal
					show={showCreateConcessionDrawer}
					onClose={closeCreateQuestionDrawer}
					userProfileId={userProfile?.id ?? ''}
					editConcessionRequest={{ ...(selectedConcession as IMappedPlanner) }}
					isEdit={editExisting}
					isProfile={false}
					onAddUpdate={onAddUpdate}
					concessionRequests={concessionRequests}
				/>
			) : null}
			{responseDrawerContext.show ? (
				<ConcessionResponseDrawer
					onClose={() => {
						setResponseDrawerContext({
							show: false,
							concession: {} as IMappedPlanner
						})
					}}
					concessionEvent={concessionRequests.find(
						cr =>
							cr.concessionRequest.id === responseDrawerContext.concession.id
					)}
				/>
			) : null}
		</>
	)
}

export default EventConcessions
