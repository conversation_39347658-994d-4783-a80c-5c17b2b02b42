/// HSBadge.test.tsx
import { render, screen } from '@testing-library/react'
import HSBadge from '.'
import { describe, it, expect } from 'vitest'

describe('HSBadge', () => {
	it('renders correctly with children as primary', () => {
		const { getByText } = render(<HSBadge>Primary Badge</HSBadge>)
		expect(getByText('Primary Badge')).toBeInTheDocument()
	})

	it('applies custom theme colors correctly', async () => {
		render(<HSBadge color='orange'>Orange Badge</HSBadge>)
		const badgeElement = await screen.findByTestId('flowbite-badge')
		expect(badgeElement).toHaveClass(
			'bg-orange-100 text-orange-800 group-hover:bg-orange-200 dark:bg-orange-200 dark:text-orange-900 dark:group-hover:bg-orange-300'
		)
	})
})
