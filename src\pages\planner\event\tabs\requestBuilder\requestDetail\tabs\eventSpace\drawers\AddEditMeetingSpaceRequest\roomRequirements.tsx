/* eslint-disable @typescript-eslint/no-unnecessary-condition */
/* eslint-disable no-constant-condition */
import { faInfoCircle } from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSCheckbox from 'components/checkbox'
import HSSelect from 'components/select'
import HSTextField from 'components/textField'
import HSTimePicker from 'components/timePicker'
import HSTooltip from 'components/tooltip'
import { roomLayoutOptions } from 'lib/helpers/roomLayouts'
import { formatTime } from '../../templates'
import meetingSpaceRequestStore from './store'
import { numberMask } from 'lib/helpers'

const RoomRequirements = () => {
	const { meetingSpaceRequest, setMeetingSpaceRequest } =
		meetingSpaceRequestStore()

	const startTimeValue = formatTime(
		meetingSpaceRequest?.startTime ?? 0,
		meetingSpaceRequest?.startMinutes ?? 0
	)
	const endTimeValue = formatTime(
		meetingSpaceRequest?.endTime ?? 0,
		meetingSpaceRequest?.endMinutes ?? 0
	)

	return meetingSpaceRequest ? (
		<div className='flex flex-col gap-6'>
			<div className='flex flex-col gap-2'>
				<div className='flex items-center gap-2'>
					<div className='text-sm font-medium text-gray-900'>Time Range</div>
					<HSTooltip content='Specify the start and end times for your meeting space, with an option to add 24-hour hold if needed'>
						<FontAwesomeIcon icon={faInfoCircle} className='text-gray-500' />
					</HSTooltip>
				</div>
				<div className='flex items-center gap-4'>
					<div className='flex flex-col gap-2'>
						<div className='text-sm font-normal text-gray-700'>Start Time</div>
						<HSTimePicker
							value={startTimeValue}
							onChange={event =>
								setMeetingSpaceRequest({
									...meetingSpaceRequest,
									startTime: Number(event.slice(0, 2)),
									startMinutes: Number(event.slice(3, 5))
								})
							}
						/>
					</div>
					<div className='flex flex-col gap-2'>
						<div className='text-sm font-normal text-gray-700'>End Time</div>
						<HSTimePicker
							value={endTimeValue}
							onChange={event =>
								setMeetingSpaceRequest({
									...meetingSpaceRequest,
									endTime: Number(event.slice(0, 2)),
									endMinutes: Number(event.slice(3, 5))
								})
							}
						/>
					</div>
				</div>
				<HSCheckbox
					label='Add 24hr Hold'
					className='text-sm font-normal text-gray-700'
					checked={meetingSpaceRequest.hold24Hours ?? false}
					onChange={event => {
						setMeetingSpaceRequest({
							...meetingSpaceRequest,
							hold24Hours: event.target.checked
						})
					}}
				/>
			</div>
			<div className='flex flex-col gap-2'>
				<div className='text-sm font-medium text-gray-900'>
					Room Requirements
				</div>
				<div className='flex gap-4'>
					<div className='flex flex-col gap-2'>
						<div className='flex items-center gap-2'>
							<div className='text-sm font-normal text-gray-700'>
								Room Function
							</div>
							<HSTooltip content='Enter the primary purpose or activity planned for this space'>
								<FontAwesomeIcon
									icon={faInfoCircle}
									className='text-gray-500'
								/>
							</HSTooltip>
						</div>
						<HSTextField
							placeholder='Enter function'
							className='w-72'
							value={meetingSpaceRequest.name ?? ''}
							onChange={event => {
								setMeetingSpaceRequest({
									...meetingSpaceRequest,
									name: event.target.value
								})
							}}
						/>
					</div>
					<div className='border-l border-gray-300' />
					<div className='flex flex-col gap-2'>
						<div className='flex items-center gap-2'>
							<div className='text-sm font-normal text-gray-700'>
								Preferred Layout
							</div>
							<HSTooltip content='Choose the optimal room setup that best suits your meeting needs'>
								<FontAwesomeIcon
									icon={faInfoCircle}
									className='text-gray-500'
								/>
							</HSTooltip>
						</div>
						<HSSelect
							required
							name='layoutStyle'
							value={meetingSpaceRequest.layoutStyle ?? ''}
							onChange={event => {
								setMeetingSpaceRequest({
									...meetingSpaceRequest,
									layoutStyle: event.target.value
								})
							}}
						>
							<option value=''>Please choose...</option>
							{Object.entries(roomLayoutOptions)
								.toSorted((c, n) =>
									(c[1]?.name ?? '') > (n[1]?.name ?? '') ? 1 : -1
								)
								.filter(Boolean)
								.map(option => (
									<option key={option[0]} value={option[0]}>
										{option[1]?.name ?? ''}
									</option>
								))}
						</HSSelect>
					</div>
				</div>
				<HSCheckbox
					label='Add A/V'
					className='text-sm font-normal text-gray-700'
					checked={meetingSpaceRequest.avRequired ?? false}
					onChange={event => {
						setMeetingSpaceRequest({
							...meetingSpaceRequest,
							avRequired: event.target.checked
						})
					}}
				/>
			</div>
			<div className='flex flex-col gap-2'>
				<div className='text-sm font-medium text-gray-900'>Capacity</div>
				<div className='flex gap-4'>
					<div className='flex flex-col gap-2'>
						<div className='flex items-center gap-2'>
							<div className='text-sm font-normal text-gray-700'>
								Room Capacity
							</div>
							<HSTooltip content='Enter the number of people the room needs to accommodate'>
								<FontAwesomeIcon
									icon={faInfoCircle}
									className='text-gray-500'
								/>
							</HSTooltip>
						</div>
						<div className='flex w-40 flex-col gap-1'>
							<HSTextField
								name='capacity'
								min={2}
								placeholder='Capacity'
								groupPlacement='left'
								groupItem={
									<div
										className={`text-xs font-medium text-gray-600 ${true ? 'text-gray-600' : 'text-white'}`}
									>
										People
									</div>
								}
								value={meetingSpaceRequest.capacity ?? ''}
								onChange={event => {
									setMeetingSpaceRequest({
										...meetingSpaceRequest,
										capacity: Number(event.target.value)
									})
								}}
							/>
						</div>
					</div>

					{roomLayoutOptions[meetingSpaceRequest.layoutStyle ?? '']
						?.additionalCapacityType ? (
						<div className='flex flex-col gap-2'>
							<div className='flex items-center gap-2'>
								<div className='text-sm font-normal text-gray-700'>
									Room Capacity
								</div>
								<HSTooltip content='Enter the number of people the room needs to accommodate'>
									<FontAwesomeIcon
										icon={faInfoCircle}
										className='text-gray-500'
									/>
								</HSTooltip>
							</div>
							<div className='flex w-40 flex-col gap-1'>
								<HSTextField
									mask={numberMask}
									name='additionalCapacity'
									value={meetingSpaceRequest.additionalCapacity || ''}
									min={2}
									placeholder='Capacity'
									onChange={event =>
										setMeetingSpaceRequest({
											...meetingSpaceRequest,
											additionalCapacity: Number(event.target.value)
										})
									}
									groupItem={
										<div className='text-xs font-normal text-gray-600'>
											{
												roomLayoutOptions[meetingSpaceRequest.layoutStyle ?? '']
													?.additionalCapacityType
											}
										</div>
									}
									groupPlacement='left'
								/>
							</div>
						</div>
					) : null}

					<div className='flex flex-col gap-2'>
						<div className='flex items-center gap-2'>
							<div className='text-sm font-normal text-gray-700'>
								Total Space (optional)
							</div>
							<HSTooltip content='Calculated based on Room Capacity and Preferred Layout. User may override to any value.'>
								<FontAwesomeIcon
									icon={faInfoCircle}
									className='text-gray-500'
								/>
							</HSTooltip>
						</div>
						<div className='flex w-40 flex-col gap-1'>
							<HSTextField
								name='amount'
								min={2}
								placeholder='Amount'
								groupPlacement='left'
								groupItem={
									<div
										className={`text-xs font-medium text-gray-600 ${true ? 'text-gray-600' : 'text-white'}`}
									>
										ft<sup>2</sup>
									</div>
								}
								value={meetingSpaceRequest.areaTotal ?? ''}
								onChange={event => {
									setMeetingSpaceRequest({
										...meetingSpaceRequest,
										areaTotal: Number(event.target.value)
									})
								}}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	) : null
}

export default RoomRequirements
