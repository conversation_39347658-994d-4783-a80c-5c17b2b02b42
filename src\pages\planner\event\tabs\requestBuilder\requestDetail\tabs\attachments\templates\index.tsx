import {
	faPenToSquare,
	faTrashXmark,
	faDownload
} from '@fortawesome/pro-light-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSButton from 'components/button'
import HSTooltipWithEllipsis from 'components/tooltipEllipsis'
import type { Attachment } from 'lib/store/attachmentsStore'
import type { EventPlan } from 'models/proposalResponseMonitor'

export const nameTemplate = (attachment: Attachment) => (
	<div className='flex flex-col'>
		<HSTooltipWithEllipsis
			className='text-sm font-medium text-gray-700'
			content={attachment.displayName ?? attachment.name}
		/>
		<div className='text-xs font-medium text-gray-400'>
			{attachment.extension}
		</div>
	</div>
)

export const ownerTemplate = (
	attachment: Attachment,
	eventPlan: EventPlan | null
) => {
	const proposalRequest = eventPlan?.proposalRequests?.find(
		pr => pr.venueId === attachment.venueId
	)
	return (
		<div>
			<div>{attachment.owner}</div>
			{proposalRequest ? (
				<div className='text-xs font-medium text-primary-700'>
					{proposalRequest.venueName}
				</div>
			) : null}
		</div>
	)
}

export const formatSharedWith = (attachment: Attachment) => {
	if (attachment.visibleToVenues) {
		const sharedWithCount = Object.keys(attachment.sharedWith).length
		if (sharedWithCount === 0) {
			return 'All hotels'
		}
		if (sharedWithCount > 2) {
			return `${sharedWithCount} hotels`
		}
		return Object.keys(attachment.sharedWith)
			.map(k => attachment.sharedWith[k])
			.join(',')
	}
	return 'Not shared with hotels'
}

export const actionsTemplate = (
	attachment: Attachment,
	onDeleteAttachments: (attachment: Attachment[]) => void,
	onEditAttachments: (attachment: Attachment) => void,
	onClickDownload: (selectedIds: string[]) => void,
	currentUser: string
	// eslint-disable-next-line @typescript-eslint/max-params
) => (
	<div className='flex items-center justify-center gap-[10px]'>
		<HSButton
			color='light'
			onClick={() => onEditAttachments(attachment)}
			disabled={currentUser !== attachment.createdBy}
		>
			<FontAwesomeIcon icon={faPenToSquare} />
		</HSButton>
		<HSButton color='light' onClick={() => onClickDownload([attachment.id])}>
			<FontAwesomeIcon icon={faDownload} />
		</HSButton>
		<HSButton
			color='light'
			onClick={() => onDeleteAttachments([attachment])}
			disabled={currentUser !== attachment.createdBy}
		>
			<FontAwesomeIcon icon={faTrashXmark} />
		</HSButton>
	</div>
)
