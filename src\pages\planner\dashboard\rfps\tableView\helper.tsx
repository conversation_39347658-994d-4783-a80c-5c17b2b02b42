import { faEllipsisVertical } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import HSDropdownButton from 'components/dropdown'
import { copyItemTypes } from 'lib/helpers/eventPlans'
import { useUserProfileContext } from 'lib/contexts/userProfile.context'
import type { EventsWithStats } from '../common'
import plannerDashboardStore from '../../dataStore'
import { eventInfoStore } from 'lib/store/plannerEvent/rfpStore'

interface IActionsTemplateProperties {
	rfp: EventsWithStats
	showTextLabel?: boolean
}

const ActionsTemplate = (properties: IActionsTemplateProperties) => {
	const { rfp, showTextLabel = false } = properties
	const { userProfile } = useUserProfileContext()
	const { setObject } = eventInfoStore()
	const {
		setShowShareModal,
		setShowDuplicateModal,
		setShowDeleteModal,
		setShowAssignOwnerModal,
		setShowCancelModal,
		setShowContractSignedDrawer
	} = plannerDashboardStore()

	const isOwnerOrEditor =
		rfp.ownerId?.toLowerCase() === userProfile?.id?.toLowerCase() ||
		userProfile?.organizationRole.isRfpEditor

	const isEditorOrAdmin =
		userProfile?.organizationRole.isRfpEditor || userProfile?.isAdmin

	const onMarkContractSigned = () => {
		setShowContractSignedDrawer(true)
		setObject(rfp)
	}

	const onShare = () => {
		setShowShareModal(true)
		setObject(rfp)
	}

	const onDuplicate = () => {
		setShowDuplicateModal(true)
		setObject(rfp)
	}

	const onDelete = () => {
		setShowDeleteModal(true)
		setObject(rfp)
	}

	const onCancel = () => {
		setShowCancelModal(true)
		setObject(rfp)
	}

	const onChangeStatus = () => {
		setObject(rfp)
	}

	const onTransfer = () => {
		setShowAssignOwnerModal(true)
		setObject(rfp)
	}

	return (
		<div className=''>
			<HSDropdownButton
				showDropdownIcon={showTextLabel}
				color='none'
				placement='auto'
				label={
					showTextLabel ? (
						<span>Actions</span>
					) : (
						<FontAwesomeIcon
							icon={faEllipsisVertical}
							className='h-4 w-6 text-primary-700'
						/>
					)
				}
				items={[
					// ...(currentUserIsEditor
					// 	? []
					// 	: [
					// 			{
					// 				id: '1',
					// 				item: 'Request Edit Permission',
					// 				clickFunction: onRequestPermission
					// 			}
					// 		]),

					// ...(currentUserIsEditor &&
					// (['submitted', 'bidding', 'contracting'].includes(
					// 	(rfp.status ?? '').toLowerCase()
					// ) ||
					// 	rfp.editingSentDateTime)
					// 	? [
					// 			{
					// 				id: '2',
					// 				item: rfp.editingSentDateTime
					// 					? 'Cancel edit'
					// 					: 'Edit this RFP...',
					// 				clickFunction: rfp.editingSentDateTime
					// 					? onClickCancelEditRfp
					// 					: onClickEditSentRfp
					// 			}
					// 		]
					// 	: []),

					...(rfp.status === 'Contracting' && isOwnerOrEditor
						? [
								{
									id: '3',
									item: 'Mark as Contract Signed',
									clickFunction: onMarkContractSigned
								}
							]
						: []),

					...(isOwnerOrEditor
						? [
								{
									id: '4',
									item: 'Share with planner(s)...',
									clickFunction: () => onShare()
								}
							]
						: []),

					{
						id: '5',
						item:
							rfp.itemType === copyItemTypes.eventPlan
								? 'Duplicate '
								: 'Create RFP...',
						clickFunction: onDuplicate
					},

					...(rfp.status === 'New' && isOwnerOrEditor
						? [
								{
									id: '6',
									item: 'Delete',
									clickFunction: onDelete
								}
							]
						: []),

					...(!['New', 'Cancelled', 'Abandoned', 'Contracting'].includes(
						rfp.status ?? ''
					) && isOwnerOrEditor
						? [
								{
									id: '7',
									item: 'Cancel',
									clickFunction: onCancel
								}
							]
						: []),

					...(userProfile?.isAdmin
						? [
								{
									id: '8',
									item: 'Change Status (ADMIN)',
									clickFunction: onChangeStatus
								}
							]
						: []),
					...(isEditorOrAdmin
						? [
								{
									id: '9',
									item: 'Assign Owner (ADMIN)',
									clickFunction: onTransfer
								}
							]
						: [])
				]}
				showTooltip={false}
			/>
		</div>
	)
}

export default ActionsTemplate
