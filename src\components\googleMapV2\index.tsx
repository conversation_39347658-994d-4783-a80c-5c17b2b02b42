/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable consistent-return */
/* eslint-disable @typescript-eslint/consistent-return */
/* eslint-disable react/no-array-index-key */
/* eslint-disable react/jsx-curly-brace-presence */
import type { MapCameraChangedEvent } from '@vis.gl/react-google-maps'
import { Map, useMap } from '@vis.gl/react-google-maps'
import { Polygon } from './components/polygon'
import React, { useCallback, useEffect, useState, type JSX } from 'react'
import HotelMarker from './components/hotelMarker'
import { v4 as uuidv4 } from 'uuid'
import type { IDrawingOptions, ILocation } from './type'
import type { MapLocation } from 'pages/hotel/platformSuggestions/helper'
import TrafficLayer from './Layers/trafficLayer'
import TransitLayer from './Layers/transitLayer'

export const defaultCenter = {
	lat: 39.831_909,
	lng: -98.539_268
}
const defaultZoom = 10

interface IGoogleMapV2Properties {
	defaultCenter?: google.maps.LatLngLiteral
	zoom?: number
	center?: google.maps.LatLngLiteral
	defaultZoom?: number
	markerSet?: ILocation[]
	onChange?: ({
		zoom,
		bounds,
		center
	}: {
		zoom: number | undefined
		bounds: google.maps.LatLngBounds | undefined
		center: google.maps.LatLng
	}) => void
	polygons?: (
		| {
				paths: MapLocation[] | undefined
				fillColor?: string
				strokeColor?: string
				onClick?: () => void
		  }
		| undefined
	)[]
	markerComponent?: JSX.Element[]
	drawingOptions?: IDrawingOptions
	onPolygonComplete?: (polygon: google.maps.Polygon) => void
	onMarkerComplete?: (marker: google.maps.Marker) => void
	onCircleComplete?: (circle: google.maps.Circle) => void
	onRectangleComplete?: (rectangle: google.maps.Rectangle) => void
	onPolyLineComplete?: (polyline: google.maps.Polyline) => void
	onBoundsChanged?: (event: MapCameraChangedEvent) => void
	loadMap?: (map: google.maps.Map | null) => void
	showTraffic?: boolean
	showTransit?: boolean
	infoMarker?: () => JSX.Element | null
}

const mapId = uuidv4()

const GoogleMapsV2 = React.memo((properties: IGoogleMapV2Properties) => {
	const {
		markerSet,
		polygons,
		zoom,
		center,
		onChange,
		markerComponent,
		defaultCenter: mapDefaultCenter = defaultCenter,
		defaultZoom: mapDefaultZoom = defaultZoom,
		drawingOptions,
		onPolygonComplete,
		onMarkerComplete,
		onCircleComplete,
		onRectangleComplete,
		onBoundsChanged,
		loadMap,
		showTraffic,
		showTransit,
		infoMarker
	} = properties
	const [selectedLocation, setSelectedLocation] = useState<ILocation[]>([])
	const [lastPolygon, setLastPolygon] = useState<google.maps.Polygon | null>(
		null
	)

	const map = useMap()

	useEffect(() => {
		if (map && drawingOptions?.enabled) {
			// Initialize drawing manager
			const manager = new google.maps.drawing.DrawingManager({
				drawingMode: drawingOptions.drawingMode ?? null,
				drawingControl: drawingOptions.drawingControl ?? true,
				drawingControlOptions: {
					position:
						drawingOptions.drawingControlOptions?.position ??
						google.maps.ControlPosition.TOP_CENTER,
					drawingModes: drawingOptions.drawingControlOptions?.drawingModes ?? [
						google.maps.drawing.OverlayType.MARKER,
						google.maps.drawing.OverlayType.POLYGON,
						google.maps.drawing.OverlayType.RECTANGLE,
						google.maps.drawing.OverlayType.CIRCLE
					]
				},
				polygonOptions: {
					fillColor: '#95D9D0',
					fillOpacity: 0.05,
					strokeWeight: 3,
					strokeOpacity: 0.8,
					strokeColor: '#027587',
					editable: true
				}
			})

			manager.setMap(map)

			// Set up drawing complete event listeners
			if (onPolygonComplete) {
				google.maps.event.addListener(
					manager,
					'polygoncomplete',
					(polygon: google.maps.Polygon) => {
						if (lastPolygon) {
							lastPolygon.setMap(null)
						}
						setLastPolygon(polygon)
						onPolygonComplete(polygon)
					}
				)
			}
			if (onMarkerComplete) {
				google.maps.event.addListener(
					manager,
					'markercomplete',
					onMarkerComplete
				)
			}
			if (onCircleComplete) {
				google.maps.event.addListener(
					manager,
					'circlecomplete',
					onCircleComplete
				)
			}
			if (onRectangleComplete) {
				google.maps.event.addListener(
					manager,
					'rectanglecomplete',
					onRectangleComplete
				)
			}

			return () => {
				manager.setMap(null)
			}
		}
	}, [
		map,
		drawingOptions,
		onPolygonComplete,
		onMarkerComplete,
		onCircleComplete,
		onRectangleComplete,
		lastPolygon
	])

	const handleBoundsChange = (
		mapZoom: number,
		bounds: google.maps.LatLngBounds,
		mapCenter: google.maps.LatLng
	) => {
		if (onChange && map) {
			onChange({ zoom: mapZoom, bounds, center: mapCenter })
		}
	}

	useEffect(() => {
		map?.panTo(center ?? mapDefaultCenter)
		map?.setZoom(zoom ?? mapDefaultZoom)
	}, [center, map, mapDefaultCenter, mapDefaultZoom, zoom])

	const renderMarkerSet = useCallback(
		() =>
			markerSet?.map((marker_, index) => (
				<HotelMarker
					key={index}
					position={{
						lat: Number(marker_.lat ?? 0),
						lng: Number(marker_.lng ?? 0)
					}}
					onClick={() => {
						setSelectedLocation([
							...selectedLocation,
							{ lat: Number(marker_.lat ?? 0), lng: Number(marker_.lng ?? 0) }
						])
					}}
					title={marker_.title ?? ''}
				/>
			)),
		[markerSet, selectedLocation]
	)

	useEffect(() => {
		if (loadMap) loadMap(map)
	}, [loadMap, map])

	return (
		<Map
			mapId={mapId}
			defaultCenter={mapDefaultCenter}
			zoom={zoom}
			reuseMaps
			defaultZoom={mapDefaultZoom}
			onZoomChanged={event =>
				handleBoundsChange(
					event.detail.zoom,
					map?.getBounds() as google.maps.LatLngBounds,
					map?.getCenter() as google.maps.LatLng
				)
			}
			onDragend={() => {
				handleBoundsChange(
					map?.getZoom() ?? 0,
					map?.getBounds() as google.maps.LatLngBounds,
					map?.getCenter() as google.maps.LatLng
				)
			}}
			gestureHandling={'greedy'}
			onBoundsChanged={event => onBoundsChanged?.(event)}
		>
			{showTraffic ? <TrafficLayer map={map} /> : null}
			{showTransit ? <TransitLayer map={map} /> : null}
			{markerComponent ?? renderMarkerSet()}
			{Number(polygons?.length) > 0
				? polygons?.map((p, index) => (
						<Polygon
							key={`${mapId}-${index}`}
							paths={p?.paths}
							fillColor={p?.fillColor || '#95D9D0'}
							fillOpacity={0.05}
							strokeWeight={2}
							strokeOpacity={0.8}
							strokeColor={p?.strokeColor || '#027587'}
							onClick={p?.onClick}
						/>
					))
				: null}
			{infoMarker ? (infoMarker() ?? null) : null}
		</Map>
	)
})

export default GoogleMapsV2
