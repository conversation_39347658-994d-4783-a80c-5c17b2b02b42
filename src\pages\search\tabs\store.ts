import { getDefaultEventPlan } from 'components/profile/tabs/overviewPlanner/helper'
import type { EventPlan } from 'models/proposalResponseMonitor'
import type { IUserProfile } from 'models/userProfiles'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Import putIntent function - adjust this path based on your project structure
import { updateEvent as putIntent } from 'lib/services/event.service'

// Create the Zustand store
const useSiteSearchStore = create<{
	// State
	selectedEventPlan: EventPlan | undefined
	saving: boolean

	// Actions
	setSaving: (saving: boolean) => void
	setObject: (eventPlan: EventPlan, clientOnly?: boolean) => void
	setProperty: (name: string, value: unknown, clientOnly?: boolean) => void
	mergeProperties: (value: Partial<EventPlan>, clientOnly?: boolean) => void

	// Initialization
	initializeStore: (userProfile: IUserProfile) => void
}>()(
	devtools((set, get) => ({
		// Initial state
		selectedEventPlan: undefined,
		saving: false,

		// Actions
		setSaving: saving => set({ saving }),

		setObject: (eventPlan, clientOnly = false) => {
			set({ selectedEventPlan: eventPlan })

			if (!clientOnly && eventPlan.id) {
				set({ saving: true })
				// Wrap the promise handling to avoid errors
				void putIntent(eventPlan.id, {
					action: { type: 'setObject' },
					value: eventPlan
				})
					.catch((error: unknown) => {
						console.error('Error updating event plan:', error)
					})
					.finally(() => {
						set({ saving: false })
					})
			}
		},

		setProperty: (name, value, clientOnly = false) => {
			set(state => {
				if (!state.selectedEventPlan) {
					return state
				}
				return {
					...state,
					selectedEventPlan: {
						...state.selectedEventPlan,
						[name]: value
					}
				}
			})

			if (!clientOnly && get().selectedEventPlan?.id) {
				set({ saving: true })
				void putIntent(get().selectedEventPlan?.id || '', {
					action: { type: 'setProperty' },
					value: { name, value }
				})
					.catch((error: unknown) => {
						console.error('Error updating event property:', error)
					})
					.finally(() => {
						set({ saving: false })
					})
			}
		},

		mergeProperties: (value, clientOnly = false) => {
			set(state => {
				if (!state.selectedEventPlan) {
					return state
				}
				return {
					...state,
					selectedEventPlan: {
						...state.selectedEventPlan,
						...value
					}
				}
			})

			if (!clientOnly && get().selectedEventPlan?.id) {
				set({ saving: true })
				void putIntent(get().selectedEventPlan?.id || '', {
					action: { type: 'mergeProperties' },
					value
				})
					.catch((error: unknown) => {
						console.error('Error merging event properties:', error)
					})
					.finally(() => {
						set({ saving: false })
					})
			}
		},

		initializeStore: userProfile => {
			const defaultPlan = getDefaultEventPlan(
				userProfile,
				'eventPlan.siteSearch'
			)
			set({ selectedEventPlan: defaultPlan })
		}
	}))
)

export default useSiteSearchStore
